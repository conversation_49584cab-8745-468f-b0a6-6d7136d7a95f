package com.cdz360.biz.ess.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "APP分享信息")
@Data
@Accessors(chain = true)
public class ShareAppVo {

    @Schema(description = "分享链接")
    @JsonInclude(Include.NON_EMPTY)
    private String url;

    @Schema(description = "分享标题")
    @JsonInclude(Include.NON_EMPTY)
    private String title;

    @Schema(description = "分享二级标题")
    @JsonInclude(Include.NON_EMPTY)
    private String subTitle;

    @Schema(description = "分享内容(html格式)")
    @JsonInclude(Include.NON_EMPTY)
    private String context;
}
