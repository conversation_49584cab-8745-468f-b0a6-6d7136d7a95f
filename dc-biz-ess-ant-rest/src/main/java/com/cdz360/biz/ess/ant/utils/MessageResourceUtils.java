package com.cdz360.biz.ess.ant.utils;

import org.springframework.context.MessageSource;
import org.springframework.lang.Nullable;
import org.springframework.web.server.ServerWebExchange;

public class MessageResourceUtils {

    public static String codeConvert(
        MessageSource messageSource, ServerWebExchange exh, String code) {
        return messageSource.getMessage(code, null, EssAntRestUtils.getLocale(exh));
    }

    public static String codeConvert(
        MessageSource messageSource, ServerWebExchange exh, String code, @Nullable Object[] args) {
        if (null == args) {
            return codeConvert(messageSource, exh, code);
        }
        return messageSource.getMessage(code, args, EssAntRestUtils.getLocale(exh));
    }
}
