package com.cdz360.biz.ess.ant.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ess.ant.feign.BizBiFeignClient;
import com.cdz360.biz.ess.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.utils.feign.iot.DeviceFeignClient;
import com.cdz360.biz.ess.ant.feign.EssFeignClient;
import com.cdz360.biz.ess.ant.utils.FeignResponseAndValidate;
import com.cdz360.biz.ess.model.data.param.EssBaseParam;
import com.cdz360.biz.ess.model.data.vo.DayEssDataBi;
import com.cdz360.biz.ess.model.dto.EssTimelyDataDto;
import com.cdz360.biz.ess.model.param.EssMapDataParam;
import com.cdz360.biz.ess.model.param.SummaryParam;
import com.cdz360.biz.ess.model.vo.CommEssMapDataVo;
import com.cdz360.biz.ess.model.vo.EssMapDataVo;
import com.cdz360.biz.ess.model.vo.EssRecentData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class DeviceBiService {

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private BizBiFeignClient bizBiFeignClient;

    @Autowired
    private DeviceFeignClient deviceFeignClient;

    @Autowired
    private EssFeignClient essFeignClient;

    public Mono<ListResponse<EssMapDataVo>> essMapData(EssMapDataParam param) {
        return essFeignClient.essMapData(param);
    }

    public Mono<ObjectResponse<CommEssMapDataVo>> commEssMapData(EssMapDataParam param) {
        return dataCoreFeignClient.commEssMapData(param);
//        // FIXME: 场站的装机容量/装机功率 -> 临时实现方式
//        ListEssSiteParam siteParam = new ListEssSiteParam().setGids(param.getGids());
//        siteParam.setTotal(false);
//        return Mono.zip(dataCoreFeignClient.commEssMapData(param)
//                    .flatMap(FeignResponseAndValidate::checkAndReturnData),
//                dataCoreFeignClient.findEssSite(siteParam)
//                    .flatMap(FeignResponseAndValidate::checkAndReturnData))
//            .map(tuple -> {
//                CommEssMapDataVo t1 = tuple.getT1();
//                List<EssSiteVo> t2 = tuple.getT2();
//                BigDecimal essCapacity = t2.stream()
//                    .map(EssSiteVo::getEssCapacity)
//                    .filter(Objects::nonNull)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//                BigDecimal essPower = t2.stream()
//                    .map(EssSiteVo::getEssPower)
//                    .filter(Objects::nonNull)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//                return t1.setDeviceCapacity(essCapacity)
//                    .setDevicePower(essPower);
//            });
    }

    public Mono<ListResponse<EssTimelyDataDto>> getEssTimelyDataList(SummaryParam param) {
        return bizBiFeignClient.getEssTimelyDataList(param)
            .doOnNext(FeignResponseAndValidate::check);
    }

    public Mono<ObjectResponse<EssRecentData>> getProfitSummary(EssBaseParam param) {
        return bizBiFeignClient.getProfitSummary(param);
    }


    public Mono<ListResponse<DayEssDataBi>> siteInOutBi7(String siteId) {
        return dataCoreFeignClient.siteRtData7Day(siteId);
//        return deviceFeignClient.siteRtData7Day(siteId);
    }
}
