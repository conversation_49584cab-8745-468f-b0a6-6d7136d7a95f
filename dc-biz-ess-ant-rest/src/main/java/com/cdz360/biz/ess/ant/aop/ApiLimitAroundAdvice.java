package com.cdz360.biz.ess.ant.aop;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.biz.ess.ant.service.CacheManagerService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Objects;

@Component
@Aspect
@Slf4j
public class ApiLimitAroundAdvice {

    @Autowired
    private CacheManagerService cacheManagerService;

    @Around(value = "@annotation(com.cdz360.biz.ess.ant.aop.ApiLimit)")
    public Object around(ProceedingJoinPoint point) {
        Method method = ((MethodSignature) point.getSignature()).getMethod();
        try {
            ApiLimit limit = method.getAnnotation(ApiLimit.class);
            if (null == limit) {
                limit = method.getDeclaringClass().getAnnotation(ApiLimit.class);
            }

            if (null != limit) {
                if (log.isDebugEnabled()) {
                    log.debug(">>> api limit: {}, {}次 / {}秒",
                        method.getDeclaringClass().getName() + method.getName(),
                        limit.max(), limit.second());
                }
                ServerHttpRequest request = null;
                for (Object arg : point.getArgs()) {
                    if (arg instanceof ServerHttpRequest) {
                        request = (ServerHttpRequest) arg;
                        break;
                    }
                }

                if (null != request) {
                    if (isLimit(request, limit)) {
                        log.warn("接口访问次数受限: {} / {}", limit.second(), limit.max());
                        throw new DcArgumentException("重复操作,请稍后重试!");
                    }
                }
            }
            return point.proceed(point.getArgs());
        } catch (DcException ex) {
            throw ex;
        } catch (Throwable throwable) {
            log.warn("api limit error: {}", throwable.getMessage(), throwable);
            throw new DcServiceException("重复操作,请稍后重试!");
        }
    }

    public boolean isLimit(ServerHttpRequest request, ApiLimit limit) {
        return limit.max() < cacheManagerService.getApiLimitAndIncrement(
            this.apikey(request), limit.second());
    }

    private String apikey(ServerHttpRequest request) {
        return "limit:" + request.getPath() + ":" +
            Objects.requireNonNull(request.getRemoteAddress()).getHostString();
    }
}
