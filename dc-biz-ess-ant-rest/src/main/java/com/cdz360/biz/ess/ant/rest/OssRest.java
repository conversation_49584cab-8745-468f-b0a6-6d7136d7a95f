package com.cdz360.biz.ess.ant.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ess.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.model.oss.OssStsDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "文件上传", description = "文件上传")
public class OssRest {

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Operation(summary = "获取文件上传的STS信息")
    @GetMapping(value = "/api/oss/getSts")
    public Mono<ObjectResponse<OssStsDto>> getSts(ServerHttpRequest request) {
        return dataCoreFeignClient.getSts();
    }

    @Operation(summary = "获取文件上传的STS信息(Private)")
    @GetMapping(value = "/api/oss/getPrivateSts")
    public Mono<ObjectResponse<OssStsDto>> getPrivateSts(ServerHttpRequest request) {
        return dataCoreFeignClient.getPrivateSts();
    }
}
