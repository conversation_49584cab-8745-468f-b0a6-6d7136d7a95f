package com.cdz360.biz.ess.ant.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.es.vo.EmuMonitorMetricsVo;
import com.cdz360.biz.ess.ant.service.EmuMonitorService;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "EMU设备监控数据接口", description = "EMU设备监控数据相关接口合集")
@Slf4j
@RestController
@RequestMapping("/device/emu/monitor")
public class EmuMonitorRest {

    @Autowired
    private EmuMonitorService emuMonitorService;

    @Operation(summary = "获取关键指标数据")
    @GetMapping("/{siteId}/metrics")
    public Mono<ObjectResponse<EmuMonitorMetricsVo>> siteEssDeviceMetrics(
        ServerHttpRequest request,
        @Valid @ApiParam(value = "场站ID", required = true)
        @PathVariable(value = "siteId") String siteId) {
        log.info("获取关键指标数据: siteId = {}", siteId);
        return emuMonitorService.siteEssDeviceMetrics(siteId);
    }

    @Operation(summary = "获取EMS通用控制信息")
    @GetMapping("/{emuDno}/universal/ctl/metrics")
    public Mono<ObjectResponse<EmuMonitorMetricsVo>> emuUniversalCtlMetrics(
        ServerHttpRequest request,
        @Valid @ApiParam(value = "EMS设备编号", required = true, example = "GWNO0123456")
        @PathVariable(value = "emuDno") String emuDno) {
        log.info("获取EMS通用控制信息: emuDno = {}", emuDno);
        return emuMonitorService.emuUniversalCtlMetrics(emuDno);
    }

}
