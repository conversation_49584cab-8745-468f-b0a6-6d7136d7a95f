package com.cdz360.biz.ess.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ess.model.data.param.EssBaseParam;
import com.cdz360.biz.ess.model.data.po.EssEquipTimelyPo;
import com.cdz360.biz.ess.model.dto.EssEquipDailyElecBiDto;
import com.cdz360.biz.ess.model.dto.EssTimelyDataDto;
import com.cdz360.biz.ess.model.param.SummaryParam;
import com.cdz360.biz.ess.model.vo.EssRecentData;
import com.cdz360.biz.model.bi.site.SiteErrorCount;
import com.cdz360.biz.model.ess.param.ListEssDailyParam;
import com.cdz360.biz.model.iot.param.SiteBiParam;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_BI,
    fallbackFactory = BizBiFeignHystrix.class)
public interface BizBiFeignClient {

    // 场站告警统计
    @PostMapping(value = "/bi/order/breakdownBi")
    Mono<ListResponse<SiteErrorCount>> breakdownBi(@RequestBody SiteBiParam param);

    // 工商储场站告警统计
    @PostMapping(value = "/bi/order/commSite/breakdownBi")
    Mono<ListResponse<SiteErrorCount>> commEssSiteBreakdownBi(@RequestBody SiteBiParam param);

    //储能数据统计
    @PostMapping("/bi/ess/getEssTimelyDataList")
    Mono<ListResponse<EssTimelyDataDto>> getEssTimelyDataList(@RequestBody SummaryParam param);

    @Operation(summary = "储能收益统计", description = "昨日/本月/上月收益")
    @PostMapping("/bi/ess/getProfitSummary")
    Mono<ObjectResponse<EssRecentData>> getProfitSummary(@RequestBody EssBaseParam param);
//    @PostMapping(value = "/bi/download/downloadFile")
//    Response downloadFile(@RequestBody DownloadFileParam param);


    // 储能设备分时段明细数据
    @PostMapping("/bi/ess/getTimelyElecList")
    Mono<ListResponse<EssEquipTimelyPo>> getTimelyElecList(
        @RequestBody ListEssDailyParam param);


    //    @Operation(summary = "按日统计各时段充放电量数据")
    @PostMapping("/bi/ess/getDailyElecBiList")
    Mono<ListResponse<EssEquipDailyElecBiDto>> getDailyElecBiList(
        @RequestBody ListEssDailyParam param);

    /**
     * 按月统计各时段充放电量数据
     */
    @PostMapping("/bi/ess/getMonthlyElecBiList")
    Mono<ListResponse<EssEquipDailyElecBiDto>> getMonthlyElecBiList(
        @RequestBody ListEssDailyParam param);

}
