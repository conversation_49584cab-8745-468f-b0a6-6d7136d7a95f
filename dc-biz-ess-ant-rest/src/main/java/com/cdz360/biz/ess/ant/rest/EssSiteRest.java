package com.cdz360.biz.ess.ant.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.param.SortParam;
import com.cdz360.base.model.base.type.OrderType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ess.ant.service.EssSiteService;
import com.cdz360.biz.ess.ant.utils.EssAntRestUtils;
import com.cdz360.biz.ess.model.site.param.ListEssSiteParam;
import com.cdz360.biz.ess.model.site.vo.EssSiteVo;
import com.cdz360.biz.model.cus.site.po.SitePo;
import com.cdz360.biz.model.site.param.AddSiteParam;
import com.cdz360.biz.model.site.param.UpdateSiteParam;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "储能站点", description = "储能站点相关接口合集")
@Slf4j
@RestController
@RequestMapping("/ess/site")
public class EssSiteRest {

    @Autowired
    private EssSiteService essSiteService;

    @Operation(summary = "获取站点列表")
    @PostMapping("/findSite")
    public Mono<ListResponse<EssSiteVo>> findSite(
        ServerHttpRequest request,
        @RequestBody ListEssSiteParam param) {
        log.info("获取站点列表: {}", param);
        param.setGids(EssAntRestUtils.getSysUserGids(request))
            .setSorts(List.of(SortParam.as("site.create_time", OrderType.desc)));
        if (CollectionUtils.isEmpty(param.getGids())) {
            return Mono.just(RestUtils.buildListResponse(List.of(), 0));
        }
        return essSiteService.findSite(param);
    }

    // 新增: /api/site/addSite
    @Operation(summary = "新增场站")
    @PostMapping("/addSite")
    public Mono<ObjectResponse<SitePo>> addEssSite(
        ServerHttpRequest request,
        @RequestBody AddSiteParam param) {
        log.info("新增ESS场站: {}", param);
        val cusInfo = EssAntRestUtils.getAppCusInfo(request);
        Long topCommId = cusInfo.getTopCommId();
        param.setTopCommId(topCommId).setOperateId(topCommId);
        return essSiteService.addEssSite(cusInfo.getCusId(), param);
    }

    // 编辑: /api/site/updateSiteInfo
    @Operation(summary = "修改场站信息")
    @PostMapping("/editSite")
    public Mono<ObjectResponse<SitePo>> editSite(
        ServerHttpRequest request,
        @RequestBody UpdateSiteParam param) {
        log.info("修改ESS场站: {}", param);
        Long topCommId = EssAntRestUtils.getAppCusInfo(request).getTopCommId();
        param.setTopCommId(topCommId).setOperateId(topCommId);
        return essSiteService.editSite(param);
    }

    // 查询: /api/site/getSiteDetail
    @Operation(summary = "获取场站详情")
    @GetMapping(value = "/{siteId}/detail")
    public Mono<ObjectResponse<EssSiteVo>> getSiteDetail(
        ServerHttpRequest request,
        @Valid @ApiParam(value = "场站ID", required = true)
        @PathVariable(value = "siteId") String siteId) {
        log.info("获取场站详情: {}", siteId);
        return essSiteService.getSiteDetail(siteId);
    }

    // 删除: /api/site/updateSiteStatus
    @Operation(summary = "上/下线场站")
    @PutMapping("/{siteId}/enable")
    public Mono<ObjectResponse<SitePo>> enableSite(
        ServerHttpRequest request,
        @Valid @ApiParam(value = "场站ID", required = true)
        @PathVariable(value = "siteId") String siteId,
        @Valid @ApiParam(value = "true(上线)/false(下线)", required = true)
        @RequestParam("enable") Boolean enable) {
        log.info("上/下线场站: enable = {}", enable);
        return essSiteService.enableEssSite(siteId, enable);
    }

    // 设备统计数据
    // /api/pvDevice/findCtrlList
    // /api/pvDevice/addCtrl
    // /api/pvDevice/countCtrlGti?siteId=22120100770
}
