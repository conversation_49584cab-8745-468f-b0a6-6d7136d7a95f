package com.cdz360.biz.ess.ant.model.vo;

import com.cdz360.base.model.es.vo.BatData;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "电池运行时数据")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class BatDataVo extends BatData {

    @Schema(title = "状态", description = "设备状态.0未知;1在线;2离线;3启动中;4告警;5故障;99,下线")
    @JsonInclude(Include.NON_NULL)
    private EquipStatus status;
}
