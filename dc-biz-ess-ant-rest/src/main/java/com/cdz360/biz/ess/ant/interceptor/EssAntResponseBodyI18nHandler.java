package com.cdz360.biz.ess.ant.interceptor;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ess.ant.utils.MessageResourceUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.core.ReactiveAdapterRegistry;
import org.springframework.http.codec.HttpMessageWriter;
import org.springframework.web.reactive.HandlerResult;
import org.springframework.web.reactive.accept.RequestedContentTypeResolver;
import org.springframework.web.reactive.result.method.annotation.ResponseBodyResultHandler;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.List;

@Slf4j
public class EssAntResponseBodyI18nHandler extends ResponseBodyResultHandler {

    private MessageSource messageSource;

    public EssAntResponseBodyI18nHandler(List<HttpMessageWriter<?>> writers,
                                         RequestedContentTypeResolver resolver, MessageSource messageSource) {
        super(writers, resolver);
        this.messageSource = messageSource;
    }

    public EssAntResponseBodyI18nHandler(List<HttpMessageWriter<?>> writers,
                                         RequestedContentTypeResolver resolver, ReactiveAdapterRegistry registry) {
        super(writers, resolver, registry);
    }

    @Override
    @SuppressWarnings("ALL")
    public Mono<Void> handleResult(ServerWebExchange exh, HandlerResult result) {
        Object returnValue = result.getReturnValue();
        Object body;
        if (returnValue instanceof String) {
            body = MessageResourceUtils.codeConvert(
                messageSource, exh, (String) returnValue); // msg convert
        } else if (returnValue instanceof Mono) {
            body = ((Mono<?>) returnValue)
                .map(x -> {
                    if (x instanceof BaseResponse) {
                        final int status = ((BaseResponse) x).getStatus();
                        final String error = ((BaseResponse) x).getError();
                        if (status != 0 || StringUtils.isNotBlank(error)) {
                            final String convert = MessageResourceUtils.codeConvert(
                                messageSource, exh, error); // msg convert

                            if (x instanceof ObjectResponse) {
                                final ObjectResponse<?> res = RestUtils.buildObjectResponse(
                                    ((ObjectResponse<?>) x).getData());
                                res.setStatus(status).setError(convert);
                                return res;
                            }

                            return RestUtils.fail(status, convert);
                        } else {
                            if (x instanceof ObjectResponse) {
                                final Object data = ((ObjectResponse<?>) x).getData();
                                if (data instanceof String) {
                                    return RestUtils.buildObjectResponse(
                                        MessageResourceUtils.codeConvert(
                                            messageSource, exh, (String) data));
                                }
                            }
                        }
                    }
                    return x;
                });
//        } else if (returnValue instanceof Flux) {
            // nothing to do
        } else {
            // nothing to do
            body = returnValue;
        }
        return writeBody(body, result.getReturnTypeSource(), exh);
    }

}
