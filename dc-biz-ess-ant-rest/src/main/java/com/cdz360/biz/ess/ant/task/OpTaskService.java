package com.cdz360.biz.ess.ant.task;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ess.ant.model.JobNode;
import com.cdz360.biz.ess.ant.model.JobType;
import com.cdz360.biz.ess.ant.model.param.EssDynamicCfgRWParam;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OpTaskService {

    private final static Map<String, JobNode<?>> JOBS_MAP = new ConcurrentHashMap<>();
    private final static Map<String, List<JobNode<?>>> DYNAMIC_JOBS_MAP = new ConcurrentHashMap<>();

    public void addDynamicJob(String dno, String category, JobNode<?> job) {
        String key = dynamicKey(category, dno);
        if (!DYNAMIC_JOBS_MAP.containsKey(key)) {
            DYNAMIC_JOBS_MAP.put(key, new ArrayList<>());
        }
        DYNAMIC_JOBS_MAP.get(key).add(job);
        log.info("加入监听队列: key = {}", key);
    }

    public void addJob(String dno, JobNode<?> job) {
        JOBS_MAP.put(dno, job);
        log.info("加入监听队列: dno = {}", dno);
    }

    public void receiveMessage(JobNode<?> msg) {
        if (StringUtils.isNotBlank(msg.getDno())) {
            if (JobType.OP_RESS_RW_CFG.equals(msg.getType())) {
                EssDynamicCfgRWParam data = JsonUtils.fromJson(
                    msg.getData().toString(), EssDynamicCfgRWParam.class);
                if (null == data) {
                    return;
                }

                String key = dynamicKey(data.getCategory(), msg.getDno());
                if (DYNAMIC_JOBS_MAP.containsKey(key)) {
                    val nodeList = DYNAMIC_JOBS_MAP.get(key);
                    if (null != nodeList) {
                        Optional<JobNode<?>> first = nodeList.stream()
                            .filter(x -> null != x.getSeq() && x.getSeq().equals(msg.getSeq()))
                            .findFirst();
                        if (first.isPresent()) {
                            first.get().getSink().success(data);
                            nodeList.remove(first.get());
                        }
                    }
                }
                return;
            }

            if (JOBS_MAP.containsKey(msg.getDno())) {
                val node = JOBS_MAP.get(msg.getDno());
                if (null != node && null != node.getSink()) {
                    node.getSink().success(msg.getData());
                    JOBS_MAP.remove(msg.getDno());
                }
            }
        }
    }

    private static String dynamicKey(String category, String dno) {
        return category + ":" + dno;
    }

//    public static void main(String[] args) {
//        String body = "{\"dno\":\"G6DDYSOU\",\"type\":\"OP_RESS_RW_CFG\",\"data\":\"{\\\"category\\\":\\\"INV_HOST\\\",\\\"tvs\\\":[{\\\"code\\\":245001,\\\"addr\\\":12290,\\\"name\\\":\\\"防逆流使能\\\",\\\"v\\\":1.0,\\\"desc\\\":\\\"0：禁止，1：使能\\\",\\\"max\\\":1,\\\"min\\\":0},{\\\"code\\\":245002,\\\"addr\\\":12292,\\\"name\\\":\\\"防逆流功率\\\",\\\"v\\\":5.0,\\\"max\\\":20,\\\"min\\\":0,\\\"unit\\\":\\\"kW\\\"},{\\\"code\\\":245003,\\\"addr\\\":12294,\\\"name\\\":\\\"相线制\\\",\\\"v\\\":0.0,\\\"desc\\\":\\\"0 为三相四线制，1 为三相三线制，默认为0\\\",\\\"max\\\":1,\\\"min\\\":0},{\\\"code\\\":245004,\\\"addr\\\":12296,\\\"name\\\":\\\"输入电压等级\\\",\\\"v\\\":0.0,\\\"desc\\\":\\\"0：380/400/415；1：440/480；2：660/690；3：1140；4：208/220；5：600\\\",\\\"max\\\":5,\\\"min\\\":0},{\\\"code\\\":245005,\\\"addr\\\":12298,\\\"name\\\":\\\"输入频率等级\\\",\\\"v\\\":0.0,\\\"desc\\\":\\\"0:50Hz 1:60Hz\\\",\\\"max\\\":1,\\\"min\\\":0},{\\\"code\\\":245006,\\\"addr\\\":12300,\\\"name\\\":\\\"工作模式\\\",\\\"v\\\":0.0,\\\"desc\\\":\\\"0：自发自用模式，1：定时充放电模式，2：分时电价模式（暂不显示），3：灾备模式，4：手动控制模式（暂不显示），5：光伏优先充电\\\",\\\"max\\\":5,\\\"min\\\":0},{\\\"code\\\":245007,\\\"addr\\\":12302,\\\"name\\\":\\\"离并网切换方式\\\",\\\"v\\\":0.0,\\\"desc\\\":\\\"0：自动离并网切换模式，1：手动离网模式，2：手动并网模式\\\",\\\"max\\\":2,\\\"min\\\":0},{\\\"code\\\":245008,\\\"addr\\\":12304,\\\"name\\\":\\\"并网不平衡补偿使能\\\",\\\"v\\\":1.0,\\\"desc\\\":\\\"0：禁止，1：使能\\\",\\\"max\\\":1,\\\"min\\\":0},{\\\"code\\\":245009,\\\"addr\\\":12306,\\\"name\\\":\\\"温度降额\\\",\\\"v\\\":1.0,\\\"desc\\\":\\\"0：禁止，1：使能\\\",\\\"max\\\":1,\\\"min\\\":0},{\\\"code\\\":245010,\\\"addr\\\":12308,\\\"name\\\":\\\"高电压穿越\\\",\\\"v\\\":0.0,\\\"desc\\\":\\\"0：禁止，1：使能\\\",\\\"max\\\":1,\\\"min\\\":0},{\\\"code\\\":245011,\\\"addr\\\":12310,\\\"name\\\":\\\"低电压穿越\\\",\\\"v\\\":0.0,\\\"desc\\\":\\\"0：禁止，1：使能\\\",\\\"max\\\":1,\\\"min\\\":0},{\\\"code\\\":245012,\\\"addr\\\":12312,\\\"name\\\":\\\"风扇档位\\\",\\\"v\\\":0.5,\\\"max\\\":1,\\\"min\\\":0.5},{\\\"code\\\":245013,\\\"addr\\\":12314,\\\"name\\\":\\\"固定功率因数\\\",\\\"v\\\":1.0,\\\"max\\\":1,\\\"min\\\":-1},{\\\"code\\\":245014,\\\"addr\\\":12316,\\\"name\\\":\\\"自老化类型\\\",\\\"v\\\":0.0,\\\"desc\\\":\\\"0：禁止自老化，1：有功自老化，2：感性自老化，3：容性自老化\\\",\\\"max\\\":3,\\\"min\\\":0},{\\\"code\\\":245015,\\\"addr\\\":12318,\\\"name\\\":\\\"补偿率\\\",\\\"v\\\":1.0,\\\"max\\\":1.1,\\\"min\\\":0},{\\\"code\\\":245016,\\\"addr\\\":12320,\\\"name\\\":\\\"测试模式\\\",\\\"v\\\":0.0,\\\"desc\\\":\\\"0：不使能，1：使能\\\",\\\"max\\\":1,\\\"min\\\":0},{\\\"code\\\":245017,\\\"addr\\\":12322,\\\"name\\\":\\\"设定CT变比\\\",\\\"v\\\":0.0,\\\"max\\\":30000,\\\"min\\\":0},{\\\"code\\\":245018,\\\"addr\\\":12324,\\\"name\\\":\\\"调试变量地址1\\\",\\\"v\\\":60583.0,\\\"desc\\\":\\\"[-0xFFFFFF~0xFFFFFF]\\\"},{\\\"code\\\":245019,\\\"addr\\\":12326,\\\"name\\\":\\\"调试变量地址2\\\",\\\"v\\\":59976.0,\\\"desc\\\":\\\"[-0xFFFFFF~0xFFFFFF]\\\"},{\\\"code\\\":245020,\\\"addr\\\":12328,\\\"name\\\":\\\"调试变量地址3\\\",\\\"v\\\":58354.0,\\\"desc\\\":\\\"[-0xFFFFFF~0xFFFFFF]\\\"},{\\\"code\\\":245021,\\\"addr\\\":12330,\\\"name\\\":\\\"调试变量地址4\\\",\\\"v\\\":58750.0,\\\"desc\\\":\\\"[-0xFFFFFF~0xFFFFFF]\\\"},{\\\"code\\\":245022,\\\"addr\\\":12332,\\\"name\\\":\\\"调试变量地址5\\\",\\\"v\\\":0.0,\\\"desc\\\":\\\"[-0xFFFFFF~0xFFFFFF]\\\"},{\\\"code\\\":245023,\\\"addr\\\":12334,\\\"name\\\":\\\"调试变量地址6\\\",\\\"v\\\":0.0,\\\"desc\\\":\\\"[-0xFFFFFF~0xFFFFFF]\\\"},{\\\"code\\\":245024,\\\"addr\\\":12336,\\\"name\\\":\\\"电池接入方式\\\",\\\"v\\\":0.0,\\\"desc\\\":\\\"0：独立接入，1：并联接入\\\",\\\"max\\\":1,\\\"min\\\":0},{\\\"code\\\":245025,\\\"addr\\\":12338,\\\"name\\\":\\\"电表接入使能\\\",\\\"v\\\":1.0,\\\"desc\\\":\\\"0：禁止，1：使能\\\",\\\"max\\\":1,\\\"min\\\":0}],\\\"dno\\\":\\\"G6DDYSOU\\\"}\"}";
//        OpTaskService taskService = new OpTaskService();
//        taskService.receiveMessage(JsonUtils.fromJson(String.valueOf(body), JobNode.class));
//    }
}
