package com.cdz360.biz.ess.ant;

import com.netflix.discovery.EurekaClient;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import reactivefeign.spring.config.EnableReactiveFeignClients;
import reactor.core.publisher.Hooks;

@SpringBootApplication
@EnableDiscoveryClient
@EnableReactiveFeignClients(basePackages = {
    "com.cdz360.biz.utils.feign.*",
    "com.cdz360.biz.ess.ant.feign"})
@Slf4j
@EnableAsync
@ComponentScan(basePackages = {
    "com.cdz360.biz",
    "com.cdz360.data"})
public class EssAntApplication {

    @Autowired
    private EurekaClient discoveryClient;

    /**
     * 主函数
     */
    public static void main(String[] args) {
        log.info("starting dc-biz-ess-ant-rest!!!");
        Hooks.enableAutomaticContextPropagation();  // 输出调用链traceId
        new SpringApplicationBuilder(EssAntApplication.class)
            .web(WebApplicationType.REACTIVE)
            .run(args);
    }

    @PreDestroy
    public void destroy() {
        log.info("going to shutdown");
        discoveryClient.shutdown();
        log.info("eureka de-registered.....");
    }
}