package com.cdz360.biz.ess.ant.utils;

import com.cdz360.biz.ess.ant.config.RedisMessageListener;
import com.cdz360.biz.ess.ant.model.JobType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RedisMqUtils {

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private RedisMessageListener listener;

    @Autowired
    private RedisMessageListenerContainer listenerContainer;

    public void subscribe(String channel, JobType type) {
        subscribe(channelKey(channel, type));
    }

    public void subscribe(String channel) {
        listenerContainer.addMessageListener(listener, new ChannelTopic(channel));
    }

    public void publish(String channel, JobType type, String message) {
        publish(channelKey(channel, type), message);
    }

    public void publish(String channel, String message) {
        redisTemplate.convertAndSend(channel, message);
    }

    public void unsubscribe(String channel, JobType type) {
        unsubscribe(channelKey(channel, type));
    }

    public void unsubscribe(String channel) {
        listenerContainer.removeMessageListener(listener, new ChannelTopic(channel));
        log.info("去掉状态监听: {}", channel);
    }

    private String channelKey(String channel, JobType type) {
        return "op:" + type.name().toLowerCase() + ":" + channel;
    }
}
