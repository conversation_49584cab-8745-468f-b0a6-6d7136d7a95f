package com.cdz360.biz.auth.sys.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class RoleUserUpdateParam {

    @Schema(description = "角色ID")
    private Long roleId;

    @Schema(description = "账号Id列表")
    private List<Long> userIdList;

    @Schema(description = "系统管理员ID")
    private Long userId;

    @Schema(description = "角色Id列表")
    private List<Long> roleIdList;

    @Schema(description = "true-启用，false-禁用")
    private Boolean enable;

    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date expireTime;
}
