package com.cdz360.biz.auth.sys.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * QuerySysUserRequest
 *
 * @since 2/27/2020 11:27 AM
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QuerySysUserRequest extends BaseListParam {

    private String keyword;

    @Schema(description = "账号/手机号/姓名/团队，or查询，同时模糊查询")
    private String key4word;

    private String roleName;
    private List<Long> userIdlist;

    @Schema(description = "商户ID列表", example = "123，456，789")
    private List<Long> commIdList;

    @Schema(description = "归属平台. 0,未知; 20, 充电管理平台; 21, 运营支撑平台")
    private Integer platform;

    @Schema(description = "状态  1：启用  2：冻结  3：删除")
    private Integer status;

    @Schema(description = "当前登录账户ID")
    private Long userId;

    @Schema(description = "用户名")
    private String Account;

    @Schema(description = "角色")
    private String role;

    @Schema(description = "组织")
    private Integer id;

    @Schema(description = "团队标签 模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String teamCatalog;

    @Schema(description = "团队标签 列表模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> teamCatalogList;

    @Schema(description = "审核组Id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String auditGroupId;

    @Schema(description = "账号或姓名 支持模糊查询")
    @JsonInclude(Include.NON_EMPTY)
    private String accountOrNameLike;

    @Schema(description = "场站组ID列表")
    @JsonInclude(Include.NON_EMPTY)
    private List<String> gidList;

    @Schema(description = "查询限制", hidden = true)
    private Boolean queryLimit;

    @Schema(description = "首先查询关联用户ID(sys_user.id)")
    private Long limitCreateBy;
}