package com.cdz360.biz.auth.corp.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

@Data
public class CorpUserOrgPo {

    @Schema(description = "企业ID，t_corp中的ID")
    private Long corpId;

    @Schema(description = "账户ID，sys_user中的ID")
    private Long sysUid;

    @Schema(description = "组织ID，t_corp_org中的ID")
    private Long orgId;

    @Schema(description = "时间")
    private Date createTime;
}
