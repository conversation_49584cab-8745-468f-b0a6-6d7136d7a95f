package com.cdz360.biz.auth.subscribe.param;

import com.cdz360.base.model.base.param.BaseListParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SubscribeListParam extends BaseListParam {

    @Schema(description = "标题名称")
    private String title;

    @Schema(description = "关联角色ID")
    private Long roleId;

    @Schema(description = "状态")
    private Boolean status;

    @Schema(description = "所属商户列表")
    private List<Long> commIdList;

}
