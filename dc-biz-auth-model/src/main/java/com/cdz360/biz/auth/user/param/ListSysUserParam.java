package com.cdz360.biz.auth.user.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListSysUserParam extends BaseListParam {

    @Schema(description = "账户ID列表")
    @JsonInclude(Include.NON_NULL)
    private List<Long> uidList;

    @Schema(description = "账号或姓名 支持模糊查询")
    @JsonInclude(Include.NON_EMPTY)
    private String accountOrNameLike;

    @Schema(description = "账号 支持模糊查询")
    @JsonInclude(Include.NON_EMPTY)
    private String accountLike;

    @Schema(description = "姓名 支持模糊查询")
    @JsonInclude(Include.NON_EMPTY)
    private String nameLike;

    @Schema(description = "角色名称")
    @JsonInclude(Include.NON_EMPTY)
    private String roleName;

    @Schema(description = "状态  1：启用  2：冻结  3：删除")
    @JsonInclude(Include.NON_EMPTY)
    private Integer status;

    @Schema(description = "团队标签相同的用户ID 查出与指定用户团队标签一样的用户列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long sameCorpWxAppNameUid;
}
