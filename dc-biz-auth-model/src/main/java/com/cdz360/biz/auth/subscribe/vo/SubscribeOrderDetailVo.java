package com.cdz360.biz.auth.subscribe.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ToString(callSuper = true)
public class SubscribeOrderDetailVo {

    @Schema(description = "支付单号")
    private String payNo;

    @Schema(description = "角色Id")
    private Long roleId;

    @Schema(description = "账号ID")
    private Long userId;

    @Schema(description = "截至时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expireTime;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "账号名称")
    private String username;


}
