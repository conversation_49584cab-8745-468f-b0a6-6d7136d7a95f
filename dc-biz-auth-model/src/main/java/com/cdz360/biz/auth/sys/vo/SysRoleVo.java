package com.cdz360.biz.auth.sys.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import java.util.Set;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class SysRoleVo {

    private Integer id;
    private Integer pid;
    @NotNull
    private String name;

    @Schema(description = "归属平台: 0,未知; 20, 充电管理平台; 21, 运营支撑平台")
    private Integer platform;

    private Integer category;
    private String categoryName;
    private String tips;
    private Integer status;
    private Integer version;
    //全部权限
    private Boolean svaha;
    private String extra;
    private Set<Long> menuIds;

    @Schema(description = "角色权限列表")
    private List<Long> authorityIdList;
}
