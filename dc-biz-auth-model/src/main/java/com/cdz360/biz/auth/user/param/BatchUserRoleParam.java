package com.cdz360.biz.auth.user.param;

import com.cdz360.biz.auth.user.type.EditType;
import com.cdz360.biz.auth.user.type.OperationType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;


@Data
public class BatchUserRoleParam  {


    @Schema(description = "修改项")
    private EditType editType;

    @Schema(description = "操作方式")
    private OperationType operationType;

    @Schema(description = "账号列表")
    private List<Long> userIdList;

    @Schema(description = "角色列表")
    private List<Long> roleIdList;

    @Schema(description = "平台")
    private Long platform;

}
