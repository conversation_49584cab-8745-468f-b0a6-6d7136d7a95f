package com.cdz360.biz.applet.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.biz.applet.aop.CusApi;
import com.cdz360.biz.applet.model.param.DeleteBankCardParam;
import com.cdz360.biz.applet.service.BankCardService;
import com.cdz360.biz.applet.utils.AppletRestUtils;
import com.cdz360.biz.model.common.vo.SessionCusInfo;
import com.cdz360.biz.model.cus.wallet.param.AddBankCardParam;
import com.cdz360.biz.model.cus.wallet.vo.BankCardVo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Tag(name = "客户银行卡相关接口", description = "客户银行卡")
public class CusBankCardController {

    @Autowired
    private BankCardService bankCardService;

    @CusApi
    @GetMapping("/v2/cus/bankCard/getBankCardList")
    @Operation(summary = "获取银行卡列表")
    public ListResponse<BankCardVo> getBankCardList(ServerHttpRequest request) {

        log.info(LoggerHelper2.formatEnterLog(request));
        SessionCusInfo cusInfo = AppletRestUtils.getAppCusInfo(request);
        ListResponse<BankCardVo> list = bankCardService.search(cusInfo.getCusId());
        log.info("获取银行卡列表。request: {}, result: {}", request, list.getTotal());
        log.debug(LoggerHelper2.formatLeaveLog(request));
        return list;
    }


    @CusApi
    @PostMapping("/v2/cus/bankCard/addBankCard")
    @Operation(summary = "添加银行卡")
    public ObjectResponse<BankCardVo> addBankCard(ServerHttpRequest request,
        @RequestBody AddBankCardParam param) {
        log.info(LoggerHelper2.formatEnterLog(request) + "param = {}", param);
        if (param == null) {
            throw new DcArgumentException("参数错误");
        }
        SessionCusInfo cusInfo = AppletRestUtils.getAppCusInfo(request);
        param.setCusId(cusInfo.getCusId());

        ObjectResponse<BankCardVo> res = bankCardService.addBankCard(param);

        log.info(LoggerHelper2.formatLeaveLog(request));
        return res;
    }

    @CusApi
    @PostMapping("/v2/cus/bankCard/updateBankCard")
    @Operation(summary = "修改银行卡")
    public ObjectResponse<BankCardVo> updateBankCard(ServerHttpRequest request,
        @RequestBody AddBankCardParam param) {
        log.info(LoggerHelper2.formatEnterLog(request) + "param = {}", param);
        if (param == null || param.getId() == null) {
            throw new DcArgumentException("参数错误");
        }
        SessionCusInfo cusInfo = AppletRestUtils.getAppCusInfo(request);
        param.setCusId(cusInfo.getCusId());

        ObjectResponse<BankCardVo> res = bankCardService.updateBankCard(param);

        log.info(LoggerHelper2.formatLeaveLog(request));
        return res;
    }


    @CusApi
    @RequestMapping(value = "/v2/cus/bankCard/deleteBankCard", method = {RequestMethod.DELETE,
        RequestMethod.POST})
    @Operation(summary = "删除银行卡", description = "逻辑删除")
    public BaseResponse deleteBankCard(ServerHttpRequest request,
        @ModelAttribute DeleteBankCardParam param) {
        log.info(LoggerHelper2.formatEnterLog(request));
        SessionCusInfo cusInfo = AppletRestUtils.getAppCusInfo(request);
        String cardNo = param.getCardNo();

        BaseResponse res = bankCardService.deleteBankCard(cusInfo.getCusId(), cardNo);

        log.info(LoggerHelper2.formatLeaveLog(request));
        return res;
    }
}
