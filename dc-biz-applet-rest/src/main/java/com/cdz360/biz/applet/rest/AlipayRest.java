package com.cdz360.biz.applet.rest;


import com.cdz360.biz.applet.service.AlipayService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = {"/internal/alipay/"})
@Tag(name = "内部接口", description = "支付宝相关内部接口")
public class AlipayRest {

    @Autowired
    private AlipayService alipayService;

//    @CusApi(login = false)
//    @Operation(summary = "更新支付宝用户accessToken")
//    @GetMapping(value = "/refreshAlipayUserAccessToken")
//    public BaseResponse refreshAlipayUserAccessToken(
//        ServerHttpRequest request, @RequestParam("alipayUserId") String alipayUserId) {
//        log.info(LoggerHelper2.formatEnterLog(request));
//        alipayService.refreshExpiresInAuthToken(alipayUserId);
//        return RestUtils.success();
//    }

}
