package com.cdz360.biz.applet.service;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.app.vo.AppCfg;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.data.cache.RedisAppReadService;
import com.chargerlinkcar.framework.common.domain.CommercialManage;
import com.chargerlinkcar.framework.common.feign.CommercialFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2019/12/16 15:06
 */
@Slf4j
@Service
public class AppVersionService {
    @Autowired
    private CommercialFeignClient merchantFeignClient;

    @Autowired
    private RedisAppReadService redisAppReadService;

    public List<AppCfg> getAppVersion(Long topCommId) {
        log.info("topCommId = {}", topCommId);
        ObjectResponse<CommercialManage> response = merchantFeignClient.getCommercialManage(topCommId);

        if (null == response || null == response.getData()) {
            log.info("无法获取该商户信息");
            return new ArrayList<>();
        }

        // appIdList
        CommercialManage manage = response.getData();
        List<String> appIdLis = new ArrayList<>();
        appIdLis.add(manage.getWxAppid()); // 微信
        appIdLis.add(manage.getAlipayAppletAppId()); // 支付宝
        appIdLis.add(formatAppId(manage.getComId(), AppClientType.IOS_APP)); // IOS
        appIdLis.add(formatAppId(manage.getComId(), AppClientType.ANDROID_APP)); // ANDROID

        List<AppCfg> collect = appIdLis.stream().map(appId -> redisAppReadService.getAppCfg(appId))
                .filter(Objects::nonNull).collect(Collectors.toList());

        log.info("size = {}", collect.size());
        return collect;
    }

    private String formatAppId(Long topCommId, AppClientType appType) {
        StringBuilder buf = new StringBuilder();
        buf.append(topCommId).append("-").append(appType.name());
        return buf.toString();
    }
}
