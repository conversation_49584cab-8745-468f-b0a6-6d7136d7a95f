package com.cdz360.biz.applet.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.bi.vo.OrderBi;
import com.chargerlinkcar.framework.common.domain.vo.CommercialCheckVo;
import com.chargerlinkcar.framework.common.domain.vo.PrepayMinVo;
import java.math.BigDecimal;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 用户feign
 *
 * <AUTHOR>
 * Created by 2018/11/19.
 */
@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_TRADING, fallbackFactory = TradingFeignClientHystrixFactory.class)
public interface TradingFeignClient {

    @GetMapping(value = "/api/balance/getCommInfoAndCheck")
    ObjectResponse<CommercialCheckVo> getCommInfoAndCheck(@RequestParam("merchants") String merchants, @RequestParam("appCommId") Long appCommId);

    @PostMapping("/api/order/checkOrderOfCard")
    ObjectResponse<Boolean> checkOrderOfCard(
            @RequestParam("cardChipNo") String cardChipNo, @RequestParam("commIdChain") String commIdChain);

    @GetMapping("/api/order/getOrderBi")
    ObjectResponse<OrderBi> getOrderBi(@RequestParam(value = "topCommId") Long topCommId, @RequestParam(value = "userId") Long userId);

    @GetMapping(value = "/api/balance/getMinPrepayAmount")
    ObjectResponse<PrepayMinVo> getMinPrepayAmount();

    @GetMapping(value = "/api/charging/getPayAmount")
    ObjectResponse<BigDecimal> getPayAmount(@RequestParam(value = "userId") Long userId,
        @RequestParam(value = "orderNo") String orderNo,
        @RequestParam(value = "scoreSettingId", required = false) Long scoreSettingId,
        @RequestParam(value = "couponId", required = false) Long couponId);
}
