package com.cdz360.biz.applet.service;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.SexType;
import com.cdz360.base.model.bi.vo.OrderBi;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.applet.feign.TradingFeignClient;
import com.cdz360.biz.applet.feign.UserFeignClient;
import com.cdz360.biz.applet.model.vo.cus.UserVo;
import com.cdz360.biz.model.common.vo.SessionCusInfo;
import com.cdz360.biz.model.cus.basic.vo.CusInfoVo;
import com.cdz360.biz.model.cus.user.param.ModifyCusInfoParam;
import com.cdz360.biz.model.cus.user.po.UserOpenidPo;
import com.cdz360.biz.model.cus.user.type.UserOpenidType;
import com.cdz360.biz.model.cus.wallet.vo.CusProfileVo;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import java.time.Duration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserService //implements IUserService
{


    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private com.cdz360.biz.utils.feign.user.UserFeignClient asyncUserFeignClient;

    @Autowired
    private AccountService accountService;

    @Autowired
    private TradingFeignClient tradingFeignClient;


    public UserVo findInfoByUid(Long customerId) {
        log.debug("-->【通过客户ID查找客户信息】{}", customerId);

        ObjectResponse<UserVo> userRes = userFeignClient.findInfoByUid(customerId, null, null);

        if (userRes == null) {
            throw new DcServiceException("获取用户信息为空");
        }

        if (userRes.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
            throw new DcServiceException("获取用户信息为空");
        }

        if (userRes.getData() == null) {
            throw new DcServiceException("获取用户信息为空");
        }

        UserVo userVo = userRes.getData();

        return userVo;
    }


    public CusProfileVo getCusProfile(long topCommId, long cusId) {

        // 获取用户的基本信息
        UserVo user = this.findInfoByUid(cusId);
        if (null == user) {
            throw new DcServiceException("无法查找该用户信息");
        }

        // 统计用户的订单信息
        ObjectResponse<OrderBi> res = tradingFeignClient.getOrderBi(topCommId, cusId);
        FeignResponseValidate.check(res);

        // 用户钱包信息
        CusProfileVo.Wallet wallet = accountService.getCusWalletInfo(cusId, topCommId);

        CusProfileVo cusProfile = new CusProfileVo(); // TODO: 待实现
        cusProfile.setCusId(user.getId())
            .setImage(user.getImage())
            .setName(user.getUsername())
            .setNickname(user.getName())
            .setPhone(user.getPhone())
            .setOrderBi(res.getData())
            .setWallet(wallet);

        return cusProfile;
    }

    /**
     * 获取客户信息
     *
     * @param cusId
     * @return
     */

    public CusInfoVo getCusInfo(long cusId) {

        // 获取用户的基本信息
        UserVo user = this.findInfoByUid(cusId);
        if (null == user) {
            throw new DcServiceException("无法查找该用户信息");
        }

        CusInfoVo res = new CusInfoVo();
        res.setCusId(user.getUid());
        res.setNickname(user.getUsername());
        res.setSex(user.getSex() == SexType.MALE.getCode() ? SexType.MALE
            : (user.getSex() == SexType.FEMALE.getCode() ? SexType.FEMALE : SexType.UNKNOWN));
        res.setCityCode(user.getCityCode());
        res.setBirthday(DateUtil.string2Date(user.getBrithday(), "yyyy-MM-dd"));
        res.setDegree(user.getDegree());
        res.setSalaryLevel(user.getSalaryLevel());
        res.setHeadIconUrl(user.getImage());
        res.setWxUnionId(user.getWxUnionId());
        res.setAlipayUserId(user.getAliUserId());
        return res;
    }

    /**
     * 修改客户信息
     *
     * @param param
     * @return
     */

    public ObjectResponse<Boolean> updateCusInfo(ModifyCusInfoParam param) {
        log.info("修改客户信息。param: {}", param);

        BaseResponse res = userFeignClient.updateCusInfo(param);

        if (res.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            if (res.getError() == null) {
                throw new DcServiceException("修改客户信息失败");
            } else {
                throw new DcServiceException(res.getError());
            }
        }
        return new ObjectResponse<>(true);

    }

    /**
     * 获取支付宝即插即充信息.
     *
     * @param cusInfo
     * @return UserOpenidPo.extraA 是 VIN
     */
    public UserOpenidPo getAlipayUserVin(SessionCusInfo cusInfo) {
        UserOpenidPo param = new UserOpenidPo();
        // 先获取即插即充VIN的 t_user_openid 记录
        param.setUid(cusInfo.getCusId())
            .setTopCommId(cusInfo.getTopCommId())
            .setType(UserOpenidType.ALIPAY_VIN);
        UserOpenidPo openid4Vin = asyncUserFeignClient.getUserOpenidByUid(param)
            .block(Duration.ofSeconds(50L)).getData();
        if (openid4Vin == null) {
            return null;
        }

        // 再获取支付宝openid记录，如果有开通芝麻信用，会在 extraA 和 extraB 字段记录开通支付信用的服务单号
        param.setUid(cusInfo.getCusId())
            .setTopCommId(cusInfo.getTopCommId())
            .setType(UserOpenidType.ALIPAY_OPENID);
        UserOpenidPo openid4Alipay = asyncUserFeignClient.getUserOpenidByUid(param)
            .block(Duration.ofSeconds(50L)).getData();
        if (openid4Alipay == null) {
            return null;
        } else if (StringUtils.isBlank(openid4Alipay.getExtraA())
            || StringUtils.isBlank(openid4Alipay.getExtraB())) {
            // extraA 或 extraB 为空，表示未开启芝麻信用，不能使用即插即充
            return null;
        }
        return openid4Vin;
    }
}
