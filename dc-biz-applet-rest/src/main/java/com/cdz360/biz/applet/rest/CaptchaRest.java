package com.cdz360.biz.applet.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.biz.applet.aop.CusApi;
import com.cdz360.biz.applet.model.vo.CaptchaVo;
import com.cdz360.biz.applet.service.CaptchaService;
import com.cdz360.biz.applet.utils.AppletRestUtils;
import com.cdz360.biz.model.common.vo.SessionCusInfo;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.http.server.reactive.ServerHttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/captcha")
public class CaptchaRest {

    @Autowired
    private CaptchaService captchaService;

    @CusApi(login = false)
    @Operation(summary = "获取验证码")
    @GetMapping("/generate")
    public ObjectResponse<CaptchaVo> createCaptchaCode(ServerHttpRequest request) {
        SessionCusInfo cusInfo = AppletRestUtils.getAppCusInfo(request);
//        CircleCaptcha captcha = CaptchaUtil.createCircleCaptcha(200, 100, 4, 5);
//        String code = captcha.getCode();
//        HttpSession session = request.getSession();
//        session.setAttribute("code", code);
//        response.setContentType("image/png");
//        captcha.write(response.getOutputStream());
//        log.debug("验证码: code = {}", code);
        if (null == cusInfo || null == cusInfo.getClientType()) {
            throw new DcArgumentException("无效客户端");
        }
        return captchaService.createCaptchaCode(cusInfo);
    }
}
