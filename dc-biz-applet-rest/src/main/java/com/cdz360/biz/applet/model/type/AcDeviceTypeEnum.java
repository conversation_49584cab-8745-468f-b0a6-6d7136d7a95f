package com.cdz360.biz.applet.model.type;
//
///**
// * 终端设备的设备类型
// *
// * <AUTHOR>
//  * @since 2017年7月20日上午11:53:43
// */
//public enum AcDeviceTypeEnum {
//
//    /**
//     * 基于国标GB/T32960-2016协议的电动汽车TBOX设备。
//     */
//    DEVICE_VEHICLE_GB_V1(100),
//    /**
//     * Amitek公司基于v1版MQTT协议的行车记录仪/后视镜设备。
//     */
//    DEVICE_RECORDER_AMI_V1(200),
//    /**
//     * ChargeLink公司基于v1版MQTT协议的充电桩TCU设备。
//     */
//    DEVICE_CHARGER_CL_V1(300),
//    /**
//     * ChargeLink公司基于v2版MQTT协议的充电桩TCU设备。
//     */
//    DEVICE_CHARGER_CL_V2(301),
//    /**
//     * ChargeLink公司基于x5版MQTT协议的充电桩TCU设备。
//     */
//    DEVICE_CHARGER_CL_X5(302),
//    /**
//     * ChargeLink公司基于x5版MQTT协议的充电桩TCU设备。
//     */
//    DEVICE_CHARGER_CL_X9(303),
//    /**
//     * ChargeLink公司基于x5版MQTT协议的充电桩TCU设备。
//     */
//    DEVICE_CHARGER_CL_X10(304),
//    /**
//     * ChargeLink公司基于x5版MQTT协议的充电桩TCU设备。
//     */
//    DEVICE_CHARGER_CL_A6(305),
//    /**
//     * 盛弘TCP协议的充电桩设备
//     */
//    DEVICE_CHARGER_SINEXCEL(306),
//    /**
//     * ChargeLink公司基于R6版TCP协议的电动车充电桩设备
//     */
//    DEVICE_CHARGER_CL_R6(307),
//    /**
//     * ChargeLink公司基于R8路由器版TCP协议的电动车充电桩路由设备
//     */
//    DEVICE_CHARGER_CL_R8(308),
//    /**
//     * ChargeLink公司基于X6P路由器版TCP协议的电动车充电桩路由设备
//     */
//    DEVICE_CHARGER_CL_X6P(309),
//    /**
//     * ChargeLink公司基于充电小队长版MQTT协议的电动自行车充电桩设备
//     */
//    DEVICE_CHARGER_CL_X5X(310),
//    /**
//     * ChargeLink公司基于X6L路由器版TCP协议的电动车充电桩路由设备。
//     */
//    DEVICE_CHARGER_CL_X6L(311),
//    /**
//     * ChargeLink公司基于R9版TCP协议的电动自行车充电桩设备。
//     */
//    DEVICE_CHARGER_CL_R9(312),
//    /**
//     * ChargeLink公司基于A10版TCP协议的电动汽车充电桩设备。
//     */
//    DEVICE_CHARGER_CL_A10(313),
//    /**
//     * ChargeLink公司基于D10版TCP协议的电动自行车充电桩设备。
//     */
//    DEVICE_CHARGER_CL_D10(314),
//
//    /**
//     * ChargeLink公司基于A8版TCP协议的电动自行车充电桩设备。
//     */
//    DEVICE_CHARGER_CL_A8(317),
//
//    /**
//     * ChargeLink公司基于D8版TCP协议的电动自行车充电桩设备。
//     */
//    DEVICE_CHARGER_CL_D8(318),
//    /**
//     * ChargeLink公司基于v1版MQTT协议的车位地锁/车位探测器设备。
//     */
//    DEVICE_PARKING_CL_V1(400),
//
//    /**
//     * ChargeLink公司基于v2版MQTT协议的车位地锁/车位探测器设备。
//     */
//    DEVICE_PARKING_CL_V2(401);
//
//    private int value;
//
//    AcDeviceTypeEnum(int value) {
//        this.value = value;
//    }
//
//    public int getValue() {
//        return value;
//    }
//
//    public void setValue(int value) {
//        this.value = value;
//    }
//
//}
