package com.cdz360.biz.applet.model.param;
//
//import com.cdz360.biz.applet.model.type.AcDeviceTypeEnum;
//import lombok.Data;
//
//import java.io.Serializable;
//
///**
// * <AUTHOR>
// *  结束充电请求
// * @since 2018/12/10 16:57
// */
//@Data
//public class AcStopChargeRequest implements Serializable {
//
//    private static final long serialVersionUID = 4089226589487769480L;
//    /**
//     * 终端设备类型
//     * <b>业务系统填写的终端设备类型，由于历史问题，不同的业务平台对应的类型编号不同</b><br>
//     * 1-从设备管理平台发出的请求，对应{@link AcDeviceTypeEnum}中的编号<br>
//     * 2-从公有云发出的请求，对应{@link com.chargerlink.device.constant.BsBoxChargerType}中的编号<br>
//     */
//    private Integer deviceType;
//    /**
//     * 终端设备ID
//     */
//    private String deviceId;
//    /**
//     * (可选)应答超时时间。单位：毫秒。null或小于等于0：表示一直等待应答。
//     */
//    @Deprecated
//    private Integer timeout;
//
//    /**
//     * 业务系统生成的全局唯一充电订单号
//     */
//    private String orderId;
//    /**
//     * (可选)充电枪ID，该ID为充电枪在终端设备中的相对序号。用于设备验证是否允许停止充电。
//     */
//    private Integer connectorId;
//    /**
//     * (可选)用户身份识别号。如：充电卡号、用户ID等。用于设备验证是否允许停止充电
//     */
//    private String userId;
//}
