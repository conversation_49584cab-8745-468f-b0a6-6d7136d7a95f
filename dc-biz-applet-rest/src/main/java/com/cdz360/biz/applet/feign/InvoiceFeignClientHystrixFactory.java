package com.cdz360.biz.applet.feign;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.applet.model.vo.*;
import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * HystrixInvoiceClientFactory
 *
 * @since 2019/7/3 11:19
 * <AUTHOR>
 */
@Slf4j
@Component
public class InvoiceFeignClientHystrixFactory implements FallbackFactory<InvoiceFeignClient> {
    private static final String KEY_FEIGN_INVOICE = "DC-INVOICE";
    @Override
    public InvoiceFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", KEY_FEIGN_INVOICE, throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", KEY_FEIGN_INVOICE, throwable.getStackTrace());

        return new InvoiceFeignClient() {

            @Override
            public ObjectResponse<InvoicedRecordVo> createInvoicedRecordV22c(
                    InvoicedRecordChargerOrderSaveVo invoicedRecordChargerOrderSaveVo) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<InvoicedModelVo> createInvoicedModel(InvoicedModelVo invoicedModelVo) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<InvoicedModelVo> defaultInvoicedModel(InvoicedModelVo invoicedModelVo) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<InvoicedModelVo> addInvoicedModel(InvoicedModelVo invoicedModelVo) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<InvoicedModelVo> updateInvoicedModel(InvoicedModelVo invoicedModelVo) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<InvoicedModelVo> getInvoicedModel(Long id) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<InvoicedModelVo> getInvoicedModels(Long userId) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<InvoicedModelVo> getDefaultInvoicedModel(Long userId) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<InvoicedModelVo> getInvoicedModelsByUserId(Long userId, int page, int size) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse deleteInvoicedModel(Long id) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<InvoicedConfigMegVo> getInvoicedMegConfig(String token) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<DescVo> getInvoicedDesc(String token) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<DescVo> getShortInvoicedDesc(String token) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<InvoicedChargerOrderVo> getAllInvoicedChargerOrders2c(
                    Long customerId, Integer status, Boolean invoicedFlag,//是否已经关联了开票申请
                    String keyword, int page, int size) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<InvoicedRecordVo> getAllInvoicedRecordsByUserId(Long userId, int page, int size) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<InvoicedRecordVo> getAllInvoicedRecordsByUserIdAndStatus(Long userId, String invoicedStatus, int page, int size, String sort) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<Map<String, String>> getPdfUrl(Long id) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<com.chargerlinkcar.framework.common.domain.invoice.InvoicedRecordVo> findByInvoiceIds(@RequestBody List<Long> invoiceIds) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ListResponse();
            }

        };
    }
}
