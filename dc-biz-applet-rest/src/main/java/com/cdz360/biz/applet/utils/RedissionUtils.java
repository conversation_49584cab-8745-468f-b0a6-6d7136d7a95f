package com.cdz360.biz.applet.utils;

import java.io.IOException;
import java.time.Duration;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.redisson.api.RedissonReactiveClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class RedissionUtils {

    @Autowired
    RedissonClient redissonClient;

    @Autowired
    RedissonReactiveClient redissonReactiveClient;

    public Mono<Void> setAsync(String key, String val) {
//        RedissonClient client = Redisson.create();
//        try {
//            log.info(redissonClient.getConfig().toJSON());
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
        return redissonReactiveClient.getBucket(key).set(val, Duration.ofSeconds(30));
    }



    public void set(String key, String val) {
//        RedissonClient client = Redisson.create();
        try {
            log.info(redissonClient.getConfig().toJSON());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        redissonClient.getBucket(key).set(val, Duration.ofSeconds(30));
    }

    public String getStringVal(String key) {
//        RedissonClient client = Redisson.create();
        Object val = redissonClient.getBucket(key).get();
        if (val == null) {
            return null;
        } else if (val instanceof String) {
            return (String) val;
        } else {
            log.error("类型不匹配. key= {}, val.class= {}", key, val.getClass().getSimpleName());
            return null;
        }
    }

    public Mono<String> getStringValAsync(String key) {
//        RedissonClient client = Redisson.create();
       Mono< Object> mono = redissonReactiveClient.getBucket(key).get();
       return mono.map(val -> {
           if (val == null) {
               return null;
           } else if (val instanceof String) {
               return (String) val;
           } else {
               log.error("类型不匹配. key= {}, val.class= {}", key, val.getClass().getSimpleName());
               return null;
           }
       });

    }

    public boolean lock(String key, long wait) {
//        RedissonClient client = Redisson.create();
        RLock lock = redissonClient.getFairLock(key);
        try {
            return lock.tryLock(wait, 5L, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.warn("try lock failed. key = {}", key);
            return false;
        }
//        return lock.isLocked();
    }
}
