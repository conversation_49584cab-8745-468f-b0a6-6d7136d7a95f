package com.cdz360.biz.applet.model.vo.cus;

import com.cdz360.base.model.base.type.PayAccountType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "账户基础信息")
public class AccountBasicInfoVo {

    @Schema(description = "账户类型")
    private PayAccountType accountType;

    @Schema(description = "账户ID")
    private Long accountId;

    @Schema(description = "集团商户ID")
    private Long topCommId;

    @Schema(description = "集团商户名称")
    private String topCommName;

    @Schema(description = "商户ID")
    private Long commId;

    @Schema(description = "商户名称")
    private String commName;

    @Schema(description = "微信(服务商)商户号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String wxMchId;

    @Schema(description = "支付宝(服务商)商户号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String alipayMchId;

    @Schema(description = "商户会员在线充值 true -- 开启")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean enableOnlinePay;
}
