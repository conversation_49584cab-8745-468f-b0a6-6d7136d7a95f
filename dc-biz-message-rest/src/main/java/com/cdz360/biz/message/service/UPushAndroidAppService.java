package com.cdz360.biz.message.service;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServerException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.message.domain.dto.UPushResponse;
import com.chargerlinkcar.framework.common.domain.CommercialManage;
import com.chargerlinkcar.framework.common.domain.dto.msg.AppPushMsg;
import com.chargerlinkcar.framework.common.domain.dto.msg.UPushAndroidAppMsg;
import com.chargerlinkcar.framework.common.feign.CommercialFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.event.Level;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.net.URI;

@Slf4j
@Service
public class UPushAndroidAppService extends AppPushService {


    @Autowired
    private CommercialFeignClient commercialFeignClient;

    @Autowired
    private RestTemplate restTemplate;

    /**
     * https://developer.umeng.com/docs/67966/detail/68343
     *
     * @param appMsg
     */
    public void sendMessage(AppPushMsg appMsg) {
        ObjectResponse<CommercialManage> commRes = commercialFeignClient.getCommercialManage(appMsg.getTopCommId());
        FeignResponseValidate.check(commRes);
        if (StringUtils.isBlank(commRes.getData().getUPushAndroidAppId())
                || StringUtils.isBlank(commRes.getData().getUPushAndroidAppSecret())) {
            log.warn("商户 {} 未配置安卓APP推送的 uPushAndroidAppId 和 uPushAndroidAppSecret", appMsg.getTopCommId());
            return;
        }

        UPushAndroidAppMsg upushMsg = new UPushAndroidAppMsg();
        upushMsg.setAppkey(commRes.getData().getUPushAndroidAppId())
                .setDeviceTokens(appMsg.getDeviceToken())
                .setTimestamp(String.valueOf(System.currentTimeMillis() / 1000));
        //if (super.isProductEnv()) {
        upushMsg.setProductionMode(Boolean.TRUE.toString());
//        } else {
//            upushMsg.setProductionMode(Boolean.FALSE.toString());
//        }

        String title4OrderStop = "充电完成,请及时挪车";
        upushMsg.getPayload().setDisplayType("notification");


        upushMsg.getPayload().getBody().setTicker(appMsg.getTitle())
//                .setTitle(appMsg.getTitle())
                .setTitle(title4OrderStop)
                .setText(appMsg.getContent())
                .setBuilderId(1)      //  订单的推送使用 1. 后续有其他的推送需要换 builder_id
                .setAfterOpen("go_activity")    // 点击推送消息后跳转到 activity
                .setActivity("com.chargerlink.app.renwochong.MainActivity");    // 点击推送消息后跳转到到首页

        upushMsg.getPayload().getExtra().setType(appMsg.getMsgType().getCode())
                .setOrderNo(appMsg.getOrderNo())
                .setCusId(appMsg.getCusId())
//                .setTitle(appMsg.getTitle())
                .setTitle(title4OrderStop)
                .setMsg(appMsg.getContent());

        upushMsg.setChannelProperties(new UPushAndroidAppMsg.ChannelProperties());
        upushMsg.getChannelProperties().setChannelActivity("com.chargerlink.app.renwochong.ui.activity.MessageActivity")
                .setMainActivity("com.chargerlink.app.renwochong.ui.activity.WelcomeActivity")
                .setHuaweiChannelImportance("NORMAL")
                .setHuaweiChannelCategory("MARKETING");

        if (StringUtils.isBlank(appMsg.getOrderNo())
                && StringUtils.isNotBlank(appMsg.getTradeNo())) {
            upushMsg.getPayload().getExtra().setOrderNo(appMsg.getTradeNo());
        }


        String reqBody = upushMsg.toString();
        String sign = super.getSign(commRes.getData().getUPushAndroidAppSecret(), reqBody);
        String url = super.getSendUrl(sign);

        String resBody = "";
        try {
//            HttpHeaders headers = new HttpHeaders();
//            headers.setContentType(MediaType.APPLICATION_JSON);
//            HttpEntity<String> requestEntity = new HttpEntity<>(reqBody,
//                    headers);

            RequestEntity<String> requestEntity = RequestEntity
                    .post(new URI(url))
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(reqBody);

            ResponseEntity<String> restRes = restTemplate.postForEntity(url, requestEntity, String.class);
            resBody = restRes.getBody();
            //log.debug("res.body = {}", resBody);
            UPushResponse res = JsonUtils.fromJson(resBody, UPushResponse.class);
            if (!StringUtils.equalsIgnoreCase("SUCCESS", res.getRet())) {
                //log.warn("发送APP推送失败. res.body = {}  ------  req.body = {}", resBody, reqBody);
                throw new DcServerException("发送APP推送失败", Level.WARN);
            }
            log.debug("res = {}", res);
        } catch (Exception e) {
            log.warn("发送APP推送失败. error = {}, res.body = {}  ------  req.body = {} ----- appId = {}, appSecret = {}, sign = {}",
                    e.getMessage(), resBody, reqBody,
                    commRes.getData().getUPushIosAppId(),
                    commRes.getData().getUPushIosAppSecret(),
                    sign,
                    e);
        }
    }
}
