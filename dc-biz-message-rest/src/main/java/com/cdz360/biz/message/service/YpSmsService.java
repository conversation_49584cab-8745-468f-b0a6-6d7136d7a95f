package com.cdz360.biz.message.service;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.message.ValimessageVo;
import com.cdz360.biz.model.sys.constant.SmsOperatorType;
import com.yunpian.sdk.YunpianClient;
import com.yunpian.sdk.constant.Code;
import com.yunpian.sdk.model.Result;
import com.yunpian.sdk.model.SmsSingleSend;
import java.io.Closeable;
import java.io.IOException;
import java.util.Iterator;
import java.util.Map;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 云片发送短信的服务
 */
@Slf4j
@Service
public class YpSmsService implements SmsService {

    @Autowired
    private MessageService messageService;

    @PostConstruct
    public void init() {
        messageService.addSmsServiceMap(SmsOperatorType.YP, this);
    }

    private static String fixMapExtras(Map<String, String> mapExtras) {
        StringBuffer stringBuffer = new StringBuffer();
        if (mapExtras != null && mapExtras.size() > 0) {
            Iterator var2 = mapExtras.keySet().iterator();

            while (var2.hasNext()) {
                String key = (String) var2.next();
                if (stringBuffer.length() > 0) {
                    stringBuffer.append("&");
                }

                if (key.startsWith("#")) {
                    stringBuffer.append(key);
                } else {
                    stringBuffer.append("#" + key + "#");
                }

                String value = mapExtras.get(key);
                if (StringUtils.isNotBlank(value)) {
                    stringBuffer.append("=" + value);
                }
            }
        }

        return stringBuffer.toString();
    }

    /**
     * 使用短信模板发送短信
     *
     * @return
     */
    public boolean sendSms(ValimessageVo valimessage) {
        boolean ret = false;
        try (YpClientWrapper wrapper = new YpClientWrapper(valimessage.getSmsApiKey())) {
            Map<String, String> param = wrapper.getYpClient().newParam(4);
            // param.put("apikey", apikey);
            param.put(YunpianClient.MOBILE, valimessage.getPhone());
            param.put(YunpianClient.TPL_ID, valimessage.getTemplateNo());
            param.put(YunpianClient.TPL_VALUE, fixMapExtras(valimessage.getParmMap()));
            Result<SmsSingleSend> result = wrapper.getYpClient()
                .sms().tpl_single_send(param);
//                .single_send(param);
            ret = result != null && result.getCode() != null && result.getCode() == Code.OK;
            if (!ret) {
                log.error(
                    "发送短信失败!!! commId = {}, apiKey = {}, mobile = {}, templateNo = {}, extra = {}, result = {}",
                    valimessage.getCommid(),
                    valimessage.getSmsApiKey(), valimessage.getPhone(), valimessage.getTemplateNo(),
                    JsonUtils.toJsonString(valimessage.getParmMap()),
                    JsonUtils.toJsonString(result));
            }
        } catch (Exception e) {
            log.error("发送短信失败 error = {}!!! apiKey = {}, mobile = {}, templateNo = {}, extra = {}",
                e.getMessage(), valimessage.getSmsApiKey(), valimessage.getPhone(),
                valimessage.getTemplateNo(), JsonUtils.toJsonString(valimessage.getParmMap()), e);
        }
        return ret;
    }

    /**
     * 包装类, 用于自动关闭云片的YunpianClient.
     * <p>
     * https://github.com/yunpian/yunpian-java-sdk
     */
    class YpClientWrapper implements Closeable {

        private YunpianClient yunpianClient;

        public YpClientWrapper(String apiKey) {
            this.yunpianClient = new YunpianClient(apiKey).init();
        }

        public YunpianClient getYpClient() {
            return this.yunpianClient;
        }

        @Override
        public void close() throws IOException {
            try {
                yunpianClient.close();
            } catch (Exception e) {
                // ignore
            }
        }
    }
}
