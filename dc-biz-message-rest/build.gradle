plugins {
    id 'java'
    id "com.gorylenko.gradle-git-properties"
}

apply plugin: 'org.springframework.boot'

sourceSets {
    main {
        resources {
            srcDirs "src/main/resources", "src/main/java"
        }
    }
}
springBoot {
    buildInfo()
}

dependencies {

    implementation project(':dc-biz-common-model')
    implementation project(':dc-biz-utils')
    implementation project(':dc-biz-ess-model')
    implementation("com.cdz360.cloud:dc-data-sync:${dcCloudVersion}")

    implementation("com.alipay.sdk:alipay-sdk-java:${alipaySdkVersion}")
    implementation('org.springframework.boot:spring-boot-starter-actuator')
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-data-redis")
    implementation('org.springframework.cloud:spring-cloud-starter-config')
    implementation('org.springframework.cloud:spring-cloud-starter-netflix-eureka-client')

    implementation('org.springframework.cloud:spring-cloud-starter-openfeign')
    implementation('org.springframework.cloud:spring-cloud-bus')
    implementation('org.springframework.cloud:spring-cloud-starter-bus-amqp')
//    implementation('org.springframework.cloud:spring-cloud-starter-sleuth')
//    implementation('org.springframework.cloud:spring-cloud-sleuth-zipkin')
    implementation 'io.micrometer:micrometer-tracing-bridge-brave'
    implementation 'io.zipkin.reporter2:zipkin-reporter-brave'
    implementation 'io.github.openfeign:feign-micrometer'
//    implementation "io.springfox:springfox-boot-starter:${swaggerVersion}"
    implementation("org.springdoc:springdoc-openapi-ui:${springdocVersion}")
    implementation("org.springdoc:springdoc-openapi-webflux-ui:${springdocVersion}")

    // https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-mail
    implementation('org.springframework.boot:spring-boot-starter-mail')

    implementation("com.playtika.reactivefeign:feign-reactor-spring-cloud-starter:${feignReactiveVersion}")
    implementation("com.playtika.reactivefeign:feign-reactor-webclient:${feignReactiveVersion}")
    implementation("com.playtika.reactivefeign:feign-reactor-cloud:${feignReactiveVersion}")
    implementation("com.playtika.reactivefeign:feign-reactor-spring-configuration:${feignReactiveVersion}")

    implementation("com.github.pagehelper:pagehelper:${pagehelperVersion}")

    implementation("org.apache.commons:commons-pool2:${commonsPoolVersion}")
    implementation("org.apache.commons:commons-lang3:${commonsLangVersion}")

    // logstash间接依赖于jaxb, 在java10+以上的环境, 缺少jaxb-api时, logstash无法正常启动
    implementation("org.glassfish.jaxb:jaxb-runtime")

    implementation("com.yunpian.sdk:yunpian-java-sdk:1.2.7")

}
