package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.dc.service.AdsService;
import com.cdz360.biz.model.ads.param.CreateAdsParam;
import com.cdz360.biz.model.ads.param.ListAdsParam;
import com.cdz360.biz.model.ads.param.UpdateAdsParam;
import com.cdz360.biz.model.ads.vo.AdsVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class AdsRest {

    @Autowired
    private AdsService adsService;


    @PostMapping(value = "/dataCore/ads/createAds")
    public BaseResponse createAds(@RequestBody CreateAdsParam req) {
        adsService.createAds(req);
        return BaseResponse.success();
    }

    @PostMapping(value = "/dataCore/ads/updateAds")
    public BaseResponse updateAds(@RequestBody UpdateAdsParam req) {
        adsService.updateAds(req);
        return BaseResponse.success();
    }

    @GetMapping(value = "/dataCore/ads/activeAds")
    public BaseResponse activeAds(@RequestParam("id") Long id) {
        adsService.activeAds(id);
        return BaseResponse.success();
    }

    @GetMapping(value = "/dataCore/ads/abortAds")
    public BaseResponse abortAds(@RequestParam("id") Long id) {
        adsService.abortAds(id);
        return BaseResponse.success();
    }

    @GetMapping(value = "/dataCore/ads/getAdsDetail")
    public ObjectResponse<AdsVo> getAdsDetail(@RequestParam("id") Long id) {
        return new ObjectResponse<>(adsService.getAdsDetail(id));
    }

    @PostMapping(value = "/dataCore/ads/listAds")
    public ListResponse<AdsVo> listAds(@RequestBody ListAdsParam req) {
        return adsService.listAds(req);
    }

    /**
     * c端获取广告列表
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/ads/getUserAdsList")
    public ListResponse<AdsVo> getUserAdsList(@RequestBody ListAdsParam param) {
        return adsService.getUserAdsList(param);
    }
}
