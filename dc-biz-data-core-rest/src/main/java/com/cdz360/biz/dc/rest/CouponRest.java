package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.dc.service.CouponService;
import com.cdz360.biz.model.trading.coupon.dto.CouponDto;
import com.cdz360.biz.model.trading.coupon.param.AppletCouponParam;
import com.cdz360.biz.model.trading.coupon.param.BatchSendCouponParam;
import com.cdz360.biz.model.trading.coupon.param.OptimalCouponSearchParam;
import com.cdz360.biz.model.trading.coupon.vo.OrderCouponVo;
import com.cdz360.biz.model.trading.coupon.vo.PriorityCouponVo;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/dataCore/coupon")
public class CouponRest {

    @Autowired
    private CouponService couponService;

    /**
     * 小程序优惠券列表展示(三种状态筛选)
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/getList")
    public ListResponse<CouponDto> getList(@RequestBody AppletCouponParam param) {
        return couponService.getList(param);
    }

    /**
     * 小程序支付确认页面-根据订单查询优先券
     *
     * @return
     */
    @GetMapping(value = "/getPriorityCoupon")
    public ObjectResponse<PriorityCouponVo> getPriorityCoupon(
        @RequestParam(value = "start", defaultValue = "0") Integer start,
        @RequestParam(value = "size", defaultValue = "1") Integer size,
        @RequestParam(value = "userId") Long userId,
        @RequestParam(value = "orderNo") String orderNo,
        @RequestParam(value = "scoreSettingId", required = false) Long scoreSettingId) {
        log.info("根据订单查询可用优先券: orderNo = {}, scoreSettingId = {}", orderNo,
            scoreSettingId);
        return couponService.getPriorityCoupon(start, size, userId, orderNo, scoreSettingId);
    }

    @Operation(summary = "获取充电订单使用的优惠券")
    @GetMapping(value = "/order")
    public Mono<ObjectResponse<OrderCouponVo>> getOrderCoupon(
//        HttpServletRequest request,
        @ApiParam("充电订单编号") @RequestParam(value = "orderNo") String orderNo) {
        log.info("获取充电订单使用的优惠券: {}", orderNo);
        return couponService.getOrderCoupon(orderNo);
    }

    @Operation(summary = "批量发券")
    @PostMapping(value = "/batchSendCoupon")
    public BaseResponse batchSendCoupon(@RequestBody BatchSendCouponParam param) {
        log.info("批量发券，param={}", JsonUtils.toJsonString(param));
        return couponService.batchSendCoupon(param);
    }

    /**
     * 充电选择页面-获取排序后的可用优惠券列表，即冲即退和微信支付分使用
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/getOptimalCouponList")
    public ListResponse<CouponDto> getOptimalCouponList(
        @RequestBody OptimalCouponSearchParam param) {
        log.info(
            "充电选择页面-获取排序后的可用优惠券列表, param = {}",
            JsonUtils.toJsonString(param));
        return couponService.getOptimalCouponList(param);
    }
}
