package com.cdz360.biz.dc.service.profit;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ds.trading.ro.corp.ds.CorpRoDs;
import com.cdz360.biz.ds.trading.ro.profit.sett.ds.GcProfitCfgRoDs;
import com.cdz360.biz.ds.trading.rw.profit.sett.ds.GcProfitCfgRwDs;
import com.cdz360.biz.model.trading.corp.po.CorpPo;
import com.cdz360.biz.model.trading.profit.sett.param.ListProfitCfgParam;
import com.cdz360.biz.model.trading.profit.sett.param.SaveProfitCfgParam;
import com.cdz360.biz.model.trading.profit.sett.po.GcProfitCfgPo;
import com.cdz360.biz.model.trading.profit.sett.type.ProfitCfgCalSource;
import com.cdz360.biz.model.trading.profit.sett.type.ProfitRuleAccountType;
import com.cdz360.biz.model.trading.profit.sett.vo.ProfitCfgVo;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class ProfitCfgService {

    @Autowired
    private GcProfitCfgRoDs profitCfgRoDs;

    @Autowired
    private GcProfitCfgRwDs profitCfgRwDs;

    @Autowired
    private CorpRoDs corpRoDs;

    public Mono<ProfitCfgVo> saveGcProfitCfg(SaveProfitCfgParam param) {
        SaveProfitCfgParam.checkParam(param);

        // 企业客户名称填充
        if (ProfitCfgCalSource.FROM_CHARGE_ORDER.equals(param.getCalSource())) {
            param.getChargeOrderRules().forEach(rule -> {
                if (StringUtils.isBlank(rule.getAccountName()) &&
                    ProfitRuleAccountType.CORP.equals(rule.getAccountType()) &&
                    null != rule.getAccountId()) {
                    CorpPo corp = corpRoDs.getCorpById(rule.getAccountId());
                    rule.setAccountName(null != corp ? corp.getCorpName() : "--");
                }
            });
        }

        return Mono.just(param)
            .map(x -> {
                GcProfitCfgPo po = new GcProfitCfgPo();
                BeanUtils.copyProperties(param, po);
                return po;
            })
            .doOnNext(po -> {
                if (po.getId() != null) {
                    GcProfitCfgPo cfg = profitCfgRwDs.getById(po.getId(), true);
                    IotAssert.isNotNull(cfg, "配置记录无效");
                    log.info("旧配置信息: {}", cfg);
                    profitCfgRwDs.updateGcProfitCfg(po);
                } else {
                    GcProfitCfgPo cfg = profitCfgRoDs.getByName(po.getName());
                    IotAssert.isNull(cfg, "配置记录名称已经存在，请更换名称重新创建");
                    profitCfgRwDs.insertGcProfitCfg(po);
                }
            })
            .map(x -> {
                ProfitCfgVo result = new ProfitCfgVo();
                BeanUtils.copyProperties(x, result);
                return result;
            });
    }

    public Mono<ProfitCfgVo> enableGcProfitCfg(Long id, Boolean enable) {
        IotAssert.isNotNull(id, "配置ID无效");
        GcProfitCfgPo cfg = profitCfgRoDs.getById(id);
        IotAssert.isNotNull(cfg, "配置无效");

        boolean b = profitCfgRwDs.updateGcProfitCfg(
            new GcProfitCfgPo().setId(id).setEnable(enable));
        if (!b) {
            String desc = (enable ? "启用" : "停用") + "操作失败";
            throw new DcArgumentException(desc);
        }

        ProfitCfgVo result = new ProfitCfgVo();
        BeanUtils.copyProperties(cfg, result);
        return Mono.just(result);
    }

    public Mono<ProfitCfgVo> deleteGcProfitCfg(Long id) {
        IotAssert.isNotNull(id, "配置ID无效");
        GcProfitCfgPo cfg = profitCfgRoDs.getById(id);
        IotAssert.isNotNull(cfg, "配置无效");

        boolean b = profitCfgRwDs.deleteGcProfitCfg(id);
        if (!b) {
            throw new DcArgumentException("删除操作失败");
        }

        ProfitCfgVo result = new ProfitCfgVo();
        BeanUtils.copyProperties(cfg, result);
        return Mono.just(result);
    }

    public Mono<ListResponse<ProfitCfgVo>> findGcProfitCfg(ListProfitCfgParam param) {
        return Mono.just(param)
            .doOnNext(p -> {
                if (null == param.getSize() || param.getSize() > 10000) {
                    param.setSize(9999);
                }
            })
            .map(this.profitCfgRoDs::findGcProfitCfg)
            .map(RestUtils::buildListResponse)
            .doOnNext(res -> {
                if (null != param.getTotal() && param.getTotal()) {
                    res.setTotal(this.profitCfgRoDs.count(param));
                } else {
                    res.setTotal(0L);
                }
            });
    }

    public Mono<ObjectResponse<ProfitCfgVo>> getGcProfitCfg(Long id) {
        if (null == id) {
            return Mono.just(RestUtils.buildObjectResponse(null));
        }

        GcProfitCfgPo cfg = profitCfgRoDs.getById(id);
        if (null == cfg) {
            return Mono.just(RestUtils.buildObjectResponse(null));
        }

        ProfitCfgVo result = new ProfitCfgVo();
        BeanUtils.copyProperties(cfg, result);
        return Mono.just(RestUtils.buildObjectResponse(result));
    }
}
