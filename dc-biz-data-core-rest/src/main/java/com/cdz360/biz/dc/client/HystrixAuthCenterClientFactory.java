package com.cdz360.biz.dc.client;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.sys.param.AccRelativeParam;
import com.cdz360.biz.auth.sys.vo.AccRelativeVo;
import com.cdz360.biz.model.cus.message.po.MessagePo;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2018/11/25.
 */
@Slf4j
@Component
public class HystrixAuthCenterClientFactory implements FallbackFactory<AuthCenterFeignClient> {


    @Override
    public AuthCenterFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_AUTH, throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_AUTH, throwable.getStackTrace());

        return new AuthCenterFeignClient() {

            @Override
            public ListResponse<SysUserVo> querySysUserByIds(List<Long> idList) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SysUserVo> findByName(String name, Boolean accurateQuery) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse addMessage(String token, MessagePo message) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<AccRelativeVo> getVoList(AccRelativeParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<String> getGidsById(Long corpId) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<Integer> getGcTypes() {
                log.error("【服务熔断】。Service = {}, 获取国充场站的gcType值",
                        DcConstants.KEY_FEIGN_DC_BIZ_AUTH);
                return RestUtils.serverBusy4ListResponse();
            }
        };
    }
}
