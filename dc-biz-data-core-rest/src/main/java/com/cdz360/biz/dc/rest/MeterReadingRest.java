package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.biz.dc.service.MeterReadingService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * MeterReadingRest
 *
 * @since 9/25/2020 11:11 AM
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "电表读数", description = "meterReading")
@RequestMapping("/dataCore/meter")
public class MeterReadingRest {

    @Autowired
    private MeterReadingService meterReadingService;

    @PostMapping("/getPrevDayReading")
    public BaseResponse getPrevDayReading(@RequestParam(value = "date", required = false)
                                              @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date date) {
        log.info("电表读数采样到统计表: {}", date);
        meterReadingService.getPrevDayReading(date);
        return BaseResponse.success();
    }

}