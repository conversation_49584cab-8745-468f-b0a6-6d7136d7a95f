package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.site.SiteBizService;
import com.cdz360.biz.ds.trading.ro.geo.ds.GeoRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.model.geo.po.DistrictPo;
import com.cdz360.biz.model.trading.site.param.ListCecSiteParam;
import com.cdz360.biz.model.trading.site.vo.PartnerSiteVo;
import com.chargerlinkcar.framework.common.domain.PartnerSiteDetailInfoVo;
import com.chargerlinkcar.framework.common.domain.SiteDetailInfoVo;
import com.chargerlinkcar.framework.common.domain.request.SiteGeoListRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;

@Slf4j
@Service
public class CecSiteService {
    @Autowired
    private SiteBizService siteBizService;

    @Autowired
    private SiteRoDs siteRoDs;
    @Autowired
    private GeoRoDs geoRoDs;

    public Mono<ListResponse<PartnerSiteVo>> cecSiteList(ListCecSiteParam param) {
        ListResponse<PartnerSiteVo> siteList = siteRoDs.cecSiteList(param);
        return Mono.just(siteList);
    }

    public Mono<ObjectResponse<PartnerSiteDetailInfoVo>> cecSiteDetail(String siteId) {
//        SitePo site = this.siteRoDs.getSite(siteId);
//        if (site == null) {
//            log.warn("该场站不存在[" + siteId + "]");
//            throw new DcServiceException("该场站不存在[" + siteId + "]");
//        }

        PartnerSiteVo site = this.siteRoDs.getPartnerSite(siteId);
        if (site == null) {
            log.error("未查询到站点信息[siteId={}]", siteId);
            throw new DcServiceException("未查询到站点信息");
        }

        PartnerSiteDetailInfoVo result = new PartnerSiteDetailInfoVo();
        SiteGeoListRequest request = new SiteGeoListRequest();
        request.setSiteId(siteId);
        SiteDetailInfoVo siteDetail = siteBizService.getSiteDetail(request);
        BeanUtils.copyProperties(siteDetail, result);
        result.setEvseOwnerCode(site.getEvseOwnerCode())
                .setEvseOwnerName(site.getEvseOwnerName())
                .setPartnerCode(site.getPartnerCode())
                .setPartnerName(site.getPartnerName())
                .setOpenSiteId(site.getOpenSiteId());

        return Mono.just(RestUtils.buildObjectResponse(result));
    }

//    public Mono<ObjectResponse<PlugStatusCountDto>> cecSitePlugStatusStats(String siteId) {
//        return Mono.just(RestUtils.buildObjectResponse(siteBizService.getChargerStatusStatisticsBySiteId(siteId)));
//    }

    public Mono<ListResponse<DistrictPo>> getDistrictByList(List<String> code) {
        return Mono.just(RestUtils.buildListResponse(geoRoDs.getDistrictByList(code)));
    }
}
