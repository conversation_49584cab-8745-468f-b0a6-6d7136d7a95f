package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.dc.domain.EvseCfgResendDto;
import com.cdz360.biz.dc.service.EvseManagerService;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.iot.param.ModifyEvseCfgParam;
import com.cdz360.biz.model.iot.vo.EvseImportVo;
import com.cdz360.biz.model.iot.vo.OfflineEvseImportVo;
import com.cdz360.biz.model.trading.evse.EvseInfo;
import com.cdz360.biz.model.trading.iot.vo.EvseInfoVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
public class EvseManagerRest {

    @Autowired
    private EvseManagerService service;


    /**
     * 获取桩信息
     *
     * @param evseNo
     * @return
     */
    @GetMapping(value = "/dataCore/evseManager/getEvseInfo")
    public ObjectResponse<EvseInfo> getEvseInfo(@RequestParam(value = "evseNo") String evseNo) {
        log.info("getEvseInfo evseNo: {}", evseNo);
        return service.getEvseInfo(evseNo);
    }

    /**
     * 按场站下发默认配置(场站下所有桩)
     *
     * @param siteId
     * @return
     */
    @GetMapping(value = "/dataCore/evseManager/downDefultSetting2AllEvse")
    public BaseResponse downDefultSetting2AllEvse(@RequestParam("siteId") String siteId) {
        log.info("downDefultSetting2AllEvse siteId: {}", siteId);
        return service.downDefultSetting2AllEvse(siteId);
    }

    /**
     * 拼装下发计费模板所需的入参
     *
     * @param siteId
     * @return
     */
    @GetMapping(value = "/dataCore/evseManager/getModifyEvseCfgParam")
    public ObjectResponse<ModifyEvseCfgParam> getModifyEvseCfgParam(@RequestParam("siteId") String siteId) {
        log.info("downDefultTemplate2AllEvse siteId: {}", siteId);
        return service.getModifyEvseCfgParam(siteId);
    }

    /**
     * 获取桩配置重发参数
     *
     * @return
     */
    @PostMapping(value = "/dataCore/evseManager/getResendEvseCfgParam")
    public ObjectResponse<ModifyEvseCfgParam> getResendEvseCfgParam(
        @RequestBody EvseCfgResendDto dto) {
        log.info("getResendEvseCfgParam dto: {}", dto);
        return service.getResendEvseCfgParam(dto);
    }

    /**
     * 按桩重新下发场站默认配置
     *
     * @param evseNo
     * @return
     */
    @GetMapping(value = "/dataCore/evseManager/downSettingByEvse")
    public BaseResponse downSettingByEvse(@RequestParam("evseNo") String evseNo) {
        log.info("downSettingByEvse evseNo: {}", evseNo);
        return service.downSettingByEvse(evseNo);
    }

    /**
     * 下发上次桩配置
     *
     * @param evseNo
     * @return
     */
    @GetMapping(value = "/dataCore/evseManager/downLastSettingByEvse")
    public BaseResponse downLastSettingByEvse(@RequestParam("evseNo") String evseNo) {
        log.info("downLastSettingByEvse evseNo: {}", evseNo);
        return service.downLastSettingByEvse(evseNo);
    }

    /**
     * 解析支撑平台脱机桩excel文件
     * @param list
     * @return
     */
    @PostMapping("/dataCore/evseManager/parseOfflineEvseExcel")
    public ObjectResponse<OfflineEvseImportVo> parseOfflineEvseExcel(@RequestBody List<List<String>> list) {
        if (CollectionUtils.isEmpty(list)) {
            log.info("无法获取有效信息");
            throw new DcServiceException("无法获取有效信息，请检查excel文件内容");
        }
        return service.parseOfflineEvseExcel(list);
    }


    /**
     * 解析管理平台桩批量导入excel文件
     * @param file
     * @return
     */
    @PostMapping("/dataCore/evseManager/parseEvseExcel")
    public Mono<ObjectResponse<EvseImportVo>> parseEvseExcel(@RequestPart("file") FilePart file) {
        return service.parseEvseExcel(file);
    }

    /**
     * 获取脱机桩列表
     * @param param
     * @return
     */
    @PostMapping("/dataCore/evseManager/getOfflineEvseList")
    public ListResponse<EvseInfoVo> getOfflineEvseList(@RequestBody ListEvseParam param) {
        log.info("param = {}", param);
        return service.getOfflineEvseList(param);
    }

}
