package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.biz.ds.trading.rw.app.ds.AppCrashLogRwDs;
import com.cdz360.biz.model.trading.app.po.AppCrashLogPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * AppLogService
 *
 * @since 10/30/2023 5:19 PM
 * <AUTHOR>
 */
@Slf4j
@RestController
public class AppLogService {

    @Autowired
    private AppCrashLogRwDs appCrashLogRwDs;

    public BaseResponse addAppCrashLog(AppCrashLogPo req) {
        appCrashLogRwDs.insertAppCrashLog(req);
        return BaseResponse.success();
    }
}