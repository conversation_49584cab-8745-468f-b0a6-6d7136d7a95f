package com.cdz360.biz.dc.service.peek.invoice;

import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.biz.model.trading.peek.invoice.dto.PeekInvoiceDto;
import com.chargerlinkcar.framework.common.domain.peek.invoice.param.PeekInvoiceParam;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * PeekInvoiceService
 * 
 * @since 3/22/2023 5:08 PM
 * <AUTHOR>
 */
@Slf4j
@Service
public class PeekInvoiceService {
    private Map<String, AbstractPeekInvoiceStrategy> strategyMap = new HashMap<>();

    public synchronized void addStrategy(
        PayAccountType mode, AbstractPeekInvoiceStrategy strategy) {
        String key = this.strategyKey(mode);
        strategyMap.put(key, strategy);
    }

    public PeekInvoiceDto peekInvoice(PeekInvoiceParam params) {
        String key = this.strategyKey(params.getMode());
        final AbstractPeekInvoiceStrategy abstractPeekInvoiceStrategy = strategyMap.get(key);
        IotAssert.isNotNull(abstractPeekInvoiceStrategy, "找不到策略" + key);
        return abstractPeekInvoiceStrategy.peekInvoice(params);
    }

    public String strategyKey(PayAccountType mode) {
        IotAssert.isNotNull(mode, "类型不能为空");
        return "peek:invoice:" + mode.name();
    }
}