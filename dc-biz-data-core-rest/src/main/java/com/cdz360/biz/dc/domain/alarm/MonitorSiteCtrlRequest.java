package com.cdz360.biz.dc.domain.alarm;

import com.cdz360.base.model.base.type.SiteCtrlStatusType;
import com.cdz360.base.utils.JsonUtils;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * MonitorSiteCtrlRequest
 *
 * @since 5/12/2020 1:22 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MonitorSiteCtrlRequest implements Serializable {

    private String ctrlNo;

    /**
     * 业务端的告警代码
     * 故障：1-255
     * 告警：2000+
     * 业务端故障：1000+
     */
    private Integer alarmCode;

    /**
     * 告警产生方式 0：桩端上报告警,1：桩端上报故障,2：告警逻辑生成告警
     */
    private Integer infoType;

    /**
     * 告警开始时间
     */
    private Date startTime;

    /**
     * 告警结束时间
     */
    private Date endTime;

    /**
     * 异常问题编码
     */
    private Integer errorCode;
    /**
     * 异常问题编码,告警码
     */
    private Integer alertCode;
    /**
     * 桩或枪错误描述
     */
    private String error;


    private String siteId;

    /**
     * 场站商户ID
     */
    private Long siteCommId;

    /**
     * 场站名称
     */
    private String siteName;

    private String linkId;

    /**
     * 负载率
     */
    private Integer loadRatio;

    /**
     * 配电柜温度
     */
    private Integer pwrTemp;

    private SiteCtrlStatusType status;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}