package com.cdz360.biz.dc.rest.profit;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.profit.ProfitCfgService;
import com.cdz360.biz.model.trading.profit.sett.param.ListProfitCfgParam;
import com.cdz360.biz.model.trading.profit.sett.param.SaveProfitCfgParam;
import com.cdz360.biz.model.trading.profit.sett.vo.ProfitCfgVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/dataCore/profitCfg")
@Tag(name = "收益配置相关操作接口", description = "收益配置相关操作接口")
public class GcProfitCfgRest {

    @Autowired
    private ProfitCfgService profitCfgService;

    @Operation(summary = "更新收益配置")
    @PostMapping(value = "/saveCfg")
    public Mono<ObjectResponse<ProfitCfgVo>> saveGcProfitCfg(
        @RequestBody SaveProfitCfgParam param) {
        log.debug("更新收益配置: param = {}", JsonUtils.toJsonString(param));
        return profitCfgService.saveGcProfitCfg(param)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "收益配置停用或启用")
    @GetMapping(value = "/enableCfg")
    public Mono<ObjectResponse<ProfitCfgVo>> enableGcProfitCfg(
        @RequestParam("id") Long id,
        @RequestParam("enable") Boolean enable) {
        log.debug("收益配置停用或启用: id = {}, enable = {}", id, enable);
        return profitCfgService.enableGcProfitCfg(id, enable)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "删除收益配置(物理删除)")
    @DeleteMapping(value = "/deleteCfg")
    public Mono<ObjectResponse<ProfitCfgVo>> deleteGcProfitCfg(
        @RequestParam("id") Long id) {
        log.debug("删除收益配置(物理删除): id = {}", id);
        return profitCfgService.deleteGcProfitCfg(id)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "获取收益配置列表")
    @PostMapping(value = "/findAll")
    public Mono<ListResponse<ProfitCfgVo>> findGcProfitCfg(
        @RequestBody ListProfitCfgParam param) {
        log.debug("获取收益配置列表: param = {}", JsonUtils.toJsonString(param));
        return profitCfgService.findGcProfitCfg(param);
    }

    @Operation(summary = "获取收益配置", description = "无效ID返回data为null")
    @GetMapping(value = "/getCfg")
    public Mono<ObjectResponse<ProfitCfgVo>> getGcProfitCfg(
        @RequestParam("id") Long id) {
        log.debug("获取收益配置: id = {}", id);
        return profitCfgService.getGcProfitCfg(id);
    }

}
