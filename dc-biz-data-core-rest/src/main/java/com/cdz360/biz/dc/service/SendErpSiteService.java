package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.domain.SendSiteBi;
import com.cdz360.biz.dc.domain.erp.*;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@Service
public class SendErpSiteService {
    @Value("${erp.host:http://***********/k3cloud}")
    private String erpHost;

    // 登录 url
    @Value("${erp.login.url:/Kingdee.BOS.WebApi.ServicesStub.AuthService.ValidateUser.common.kdsvc}")
    private String loginUrl;

    // 登录用户名
    @Value("${erp.login.username:kingdee}")
    private String username;

    // 登录用户密码
    @Value("${erp.login.password:htgdkingdee_2020}")
    private String password;

    // 登录账套ID
    @Value("${erp.login.acctID:5d88e76b37e46e}")
    private String acctID;

    @Value("${erp.login.lcid:2052}")
    private Integer lcid;

    // 运营数据表单Id
    @Value("${erp.op.formid:HTGD_projectincome}")
    private String formid;

    // 保存表单数据 url
    @Value("${erp.op.save.url:/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Save.common.kdsvc}")
    private String saveUrl;

    // 查看表单数据 url
    @Value("${erp.op.view.url:/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.View.common.kdsvc}")
    private String viewUrl;

    // HTTP 请求客户端
    private static final CloseableHttpClient client = HttpClients.createDefault();

    // HTTP 请求配置信息
    private static final RequestConfig config = RequestConfig.custom()
            .setSocketTimeout(10000) // 传输超时时间
            .setConnectTimeout(30000) // 连接超时时间
            .build();

    // 是否登录
    private static AtomicBoolean login = new AtomicBoolean(Boolean.FALSE);

    /**
     * ERP 数据保存
     *
     * @param bi
     * @return
     */
    public ObjectResponse<String> save(SendSiteBi bi) {
        ErpSaveModel model = bi.map2ErpSaveModel();
        ErpBaseRequest<Model<ErpSaveModel>> request = new ErpBaseRequest<>();
        request.setFormid(this.formid);
        request.setData(new Model<ErpSaveModel>().setModel(model));

        return send(this.saveUrl, request);
    }

    /**
     * ERP 数据查看
     *
     * @param bi
     * @return
     */
    public ObjectResponse<String> view(SendSiteBi bi) {
        ErpViewModel model = new ErpViewModel().setId(bi.getErpId()).setNumber(bi.getSeqNo());
        ErpBaseRequest<ErpViewModel> request = new ErpBaseRequest<>();
        request.setFormid(this.formid);
        request.setData(model);

        return send(this.viewUrl, request);
    }

    /**
     * ERP 保存数据
     *
     * @param request
     * @return
     */
    private ObjectResponse<String> send(String url, ErpBaseRequest request) {
        log.info("send: data = {}, url = {},", JsonUtils.toJsonString(request), url);

        ObjectResponse<String> res = RestUtils.buildObjectResponse(null);

        // 先登录
        if (!login.get()) {
            this.login();
        }

        if (!login.get()) {
            log.error("ERP系统登陆失败");
            res.setStatus(5000).setError("ERP系统登陆失败");
            return res;
        }

        // 保存
        Optional<String> result = sendPost(this.erpHost + url, JsonUtils.toJsonString(request));
        log.info("send result = {}", result);

        // 登陆失效
        // {"Result":{"ResponseStatus":{"ErrorCode":500,"IsSuccess":false,"Errors":[{"FieldName":null,"Message":"会话信息已丢失，请重新登录","DIndex":0}],
        // "SuccessEntitys":[],"SuccessMessages":[],"MsgCode":1}}}

        // 推送成功
        // {"Result":{"ResponseStatus":{"IsSuccess":true,"Errors":[],"SuccessEntitys":[{"Id":105001,"Number":"2020032***********","DIndex":0}],"SuccessMessages":[],"MsgCode":0},"Id":105001,"Number":"2020032***********","NeedReturnData":[{}]}}

        // 查看失败
        // {"Result":{"ResponseStatus":{"ErrorCode":500,"IsSuccess":false,"Errors":[{"FieldName":null,"Message":"您要读取的数据在系统中不存在,可能已经被删除！[ID=66666666,Type=BillHead]\r\nServer stack trace: \r\n   在 Kingdee.BOS.Orm.DataManager.DataManagerImplement.Read(Object oid, OperateOption option)\r\n   在 Kingdee.BOS.App.Core.BusinessDataReader.LoadSingle(Object pk, DynamicObjectType type, Boolean loadReferenceData, OperateOption option)\r\n   在 Kingdee.BOS.App.Core.ViewService.LoadSingle(Context ctx, Object pk, DynamicObjectType type, OperateOption option)\r\n\r\nException rethrown at [0]: \r\n   在 System.Runtime.Remoting.Proxies.RealProxy.HandleReturnMessage(IMessage reqMsg, IMessage retMsg)\r\n   在 System.Runtime.Remoting.Proxies.RealProxy.PrivateInvoke(MessageData& msgData, Int32 type)\r\n   在 Kingdee.BOS.Contracts.IViewService.LoadSingle(Context ctx, Object pk, DynamicObjectType type, OperateOption option)\r\n   在 Kingdee.BOS.ServiceHelper.BusinessDataServiceHelper.LoadSingle(Context ctx, Object pk, DynamicObjectType type, OperateOption option)\r\n   在 Kingdee.BOS.Model.Bill.BillModel.Load(Object pk)\r\n   在 Kingdee.BOS.WebApi.FormService.View.Execute()\r\n   在 Kingdee.BOS.WebApi.FormService.BillOperationService.ExecuteOperation(FormOperation op, String data)","DIndex":0}],"SuccessEntitys":[],"SuccessMessages":[],"MsgCode":0}}}
        if (result.isPresent()) {
            // 推送结果
            if (getResult(result.get())) { // 成功
                log.info("success = {}", result.get());
                return RestUtils.buildObjectResponse(result.get());
            } else { // 失败
                log.error("推送失败: {}", getErrorMsg(result.get()));
                Optional<Integer> errorCode = getErrorCode(result.get());
                Optional<String> errorMsg = getErrorMsg(result.get());

                res.setStatus(errorCode.orElse(5003)).setError(errorMsg.orElse("自定义失败"));
            }
        } else {
            log.error("推送失败(执行推送请求异常)");

            // 推送失败
            res.setStatus(5001).setError("推送失败(执行推送请求异常)");

            // 重送机制是否需要？
        }

        return res;
    }

    public void clearLogin() {
        login.set(Boolean.FALSE);
    }

    /**
     * ERP 系统登录
     *
     */
    public void login() {
        ErpLoginRequest request = new ErpLoginRequest();
        request.setAcctID(this.acctID)
                .setLcid(this.lcid)
                .setUsername(this.username)
                .setPassword(this.password);

        // 登录
        String loginUrl = this.erpHost + this.loginUrl;
        Optional<String> result = sendPost(loginUrl, JsonUtils.toJsonString(request));
//        log.info("login result = {}", result);

        // {"Message":null,"MessageCode":"CheckPasswordPolicy","LoginResultType":1,"Context":{"UserLocale":"zh-CN","LogLocale":"","DBid":"5d88e76b37e46e","DatabaseType":2,"SessionId":"ilui20uzuittsvedtfkm2355","UseLanguages":[{"LocaleId":2052,"LocaleName":"中文(简体/中国)","Alias":"CN"}],"UserId":101509,"UserName":"kingdee","CustomName":"国充充电科技江苏股份有限公司","DisplayVersion":"7.3.1351.3","DataCenterName":"亨通光电","UserToken":"15c56eec-abfb-452b-947e-686ce076a6eb","CurrentOrganizationInfo":{"ID":101505,"AcctOrgType":"1","Name":"国充充电科技江苏股份有限公司","FunctionIds":[101,102,103,104,107,108,109,110,111,112,113]},"IsCH_ZH_AutoTrans":false,"ClientType":32,"WeiboAuthInfo":{"WeiboUrl":null,"NetWorkID":null,"CompanyNetworkID":null,"Account":" @","AppKey":"FkdTqJiNeCQC0ugp","AppSecret":"yCP3ucK2IQUm2D3heHxiarq1RJZwfcnKullRSMOIEM","TokenKey":" ","TokenSecret":" ","Verify":null,"CallbackUrl":null,"UserId":" ","Charset":{"BodyName":"utf-8","EncodingName":"Unicode (UTF-8)","HeaderName":"utf-8","WebName":"utf-8","WindowsCodePage":1200,"IsBrowserDisplay":true,"IsBrowserSave":true,"IsMailNewsDisplay":true,"IsMailNewsSave":true,"IsSingleByte":false,"EncoderFallback":{"DefaultString":"�","MaxCharCount":1},"DecoderFallback":{"DefaultString":"�","MaxCharCount":1},"IsReadOnly":true,"CodePage":65001}},"UTimeZone":{"OffsetTicks":************,"StandardName":"(UTC+08:00)北京，重庆，香港特别行政区，乌鲁木齐","Id":230,"Number":"1078_SYS","CanBeUsed":true},"STimeZone":{"OffsetTicks":************,"StandardName":"(UTC+08:00)北京，重庆，香港特别行政区，乌鲁木齐","Id":230,"Number":"1078_SYS","CanBeUsed":true},"GDCID":"bae2a6ff-907f-4c13-b4e7-153099a802e5"},"KDSVCSessionId":null,"FormId":null,"RedirectFormParam":null,"FormInputObject":null,"ErrorStackTrace":null,"Lcid":0,"AccessToken":null,"KdAccessResult":null,"IsSuccessByAPI":true}
        if (result.isPresent() &&
                JsonUtils.fromJson(result.get()).get("LoginResultType") != null &&
                JsonUtils.fromJson(result.get()).get("LoginResultType").asInt() == ErpLoginStatus.SUCCESS.getCode()) {
            login.set(Boolean.TRUE);
        } else {
            log.error("登录失败: {}", result.isPresent() ? getLoginErrorMsg(result.get()) : "");
            login.set(Boolean.FALSE);
        }
    }

    private static Optional<String> sendPost(String url, String json) {
        HttpPost httpPost = new HttpPost(url);
        httpPost.setConfig(config);
        StringEntity data = new StringEntity(json, "UTF-8");
        httpPost.setHeader("Accept", "application/json");
        httpPost.setHeader("Content-type", "application/json");
        httpPost.setEntity(data);

        CloseableHttpResponse response = null;
        try {
            response = client.execute(httpPost);
            HttpEntity entity = response.getEntity();
            String result = EntityUtils.toString(entity, "UTF-8");
            log.info("erp 返回结果: result = {}", result);
            return Optional.of(result);
        } catch (IOException e) {
            log.error("推送ERP异常: err = {}", e.getMessage(), e);
            login.set(Boolean.FALSE);
        } finally {
            if (null != response) {
                try {
                    response.close();
                } catch (IOException e) {
                    log.error("response 关闭异常: err = {}", e.getMessage(), e);
                }
            }
        }

        return Optional.empty();
    }

    private static Boolean getResult(String json) {
        JsonNode node = JsonUtils.fromJson(json)
                .get("Result")
                .get("ResponseStatus");

        // 查看接口请求成功没有返回状态信息: ResponseStatus: null
        if ("null".equals(node.asText())) {
            return Boolean.TRUE;
        }

        return node.get("IsSuccess")
                .asBoolean(Boolean.FALSE);
    }

    private static Optional<Integer> getErrorCode(String json) {
        return Optional.of(JsonUtils.fromJson(json)
                .get("Result")
                .get("ResponseStatus")
                .get("ErrorCode").asInt());
    }

    private static Optional<String> getLoginErrorMsg(String json) {
        JsonNode node = JsonUtils.fromJson(json);
        if (null != node) {
            return Optional.of(node.get("Message").asText());
        } else {
            return Optional.empty();
        }
    }

    private static Optional<String> getErrorMsg(String json) {
        JsonNode node = JsonUtils.fromJson(json)
                .get("Result")
                .get("ResponseStatus")
                .get("Errors").get(0);
        if (null != node) {
            return Optional.of(node.get("Message").asText());
        } else {
            return Optional.empty();
        }
    }
}
