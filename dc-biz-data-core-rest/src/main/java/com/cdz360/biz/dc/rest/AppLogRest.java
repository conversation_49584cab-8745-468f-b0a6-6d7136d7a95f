package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.dc.service.AppLogService;
import com.cdz360.biz.model.trading.app.po.AppCrashLogPo;
import com.cdz360.biz.model.trading.coupon.param.ListActivityParam;
import com.cdz360.biz.model.trading.coupon.vo.ActivityVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * AppLogRest
 *
 * @since 10/30/2023 5:18 PM
 * <AUTHOR>
 */
@Slf4j
@RestController
public class AppLogRest {

    @Autowired
    private AppLogService appLogService;

    @PostMapping(value = "/dataCore/app/addAppCrashLog")
    public BaseResponse addAppCrashLog(@RequestBody AppCrashLogPo req) {
        return appLogService.addAppCrashLog(req);
    }
}