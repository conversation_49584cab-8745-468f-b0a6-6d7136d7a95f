package com.cdz360.biz.dc.client;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.chargerlinkcar.core.domain.Commercial;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_USER, fallbackFactory = HystrixMerchantClientFactory.class)
public interface MerchantFeignClient {
//    /**
//     * 根据商户id获取商户ID及子商户ID集合
//     * @param commId
//     * @return 商户ID及子商户ID集合
//     */
//    @RequestMapping(value = "/api/merchant/getCommIdListByCommId",method = RequestMethod.POST)
//    ListResponse<Long> getCommIdListByCommId(@RequestParam(value = "commId") Long commId);

    /**
     * 根据token获取商户
     *
     * @param token
     * @return 商户
     */
    @GetMapping("/api/commercial/getCommercialByToken")
    ObjectResponse<Commercial> getCommercialByToken(@RequestParam("token") String token);
}
