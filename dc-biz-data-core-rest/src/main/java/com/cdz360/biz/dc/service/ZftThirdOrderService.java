package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ds.trading.ro.bill.ds.ZftThirdOrderRoDs;
import com.cdz360.biz.ds.trading.rw.bill.ds.ZftThirdOrderRwDs;
import com.cdz360.biz.model.trading.bill.dto.ZftThirdOrderDto;
import com.cdz360.biz.model.trading.bill.param.ListZftThirdOrderParam;
import com.cdz360.biz.model.trading.bill.vo.TradeOrderBi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class ZftThirdOrderService {

    @Autowired
    private ZftThirdOrderRoDs zftThirdOrderRoDs;

    @Autowired
    private ZftThirdOrderRwDs zftThirdOrderRwDs;


    public Mono<ListResponse<ZftThirdOrderDto>> findAllZftThirdOrder(ListZftThirdOrderParam param) {
        return Mono.just(param)
                .map(zftThirdOrderRoDs::findAllZftThirdOrder)
                .map(RestUtils::buildListResponse)
                .doOnNext(res -> {
                    if (null != param.getTotal() && param.getTotal()) {
                        res.setTotal(zftThirdOrderRoDs.count(param));
                    }
                });
    }

    public Mono<ListResponse<TradeOrderBi>> tradeOrderBi(ListZftThirdOrderParam param) {
        return Mono.just(param)
                .map(zftThirdOrderRoDs::tradeOrderBi)
                .map(RestUtils::buildListResponse);
    }

}
