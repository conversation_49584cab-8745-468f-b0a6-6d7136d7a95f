package com.cdz360.biz.dc.domain;

import com.cdz360.biz.model.site.type.AlarmEventTypeEnum;
import com.cdz360.biz.model.site.type.AlarmStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * WWarningRecordInMongo
 *  来自于 dz-chargelink-device: com/chargerlink/device/monitor/entity/po/WWarningRecord.java
 * @since 3/29/2020 5:09 PM
 * <AUTHOR>
 */
@Data
@Document(collection = "w_warning_record")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WWarningRecordInMongo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 告警记录id-告警报表中‘告警编号’
     */
    @Schema(description = "告警编号")
    private Long warningId;

    /**
     * 告警报表中‘上报设备/编号’
     * 为空时，取boxOutFactoryCode值
     */
    private String sourceNo;

    /**
     * 告警编码-告警报表中‘告警代码’
     */
    @Schema(description = "告警代码")
    private String warningCode;
    /**
     * 告警名-告警报表中‘告警备注’
     */
    @Schema(description = "告警备注")
    private String warningName;
    /**
     * 告警处理说明-告警报表中‘补充说明’
     */
    @Schema(description = "补充说明")
    private String warningInstructions;

    /**
     * 设备id
     */
    private String deviceId;


    /**
     * 设备序列号/桩号-告警报表中‘告警对象/编号’
     */
    @Schema(description = "告警对象/编号")
    private String boxOutFactoryCode;

    /**
     * 充电接口(枪头)序号
     */
    private Integer connectorId;

    /**
     * 储能ESS唯一编号
     */
    private String dno;

    /**
     * 站点id-告警报表中‘告警所属场站’
     */
    @Schema(description = "告警所属场站")
    private String siteId;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 商户id
     */
    private String businessId;

    /**
     * 告警等级
     */
    private Integer level;

    /**
     * {@link AlarmEventTypeEnum}
     * -告警报表中‘告警类型’
     */
    @Schema(description = "告警类型")
    private Integer warningType;

    /**
     * 开始时间-告警报表中‘发生时间’
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "发生时间")
    private Date startTime;
    /**
     * 结束时间-告警报表中‘结束时间’
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "结束时间")
    private Date endTime;
    /**
     * 告警最后上报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date warningUpdateTime;

    /**
     * {@link AlarmStatusEnum}
     * -告警报表中‘告警状态’
     */
    @Schema(description = "告警状态")
    private Integer status;

    /**
     * 持续时长单位秒（暂时由计算得出）
     */
    private Long duration;

    /**
     * 备注信息（用于存储说明信息）
     */
    private String remark;
    /**
     * 错误描述
     */
    private String error;
    /**
     * 温度 温度异常时有值
     */
    private Integer temp;

    /**
     * 设备最后心跳时间
     */
    private Long heartBeatTime;

    /**
     * 操作人id
     */
    private Long updateBy;

    private String gwno;

    private String appName;
    /**
     * 光储ESS设备ID
     */
    private Long  equipId;

    /**
     * 光储设备ESS名称
     */
    private String essEquipName;

}