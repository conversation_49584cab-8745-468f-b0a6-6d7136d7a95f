package com.cdz360.biz.dc.config;

import com.cdz360.base.utils.JsonUtils;
import java.nio.charset.Charset;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;

/**
 * 用来序列化value
 *
 * <AUTHOR>
 * @since Create on 2018/6/16 18:07
 */
@Slf4j
public class FastJsonRedisSerializer implements RedisSerializer<Object> {

    public static final Charset DEFAULT_CHARSET = Charset.forName("UTF-8");
    private Class<Object> clazz;
    private JdkSerializationRedisSerializer jdkSerializationRedisSerializer;

    public FastJsonRedisSerializer() {
        this.clazz = Object.class;
        jdkSerializationRedisSerializer = new JdkSerializationRedisSerializer();
    }

    @Override
    public byte[] serialize(Object t) throws SerializationException {
        if (t == null) {
            return new byte[0];
        }
        //FIXME 通过“JsonUtils.toJsonString(t, SerializerFeature.WriteClassName)”转换得到的json字符串，包含t的包名“"@type":"com.chargerlink.xxx"”
        //FIXME 当反序列化的类型与t不一致时，会抛出类型转换失败的异常
        return JsonUtils.toJsonString(t).getBytes(DEFAULT_CHARSET);
//        return JsonUtils.toJsonString(t, SerializerFeature.WriteClassName).getBytes(DEFAULT_CHARSET);
//        return JsonUtils.toJsonString(t).getBytes(RedisAutoConfiguration.DEFAULT_CHARSET);
    }

    @Override
    public Object deserialize(byte[] bytes) throws SerializationException {
        if (bytes == null || bytes.length <= 0) {
            return null;
        }
        String str = new String(bytes, DEFAULT_CHARSET);
        Object obj;
        try {
            obj = JsonUtils.fromJson(str, clazz);
        } catch (Exception e) {
            log.info("反序列化失败");
            e.printStackTrace();
            obj = jdkSerializationRedisSerializer.deserialize(bytes);
        }
        return obj;
    }

}