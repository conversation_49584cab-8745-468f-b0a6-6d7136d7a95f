package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.dc.service.CecSiteService;
import com.cdz360.biz.model.geo.po.DistrictPo;
import com.cdz360.biz.model.trading.site.param.ListCecSiteParam;
import com.cdz360.biz.model.trading.site.vo.PartnerSiteVo;
import com.chargerlinkcar.framework.common.domain.PartnerSiteDetailInfoVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

@Slf4j
@Tag(name = "CEC 互联站点相关接口", description = "CEC 互联站点相关接口")
@RestController
public class CecSiteRest {

    @Autowired
    private CecSiteService cecSiteService;

    @PostMapping(value = "/dataCore/cec/siteList")
    public Mono<ListResponse<PartnerSiteVo>> cecSiteList(
            @RequestBody ListCecSiteParam param) {
        log.debug("互联站点列表: param = {}", JsonUtils.toJsonString(param));
        return cecSiteService.cecSiteList(param);
    }

    @GetMapping(value = "/dataCore/cec/siteDetail")
    public Mono<ObjectResponse<PartnerSiteDetailInfoVo>> cecSiteDetail(
            @RequestParam("siteId") String siteId) {
        log.debug("互联站点详情: siteId = {}", siteId);
        return cecSiteService.cecSiteDetail(siteId);
    }

//    @GetMapping(value = "/dataCore/cec/sitePlugStatusStats")
//    public Mono<ObjectResponse<PlugStatusCountDto>> cecSitePlugStatusStats(
//            @RequestParam("siteId") String siteId) {
//        log.debug("互联站点详情: siteId = {}", siteId);
//        return cecSiteService.cecSitePlugStatusStats(siteId);
//    }

    @PostMapping(value = "/dataCore/ces/getDistrictByList")
    public Mono<ListResponse<DistrictPo>> getDistrictByList(@RequestBody List<String> code) {
        log.info("getDistrictByList code.size = {}", code.size());
        return cecSiteService.getDistrictByList(code);
    }
}
