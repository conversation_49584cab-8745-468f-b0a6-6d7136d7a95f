package com.cdz360.biz.dc.domain.erp;

import lombok.Getter;

/**
 * ERP 登录返回状态
 */
@Getter
public enum ErpLoginStatus {
    ACTIVATION(-7, "激活"),
    ENTRY_CLOUD_UNBIND(-6, "云通行证未绑定Cloud账号"),
    DEAL_WITH_FORM(-5, "需要表单处理"),
    WARNNING(-4, "登录警告"),
    PW_INVALID_REQUIRED(-3, "密码验证不通过（强制的）"),
    PW_INVALID_OPTIONAL(-2, "密码验证不通过（可选的）"),
    FAILURE(-1, "登录失败"),
    PW_ERROR(0, "用户或密码错误"),
    SUCCESS(1, "登录成功")
    ;

    private long code;
    private String desc;

    ErpLoginStatus(long code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
