package com.cdz360.biz.dc.service.parse;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.config.MchBillRegexConfig;
import com.cdz360.biz.dc.service.parse.line.AliPayLineParse;
import com.cdz360.biz.dc.service.parse.line.LineParse;
import com.cdz360.biz.model.trading.bill.po.ZftThirdOrderPo;
import com.cdz360.biz.model.trading.bill.type.DailyBillCheckResult;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrder;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AliPayFileParse extends AbstractFileParse {

    private LineParse lineParse;

    public AliPayFileParse(AliPayLineParse lineParse) {
        this.lineParse = lineParse;
    }

    @Override
    public void parseFile(String absolutePath, Long dailyBillId) {
        BufferedReader br = null;

        File file = new File(absolutePath);
        try {
            List<ZftThirdOrderPo> list = new ArrayList<>(1000);
            log.info("文件名称:" + absolutePath);
            br = new BufferedReader(new InputStreamReader(new FileInputStream(file), "GBK"));

            String strLine = null;
            while ((strLine = br.readLine()) != null) {
                if (strLine.startsWith("#")) {
                    log.warn("【支付宝对账】注释数据行信息，Line Info:" + strLine);
                    continue;
                }
                if (strLine.startsWith("支付宝交易号")) {
                    log.warn("【支付宝对账】注释数据行信息，Line Info:" + strLine);
                    continue;
                }

                ZftThirdOrderPo thirdOrderPo = (ZftThirdOrderPo) lineParse.resolveLine(strLine);
                if (thirdOrderPo == null) {
                    continue;
                }

                thirdOrderPo.setDailyBillId(dailyBillId);
                super.billCheck(thirdOrderPo, dailyBillId);

                list.add(thirdOrderPo);
                if (list.size() >= 1000) {
                    log.warn("【支付宝对账】保存1000条信息到数据库");
                    zftThirdOrderRwDs.saveBatchList(list);
                    list.clear();
                }
            }

            // 继续保存这些数据
            if (list.size() > 0) {
                log.warn("【支付宝对账】保存" + list.size() + "条信息到数据库");
                zftThirdOrderRwDs.saveBatchList(list);
                list.clear();
            }
//            br.close();
        } catch (Exception e) {
            if (e instanceof FileNotFoundException) {
                log.error("文件是空文件,今日对账结束");
                throw new DcServiceException(e.getMessage());
            }
            log.info("【支付宝对账文件解析】 异常信息：{}", e.getMessage());
            throw new DcServiceException("支付宝对账文件解析及入DB库出错");
        } finally {
            if (br != null) {
                try {
                    br.close();
                    boolean delete = file.delete();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @Override
    public void regexParseFile(String absolutePath, MchBillRegexConfig.CorpMchInfo corpMchInfo) {
        BufferedReader br = null;
        Long corpId = corpMchInfo.getCorpId();

        File file = new File(absolutePath);
        try {
            List<ZftThirdOrderPo> list = new ArrayList<>(1000);
            log.info("文件名称:" + absolutePath);
            br = new BufferedReader(new InputStreamReader(new FileInputStream(file), "GBK"));

            String strLine = null;
            while ((strLine = br.readLine()) != null) {
                if (strLine.startsWith("#")) {
                    log.warn("【支付宝对账】注释数据行信息，Line Info:" + strLine);
                    continue;
                }
                if (strLine.startsWith("支付宝交易号")) {
                    log.warn("【支付宝对账】注释数据行信息，Line Info:" + strLine);
                    continue;
                }

                ZftThirdOrderPo thirdOrderPo = (ZftThirdOrderPo) lineParse.regexResolveLine(strLine,
                    corpMchInfo.getRegex());
                if (thirdOrderPo == null) {
                    continue;
                }

                if (StringUtils.isNotBlank(thirdOrderPo.getOpenOrderId())) {
                    ChargerOrder chargerOrder = chargerOrderRoMapper.findByOpenOrderId(
                        thirdOrderPo.getOpenOrderId());
                    if (null == chargerOrder) {
                        thirdOrderPo.setCheckResult(DailyBillCheckResult.NO_NOT_MATCH);
                    }
                } else {
                    thirdOrderPo.setCheckResult(DailyBillCheckResult.NO_NOT_MATCH);
                }
                thirdOrderPo.setDailyBillId(0L);
                thirdOrderPo.setCorpId(corpId);

                // FIXME: 第三方订单对账
//                thirdOrderPo.setDailyBillId(dailyBillId);
//                super.billCheck(thirdOrderPo, dailyBillId);

                list.add(thirdOrderPo);

                if (list.size() >= 1000) {
                    log.warn("【支付宝对账】保存1000条信息到数据库");
                    super.billRegexCheck(list);

                    zftThirdOrderRwDs.saveBatchList(list);
                    list.clear();
                }
            }

            // 继续保存这些数据
            if (list.size() > 0) {
                log.warn("【支付宝对账】保存" + list.size() + "条信息到数据库");
                super.billRegexCheck(list);

                zftThirdOrderRwDs.saveBatchList(list);
                list.clear();
            }

            br.close();
        } catch (Exception e) {
            if (e instanceof FileNotFoundException) {
                log.error("文件是空文件,今日对账结束");
                throw new DcServiceException(e.getMessage());
            }
            log.info("【支付宝对账文件解析】 异常信息：{}", e.getMessage());
            throw new DcServiceException("支付宝对账文件解析及入DB库出错");
        } finally {
            if (br != null) {
                try {
                    br.close();
                    boolean delete = file.delete();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
