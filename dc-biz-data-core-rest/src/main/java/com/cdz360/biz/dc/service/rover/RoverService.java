package com.cdz360.biz.dc.service.rover;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.auth.user.vo.SiteGroupUserRefVo;
import com.cdz360.biz.ds.trading.ro.rover.ds.SiteRoverAssetRoDs;
import com.cdz360.biz.ds.trading.ro.rover.ds.SiteRoverCfgRoDs;
import com.cdz360.biz.ds.trading.ro.rover.ds.SiteRoverRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.ds.trading.rw.rover.ds.SiteRoverAssetRwDs;
import com.cdz360.biz.ds.trading.rw.rover.ds.SiteRoverCfgRwDs;
import com.cdz360.biz.ds.trading.rw.rover.ds.SiteRoverRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteRwDs;
import com.cdz360.biz.model.FileItem;
import com.cdz360.biz.model.OldOrderImagesStruct;
import com.cdz360.biz.model.sys.constant.SiteGroupType;
import com.cdz360.biz.model.sys.param.ListSiteGroupParam;
import com.cdz360.biz.model.sys.vo.SiteGroupVo;
import com.cdz360.biz.model.trading.rover.dto.RoverMixDto;
import com.cdz360.biz.model.trading.rover.param.RoverSearchParam;
import com.cdz360.biz.model.trading.rover.param.SiteListRoverCycleParam;
import com.cdz360.biz.model.trading.rover.po.SiteRoverAssetPo;
import com.cdz360.biz.model.trading.rover.po.SiteRoverCfgPo;
import com.cdz360.biz.model.trading.rover.po.SiteRoverPo;
import com.cdz360.biz.model.trading.rover.type.RoverStatusType;
import com.cdz360.biz.model.trading.rover.vo.SiteRoverCfgVo;
import com.cdz360.biz.model.trading.rover.vo.SiteRoverVo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.utils.feign.hlht.OpenHlhtFeignClient;
import com.chargerlinkcar.framework.common.domain.param.CorpWxSendMsgCommonParam;
import com.chargerlinkcar.framework.common.domain.param.CorpWxSendMsgKV;
import com.chargerlinkcar.framework.common.feign.AuthCenterFeignClient;
import com.chargerlinkcar.framework.common.service.RedisNoGen;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

/**
 * RoverService
 *
 * @since 7/27/2022 2:00 PM
 * <AUTHOR>
 */
@Slf4j
@Service
public class RoverService {

    @Autowired
    private OpenHlhtFeignClient openHlhtFeignClient;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private RedisNoGen redisNoGen;

    @Autowired
    private SiteRwDs siteRwDs;

    @Autowired
    private SiteRoDs siteRoDs;

    @Autowired
    private SiteRoverRoDs siteRoverRoDs;

    @Autowired
    private SiteRoverRwDs siteRoverRwDs;

    @Autowired
    private SiteRoverAssetRwDs siteRoverAssetRwDs;

    @Autowired
    private SiteRoverAssetRoDs siteRoverAssetRoDs;

    @Autowired
    private SiteRoverCfgRoDs siteRoverCfgRoDs;

    @Autowired
    private SiteRoverCfgRwDs siteRoverCfgRwDs;

    @Transactional
    public Mono<RoverMixDto> createRover(RoverMixDto param) {
        return Mono.just(param)
            .doOnNext(e -> {
                IotAssert.isNotNull(param.getSiteRoverVo(), "请传入运营巡查信息");

                final SitePo siteById = siteRwDs.getSiteById(param.getSiteRoverVo().getSiteId(),
                    false);
                IotAssert.isNotNull(siteById, "找不到对应场站信息");

                String roverNo = redisNoGen.roverNo();
                log.info("new roverNo: {}", roverNo);
                param.getSiteRoverVo().setNo(roverNo)
                    .setEnable(true);
                if (param.getSiteRoverVo().getStatus() == null) {
                    param.getSiteRoverVo().setStatus(RoverStatusType.INIT);
                }

                if (RoverStatusType.WAIT_RANK.equals(param.getSiteRoverVo().getStatus())) {
                    log.info("新增提交，设定巡查时间为当前时间");
                    param.getSiteRoverVo().setRoverTime(new Date());
                }

                if (siteRoverRwDs.insertSiteRover(param.getSiteRoverVo())) {
                    Optional.ofNullable(e.getAssetPos()).ifPresentOrElse(opt -> {
                        opt.forEach(po -> {
                            po.setRoverId(e.getSiteRoverVo().getId()).setEnable(true);
                            siteRoverAssetRwDs.insertSiteRoverAsset(po);
                        });
                    }, () -> log.info("巡查详情"));
                }
            })
            .map(e -> e);
    }

    @Transactional
    public Mono<RoverMixDto> updateRover(RoverMixDto param) {
        return Mono.just(param)
            .doOnNext(e -> {
                IotAssert.isNotNull(param.getSiteRoverVo(), "请传入运营巡查信息");
                IotAssert.isNotNull(param.getSiteRoverVo().getId(), "请传入运营巡查信息id");

                final SiteRoverPo byId = siteRoverRwDs.getById(
                    param.getSiteRoverVo().getId(), true);

                if (RoverStatusType.INIT.equals(byId.getStatus()) &&
                    RoverStatusType.WAIT_RANK.equals(param.getSiteRoverVo().getStatus())) {
                    log.info("状态由INIT->WAIT_RANK,设定巡查时间");
                    param.getSiteRoverVo().setRoverTime(new Date());
                }

                siteRoverRwDs.updateSiteRover(param.getSiteRoverVo());

                Optional.ofNullable(e.getAssetPos()).ifPresentOrElse(opt -> {
                    log.info("disable count evse: {}",
                        siteRoverAssetRwDs.disableAllByRoverId(e.getSiteRoverVo().getId()));
                    opt.forEach(po -> {
                        po.setRoverId(e.getSiteRoverVo().getId());
                        siteRoverAssetRwDs.insertSiteRoverAsset(po);
                    });
                }, () -> log.info("未传入场站设备信息"));
            })
            .map(e -> e);
    }

    public Mono<ListResponse<RoverMixDto>> searchPrerunList(RoverSearchParam param) {
        return Mono.just(param)
            .map(e -> {
                IotAssert.isNotNull(param, "请传入搜索条件");
                if (param.getSize() == null) {
                    param.setSize(10);
                }
                List<SiteRoverVo> roverVos = siteRoverRoDs.searchRoverList(param);
                return roverVos;
            })
            .map(e -> e.stream().map(rover -> {
                RoverMixDto ret = new RoverMixDto();
                return ret.setAssetPos(siteRoverAssetRoDs.getByRoverId(rover.getId()))
                    .setSiteRoverVo(rover);
            }).collect(Collectors.toList()))
            .map(RestUtils::buildListResponse)
            .doOnNext(e -> {
                if (Boolean.TRUE.equals(param.getTotal())) {
                    long total = siteRoverRoDs.searchRoverListCount(param);
                    e.setTotal(total);
                }
//                else {
//                    e.setTotal(0L);
//                }
            });
    }

    public SiteRoverCfgPo getSiteRoverCfg(String siteId) {
        SiteRoverCfgPo siteRover = siteRoverCfgRoDs.getBySiteId(siteId);
        if (siteRover != null && Boolean.TRUE.equals(siteRover.getEnable())) {
            siteRover.setGidList(siteRoverCfgRoDs.getGidsBySite(siteId));
        }
        return siteRover;
    }

    @Transactional
    public SiteRoverCfgPo setSiteRoverCfg(SiteRoverCfgPo param) {
        IotAssert.isNotNull(param, "请传入设定参数");
        IotAssert.isNotBlank(param.getSiteId(), "请传入场站id");
        IotAssert.isNotNull(param.getCycle(), "请传入周期值");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getGidList()), "提醒通知范围不能为空");

        final SiteRoverCfgPo bySiteId = siteRoverCfgRoDs.getBySiteId(param.getSiteId());
        if (bySiteId != null) {
            log.info("清除旧数据: {}", JsonUtils.toJsonString(bySiteId));
            bySiteId.setEnable(false);
            siteRoverCfgRwDs.updateSiteRoverCfg(bySiteId);
        }

        if (Boolean.TRUE.equals(param.getEnable())) {
            log.info("新增新数据: {}", JsonUtils.toJsonString(param));
            siteRoverCfgRwDs.insertSiteRoverCfg(param);
            // 新增提醒通知组信息
            siteRoverCfgRwDs.updateSiteRoverGroup(param.getSiteId());
            siteRoverCfgRwDs.batchInsertSiteRoverGroup(param.getSiteId(), param.getGidList());
        }
        return param;
    }

    public Boolean cancelRover(Long roverId, Long cancellerUid, String cancellerName) {
        IotAssert.isNotNull(roverId, "请传入巡查id");
        final SiteRoverPo byId = siteRoverRoDs.getById(roverId);
        IotAssert.isNotNull(byId, "运营巡查信息不存在");
        byId.setStatus(RoverStatusType.CANCEL)
            .setCancellerName(cancellerName)
            .setCancellerUid(cancellerUid)
            .setCancelTime(new Date());
        return siteRoverRwDs.updateSiteRover(byId);
    }

    public Boolean deleteRover(Long roverId) {
        IotAssert.isNotNull(roverId, "请传入巡查id");
        final SiteRoverPo byId = siteRoverRoDs.getById(roverId);
        byId.setEnable(false);
        return siteRoverRwDs.updateSiteRover(byId);
    }

    public Mono<SiteRoverPo> rankRover(SiteRoverPo param) {
        return Mono.just(param)
            .map(e -> {
                RoverMixDto ret = new RoverMixDto();
                SiteRoverVo vo = new SiteRoverVo();
                BeanUtils.copyProperties(param, vo);
                return ret.setSiteRoverVo(vo);
            })
            .flatMap(this::updateRover)
            .map(RoverMixDto::getSiteRoverVo);
    }

    public Mono<List<SiteRoverVo>> getSiteRoverCycleList(SiteListRoverCycleParam param) {
        return Mono.just("")
            .map(e -> {
                final ListResponse<SiteGroupVo> res = authCenterFeignClient.getSiteGroupsByUid(
                    param.getToken());
                FeignResponseValidate.check(res);
                return res.getData();
            })
            .map(e -> {
                if (CollectionUtils.isEmpty(e)) {
                    log.warn("用户不在任何场站组中");
                    return List.of();
                }

                final List<String> gids = e.stream().map(SiteGroupVo::getGid)
                    .collect(Collectors.toList());

                if (CollectionUtils.isEmpty(gids)) {
                    log.warn("无任何场站组");
                    return List.of();
                }

                List<SiteRoverCfgVo> cfgs = siteRoverCfgRoDs.getByGids(gids);

                if (CollectionUtils.isEmpty(cfgs)) {
                    log.warn("无任何场站开启了周期提醒");
                    return List.of();
                }

                List<String> siteIds = cfgs.stream()
                    .map(SiteRoverCfgPo::getSiteId)
                    .distinct()
                    .collect(Collectors.toList());

                log.info("这些场站配置了周期: {}", siteIds);

                final Map<String, SiteRoverCfgVo> cfgMap = cfgs.stream()
                    .collect(Collectors.toMap(SiteRoverCfgVo::getSiteId, o -> o, (n, o) -> n));

                // 获取上次场站巡查的（最近一个待评分或已完结）
                List<SiteRoverVo> siteRoverVos = siteRoverRoDs.getRecentlyBySiteIds(siteIds);

                log.debug("siteRoverVos: {}", siteRoverVos);
                final Map<String, SiteRoverVo> roverMap = siteRoverVos.stream()
                    .collect(Collectors.toMap(SiteRoverVo::getSiteId, o -> o, (n, o) -> n));
                final Iterator<Entry<String, SiteRoverCfgVo>> it = cfgMap.entrySet()
                    .iterator();
                while (it.hasNext()) {
                    final Entry<String, SiteRoverCfgVo> next = it.next();
                    final String siteId = next.getKey();
                    final SiteRoverCfgVo cfg = next.getValue();

                    if (roverMap.get(siteId) != null) {
                        // 存在最近巡查项目
                        // 计算下次应查时间
                        final Date nextRoverTime = getNextRoverTime(
                            roverMap.get(siteId).getRoverTime(), cfg.getCycle());

//                        final int dayGap = cfg.getCycle() - dayCount(startMomentOfDay(new Date()),
//                            startMomentOfDay(roverMap.get(siteId).getRoverTime()));
                        final int dayGap = getDayGap(cfg.getCycle(),
                            startMomentOfDay(roverMap.get(siteId).getRoverTime()));
                        log.info("now: {}, next roverTime: {}, gap: {}",
                            new Date(), nextRoverTime, dayGap);

                        roverMap.get(siteId).setExpireDays(dayGap);
                    } else {
                        // 设置周期时的时间，设定为 伪上次巡查时间，由此计算下次应查时间
                        final Date nextRoverTime = getNextRoverTime(
                            cfg.getUpdateTime(), cfg.getCycle());

                        final int dayGap = getDayGap(cfg.getCycle(), cfg.getUpdateTime());
                        log.info("autoFill, now: {}, next roverTime: {}, gap: {}",
                            new Date(), nextRoverTime, dayGap);

                        SiteRoverVo siteRoverVo = new SiteRoverVo();
                        siteRoverVo.setExpireDays(dayGap);
                        siteRoverVo.setSiteName(cfg.getSiteName());
                        siteRoverVo.setSiteId(cfg.getSiteId());
                        roverMap.put(siteId, siteRoverVo);
                    }
                }
                final List<SiteRoverVo> sortedList = roverMap.values()
                    .stream()
                    .filter(r -> r.getExpireDays() != null)
                    .sorted(Comparator.comparing(SiteRoverVo::getExpireDays))
                    .collect(Collectors.toList());
                log.debug("sorted: {}", sortedList);
                return sortedList;
            });
    }

    private static int getDayGap(Integer cycle, Date d) {
        return cycle - dayCount(startMomentOfDay(new Date()),
            startMomentOfDay(d));
    }

    private static Date getNextRoverTime(Date date, Integer cycle) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startMomentOfDay(date));

        calendar.add(Calendar.DATE, cycle);
        return calendar.getTime();
    }

    private static Date startMomentOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.clear(Calendar.MILLISECOND);
        calendar.clear(Calendar.SECOND);
        calendar.clear(Calendar.MINUTE);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        return calendar.getTime();
    }

    private static LocalDate convertToLocalDateViaInstant(Date dateToConvert) {
        return dateToConvert.toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalDate();
    }

    /**
     * date1.day - date2.day
     *
     * @param date1
     * @param date2
     * @return
     */
    private static int dayCount(Date date1, Date date2) {
        final LocalDate localDate1 = convertToLocalDateViaInstant(date1);
        final LocalDate localDate2 = convertToLocalDateViaInstant(date2);
        int numDays = Period.between(localDate2, localDate1).getDays();
        return numDays;
    }

    public Mono<SiteRoverVo> getSiteRoverLatest(String siteId) {
        return Mono.just(siteId)
            .map(this::getSiteRoverVoLatest);
    }

    private SiteRoverVo getSiteRoverVoLatest(String siteId) {
        final List<SiteRoverVo> recentlyBySiteIds = siteRoverRoDs.getRecentlyBySiteIds(
            List.of(siteId));
        if (CollectionUtils.isNotEmpty(recentlyBySiteIds)) {
            return recentlyBySiteIds.get(0);
        } else {
            SiteRoverVo ret = new SiteRoverVo();
            final SitePo site = siteRoDs.getSite(siteId);
            if (site != null) {
                ret.setSiteName(site.getSiteName())
                    .setSiteAddress(site.getAddress());
            }
            return ret;
        }
    }

    public Mono<Integer> clearInitRover(Integer expireHour) {
        return Mono.just(expireHour)
            .map(e -> {

                RoverSearchParam param = new RoverSearchParam();
                param.setStatusTypes(List.of(RoverStatusType.INIT));
                param.setEnable(true);
                param.setSize(999);
                final List<SiteRoverVo> siteRoverVos = siteRoverRoDs.searchRoverList(param);

                AtomicInteger count = new AtomicInteger(0);
                if (CollectionUtils.isNotEmpty(siteRoverVos)) {
                    final Date ahora = new Date();
                    final long ayer = ahora.getTime() - expireHour * 3600 * 1000;
                    siteRoverVos.stream()
                        .filter(rover -> rover.getCreateTime().getTime() < ayer)
                        .forEach(rover -> {
                            rover.setStatus(RoverStatusType.CANCEL)
                                .setCancelTime(ahora)
                                .setCancellerUid(0L)
                                .setCancellerName("自动取消");
                            siteRoverRwDs.updateSiteRover(rover);
                            count.incrementAndGet();
                        });
                }
                return count.get();
            });
    }


    public Mono<ObjectResponse<Integer>> siteRoverNotify(final Integer beforeDay) {
        return Mono.just(beforeDay)
            .map(dayRange -> {
                // 获取开通 巡检提醒的场站
                List<SiteRoverCfgVo> siteList = siteRoverCfgRoDs.getSiteListEnable();
                if (CollectionUtils.isEmpty(siteList)) {
                    log.info("无场站需要推送消息");
                    return 0;
                }
                List<String> gidList = siteList.stream().map(SiteRoverCfgVo::getGidList)
                    .filter(CollectionUtils::isNotEmpty).flatMap(e -> e.stream()).distinct()
                    .collect(
                        Collectors.toList());
                if (CollectionUtils.isEmpty(gidList)) {
                    log.info("不存在需要推送的组信息");
                    return 0;
                }

                // 获取要推送的wxCorpUid
                ListSiteGroupParam params = new ListSiteGroupParam();
                params.setGidList(gidList)
                    .setTotal(Boolean.FALSE)
                    .setSize(1000);
                ListResponse<SiteGroupVo> siteGroupRes = authCenterFeignClient.findSiteGroupAndUser(
                    params);
                FeignResponseValidate.check(siteGroupRes);
                if (CollectionUtils.isEmpty(siteGroupRes.getData())) {
                    log.info("未获取到场站组信息");
                    return 0;
                }
                List<SiteGroupVo> siteGroupList = siteGroupRes.getData();

                // 获取场站上次推送日期
                List<SiteRoverVo> recentlyList = siteRoverRoDs.getRecentlyBySiteIds(
                    siteList.stream().map(SiteRoverCfgVo::getSiteId).collect(
                        Collectors.toList()));
                Map<String, Date> recentMap = recentlyList.stream()
                    .collect(Collectors.toMap(SiteRoverVo::getSiteId, SiteRoverVo::getRoverTime));
                AtomicInteger sendCount = new AtomicInteger(0);
                siteList.forEach(e -> {
                    // 判断时间间隙是否达到推送条件
                    int dayGap;
                    if (recentMap.get(e.getSiteId()) != null) {
                        // 有最近巡查记录,使用上次巡查时间作为间隙计算
                        dayGap = getDayGap(e.getCycle(),
                            recentMap.get(e.getSiteId()));
                    } else {
                        // 无最近巡查记录,使用配置时间作为间隙计算
                        dayGap = getDayGap(e.getCycle(),
                            e.getUpdateTime());
                    }
                    if (0 > dayGap || dayGap > dayRange) {
                        log.info("未达到推送条件: siteId = {},siteName={},dayGap={}", e.getSiteId(),
                            e.getSiteName(), dayGap);
                        return;
                    }

                    // 获取推送的corpWxUid
                    List<String> corpWxUids = siteGroupList.stream().filter(
                            i -> CollectionUtils.isNotEmpty(i.getCorpWxUidList()) && e.getGidList()
                                .contains(i.getGid())).flatMap(i -> i.getCorpWxUidList().stream())
                        .distinct()
                        .filter(StringUtils::isNotEmpty)
                        .collect(
                            Collectors.toList());
                    if (CollectionUtils.isEmpty(corpWxUids)) {
                        log.info("没有需要推送的成员: siteId={}", e.getSiteId());
                    }

                    // 以下开始推送
                    CorpWxSendMsgCommonParam msgParam = new CorpWxSendMsgCommonParam();
                    msgParam.setTitle("场站巡检");
                    CorpWxSendMsgKV kv1 = new CorpWxSendMsgKV("场站名称",
                        e.getSiteName());

                    String lastTime = "--";
                    if (recentMap.get(e.getSiteId()) != null) {
                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(
                            "MM月dd日 HH:mm");
                        lastTime = simpleDateFormat.format(
                            recentMap.get(e.getSiteId()));
                    }
                    CorpWxSendMsgKV kv2 = new CorpWxSendMsgKV("上次巡检时间", lastTime);
                    CorpWxSendMsgKV kv3 = new CorpWxSendMsgKV("场站巡检周期",
                        e.getCycle() + "天");

                    msgParam.setContentItems(List.of(kv1, kv2, kv3));
                    msgParam.setToUser(String.join("|", corpWxUids));

                    msgParam.setPage(
                        "pages/jumper/jumper?toPage=roverList&siteId=" + e.getSiteId());

                    sendCount.incrementAndGet();
                    openHlhtFeignClient.sendMsgCommon(e.getTopCommId(),
                            msgParam)
                        .subscribe(x -> log.info("企业推送结束: {}", x.getData()));

                });
                return sendCount.get();
            })
            .map(RestUtils::buildObjectResponse);
    }

    public Mono<ObjectResponse<Integer>> siteRoverNotify1(final Integer beforeDay) {
        return Mono.just(beforeDay)
            .map(dayRange -> {

                ListSiteGroupParam gParam = new ListSiteGroupParam();
                gParam.setTypeList(List.of(SiteGroupType.YY))
                    .setEnable(true)
                    .setSize(999);
                ListResponse<SiteGroupVo> siteGroupRes = authCenterFeignClient.findSiteGroup(
                    gParam);
                FeignResponseValidate.check(siteGroupRes);

                final List<SiteGroupVo> data = siteGroupRes.getData();
                if (CollectionUtils.isEmpty(data)) {
                    log.info("无运营场站组");
                    return 0;
                }

                final List<String> siteGids = data.stream()
                    .map(SiteGroupVo::getGid)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(siteGids)) {
                    log.info("无有效运营场站组");
                    return 0;
                }

                List<SiteRoverCfgVo> cfgs = siteRoverCfgRoDs.getByGids(siteGids);
                if (CollectionUtils.isNotEmpty(cfgs)) {
                    AtomicInteger sendCount = new AtomicInteger(0);
                    log.info("cfgs: {}", cfgs);
                    final Map<String, List<String>> groupGid = cfgs.stream()
                        .collect(Collectors.groupingBy(SiteRoverCfgVo::getSiteId,
                            Collectors.mapping(
                                SiteRoverCfgVo::getGid, Collectors.toList())));
                    log.info("groupGid: {}", groupGid);

                    final Map<String, SiteRoverCfgVo> cfgInfoMap = cfgs.stream()
                        .collect(Collectors.toMap(SiteRoverCfgVo::getSiteId, o -> o, (o, n) -> n));

                    final List<String> gids = cfgs.stream()
                        .map(SiteRoverCfgVo::getGid)
                        .distinct()
                        .collect(Collectors.toList());
                    log.info("gids: {}", gids);

                    final ListResponse<SiteGroupUserRefVo> res =
                        authCenterFeignClient.getUidsByGids(gids);
                    FeignResponseValidate.check(res);
                    final List<SiteGroupUserRefVo> refs = res.getData();
                    if (CollectionUtils.isEmpty(refs)) {
                        return 0;
                    }
                    final Map<String, List<String>> groupCorpWxUidMap = refs.stream()
                        .collect(Collectors.groupingBy(SiteGroupUserRefVo::getGid,
                            Collectors.mapping(SiteGroupUserRefVo::getCorpWxUid,
                                Collectors.toList())));
                    log.info("groupUidsMap: {}", groupCorpWxUidMap);

                    groupGid.entrySet()
                        .stream()
                        .forEach(g -> {
                            final String siteId = g.getKey();
                            final List<String> value = g.getValue();
                            final List<String> corpWxUids = value.stream()
                                .map(groupCorpWxUidMap::get)
                                .filter(CollectionUtils::isNotEmpty)
                                .flatMap(Collection::stream)
                                .distinct()
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.toList());
                            log.info("corpWxUids: {}", corpWxUids);
                            if (CollectionUtils.isNotEmpty(corpWxUids)) {
                                log.info("推送场站:{}提醒到:{}", siteId, corpWxUids);
                                CorpWxSendMsgCommonParam msgParam = new CorpWxSendMsgCommonParam();
                                msgParam.setTitle("场站巡查");

                                final SiteRoverCfgVo siteRoverCfgVo = cfgInfoMap.get(siteId);

                                CorpWxSendMsgKV kv1 = new CorpWxSendMsgKV("场站名称",
                                    siteRoverCfgVo.getSiteName());
                                final SiteRoverVo siteRoverVoLatest = this.getSiteRoverVoLatest(
                                    siteId);

                                int dayGap;
                                if (siteRoverVoLatest.getRoverTime() != null) {
                                    // 有最近巡查记录,使用上次巡查时间作为间隙计算
                                    dayGap = getDayGap(siteRoverCfgVo.getCycle(),
                                        siteRoverVoLatest.getRoverTime());
                                } else {
                                    // 无最近巡查记录,使用配置时间作为间隙计算
                                    dayGap = getDayGap(siteRoverCfgVo.getCycle(),
                                        siteRoverCfgVo.getUpdateTime());
                                }
                                log.info("间隙: {}", dayGap);
                                if (0 > dayGap || dayGap > dayRange) {
                                    // 不需要推送
                                    log.info("未达到推送条件: {}", dayGap);
                                    return;
                                }

                                String lastTime = "--";
                                if (siteRoverVoLatest.getRoverTime() != null) {
                                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat(
                                        "MM月dd日 HH:mm");
                                    lastTime = simpleDateFormat.format(
                                        siteRoverVoLatest.getRoverTime());
                                }
                                CorpWxSendMsgKV kv2 = new CorpWxSendMsgKV("上次巡查时间", lastTime);
                                CorpWxSendMsgKV kv3 = new CorpWxSendMsgKV("场站巡查周期",
                                    siteRoverCfgVo.getCycle() + "天");

                                msgParam.setContentItems(List.of(kv1, kv2, kv3));
                                msgParam.setToUser(String.join("|", corpWxUids));

                                msgParam.setPage(
                                    "pages/jumper/jumper?toPage=roverList&siteId=" + siteId);

                                sendCount.incrementAndGet();
                                openHlhtFeignClient.sendMsgCommon(siteRoverCfgVo.getTopCommId(),
                                        msgParam)
                                    .subscribe(x -> log.info("企业推送结束: {}", x.getData()));
                            }
                        });
                    return sendCount.get();

//                    final List<String> collect = groupGid.values()
//                        .stream()
//                        .flatMap(Collection::stream)
//                        .collect(Collectors.toList());

                } else {
                    log.info("没有配置周期的场站");
                }
                return 0;
            })
            .map(RestUtils::buildObjectResponse);
    }

    @Deprecated(since = "20230309")
    public Mono<BaseResponse> roverOrderFixImages() {
        int size = 100;
        int count = 0;
        while (count++ < 100) {
            List<OldOrderImagesStruct> result = siteRoverAssetRoDs.oldRoverOrder(size);
            if (CollectionUtils.isEmpty(result)) {
                break;
            }
            log.info("需要处理个数: {}", result.size());
            result.stream()
                .map(x -> new SiteRoverAssetPo()
                    .setId(Long.parseLong(x.getOrderNo()))
                    .setImages(x.getImages()
                        .stream().map(k -> new FileItem().setUrl(k))
                        .collect(Collectors.toList())))
                .forEach(order -> {
                    boolean b = siteRoverAssetRwDs.updateSiteRoverAsset(order);
                    log.info("更新订单: {}, {}", order, b);
                });
        }
        return Mono.just(RestUtils.success());
    }
}