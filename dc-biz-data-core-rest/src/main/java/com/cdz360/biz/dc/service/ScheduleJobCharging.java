package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcException;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.biz.dc.service.site.SiteBizService;
import com.cdz360.biz.model.cus.site.param.SiteBlacklistEnableParam;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.data.cache.RedisIotReadService;
import com.chargerlinkcar.framework.common.domain.param.AuthMediaParam;
import com.chargerlinkcar.framework.common.domain.request.StartChargerRequest;
import com.chargerlinkcar.framework.common.domain.request.StopChargerRequest;
import com.chargerlinkcar.framework.common.domain.vo.AuthMediaResult;
import com.chargerlinkcar.framework.common.domain.vo.CloudChargeTempVo;
import com.chargerlinkcar.framework.common.domain.vo.CloudChargeVo;
import com.chargerlinkcar.framework.common.domain.vo.CommCusRef;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUserVo;
import com.chargerlinkcar.framework.common.domain.vo.UserVo;
import com.chargerlinkcar.framework.common.exception.DcBalanceException;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.feign.SiteBlacklistUserFeignClient;
import com.chargerlinkcar.framework.common.feign.SiteDataCoreFeignClient;
import com.chargerlinkcar.framework.common.feign.UserFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.PlugNoUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ScheduleJobCharging extends PlatformStartCharging {

    @Value("${iot.fee.min:5}")
    private BigDecimal cloudChargeStartAmount;//默认启动金额作为平台单枪开启充电最小金额

    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;
    @Autowired
    private UserFeignClient userFeignClient;
    @Autowired
    private SiteBlacklistUserFeignClient siteBlacklistUserFeignClient;
    @Autowired
    private SiteDataCoreFeignClient siteDataCoreFeignClient;
    @Autowired
    private RedisIotReadService redisIotReadService;
    @Autowired
    private ChargerOrderBizService chargerOrderBizService;

    @Autowired
    private SiteBizService siteService;

    @PostConstruct
    public void init() {
        chargerOrderBizService.addStartChargingMap(OrderStartType.SCHEDULE_JOB, this);
    }

    @Override
    public CloudChargeVo checkChargingQueue(StartChargerRequest chargerRequest) {
        log.info("平台开启充电参数 chargerRequest：{}", JsonUtils.toJsonString(chargerRequest));
        IotAssert.isNotNull(chargerRequest.getStartType(), "订单启动方式不能为空");

        IotAssert.isTrue(chargerRequest != null && chargerRequest.getSiteId() != null,
            "请传入要开启充电的场站ID");

        CloudChargeVo cloudChargeVo = new CloudChargeVo();
        Map<String, String> failPlugNoMap = new HashMap<>();
        cloudChargeVo.setFailPlugNoList(failPlugNoMap);

        List<String> plugNoList = new ArrayList<>();
        if (chargerRequest.getPlugNo() != null) {
            plugNoList.add(chargerRequest.getPlugNo());
        }
        if (CollectionUtils.isNotEmpty(chargerRequest.getPlugNoList())) {
            plugNoList.addAll(chargerRequest.getPlugNoList());
        }
        log.info("plugNoList: {}", plugNoList);

        //STEP 0.校验是否允许后台充电，并校验账户正确性
//        ObjectResponse<SitePo> siteSimpleInfoVoObjectResponse
//                = siteDataCoreFeignClient.getSiteById(chargerRequest.getSiteId());
//        FeignResponseValidate.check(siteSimpleInfoVoObjectResponse);
//        SitePo site = siteSimpleInfoVoObjectResponse.getData();
        SitePo site = siteService.getSiteById(chargerRequest.getSiteId());
        BigDecimal siteFrozenAmount = site.getFrozenAmount();
        List<String> siteGids = null;
        Long orderCustomerId = null;
        Long orderCustomerCommId = null;
        String orderCustomerName = null;
        Long orderPayAccountId = null;
        String accountNo = null;
        String orderMobilePhone = null;
        if (site.getDefaultPayType().equals(PayAccountType.UNKNOWN.getCode())) {
            plugNoList.forEach(e -> {
                failPlugNoMap.put(e, "后台启动被禁用");
            });
//            cloudChargeVo.setFailPlugNoList(failPlugNoMap);
            return cloudChargeVo;
        } else {
            try {
                //重新获取相关信息
                CloudChargeVo tempCloudChargeVo = getCloudChargeInfo(
                    chargerRequest.getScheduleJobAccountId(),
                    chargerRequest.getSiteId(),
                    chargerRequest.getScheduleJobAccountType());
                orderCustomerId = tempCloudChargeVo.getOrderCustomerId();
                orderCustomerCommId = tempCloudChargeVo.getOrderCustomerCommId();
                orderCustomerName = tempCloudChargeVo.getOrderCustomerName();
                orderPayAccountId = tempCloudChargeVo.getOrderPayAccountId();
                siteGids = tempCloudChargeVo.getSiteGids();
                accountNo = tempCloudChargeVo.getAccountNo();
                orderMobilePhone = tempCloudChargeVo.getOrderMobilePhone();

                if (site.getParkTimeoutFee() != null && site.getParkTimeoutFee()) {
                    //校验启动账户是否因超时停充被系统拉黑
                    SiteBlacklistEnableParam param = new SiteBlacklistEnableParam();
                    param.setSiteId(chargerRequest.getSiteId()).setUid(orderCustomerId);
                    ObjectResponse<Boolean> response = siteBlacklistUserFeignClient.userInSiteBlacklist(
                        param);
                    FeignResponseValidate.check(response);
                    if (response.getData()) {
                        plugNoList.forEach(e -> {
                            failPlugNoMap.put(e, "结算账户不可用");
                        });
                        return cloudChargeVo;
                    }
                }
            } catch (DcException ex) {
                log.info("error: {}", ex.getMessage(), ex);
                plugNoList.forEach(e -> {
                    failPlugNoMap.put(e, "结算账户不可用");
                });
//                cloudChargeVo.setFailPlugNoList(failPlugNoMap);
                return cloudChargeVo;
            } catch (Exception ex) {
                log.info("error: {}", ex.getMessage(), ex);
                plugNoList.forEach(e -> {
                    failPlugNoMap.put(e, "其他");
                });
//                cloudChargeVo.setFailPlugNoList(failPlugNoMap);
                return cloudChargeVo;
            }

        }

        //STEP 1.开启充电前置检查
        List<PlugVo> plugVoList = redisIotReadService.getPlugList(plugNoList);

        Map<String, PlugVo> plugVoMap = plugVoList.stream()
            .collect(Collectors.toMap(PlugVo::getPlugNo, o -> o));
        Map<String, EvseVo> evseVoMap =
            redisIotReadService.getEvseList(
                    plugVoList.stream().map(PlugVo::getEvseNo).distinct().collect(Collectors.toList()))
                .stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(EvseVo::getEvseNo, o -> o));

        plugNoList.forEach(plug -> {
            PlugVo vo = plugVoMap.get(plug);
            EvseVo evseVo = evseVoMap.get(vo.getEvseNo());
            if (vo == null) {
                failPlugNoMap.put(plug, "无法找到枪头信息");
            } else if (vo.getStatus() == null) {
                failPlugNoMap.put(plug, "桩未上线");
            } else if (Boolean.FALSE == (vo.getStatus() == PlugStatus.CONNECT
                || vo.getStatus() == PlugStatus.RECHARGE_END)) {
                failPlugNoMap.put(plug,
                    String.format("当前枪头状态(%s)无法开启充电", vo.getStatus().name()));
            } else if (StringUtils.isNotBlank(vo.getOrderNo())
                && vo.getStatus() != PlugStatus.RECHARGE_END) {
                failPlugNoMap.put(plug, "枪头有进行中的订单");
            } else if (evseVo == null) {
                failPlugNoMap.put(plug, "无法找到桩信息");
            } else if (evseVo.getPriceCode() == null || evseVo.getPriceCode() <= 0) {
                failPlugNoMap.put(plug, "桩未下发计费模板");
            }
        });

//        TokenRequest tokenRequest = new TokenRequest();
//        tokenRequest.setToken(token);
//        JSONObject json = authCenterFeignClient.getCurrentUser(tokenRequest);
//        if (json == null || json.get("status") == null || !StringUtils.equals(json.get("status").toString(), "0")) {
//            throw new DcServiceException("当前商户信息获取失败");
//        }
//        SysUser sysUser = JSONObject.parseObject(JsonUtils.toJsonString(json.get("data")), SysUser.class);

        //STEP 2.根据有效的枪头数量计算最小开启金额，若账户余额不足则失败
        plugNoList.removeAll(failPlugNoMap.keySet());
        if (plugNoList.size() == 0) {
            return cloudChargeVo;
        }
        BigDecimal initAmount = null;
        if (plugNoList.size() == 1) {
            initAmount = cloudChargeStartAmount;
        } else {
            //initAmount =  Math.toIntExact(plugNoList.size() * DecimalUtils.yuan2fen(siteFrozenAmount));
            initAmount = siteFrozenAmount.multiply(BigDecimal.valueOf(plugNoList.size()));
        }
        AuthMediaResult authMediaResult = null;
        try {
            AuthMediaParam authParam = new AuthMediaParam();
            authParam.setTopCommId(orderCustomerCommId)
                .setSiteId(chargerRequest.getSiteId())
                .setSiteCommId(site.getOperateId())
                .setSiteGids(siteGids)
                .setEvseNo(null)
                .setCusId(orderCustomerId)
                .setBalanceId(orderPayAccountId)
                .setDefaultPayType(chargerRequest.getScheduleJobAccountType())
                .setPayType(chargerRequest.getPayType())
                .setRealTimeFlag(true)
                .setFrozenAmount(siteFrozenAmount)
                .setInitAmount(initAmount);
            ObjectResponse<AuthMediaResult> authMediaResultObjectResponse =
                userFeignClient.authByBalanceIdAndPayType(authParam);
            FeignResponseValidate.check(authMediaResultObjectResponse);
            authMediaResult = authMediaResultObjectResponse.getData();
        } catch (DcBalanceException ex) {
            log.info("msg: {}", ex.getMessage(), ex);
            plugNoList.forEach(e -> {
                failPlugNoMap.put(e, "账户余额不足");
            });
//            cloudChargeVo.setFailPlugNoList(failPlugNoMap);
            return cloudChargeVo;
        } catch (DcException ex) {
            log.info("msg: {}", ex.getMessage(), ex);
            plugNoList.forEach(e -> {
                failPlugNoMap.put(e, "结算账户不可用");
            });
//            cloudChargeVo.setFailPlugNoList(failPlugNoMap);
            return cloudChargeVo;
        } catch (Exception ex) {
            log.info("msg: {}", ex.getMessage(), ex);
            plugNoList.forEach(e -> {
                failPlugNoMap.put(e, "其它");
            });
//            cloudChargeVo.setFailPlugNoList(failPlugNoMap);
            return cloudChargeVo;
        }
        if (authMediaResult.getFrozenAmount() == null || authMediaResult.getBalance() == null) {
            plugNoList.forEach(e -> {
                failPlugNoMap.put(e, "账户余额不足");
            });
//            cloudChargeVo.setFailPlugNoList(failPlugNoMap);
            return cloudChargeVo;
        }

        //STEP 3.组装队列元素并返回
        log.info("plugNoList: {}", plugNoList);
        List<CloudChargeTempVo> tempVos = plugNoList.stream().map(e -> {
            CloudChargeTempVo vo = new CloudChargeTempVo();
            Pair<String, Integer> temp = PlugNoUtils.splitPlugNo(e);
            vo.setEvseNo(temp.getFirst())
                .setPlugId(temp.getSecond())
                .setPlugNo(e);
            return vo;
        }).collect(Collectors.toList());
        log.info("tempVos: {}", tempVos);

        cloudChargeVo.setSiteId(chargerRequest.getSiteId())
            .setSiteOperateId(site.getOperateId())
            .setSiteTopCommId(site.getTopCommId())
            .setBcCodeList(tempVos)
            .setOpId(chargerRequest.getSysUserId())
            .setOrderCustomerId(orderCustomerId)
            .setOrderCustomerName(orderCustomerName)
            .setOrderPayAccountId(orderPayAccountId)
            .setDefaultPayType(chargerRequest.getScheduleJobAccountType())
            .setPayType(PayAccountType.valueOf(chargerRequest.getScheduleJobAccountType()))
            .setFrozenAmount(authMediaResult.getFrozenAmount())
            .setAccountTotalAmount(authMediaResult.getBalance())
            .setAccountNo(accountNo)
            .setOrderMobilePhone(orderMobilePhone)
            .setSiteContactsPhone(site.getContactsPhone())
            .setStartType(OrderStartType.SCHEDULE_JOB)
//                .setDiscountRefId(authMediaResult.getDiscountRefId())
        ;

        // 停充SOC
        if (null != chargerRequest.getStopSoc()) {
            cloudChargeVo.setStopSoc(chargerRequest.getStopSoc());
        } else if (null != site.getStopSoc() && site.getStopSoc() > 0) {
            cloudChargeVo.setStopSoc(site.getStopSoc());
        }

        log.info("checkChargingQueue end");
        return cloudChargeVo;
    }

    private CloudChargeVo getCloudChargeInfo(Long chargePayAccountId,
        String siteId,
        Integer chargeDefaultPayType) {
        Long orderCustomerId = null;
        Long orderCustomerCommId = null;
        String orderCustomerName = null;
        Long orderPayAccountId = null;
        List<String> siteGids = null;
        String accountNo = null;
        String orderMobilePhone = null;
        switch (chargeDefaultPayType) {
            case 1:
                ObjectResponse<UserVo> userVoObjectResponse = userFeignClient.findInfoByUid(
                    chargePayAccountId, null, null);
                FeignResponseValidate.check(userVoObjectResponse);
                UserVo vo = userVoObjectResponse.getData();
                IotAssert.isTrue(NumberUtils.equals(vo.getStatus(), 10001),
                    "找不到有效的个人客户，请重新配置结算账户");
                IotAssert.isTrue(Boolean.FALSE == vo.getDebt(),
                    "您存在欠费订单，请将欠费的订单处理再充电");
                orderCustomerId = chargePayAccountId;
                orderCustomerCommId = vo.getCommId();
                orderCustomerName = vo.getUsername();
                orderPayAccountId = vo.getCommId();
                accountNo = vo.getPhone();
                orderMobilePhone = vo.getPhone();
                break;
            case 2:
                // TODO: 2020/3/16 校验 corpUserId
                ObjectResponse<RBlocUserVo> rBlocUserListResponse = userFeignClient.findRBlocUserVoById(
                    chargePayAccountId);
                FeignResponseValidate.check(rBlocUserListResponse);
                RBlocUserVo rBlocUserVo = rBlocUserListResponse.getData();
                IotAssert.isTrue(NumberUtils.equals(rBlocUserVo.getStatus(), 1),
                    "该企业客户已被禁用，请重新配置结算账户");
                IotAssert.isTrue(Boolean.FALSE == rBlocUserVo.getUserDebt(),
                    "您存在欠费订单，请将欠费的订单处理再充电");
                siteGids = super.checkCorpGidsAuthority(rBlocUserVo.getCorpUid(), siteId);
                orderCustomerId = rBlocUserVo.getUserId();
                orderCustomerCommId = rBlocUserVo.getCommId();
                orderCustomerName = rBlocUserVo.getUserName();
                orderPayAccountId = chargePayAccountId;
                accountNo = rBlocUserVo.getPhone();
                orderMobilePhone = rBlocUserVo.getPhone();
                break;
            case 3:
                // TODO: 2020/3/16 校验commId
                ObjectResponse<CommCusRef> commCusRefObjectResponse = userFeignClient.merFindById(
                    chargePayAccountId);
                FeignResponseValidate.check(commCusRefObjectResponse);
                CommCusRef ref = commCusRefObjectResponse.getData();
                IotAssert.isTrue(NumberUtils.equals(ref.getEnable(), 1),
                    "商户会员已停用，请重新配置结算账户");
                IotAssert.isTrue(Boolean.FALSE == ref.getUserDebt(),
                    "您存在欠费订单，请将欠费的订单处理再充电");
                orderCustomerId = ref.getUserId();
                orderCustomerCommId = ref.getUserCommId();
                orderCustomerName = ref.getUserName();
                orderPayAccountId = ref.getCommId();
                accountNo = ref.getUserPhone();
                orderMobilePhone = ref.getUserPhone();
                break;
            default:
                log.info("场站后台充电-扣款账户默认扣款类型异常, defaultPayType:{}",
                    chargeDefaultPayType);
                return null;
        }
        CloudChargeVo res = new CloudChargeVo();
        res.setOrderCustomerId(orderCustomerId)
            .setOrderCustomerCommId(orderCustomerCommId)
            .setOrderCustomerName(orderCustomerName)
            .setOrderPayAccountId(orderPayAccountId)
            .setSiteGids(siteGids)
            .setAccountNo(accountNo)
            .setOrderMobilePhone(orderMobilePhone);
        return res;
    }

    /**
     * 停止充电检查
     *
     * @param stopRequest
     * @return {@code <plugno, failReasonString>}
     */
    @Override
    public Optional<Map<String, String>> checkStopingQueue(List<StopChargerRequest> stopRequest) {
        Map<String, String> failPlugNoMap = new HashMap<>();

        //STEP 1.停止充电前置检查
        List<PlugVo> plugVoList = redisIotReadService.getPlugList(
            stopRequest.stream().map(StopChargerRequest::getPlugNo).collect(Collectors.toList()));
        Optional<PlugVo> voOptional = plugVoList.stream().filter(e -> e.getSiteId() != null)
            .findFirst();
        if (voOptional.isPresent()) {
            SitePo site = siteService.getSiteById(voOptional.get().getSiteId());
            if (NumberUtils.equals(site.getDefaultPayType(), PayAccountType.UNKNOWN.getCode())) {
                stopRequest.forEach(e -> {
                    failPlugNoMap.put(e.getPlugNo(), "后台启动被禁用");
                });
                stopRequest.clear();
                return Optional.of(failPlugNoMap);
            }
        } else {
            stopRequest.forEach(e -> {
                failPlugNoMap.put(e.getPlugNo(), "未绑定场站");
            });
            stopRequest.clear();
            return Optional.of(failPlugNoMap);
        }
        Map<String, PlugVo> plugVoMap = plugVoList.stream()
            .collect(Collectors.toMap(PlugVo::getPlugNo, o -> o));
        stopRequest.forEach(req -> {
            PlugVo vo = plugVoMap.get(req.getPlugNo());
            if (vo == null) {
                failPlugNoMap.put(req.getPlugNo(), "无法找到枪头信息");
            } else if (!PlugStatus.BUSY.equals(vo.getStatus())) {
                failPlugNoMap.put(req.getPlugNo(), "枪头状态非充电中");
            } else if (PlugStatus.BUSY.equals(vo.getStatus())) {

                req.setOrderNo(vo.getOrderNo()); // 填入订单号
            }
        });

        //STEP 2.删去无效的枪头
        for (int i = 0; i < stopRequest.size(); i++) {
            StopChargerRequest req = stopRequest.get(i);
            if (failPlugNoMap.get(req.getPlugNo()) != null) {
                stopRequest.remove(i);
            }
        }

        return Optional.of(failPlugNoMap);
    }
}
