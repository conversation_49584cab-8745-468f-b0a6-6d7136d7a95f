package com.cdz360.biz.dc.domain.alarm;

import com.cdz360.base.model.base.type.EvseStatus;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * MonitorEvseRequest
 *
 * @since 5/18/2020 2:13 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MonitorEvseRequest implements Serializable {

    private String evseNo;

    private EvseStatus status;

    private String ctrlNo;

    /**
     * 业务端的告警代码
     * 故障：1-255
     * 告警：2000+
     * 业务端故障：1000+
     */
    private Integer alarmCode;

    /**
     * 告警产生方式 0：桩端上报告警,1：桩端上报故障,2：告警逻辑生成告警
     */
    private Integer infoType;

    /**
     * 告警开始时间
     */
    private Date startTime;

    /**
     * 告警结束时间
     */
    private Date endTime;

    /**
     * 异常问题编码
     */
    private Integer errorCode;
    /**
     * 异常问题编码,告警码
     */
    private Integer alertCode;
    /**
     * 桩或枪错误描述
     */
    private String error;


    private String siteId;

    /**
     * 场站商户ID
     */
    private Long siteCommId;

    /**
     * 场站名称
     */
    private String siteName;

    private String linkId;
}