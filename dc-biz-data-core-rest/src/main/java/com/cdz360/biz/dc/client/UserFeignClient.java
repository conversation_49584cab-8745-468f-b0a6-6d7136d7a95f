package com.cdz360.biz.dc.client;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.corp.dto.CorpOrgSyncDto;
import com.cdz360.base.model.corp.dto.CorpSyncDto;
import com.cdz360.biz.model.cus.balance.po.BalanceApplicationPo;
import com.cdz360.biz.model.cus.corp.param.ListCorpParam;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.corp.vo.CorpVo;
import com.cdz360.biz.model.cus.score.dto.UserScoreSettingLevelSiteGidDto;
import com.cdz360.biz.model.cus.score.param.SearchScoreLogParam;
import com.cdz360.biz.model.cus.settlement.param.ListSettlementParam;
import com.cdz360.biz.model.cus.settlement.param.UpdateSettlementInvoicedAmountParam;
import com.cdz360.biz.model.cus.settlement.vo.SettlementBi;
import com.cdz360.biz.model.cus.site.param.UpdateSiteParkFeeUserParam;
import com.cdz360.biz.model.cus.site.po.SiteParkFeeUserPo;
import com.cdz360.biz.model.iot.vo.WhiteCardCfgVo;
import com.cdz360.biz.model.trading.coupon.vo.UserInfoVo;
import com.chargerlinkcar.framework.common.domain.Dict;
import com.chargerlinkcar.framework.common.domain.SitePersonaliseDTO;
import com.chargerlinkcar.framework.common.domain.UserCommRef;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceInfoVo;
import com.chargerlinkcar.framework.common.domain.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Created by zengkq on 2018/11/25.
 */
@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_USER, fallbackFactory = HystrixUserClientFactory.class)
public interface UserFeignClient {

    @GetMapping("/api/rblocUser/findRBlocUserById")
    ObjectResponse<com.chargerlinkcar.framework.common.domain.vo.RBlocUser> findRBlocUserById(@RequestParam(value = "rBlocUserId") Long rBlocUserId);

    @PostMapping(value = "/api/rblocUser/selectRBlocUserIds")
    ListResponse<RBlocUser> selectRBlocUserIds(@RequestBody List<Long> ids);

    @GetMapping("/api/corp/getCorp")
    ObjectResponse<CorpPo> getCorp(@RequestParam(value = "corpId") Long corpId);

    @PostMapping("/api/corp/getCorpList")
    ListResponse<CorpVo> getCorpList(@RequestBody ListCorpParam param);

    @PostMapping(value = "/api/user/addUserCommRef")
    BaseResponse addUserCommRef(@RequestBody UserCommRef userCommRef);

    @GetMapping(value = "/api/merchantBalance/findByCommIdAndPhone")
    ObjectResponse<CommCusRef> findByCommIdAndPhone(@RequestParam("commId") Long commId,
                                                    @RequestParam("phone") String phone);

    /**
     * 根据手机号和商户id查询客户信息
     *
     * @param phone
     * @param commId
     * @return
     */
    @RequestMapping(value = "/api/user/findByPhone", method = RequestMethod.POST)
    ObjectResponse<UserPropVO> findByPhone(@RequestParam(value = "phone") String phone,
                                           @RequestParam(value = "commId") Long commId);

    /**
     * 集团客户id查询集团客户详情
     *
     * @param rBlocUserId
     * @return
     */
    @GetMapping("/api/rblocUser/findRBlocUserVoById")
    ObjectResponse<RBlocUserVo> findRBlocUserVoById(@RequestParam("rBlocUserId") Long rBlocUserId);

    @GetMapping(value = "/api/merchantBalance/findById")
    ObjectResponse<CommCusRef> merFindById(@RequestParam("id") Long id);

    @PostMapping(value = "/api/merchantBalance/findByCondition")
    ListResponse<CommCusRef> findByCondition(@RequestBody CommCusRef ref);

    @PostMapping(value = "/api/corp/syncCorpInfo")
    BaseResponse syncCorpInfo(@RequestBody CorpSyncDto ref);
    @PostMapping(value = "/api/corp/syncCorpOrgInfo")
    BaseResponse syncCorpOrgInfo(@RequestBody CorpOrgSyncDto ref);

    /**
     * 字典分页查询
     * @param dict
     * @return
     */
    @RequestMapping(value = "/api/sysDict/queryPage",method = RequestMethod.POST)
    ListResponse<Dict> queryPage(@RequestBody(required = false) Dict dict);

    /**
     * 根据客户ID查询客户信息
     *
     * @return
     */
    @PostMapping(value = "/api/user/findInfoByUid")
    ObjectResponse<UserVo> findInfoByUid(
            @RequestParam(value = "userId") Long userId,
            @RequestParam(value = "topCommId", required = false) Long topCommId,
            @RequestParam(value = "commIdChain", required = false) String commIdChain);

    /**
     * 统计账单相关金额
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/invoice/settlementBi")
    ListResponse<SettlementBi> settlementBi(@RequestBody ListSettlementParam param);

    @PostMapping(value = "/api/corp/settlementBiForCorp")
    ObjectResponse<OrderBiVo> settlementBiForCorp(@RequestBody ListSettlementParam param);

    /**
     * 回滚账单相关更新数据
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/invoice/updateSettlementInvoicedAmount")
    BaseResponse updateSettlementInvoicedAmount(UpdateSettlementInvoicedAmountParam param);

    /**
     * 查询商户用户关联表
     *
     * @param userPhone   用户手机号
     * @param commId      商户id
     * @param enable      状态（1启用，0禁用）
     * @param commIdChain 当前商户及子商户id列表
     * @return
     */
    @RequestMapping(value = "/api/user/queryCommCusRefs", method = RequestMethod.GET)
    ListResponse<CommCusRef> queryCommCusRefs(@RequestParam("_index") Integer _index,
                                              @RequestParam("_size") Integer _size,
                                              @RequestParam(value = "userPhone") String userPhone,
                                              @RequestParam(value = "enable") Boolean enable,
                                              @RequestParam(value = "commId") Long commId,
                                              @RequestParam(value = "userId") Long userId,
                                              @RequestParam(value = "cusName") String cusName,
                                              @RequestParam(value = "commIdChain") String commIdChain);

    @GetMapping("/api/siteAuth/getWhiteCardCfgVo")
    ListResponse<WhiteCardCfgVo> getWhiteCardCfgVo(@RequestParam("siteId") String siteId);

    /**
     * 获取企业客户的开票设置
     *
     * @param corpId
     * @return
     */
    @GetMapping(value = "/api/invoice/getCorpInvoiceInfo")
    ObjectResponse<CorpInvoiceInfoVo> getCorpInvoiceInfo(@RequestParam(value = "corpId") Long corpId);

    /**
     * 获取场站超停收费用户列表
     *
     * @param siteId 场站ID
     * @return
     */
    @GetMapping(value = "/api/siteParkFeeUser/findBySiteId")
    ListResponse<SiteParkFeeUserPo> findSiteParkFeeUserBySiteId(@RequestParam(value = "siteId") String siteId);

    /**
     * 更新场站超停收费用户列表
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/siteParkFeeUser/updateSiteParkFeeUser")
    ObjectResponse<Integer> updateSiteParkFeeUser(@RequestBody UpdateSiteParkFeeUserParam param);

    @PostMapping(value = "/api/rblocUser/moveCorp")
    ObjectResponse<Integer> moveCorp(@RequestParam("corpId") Long corpId, @RequestParam("commId") Long commId);

    @PostMapping(value = "/api/vin/getByIdList")
    ListResponse<VinDto> getByIdList(@RequestBody List<Long> idList);

    @PostMapping(value = "/api/siteParkFeeUser/updateSiteParkFeeUserList")
    ObjectResponse<Integer> updateSiteParkFeeUserList(@RequestBody SitePersonaliseDTO param);

    @PostMapping(value = "/api/siteParkFeeUser/getSiteParkFeeUserList")
    ObjectResponse<SitePersonaliseDTO> getSiteParkFeeUserList(@RequestBody SitePersonaliseDTO param);

    @GetMapping(value = "/balanceApplication/getByOrderId")
     ObjectResponse<BalanceApplicationPo> getByOrderId(@RequestParam(value = "orderId") String orderId);

    @PostMapping(value = "/balanceApplication/getByOrderIds")
    ListResponse<BalanceApplicationPo> getByOrderIds(@RequestBody List<String> orderIds);

    /**
     * 获取场站组用户的积分信息
     * @param param
     * @return
     */
    @PostMapping(value = "/api/accountScore/getSiteGroupUserScoreSetting")
    ListResponse<UserScoreSettingLevelSiteGidDto> getSiteGroupUserScoreSetting(
        @RequestBody SearchScoreLogParam param);


    @GetMapping(value = "/api/user/findUserInfoById")
    ObjectResponse<UserInfoVo> findUserInfoById(@RequestParam("id") Long id);
}
