package com.cdz360.biz.dc.rest.site;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.dc.service.site.SiteWeatherService;
import com.cdz360.biz.model.trading.site.po.SiteWeatherPo;
import com.cdz360.biz.model.trading.site.vo.SiteWeatherVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

@Slf4j
@RestController
@Tag(name = "场站天气相关接口", description = "场站天气")
public class SiteWeatherRest {


    @Autowired
    private SiteWeatherService service;

    @Operation(summary = "刷新场站天气")
    @PostMapping("/dataCore/siteWeather/refresh")
    public Mono<BaseResponse> refreshSiteWeather(@RequestBody List<SiteWeatherPo> poList) {
        log.info("refreshSiteWeather. poList.size: {}", poList.size());
        return Mono.just(service.refreshSiteWeather(poList));
    }

    @Operation(summary = "获取场站天气")
    @GetMapping("/dataCore/siteWeather/findBySiteId")
    public Mono<ObjectResponse<SiteWeatherVo>> findBySiteId(@RequestParam(value = "siteId") String siteId) {
        log.info("findBySiteId. siteId: {}", siteId);
        return Mono.just(service.findBySiteId(siteId));
    }

}
