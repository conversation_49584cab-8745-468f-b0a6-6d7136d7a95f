package com.cdz360.biz.dc.repository;


import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.dc.domain.OrderInMongo;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargerOrderRwDs;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrder;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Date;

@Slf4j
@Repository
public class ChargerOrderRepository {


    @Autowired
    private MongoTemplate template;

    @Autowired
    private ChargerOrderRwDs chargerOrderRwDs;

    public OrderInMongo getByOrderNo(String orderNo) {

        // 查询条件
        Criteria criteria = Criteria.where("orderNo").is(orderNo);
        OrderInMongo order = template.findOne(new Query(criteria), OrderInMongo.class);
        log.debug("orderNo in mongo = {}", order == null ? null : order.getOrderNo());
        return order;
    }

    /**
     * 更新拔枪时间
     * @param orderNo
     * @param plugOutTime unix timestamp
     */
    public void update(String orderNo, Long plugOutTime) {
        log.info(">> 更新订单数据，更新拔枪时间: {}, {}", orderNo, plugOutTime);
        IotAssert.isNotBlank(orderNo, "不能处理订单号为空的订单");
        IotAssert.isNotNull(plugOutTime, "请传入拔枪时间");
        Update update = new Update();
        if(this.getByOrderNo(orderNo) == null) {
            log.info("mongodb中还不存在该订单信息，需要新增, orderNo={}", orderNo);

            // 查询数据库中的订单
            ChargerOrder sqlOrder = chargerOrderRwDs.findByOrderNo(orderNo);
            if (null == sqlOrder) return;
            if (null == sqlOrder.getCreateTime()) {
                sqlOrder.setCreateTime(new Date());
            }

            update.set("orderNo", orderNo)
                    .set("orderStatus", sqlOrder.getStatus())
                    .set("abnormal", sqlOrder.getAbnormal())
                    .set("evseId", sqlOrder.getBoxCode())
                    .set("plugNo", sqlOrder.getPlugNo())
                    .set("plugId", sqlOrder.getConnectorId())
                    .set("startType", OrderStartType.valueOf(sqlOrder.getOrderType()))
                    .set("payAccountId", sqlOrder.getPayAccountId())
                    .set("defaultPayType", sqlOrder.getDefaultPayType())
                    .set("frozenAmount", sqlOrder.getFrozenAmount())
                    .set("siteId", sqlOrder.getStationId())
                    .set("siteName", sqlOrder.getStationName())
                    .set("startTime", sqlOrder.getChargeStartTime())
                    .set("createTime", (long) Math.ceil((double) sqlOrder.getCreateTime().getTime() / 1000));
        }
        update.set("plugOutTime", plugOutTime);

        // 更新查询条件
        Criteria criteria = Criteria.where("orderNo").is(orderNo);
        log.info("从mongodb中更新订单数据的查询条件: {} , update ==> {}",
                JsonUtils.toJsonString(criteria), update);

        // 存在则更新，不存在则新增
        // log.info("更新订单数据, update={}", update);
        UpdateResult result = template.upsert(Query.query(criteria), update, OrderInMongo.class);
        log.info("<< 更新结果: result={}", result);
    }
}
