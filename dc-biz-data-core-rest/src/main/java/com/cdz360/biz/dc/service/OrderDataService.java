package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.model.base.type.ChargeOrderStatus;
import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.model.corp.dto.CorpUserSyncDto;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DateUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.domain.OrderInMongo;
import com.cdz360.biz.dc.repository.ChargerOrderRepository;
import com.cdz360.biz.dc.repository.FlushMongoOrderService;
import com.cdz360.biz.dc.service.invoice.InvoiceProcess;
import com.cdz360.biz.ds.trading.ro.corp.ds.UserSyncRoDs;
import com.cdz360.biz.ds.trading.ro.invoice.ds.CorpInvoiceRecordRoDs;
import com.cdz360.biz.ds.trading.ro.invoice.ds.InvoiceRecordOrderRefRoDs;
import com.cdz360.biz.ds.trading.ro.invoice.ds.InvoicedRecordBillRefRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderPayRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderRoDs;
import com.cdz360.biz.ds.trading.ro.partner.ds.SiteHlhtRoDs;
import com.cdz360.biz.ds.trading.ro.score.ds.ScoreDiscountRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.CommRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.OvertimeParkFeeOrderRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.TRCorpUserRoDs;
import com.cdz360.biz.ds.trading.rw.invoice.ds.InvoiceRecordOrderRefRwDs;
import com.cdz360.biz.ds.trading.rw.invoice.ds.InvoicedRecordBillRefRwDs;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargerOrderInterimRwDs;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargerOrderPayRwDs;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargerOrderRwDs;
import com.cdz360.biz.ds.trading.rw.order.ds.PayBillRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteDefaultSettingRwDs;
import com.cdz360.biz.model.bi.dashboard.SiteAndPlugBiVo;
import com.cdz360.biz.model.cus.site.po.SiteBlacklistPo;
import com.cdz360.biz.model.oa.vo.BillInvoiceVo;
import com.cdz360.biz.model.trading.cus.vo.UnliquidatedOrderVo;
import com.cdz360.biz.model.trading.invoice.po.InvoiceRecordOrderRefPo;
import com.cdz360.biz.model.trading.invoice.po.InvoicedRecordBillRefPo;
import com.cdz360.biz.model.trading.order.dto.CusCorpOrderBiDto;
import com.cdz360.biz.model.trading.order.dto.CusLastOrderSiteDto;
import com.cdz360.biz.model.trading.order.dto.CusOrderBiDto;
import com.cdz360.biz.model.trading.order.dto.SyncOrderDtoDetail;
import com.cdz360.biz.model.trading.order.param.BillInvoiceVoParam;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.order.param.ListCusOrderBiParam;
import com.cdz360.biz.model.trading.order.param.PrepaidOrderListParam;
import com.cdz360.biz.model.trading.order.param.PrepaidOrderListParam.PageType;
import com.cdz360.biz.model.trading.order.param.RewriteInterimParam;
import com.cdz360.biz.model.trading.order.param.SiteOrderBiParam;
import com.cdz360.biz.model.trading.order.param.UpdateOrderInvoicedAmountParam;
import com.cdz360.biz.model.trading.order.param.UpdateOrderInvoicedAmountParam.OpType;
import com.cdz360.biz.model.trading.order.po.ChargerOrderPo;
import com.cdz360.biz.model.trading.order.vo.EvseOrderBi;
import com.cdz360.biz.model.trading.order.vo.OrderBi;
import com.cdz360.biz.model.trading.order.vo.PlugOrderBi;
import com.cdz360.biz.model.trading.order.vo.PrepaidOperationVo;
import com.cdz360.biz.model.trading.order.vo.SiteOrderBi;
import com.cdz360.biz.model.trading.score.po.ScoreDiscountPo;
import com.cdz360.biz.model.trading.site.po.CommPo;
import com.cdz360.biz.model.trading.site.po.OvertimeParkFeeOrderPo;
import com.cdz360.biz.model.trading.site.po.SiteDefaultSettingPo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.chargerlinkcar.framework.common.constant.OrderStatus;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import com.chargerlinkcar.framework.common.domain.OrderOvertimeParkingBi;
import com.chargerlinkcar.framework.common.domain.vo.BatteryChargingVo;
import com.chargerlinkcar.framework.common.domain.vo.BatteryVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerDetail;
import com.chargerlinkcar.framework.common.domain.vo.ChargerDetailVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrder;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDataVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import com.chargerlinkcar.framework.common.domain.vo.LatestOrderInfoResult;
import com.chargerlinkcar.framework.common.domain.vo.VinDto2;
import com.chargerlinkcar.framework.common.feign.UserFeignClient;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.OptionalUtils;
import com.chargerlinkcar.framework.common.utils.ZipUtils;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 订单相关数据统计服务
 *
 * OrderDataServiceImpl
 * <AUTHOR>
 *
 * @since 2019.3.6
 * 喜笑悲哀都是假,贪求思慕总因痴
 */
@Slf4j
@Service
public class OrderDataService //implements IOrderDataService
{
    @Autowired
    private ChargerOrderRepository chargerOrderRepository;

    @Autowired
    private FlushMongoOrderService flushMongoOrderService;

    @Autowired
    private ChargerOrderRoDs chargerOrderRoDs;

    @Autowired
    private ChargerOrderInterimRwDs chargerOrderInterimRwDs;

    @Autowired
    private ChargerOrderPayRoDs chargerOrderPayRoDs;
    @Autowired
    private ChargerOrderPayRwDs chargerOrderPayRwDs;

    @Autowired
    private PayBillRwDs payBillRwDs;

    @Autowired
    private InvoiceProcess invoiceProcess;

    @Autowired
    private InvoiceRecordOrderRefRoDs invoiceRecordOrderRefRoDs;

    @Autowired
    private InvoicedRecordBillRefRoDs invoicedRecordBillRefRoDs;

    @Autowired
    private CorpInvoiceRecordRoDs corpInvoiceRecordRoDs;

    @Autowired
    private InvoiceRecordOrderRefRwDs invoiceRecordOrderRefRwDs;

    @Autowired
    private InvoicedRecordBillRefRwDs invoicedRecordBillRefRwDs;
    
    @Autowired
    private SiteHlhtRoDs siteHlhtRoDs;
    @Autowired
//    private TRCommercialRoDs trCommercialRoDs;
    private CommRoDs commRoDs;


    @Autowired
    private ChargerOrderRwDs chargerOrderDs;


    @Autowired
    private OrderUserCommRefQueue orderUserCommRefQueue;

    @Autowired
    private SiteDefaultSettingRwDs siteDefaultSettingRwDs;

    @Autowired
    private SiteRoDs siteRoDs;

//    @Autowired
//    private TradingFeignClient tradingFeignClient;

//    @Autowired
//    private ChargerQueryService chargerQueryService;

    @Autowired
    private OrderMongoService orderMongoService;

    @Autowired
    private OssArchiveBizService ossArchiveBizService;

    @Autowired
    private OvertimeParkFeeOrderRoDs overtimeParkFeeOrderRoDs;

    @Autowired
    private UserSyncRoDs userSyncRoDs;

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private TRCorpUserRoDs trCorpUserRoDs;

    @Autowired
    private ScoreDiscountRoDs scoreDiscountRoDs;

    /**
     * 通过充电订单同步用户的商户关系
     */
    public void syncUserCommRef() {
        // 查询前 24 小时的充电订单
        // t_charger_order 和 t_r_order_user_comm
        // t_r_order_user_comm 中不存在的订单
        // 调整的逻辑: /dc/order/start
        final LocalDateTime now = LocalDateTime.now();
        final LocalDateTime yesterday = now.minusDays(1);
        List<ChargerOrder> orderList = chargerOrderDs.minusOneDayChargerOrderList(yesterday, now);
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }

        // 对待处理的进行处理
        this.orderUserCommRefQueue.pushAll(orderList);

    }


    public List<CusOrderBiDto> getCusOrderBiList(ListCusOrderBiParam param) {
        return this.chargerOrderRoDs.getCusOrderBiList(param);
    }


    public List<OvertimeParkFeeOrderPo> getCusOvertimeParkOrderLastList(ListCusOrderBiParam param) {
        if (CollectionUtils.isEmpty(param.getOrderNoList())) {
            return List.of();
        }
        return overtimeParkFeeOrderRoDs.getCusOvertimeParkOrderLastList(param.getOrderNoList());
    }


    public List<CusLastOrderSiteDto> getCusOrderLastSiteInfoList(ListCusOrderBiParam param) {
        return this.chargerOrderRoDs.getCusOrderLastSiteInfoList(param);
    }

    /**
     * 统计最近10分钟的超停数据
     */
    public List<OrderOvertimeParkingBi> orderOvertimeParkingBi() {
        List<OrderOvertimeParkingBi> result = this.chargerOrderRoDs.orderOvertimeParkingBi();
        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }

        // 场站停充超时充电允许次数
        Map<String, Integer> num = new HashMap<>();
        result.forEach(i -> {
            if (num.get(i.getSiteId()) != null) {
                i.setOvertimeParkingNum(num.get(i.getSiteId()));
            } else {
                SiteDefaultSettingPo setting = siteDefaultSettingRwDs.getBySiteId(i.getSiteId());

                if (null != setting) {
                    i.setOvertimeParkingNum(setting.getOvertimeParkingNum());
                    num.put(i.getSiteId(), setting.getOvertimeParkingNum());
                }
            }
        });

        return result;
    }


    public List<SyncOrderDtoDetail> getCcSyncOrders(List<String> orderNoList) {
        List<SyncOrderDtoDetail> res = chargerOrderRoDs.getCcSyncOrders(orderNoList);
        return res;
    }

    public SiteAndPlugBiVo siteAndPlugStatusFillHlhtData(@Nullable Long commId, @Nullable String idChain) {
        if (commId != null) {
            CommPo commercialPo = commRoDs.getCommById(commId);
            idChain = commercialPo.getIdChain();
        }
        SiteAndPlugBiVo res = siteHlhtRoDs.siteAndPlugStatusFillHlhtData(idChain);
        return res;
    }

    public ObjectResponse<VinDto2> getVinDto2RankInfo(String vin, String commIdChain) {
        VinDto2 res = chargerOrderRoDs.getVinDto2ElecInfo(vin, commIdChain);
        res.setSiteElecRankList(chargerOrderRoDs.getVinDto2RankInfo(vin, commIdChain));
        return RestUtils.buildObjectResponse(res);
    }

    public ObjectResponse<BatteryVo> getBatteryVo(String vin, String commIdChain) {
        BatteryVo res = new BatteryVo();
        BatteryVo vo = chargerOrderRoDs.getBatteryVo(vin, commIdChain);
        if (vo != null) {
            res = vo;
        }

        List<BatteryChargingVo> list = chargerOrderRoDs.getBatteryChargingVo(vin, commIdChain);

        if (CollectionUtils.isNotEmpty(list)) {
            for (BatteryChargingVo tmp : list) {
                ListResponse<ChargerDetailVo> ret = this.getOrderSamplingInfo(tmp.getOrderNo());
                log.debug("ceshi,param={}", ret.getData());
                if (ret.getData() != null) {
                    List<ChargerDetailVo> batteryChargingVos = ret.getData();
                    //最高单体温度
                    Optional<BigDecimal> maxTmp = batteryChargingVos.stream().filter(e -> e.getMaxTemp() != null)
                            .map(ChargerDetailVo::getMaxTemp).map(BigDecimal::new).max(BigDecimal::compareTo);
                    //最低单体温度
                    Optional<BigDecimal> minTmp = batteryChargingVos.stream().filter(e -> e.getMinTemp() != null)
                            .map(ChargerDetailVo::getMinTemp).map(BigDecimal::new).min(BigDecimal::compareTo);
                    //最高单体电压
                    Optional<BigDecimal> maxVoltage = batteryChargingVos.stream().filter(e -> e.getMaxVoltage() != null)
                            .map(ChargerDetailVo::getMaxVoltage).max(BigDecimal::compareTo);

                    tmp.setMaxTemp(maxTmp.isPresent() ? maxTmp.get() : null);
                    tmp.setMinTemp(minTmp.isPresent() ? minTmp.get() : null);
                    tmp.setMaxVoltage(maxVoltage.isPresent() ? maxVoltage.get() : null);
                }
            }
        }

        res.setBatteryChargingVoList(list);
        return RestUtils.buildObjectResponse(res);
    }

    /**
     * 获取订单的（心跳）详情
     */
    public ListResponse<ChargerDetailVo> getOrderSamplingInfo(@RequestParam String orderNo) {
        // 订单编号
        if (StringUtils.isBlank(orderNo)) {
            log.info("<< 订单编号不能为空.");
            throw new DcArgumentException("订单编号不能为空");
        }
        // mongodb中数据
        OrderInMongo one = orderMongoService.findOne(orderNo);
        if (one == null) {
            one = this.getOssOrderDetail(orderNo);
        }
        if (null == one) {
            log.info("<< mongodb中不存在该订单信息, orderNo={}", orderNo);
            return new ListResponse<>();
        }

        // 充电细节信息
        List<ChargerDetail> details = one.getDetails();
        if (CollectionUtils.isNotEmpty(details)) {
            // 计算采样点
            // 1, 采样算法(方式):
            // a) 采样点必须包含第1个和最后1个数据样本
            // b) 每分钟抽取1个样本, 取每分钟的第一个样本
            // c) 如果当前分钟内没有样本, 则跳过取下一分钟的样本
            // 前端对这些点使用 [贝塞尔曲线] 渲染成曲线
            Long startTimeX = one.getStartTime();
            List<ChargerDetailVo> sampling = this.sampling(details).stream()
                    .map(vo -> vo.setStartTime(startTimeX))
                    .collect(Collectors.toList());
            log.info("<< sampling.size ={}", sampling.size());
            return new ListResponse<>(sampling, (long) sampling.size());
        } else {
            log.info("<< 该订单mongodb中订单数据没有采样信息");
            return new ListResponse<>();
        }
    }

    /**
     * 数据采样
     *
     * @param details
     * @return
     */
    private List<ChargerDetailVo> sampling(List<ChargerDetail> details) {
//        log.info(">> 进行数据采样: details={}", details);
        // 1, 采样算法(方式):
        // a) 采样点必须包含第1个和最后1个数据样本
        // b) 每分钟抽取1个样本, 取每分钟的第一个样本
        // c) 如果当前分钟内没有样本, 则跳过取下一分钟的样本

        List<ChargerDetailVo> result = new ArrayList<>();
        Integer lastDuration = null;
        for (int i = 0, len = details.size(); i < len; i++) {
            ChargerDetail detail = details.get(i);

            // 当前功率
            // 直流桩: 直流输出电压 * 直流输出电流
            // 交流桩: A相电流 * A相电压 * 1.73 【交流桩不显示】
            BigDecimal curPower = null;
            BigDecimal voltageO = null;
            BigDecimal currentO = null;
            if (detail.getDcVoltageO() != null && detail.getDcCurrentO() != null) {
                // 直流
//                curPower = detail.getDcVoltageO().multiply(detail.getDcCurrentO())
//                        .divide(BigDecimal.valueOf(1000), 4, RoundingMode.HALF_DOWN);

                voltageO = detail.getDcVoltageO();
                currentO = detail.getDcCurrentO();
            } else if (detail.getAcCurrentA() != null && detail.getAcVoltageA() != null) {

                // 产品: 交流桩取A相
                voltageO = detail.getAcVoltageA();
                currentO = detail.getAcCurrentA();
            }

            if (detail.getPower() != null) {
                curPower = detail.getPower();
            } else if (detail.getDcVoltageO() != null && detail.getDcCurrentO() != null) {
                // 仅直流
                curPower = detail.getDcVoltageO().multiply(detail.getDcCurrentO())
                        .divide(BigDecimal.valueOf(1000), 4, RoundingMode.HALF_DOWN);
            }

            // 充电时长
            Integer duration = detail.getDuration() == null ? 0 : detail.getDuration().intValue();

            // 充电时长单位: 分钟
            if (null == lastDuration || (duration - lastDuration > 1) || i == (len - 1)) {
                // 保留最后一个
                if (i == (len - 1) && (null != lastDuration && lastDuration.equals(duration))) {
                    result.remove(result.size() - 1);
                }

                // 缓存充电时长
                lastDuration = duration;

                // 采样
                ChargerDetailVo chargerDetailVo = new ChargerDetailVo()
                        .setDuration(detail.getDuration() == null ? null : detail.getDuration().longValue())
                        .setKwh(detail.getKwh())
                        .setElecFee(detail.getElecFee())
                        .setServFee(detail.getServFee())
                        .setCurPower(curPower)
                        .setCurSoc(detail.getSoc())
                        .setVoltageO(voltageO)
                        .setCurrentO(currentO);

                if (detail.getBms() != null) {
                    chargerDetailVo.setNeedCurrent(detail.getBms().getNeedCurrent())
                            .setNeedVoltage(detail.getBms().getNeedVoltage());

                }

                if (detail.getBattery() != null) {
                    chargerDetailVo.setTemp(detail.getBattery().getTemp())
                            .setMaxTemp(detail.getBattery().getMaxTemp())
                            .setMinTemp(detail.getBattery().getMinTemp())
                            .setMaxVoltage(detail.getBattery().getMaxVoltage())
                            .setMinVoltage(detail.getBattery().getMinVoltage());
                }

                result.add(chargerDetailVo);


            }
        }

        log.info("<< 采样结果: result.size = {}", result.size());
        return result;
    }

    public ListResponse<UnliquidatedOrderVo> getUnliquidatedNum(List<Long> userIdList, String commIdChain) {
        return RestUtils.buildListResponse(chargerOrderRoDs.getUnliquidatedNum(userIdList, commIdChain));
    }

    @Transactional(readOnly = true)
    public ObjectResponse<SiteOrderBi> getSiteOrderBi(SiteOrderBiParam param) {
        log.info("param:{}", JsonUtils.toJsonString(param));
        SiteOrderBi result = new SiteOrderBi();
        OrderBi siteOrderBi = chargerOrderRoDs.findOrderBiBySiteId(param);
        result.setDuration(siteOrderBi.getDuration())
                .setKwh(siteOrderBi.getKwh())
                .setOrderNum(siteOrderBi.getOrderNum())
                .setFee(siteOrderBi.getFee())
                .setElecFee(siteOrderBi.getElecFee())
                .setServFee(siteOrderBi.getServFee());
        List<EvseOrderBi> evseOrderBiList = chargerOrderRoDs.findEvseOrderBiBySiteId(param);

        List<PlugOrderBi> plugOrderBiList = chargerOrderRoDs.findPlugOrderBiBySiteId(param.getSiteId(),
                param.getStartTime(),
                param.getEndTime(),
                evseOrderBiList.stream().map(EvseOrderBi::getEvseNo).collect(Collectors.toList()));
        Map<String, List<PlugOrderBi>> evseNoPlugBiMap = plugOrderBiList.stream().collect(Collectors.groupingBy(PlugOrderBi::getEvseNo));
        evseOrderBiList.forEach(e -> {
            e.setPlugs(evseNoPlugBiMap.get(e.getEvseNo()));
        });
        result.setEvseList(evseOrderBiList);
        log.debug("result: {}", result);
        return RestUtils.buildObjectResponse(result);
    }


    /**
     * 订单详情归档
     */
    @Async
    public synchronized void archiveOrderDetail(Date dateIn, String tid) {
        log.info("订单详情归档 [{}] 开始. dateIn = {}", tid, dateIn);
        Date date = dateIn;
        if (date == null) {
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.YEAR, -1);
            date = cal.getTime();
        }
        ListChargeOrderParam listOrderParam = new ListChargeOrderParam();
        int size = 1000;
        TimeFilter stopTimeFilter = new TimeFilter();
        stopTimeFilter.setStartTime(DateUtils.fromYyyyMmDdHhMmSs(DateUtils.toStringFormat(date, "yyyy-MM-dd") + " 00:00:00"));
        Date end = DateUtils.addDays(date, 1);
        stopTimeFilter.setEndTime(DateUtils.fromYyyyMmDdHhMmSs(DateUtils.toStringFormat(end, "yyyy-MM-dd") + " 00:00:00"));
        listOrderParam.setStopTimeFilter(stopTimeFilter)
                .setTotal(false)
                .setStart(0L)
                .setSize(size);
        ListResponse<ChargerOrder> orderListRes;
        do {
            orderListRes = this.chargerOrderRoDs.listChargeOrder(listOrderParam);
            if (orderListRes == null ||
                    CollectionUtils.isEmpty(orderListRes.getData())) {
                break;
            }
            log.info("start = {}, size = {}", listOrderParam.getStart(), orderListRes.getData().size());
            listOrderParam.setStart(listOrderParam.getStart() + orderListRes.getData().size());

            orderListRes.getData().stream().forEach(o -> archiveOrderDetailSingle(o));
        }
        while (orderListRes.getData().size() >= size);
        log.info("订单详情归档 [{}] 结束", tid);
    }

    /**
     * 临时洗数据使用
     *
     * @param startIn
     * @param endIn
     * @param tid
     */
    public synchronized void archiveOrderDetailTemp(Date startIn, Date endIn, String tid) {
        log.info("订单详情归档 [{}] 开始. startIn = {}, endIn = {}", tid, startIn, endIn);
        Date date = (Date) startIn.clone();
        while (date.before(endIn)) {
            ListChargeOrderParam listOrderParam = new ListChargeOrderParam();
            int size = 1000;
            TimeFilter stopTimeFilter = new TimeFilter();
            stopTimeFilter.setStartTime(DateUtils.fromYyyyMmDdHhMmSs(DateUtils.toStringFormat(date, "yyyy-MM-dd") + " 00:00:00"));
            Date end = DateUtils.addDays(date, 1);
            stopTimeFilter.setEndTime(DateUtils.fromYyyyMmDdHhMmSs(DateUtils.toStringFormat(end, "yyyy-MM-dd") + " 00:00:00"));
            listOrderParam.setStopTimeFilter(stopTimeFilter)
                    .setTotal(false)
                    .setStart(0L)
                    .setSize(size);
            ListResponse<ChargerOrder> orderListRes;
            do {
                orderListRes = this.chargerOrderRoDs.listChargeOrder(listOrderParam);
                if (orderListRes == null ||
                        CollectionUtils.isEmpty(orderListRes.getData())) {
                    break;
                }
                log.info("start = {}, size = {}", listOrderParam.getStart(), orderListRes.getData().size());
                listOrderParam.setStart(listOrderParam.getStart() + orderListRes.getData().size());

                orderListRes.getData().stream().forEach(o -> archiveOrderDetailSingle(o));
            }
            while (orderListRes.getData().size() >= size);
            date = DateUtils.addDays(date, 1);
        }
        log.info("订单详情归档 [{}] 结束", tid);
    }

    /**
     * 归档单个订单
     */
    private void archiveOrderDetailSingle(ChargerOrder o) {
        OrderInMongo order = this.orderMongoService.findOne(o.getOrderNo());
//        log.info("order = {}", JsonUtils.toJsonString(order));
        if (order == null) {
            return;
        }
        byte[] buf = ZipUtils.zip(JsonUtils.toJsonString(order));
        boolean ret = this.ossArchiveBizService.uploadOrderDetail(o, buf);
        if (ret) {
            this.orderMongoService.deleteOrder(o.getOrderNo());
        }
    }

    public OrderInMongo getOssOrderDetail(String orderNo) {
        OrderInMongo result = null;
        try {
            ChargerOrderPo o = chargerOrderRoDs.getChargeOrderPo2(orderNo, false);
            if (o == null) {
                log.warn("订单不存在. orderNo = {}", orderNo);
                return null;
            }
            result = this.ossArchiveBizService.getOrderDetail(o.getStopTime(), o.getStationId(), orderNo);

        } catch (Exception e) {
            log.warn("获取oss订单详情失败 orderNo = {}, message = {}", orderNo, e.getMessage(), e);
        }
        if (result == null) {
            log.warn("获取oss订单详情失败 orderNo = {}", orderNo);
        } else {
            log.info("从oss获取到订单. orderNo = {}", result.getOrderNo());
        }
        return result;
    }

    public ObjectResponse<LatestOrderInfoResult> getLatestOrderInfo(Long accountId,
                                                                    PayAccountType accountType,
                                                                    String siteId) {
        IotAssert.isNotNull(accountId, "请传入用户id");
        IotAssert.isNotNull(accountType, "请传入支付类型");
        IotAssert.isNotBlank(siteId, "请传入用户场站id");
        LatestOrderInfoResult ret = new LatestOrderInfoResult();

        SitePo site = siteRoDs.getSite(siteId);
        IotAssert.isNotNull(site, "找不到场站信息");

        if (site.getParkTimeoutFee() == null || Boolean.FALSE.equals(site.getParkTimeoutFee())) {
            return RestUtils.buildObjectResponse(ret);
        }

        SiteDefaultSettingPo setting = siteDefaultSettingRwDs.getBySiteId(siteId);
        IotAssert.isNotNull(setting, "找不到场站配置信息");
        ret.setTotalOvertimeParkTime(setting.getOvertimeParkingNum());

        long corpId = 0;
        if (PayAccountType.CREDIT.equals(accountType)) {
            CorpUserSyncDto corpUserSyncDto = trCorpUserRoDs.selectById(accountId);
            IotAssert.isNotNull(corpUserSyncDto, "找不到对应授信帐户");
            corpId = corpUserSyncDto.getCorpId();
            accountId = 0L;
        }

        ObjectResponse<SiteBlacklistPo> blackRes = userFeignClient.getBlack(accountId, siteId, corpId, 0L);
        IotAssert.isTrue(blackRes != null && blackRes.getStatus() == ResultConstant.RES_SUCCESS_CODE,
                "停充信息查询失败，请重试");

        if (blackRes.getData() == null) {
            log.info("未产生过占位费的用户");
            ret.setFreeOvertimeParkTime(0);
        } else {
            ret.setFreeOvertimeParkTime(blackRes.getData().getOvertimeParkingNum());
            if (StringUtils.isNotBlank(blackRes.getData().getLatestOrderNo())) {
                OvertimeParkFeeOrderPo byOrderNo =
                        overtimeParkFeeOrderRoDs.getByOrderNo(blackRes.getData().getLatestOrderNo(), false);
                ret.setLatestOvertimeParkOrder(byOrderNo);
            }
        }

        return RestUtils.buildObjectResponse(ret);

//        CusRepVo userByUid = userSyncRoDs.getUserByUid(userId);
//        IotAssert.isNotNull(userByUid, "找不到用户信息");
//        if(StringUtils.isNotBlank(userByUid.getLatestOrderNo())) {
//            OvertimeParkFeeOrderPo byOrderNo =
//                    overtimeParkFeeOrderRoDs.getByOrderNo(userByUid.getLatestOrderNo(), false);
//            ret.setLatestOvertimeParkOrder(byOrderNo);
//            int userOvertimeParkCount = overtimeParkFeeOrderRoDs.getUserOvertimeParkCount(userId, siteId);
//            ret.setFreeOvertimeParkTime(userOvertimeParkCount);
//        }
//        return RestUtils.buildObjectResponse(ret);
    }

    public CusCorpOrderBiDto getCusAndCorpOrderBiList(ListCusOrderBiParam param) {
        CusCorpOrderBiDto ret = new CusCorpOrderBiDto();
        List<CusOrderBiDto> cusOrderBiList = CollectionUtils.isNotEmpty(param.getUidList()) ?
                this.chargerOrderRoDs.getCusNonCorpOrderBiList(param) :
                List.of();
        List<CusOrderBiDto> corpOrderBiList = CollectionUtils.isNotEmpty(param.getCorpIdList()) ?
                this.chargerOrderRoDs.getCorpOrderBiList(param) :
                List.of();
        return ret.setUserBi(cusOrderBiList)
                .setCorpBi(corpOrderBiList);
    }

    public void orderFlushSiteInfo(int size, int skip) {
        flushMongoOrderService.orderFlushSiteInfo(size, skip);
    }

    public void setRunFlag(boolean b) {
        flushMongoOrderService.setRunFlag(b);
    }

    public String getSkipInfo() {
        return flushMongoOrderService.getSkipInfo();
    }


    private void prepaidOrderListFilter(PrepaidOrderListParam param) {
        param.setStatus(OrderStatus.ORDER_STATUS_RECEIVE_MONEY);
        param.setOrderStatus(ChargeOrderStatus.STOP);
        param.setSiteInvoicedValid(true);
        param.setExcludeSettlementType(SettlementType.POSTPAID);
        if (PayAccountType.PERSONAL.equals(param.getAccountType())) {
            param.setAccountType(null); // 企业账户和商户会员之外的预付订单都归类到个人账户下开票，所以此时忽略扣款账户类型
        }
        if (PageType.ADDIBLE.equals(param.getPageType())
            && StringUtils.isNotBlank(param.getProcInstId())) {
            param.setUnionQuery(true);
        }
    }

    /**
     * 根据PageType获取预付订单
     */
    public ListResponse<ChargerOrderVo> getPrepaidOrderList(PrepaidOrderListParam param) {
        param.paramCheckAndFilter();
        this.prepaidOrderListFilter(param);

        List<ChargerOrderVo> res = new ArrayList<>();
//        if ((param.getStart() == null || NumberUtils.isZero(param.getStart()))
//            && Boolean.TRUE.equals(param.getUnionQuery())) {
//            List<ChargerOrderVo> refOrders = chargerOrderRoDs.queryPrePaidOrderListByRef(param);
//            OptionalUtils.ofEmptyListAble(refOrders)
//                .ifPresent(res::addAll);
//        }
        param.setUseInterimTable(true);
        List<ChargerOrderVo> list = chargerOrderRoDs.queryPrePaidOrderList(param);
        OptionalUtils.ofEmptyListAble(list)
            .ifPresent(res::addAll);
        return RestUtils.buildListResponse(res);
    }
    public ObjectResponse<Long> getPrepaidOrderListCount(PrepaidOrderListParam param) {
        param.paramCheckAndFilter();
        this.prepaidOrderListFilter(param);

        Long refOrderTotal = 0L;
//        if (Boolean.TRUE.equals(param.getUnionQuery())) {
//            refOrderTotal = chargerOrderRoDs.queryPrePaidOrderListCountByRef(param);
//        }
        param.setUseInterimTable(true);
        Long total = chargerOrderRoDs.queryPrePaidOrderListCount(param);
        return RestUtils.buildObjectResponse(NumberUtils.sum(refOrderTotal, total));
    }

    public String generateOrderInterimCode() {
        return DateUtils.toStringFormat(new Date(), "yyyyMMddHHmmss")
            .concat(RandomStringUtils.randomAlphanumeric(10));
    }

    @Transactional
    public ObjectResponse<String> prepaidOrderOperation(PrepaidOrderListParam param) {
        IotAssert.isNotNull(param.getAppend(), "入参缺失");
        if (Boolean.TRUE.equals(param.getAppend())) {
            IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getOrderNoList()),
                "缺少订单列表参数");
        } else {
            if (BooleanUtils.isTrue(param.getOperateAll())) {
                if (PayAccountType.CREDIT.equals(param.getAccountType())) {
                    // nothing to do
                } else {
                    IotAssert.isNotNull(param.getSize(), "缺少分页参数");
                }
            } else {
                IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getOrderNoList()),
                    "缺少订单列表参数");
            }
        }

        String code = StringUtils.isNotBlank(param.getInterimCode())
            ? param.getInterimCode()
            : this.generateOrderInterimCode();

        boolean end;
        int i;
        if (Boolean.TRUE.equals(param.getAppend())) {
            i = chargerOrderInterimRwDs.insertIgnore(code, param.getOrderNoList());
            end = true;
        } else {
            i = chargerOrderInterimRwDs.removeByCondition(code, param.getOrderNoList());
            end = this.delRefDataByOa(param.getProcInstId(), param.getOperateAll(),
                param.getSize(), param.getOrderNoList());
        }
        if (end) {
            return RestUtils.buildObjectResponse(code);
        } else {
            throw new DcServiceException("操作失败");
        }
    }

    /**
     * 根据procInstId删除关联的订单
     * @return 固定返回true
     */
    public boolean delRefDataByOa(@Nullable String procInstId, @Nullable Boolean operateAll,
        @Nullable Integer size,
        @Nullable List<String> orderNoList) {
        if (StringUtils.isBlank(procInstId)) {
            return true;
        }
        String applyNo = corpInvoiceRecordRoDs.getApplyNoByProcInstId(procInstId);
        if (StringUtils.isNotBlank(applyNo)) {
            // 企业账户
            invoiceRecordOrderRefRwDs.delete(InvoicingMode.POST_CHARGER, applyNo,
                Boolean.TRUE.equals(operateAll) ? null : orderNoList);
            return true;
        }

        // 个人账户或商户会员账户
        IotAssert.isTrue(CollectionUtils.isNotEmpty(orderNoList) || size != null,
            "此账户类型不支持一次性全删");
        List<String> needDeleteOrderNoList = orderNoList;
        if (size != null) {
            needDeleteOrderNoList = invoicedRecordBillRefRoDs.getOrderNoListByOa(
                procInstId, InvoicingMode.POST_CHARGER,
                0L, size);
        }
        if (CollectionUtils.isEmpty(needDeleteOrderNoList)) {
            return true;
        }
        List<InvoicedRecordBillRefPo> list = invoicedRecordBillRefRoDs.getListByOa(procInstId,
            InvoicingMode.POST_CHARGER, needDeleteOrderNoList);
        if (CollectionUtils.isNotEmpty(list)) {

            Map<String, List<InvoicedRecordBillRefPo>> collect = list.stream()
                .collect(Collectors.groupingBy(InvoicedRecordBillRefPo::getOrderNo));
            List<UpdateOrderInvoicedAmountParam> payBillUpdateList = new ArrayList<>();
            List<UpdateOrderInvoicedAmountParam> orderPayUpdateList = new ArrayList<>();
            collect.forEach((orderNo, refPos) -> {

                AtomicReference<BigDecimal> amountRef = new AtomicReference<>(BigDecimal.ZERO);
                refPos.forEach(e -> {
                    if (e.getCzInvoiceAmount() == null
                        || DecimalUtils.isZero(e.getCzInvoiceAmount())) {
                        return;
                    }

                    UpdateOrderInvoicedAmountParam payBillItem = new UpdateOrderInvoicedAmountParam();
                    payBillItem.setOpType(OpType.SUB)
                        .setAmount(e.getCzInvoiceAmount())
                        .setOrderNo(e.getCzOrderId());
                    payBillUpdateList.add(payBillItem);

                    amountRef.set(DecimalUtils.add(amountRef.get(), e.getCzInvoiceAmount()));
                });

                UpdateOrderInvoicedAmountParam orderPayItem = new UpdateOrderInvoicedAmountParam();
                orderPayItem.setOpType(UpdateOrderInvoicedAmountParam.OpType.SUB)
                    .setOrderNo(orderNo)
                    .setAmount(amountRef.get());
                orderPayUpdateList.add(orderPayItem);
            });

            if (CollectionUtils.isNotEmpty(payBillUpdateList)) {
                // t_pay_bill记录处理，分批操作
                Lists.partition(payBillUpdateList, 100)
                    .forEach(payBillRwDs::updateOrderInvoicedAmount);
            }

            invoiceProcess.orderInvoiceDataProcess(orderPayUpdateList);

            invoicedRecordBillRefRwDs.disableByProcInstId(InvoicingMode.POST_CHARGER,
                procInstId, needDeleteOrderNoList);
        }
        return true;
    }

    public BaseResponse rewriteInterimCodeByOa(RewriteInterimParam param) {
        IotAssert.isTrue(StringUtils.isNotBlank(param.getInterimCode())
            && StringUtils.isNotBlank(param.getProcInstId()), "入参错误");

        if (PayAccountType.CREDIT.equals(param.getAccountType())) {
            if (StringUtils.isBlank(param.getApplyNo())) {
                log.error("rewriteInterimCodeByOa. applyNo为空, param: {}", param);
                return RestUtils.success();
            }

            // 先判断企业关联开票订单是否处理结束
            Long orderDistinctCount = invoiceRecordOrderRefRoDs.countDistinctOrderNoByCondition(
                InvoicingMode.POST_CHARGER,
                param.getApplyNo(),
                null);
            Long interimCount = chargerOrderInterimRwDs.countByCondition(param.getInterimCode());
            if (orderDistinctCount.equals(interimCount)) {
                // 相等则认为关联开票订单已处理结束
                chargerOrderInterimRwDs.rewriteInterimCode(param.getInterimCode(),
                    param.getProcInstId());
            } else {
                // 等关联开票订单异步处理后，有代码逻辑去修改
            }
        } else {
            chargerOrderInterimRwDs.rewriteInterimCode(param.getInterimCode(),
                param.getProcInstId());
        }
        return RestUtils.success();
    }

    public BaseResponse orderInterimPeriodicCleaning() {
        chargerOrderInterimRwDs.periodicCleaning();
        return RestUtils.success();
    }

    /**
     * 获取全部能开票的预付订单号
     */
    public ObjectResponse<PrepaidOperationVo> getAllPrepaidOrderNos(PrepaidOrderListParam param) {
        param.setPageType(PageType.ADDIBLE);
        param.paramCheckAndFilter();
        this.prepaidOrderListFilter(param);

        List<String> data = new ArrayList<>();
//        if ((param.getStart() == null || NumberUtils.isZero(param.getStart()))
//            && Boolean.TRUE.equals(param.getUnionQuery())) {
//            List<String> refOrderNo = chargerOrderRoDs.getAllPrepaidOrderNosByRef(param);
//            OptionalUtils.ofEmptyListAble(refOrderNo)
//                .ifPresent(data::addAll);
//        }
        param.setUseInterimTable(true);
        List<String> allPrepaidOrderNos = chargerOrderRoDs.getAllPrepaidOrderNos(param);
        OptionalUtils.ofEmptyListAble(allPrepaidOrderNos)
            .ifPresent(data::addAll);

        PrepaidOperationVo res = new PrepaidOperationVo();
        res.setOrderNoList(data);
        OptionalUtils.ofEmptyListAble(data)
            .ifPresent(e -> {
                String code = StringUtils.isNotBlank(param.getInterimCode())
                    ? param.getInterimCode()
                    : this.generateOrderInterimCode();
                int i = chargerOrderInterimRwDs.insertIgnore(code, e);
                if (i > 0) {
                    res.setInterimCode(code);
                } else {
                    throw new DcServiceException("操作失败");
                }
            });
        return RestUtils.buildObjectResponse(res);
    }

    public ListResponse<BillInvoiceVo> getBillInvoiceVoList(BillInvoiceVoParam param) {
        IotAssert.isTrue(
            StringUtils.isNotBlank(param.getInterimCode()) || CollectionUtils.isNotEmpty(
                param.getOrderNoList()), "入参缺失");

        AtomicReference<List<BillInvoiceVo>> billInvoiceVoListRef = new AtomicReference<>(
            new ArrayList<>());

        if (StringUtils.isNotBlank(param.getInterimCode())) {
            billInvoiceVoListRef.set(
                chargerOrderPayRoDs.getBillInvoiceVoListByInterimCode(param.getInterimCode()));
        } else {
            billInvoiceVoListRef.set(
                chargerOrderPayRoDs.getBillInvoiceVoList(param.getOrderNoList()));
        }

        if (CollectionUtils.isNotEmpty(billInvoiceVoListRef.get())
            && StringUtils.isNotBlank(param.getProcInstId())) {
            List<String> ordersWhoseInvoiceAmountHasBeenFrozen = billInvoiceVoListRef.get().stream()
                .filter(
                    e -> e.getInvoiceAmount() == null || DecimalUtils.isZero(e.getInvoiceAmount()))
                .map(BillInvoiceVo::getOrderNo).filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

            OptionalUtils.ofEmptyListAble(ordersWhoseInvoiceAmountHasBeenFrozen)
                .map(e -> invoicedRecordBillRefRoDs.getBillInvoiceVoList(e, param.getProcInstId()))
                .filter(CollectionUtils::isNotEmpty)
                .map(e -> e.stream().collect(Collectors.toMap(BillInvoiceVo::getOrderNo,
                    BillInvoiceVo::getInvoiceAmount)))
                .map(map -> {
                    return billInvoiceVoListRef.get().stream().peek(e -> {
                        if (e.getInvoiceAmount() == null || DecimalUtils.isZero(
                            e.getInvoiceAmount())) {
                            e.setInvoiceAmount(map.getOrDefault(e.getOrderNo(), BigDecimal.ZERO));
                        }
                    }).collect(Collectors.toList());
                })
                .ifPresent(billInvoiceVoListRef::set);
        }

        return RestUtils.buildListResponse(billInvoiceVoListRef.get());
    }

    /**
     * 获取预付订单流程详情页的订单详细数据
     *
     * @param param accountType和orderNoList不能为空
     * @return
     */
    public ListResponse<ChargerOrderVo> queryPrePaidOrderDetailList(PrepaidOrderListParam param) {
        return RestUtils.buildListResponse(chargerOrderRoDs.queryPrePaidOrderDetailList(param));
    }

    public ObjectResponse<ChargerOrderDataVo> getPrepaidOrderDataVo(PrepaidOrderListParam param) {
        IotAssert.isTrue(param != null
            && (StringUtils.isNotBlank(param.getInterimCode()) || CollectionUtils.isNotEmpty(
            param.getOrderNoList()))
            && param.getAccountType() != null, "缺少必要参数");

        if (StringUtils.isNotBlank(param.getInterimCode())) {
            ChargerOrderDataVo res = chargerOrderRoDs.getSimpleChargerOrderDataByInterimCode(
                param.getInterimCode());
            return RestUtils.buildObjectResponse(res);
        }

        BigDecimal totalInvoiceAmountByOa = BigDecimal.ZERO;
        if (StringUtils.isNotBlank(param.getProcInstId())) {

            if (PayAccountType.CREDIT.equals(param.getAccountType())) {
                // 找出procInstId对应的orderNoList
                List<InvoiceRecordOrderRefPo> refList = invoiceRecordOrderRefRoDs.findListByOa(
                    param.getProcInstId());
                List<String> originalList = refList.stream()
                    .map(InvoiceRecordOrderRefPo::getOrderNo)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

                // 判断订单是否已被占用
                List<String> appendList = param.getOrderNoList().stream()
                    .filter(r -> !originalList.contains(r))
                    .collect(Collectors.toList());
                OptionalUtils.ofEmptyListAble(appendList).ifPresent(t -> {
                    Long count = invoiceRecordOrderRefRoDs.countByCondition(
                        InvoicingMode.POST_CHARGER, null, t);
                    IotAssert.isTrue(count == 0, "订单已添加，请刷新后重试");
                });
            } else {

                // 找出procInstId对应的orderNoList
                List<String> originalList = invoicedRecordBillRefRoDs.getOrderNoListByOa(
                    param.getProcInstId());

                // 判断订单是否已被占用
                List<String> appendList = param.getOrderNoList().stream()
                    .filter(r -> !originalList.contains(r))
                    .collect(Collectors.toList());
                OptionalUtils.ofEmptyListAble(appendList).ifPresent(t -> {
                    Long count = invoicedRecordBillRefRoDs.countByOrderNoList(t,
                        InvoicingMode.POST_CHARGER);
                    IotAssert.isTrue(count == 0, "订单已添加，请刷新后重试");
                });

                totalInvoiceAmountByOa = invoicedRecordBillRefRoDs.getTotalInvoiceAmountByOa(
                    param.getProcInstId(), InvoicingMode.POST_CHARGER,
                    param.getOrderNoList());
            }
        }

        ChargerOrderDataVo res = chargerOrderRoDs.getSimpleChargerOrderDataByOrderNos(
            param.getOrderNoList());
        if (res != null) {
            res.setInvoiceAmount(DecimalUtils.add(res.getInvoiceAmount(), totalInvoiceAmountByOa));
        }
        return RestUtils.buildObjectResponse(res);
    }

    /**
     * 检查所选预付订单是否可开票
     */
    public BaseResponse checkWhetherThePrepaidOrderCanBeInvoiced(
        PrepaidOrderListParam param) {
        param.setPageType(PageType.ADDIBLE);
        IotAssert.isTrue(
            StringUtils.isNotBlank(param.getInterimCode()) || CollectionUtils.isNotEmpty(
                param.getOrderNoList()), "缺少必要参数");

        if (StringUtils.isNotBlank(param.getInterimCode())) {
            param.paramCheckAndFilter();
            this.prepaidOrderListFilter(param);
            param.setUseInterimTable(false);
            Long valid = chargerOrderRoDs.countOrderByInterimCode(param);
            Long total = chargerOrderInterimRwDs.countByCondition(param.getInterimCode());
            IotAssert.isTrue(NumberUtils.equals(valid, total), "订单已添加，请刷新后重试");
        } else {
            ObjectResponse<Long> response = this.getPrepaidOrderListCount(param);
            Long aLong = response.getData();
            IotAssert.isTrue(aLong == param.getOrderNoList().size(), "订单已添加，请刷新后重试");
        }

        return RestUtils.success();
    }

    public ListResponse<String> queryOrderNoListBySiteIdAndDay(ListChargeOrderParam param) {
        return RestUtils.buildListResponse(chargerOrderRoDs.queryOrderNoListBySiteIdAndDay(param));
    }

    public ObjectResponse<ScoreDiscountPo> getScoreDiscount(String orderNo) {
        return RestUtils.buildObjectResponse(scoreDiscountRoDs.getByOrderNo(orderNo));
    }

    public ListResponse<String> queryOpenOrderNoListByOrderNoList(ListChargeOrderParam param) {
        return RestUtils.buildListResponse(chargerOrderRoDs.queryOpenOrderNoListByOrderNoList(param));
    }
}
