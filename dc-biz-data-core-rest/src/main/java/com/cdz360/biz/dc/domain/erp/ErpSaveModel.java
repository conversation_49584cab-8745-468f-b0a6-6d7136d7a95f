package com.cdz360.biz.dc.domain.erp;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "运营数据推送保存实体")
public class ErpSaveModel {

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "ERP(金蝶)系统中的ID保存的记录值" +
            "(保存时，传入ID会做覆盖操作，不传ID会新增记录)")
    private String FID;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "单据编号")
    private String FBillNo;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "收入日期")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date F_FDATE;
    private String F_FDATE;

//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    @Schema(description = "场站名称")
//    private String F_HTGD_ITEMNAME;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "场站信息")
    private ErpSiteInfo F_ITEMNUMBER;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "平台内场站ID")
    private String F_ITEMID;

//    @Schema(description = "场站地址")
//    private String F_Projectaddress;

//    @Schema(description = "场站总功率")
//    @NotNull
//    private BigDecimal F_HTGD_ITEMNAME2;

    @Schema(description = "平台总电量")
    @NotNull
    private BigDecimal F_Electricity;

    @Schema(description = "平台总收入")
    @NotNull
    private BigDecimal F_Grossincome;

    @Schema(description = "服务费收入")
    @NotNull
    private BigDecimal F_Servicerevenue;

    @Schema(description = "电费收入")
    @NotNull
    private BigDecimal F_Electricityincome;

    // ==以下字段暂时不使用
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "租金, 单位: 万元")
    private BigDecimal F_Rent;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "分成（按比例）")
    private BigDecimal F_F_Proportionshare;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "分成（按固定值）")
    private BigDecimal F_Fixedvalue;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "分成金额: ")
    private BigDecimal F_Bonus;
}
