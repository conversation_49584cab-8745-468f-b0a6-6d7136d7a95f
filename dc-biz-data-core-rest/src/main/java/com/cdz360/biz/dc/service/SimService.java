package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ds.trading.ro.iot.ds.BsBoxRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.model.common.constant.ExcelCheckResult;
import com.cdz360.biz.model.sim.vo.CheckResult;
import com.cdz360.biz.model.sim.vo.SimImportItem;
import com.cdz360.biz.model.sim.vo.SimImportParam;
import com.cdz360.biz.model.sim.vo.SimImportVo;
import com.cdz360.biz.model.trading.iot.po.BsBoxPo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.data.cache.RedisIotReadService;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SimService {

    @Autowired
    private BsBoxRoDs bsBoxRoDs;
    @Autowired
    private SiteRoDs siteRoDs;
    @Autowired
    private RedisIotReadService redisIotReadService;
    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;

    public ObjectResponse<SimImportVo> parseSimExcel(SimImportParam param) {
    List<List<String>> list = param.getList();
        log.info(">> 解析桩批量导入excel文件 ");
        try {
            List<SimImportItem> simList = new ArrayList<>(); //excel导入的集合
            List<SimImportItem> valid = new ArrayList<>(); // 有效
            List<SimImportItem> invalid = new ArrayList<>(); // 无效
            log.info("从 excel 中获取内容: {}", list);

            list.forEach(evse -> {
                SimImportItem vo = new SimImportItem();
                vo.setCode(null == evse.get(0) || evse.get(0) == "" ? "" : evse.get(0).trim()); // ICCID/MSISDN
                vo.setSiteName(null == evse.get(1) || evse.get(1) == "" ? "" : evse.get(1).trim());   // 站点名称
                vo.setEvseNo(null == evse.get(2) || evse.get(2) == "" ? "" : evse.get(2).trim());  // 桩编号
                vo.setRemark(null == evse.get(3) || evse.get(3) == "" ? "" : evse.get(3).trim());  // 备注


                ExcelCheckResult checkResult = checkSimFormat(vo);
                if (checkResult.getCode() != ExcelCheckResult.SIM_VALID.getCode()) {
                    vo.setDetail(checkResult.getDesc());
                    invalid.add(vo);
                } else {
                    valid.add(vo);
                }
                simList.add(vo);
            });
            HashSet<String> vin = new HashSet<>();
            HashSet<String> vinLst = new HashSet<>();
            SimImportVo importVo = checkSimInExcel(vin, valid, invalid);
            log.info("valid.size = {}, invalid.size = {}", importVo.getValid().size(), importVo.getInvalid().size());
            //相同数据第一次add的时候是能插入HashSet的，需将这部分数据也标记为重复数据
            importVo.getInvalid().forEach(evseVo -> {
                vinLst.add(evseVo.getCode());
            });
            importVo = checkSimInExcel(vinLst, importVo.getValid(), invalid);
            log.info("valid.size = {}, invalid.size = {}", importVo.getValid().size(), importVo.getInvalid().size());
            importVo = checkSimInDatebase(param.getCommIdChain(), importVo.getValid(), invalid);
            log.info("valid.size = {}, invalid.size = {}", importVo.getValid().size(), importVo.getInvalid().size());
            return RestUtils.buildObjectResponse(importVo);
        } catch (DcServiceException e) {
            throw new DcServiceException(e.getMessage());
        } catch (Exception e) {
            log.warn("<< 解析excel文件异常. message = {}", e.getMessage(), e);
            throw new DcServiceException("解析excel文件异常，请求确认文件内容.");
        }

    }

    private ExcelCheckResult checkSimFormat(SimImportItem sim) {

        // 有效性检测
        if (StringUtils.isEmpty(sim.getCode())) {
            return ExcelCheckResult.CODE_NOT_EXIST;
        }

        if (StringUtils.isNotBlank(sim.getEvseNo())) {
            if (StringUtils.isBlank(sim.getSiteName())) {
                return ExcelCheckResult.SITE_NAME_NOT_EXIST;
            }
        }

        // remark: 最大20位
        if (StringUtils.isNotEmpty(sim.getRemark())) {
            if (!(sim.getRemark().length() <= 20)) {
                return ExcelCheckResult.REMARK_ERROR;
            }
        }

        return ExcelCheckResult.SIM_VALID;
    }


    public SimImportVo checkSimInExcel(HashSet<String> simSet, List<SimImportItem> simList, List<SimImportItem> invalid) {
        SimImportVo importVo = new SimImportVo();
        List<SimImportItem> valid = new ArrayList<>(); // 新的有效数据集合

        simList.forEach(evse -> {
            ExcelCheckResult checkResult = null;
            if (simSet.add(evse.getCode())) {
                checkResult = ExcelCheckResult.SIM_VALID;
            } else {
                checkResult = ExcelCheckResult.CODE_NOT_EXIST;
            }
            evse.setDetail(checkResult.getDesc());
            if (checkResult.getCode() != ExcelCheckResult.SIM_VALID.getCode()) {
                invalid.add(evse);
            } else {
                valid.add(evse);
            }
        });
        importVo.setValid(valid);
        importVo.setInvalid(invalid);
        return importVo;
    }

    /**
     * 校验数据
     *
     * @param simList 需要判断的数据
     * @param invalid 无效的数据
     * @return
     */
    public SimImportVo checkSimInDatebase(String commIdChain, List<SimImportItem> simList, List<SimImportItem> invalid) {
        SimImportVo simListVo = new SimImportVo();
        List<SimImportItem> valid = new ArrayList<>(); // 新的有效数据集合

        if (CollectionUtils.isEmpty(simList)) {
            simListVo.setValid(valid);
            simListVo.setInvalid(invalid);
            return simListVo;
        }

        ListResponse<CheckResult> response = iotDeviceMgmFeignClient.checkInDB(simList.stream().map(SimImportItem::getCode).collect(Collectors.toList()));
        FeignResponseValidate.check(response);
        Map<String, CheckResult> checkMap = response.getData().stream().collect(Collectors.toMap(CheckResult::getCode, o -> o));

        List<SitePo> nameFilterList = siteRoDs.filterWithSimSiteName(commIdChain, simList.stream().map(SimImportItem::getSiteName).collect(Collectors.toList()));
        Map<String, String> siteNameMap = nameFilterList.stream().collect(Collectors.toMap(SitePo::getSiteName, SitePo::getId));

        List<BsBoxPo> evseList = bsBoxRoDs.findByCondition(null, simList.stream().map(SimImportItem::getEvseNo).collect(Collectors.toList()));
        Map<String, String> evseMap = evseList.stream().collect(Collectors.toMap(BsBoxPo::getEvseNo, BsBoxPo::getEvseName));


        simList.forEach(sim -> {
            ExcelCheckResult checkResult = ExcelCheckResult.SIM_VALID;
            CheckResult checkRes = checkMap.get(sim.getCode());
            String siteId = siteNameMap.get(sim.getSiteName());
            String evseName = evseMap.get(sim.getEvseNo());

            if (Boolean.FALSE.equals(checkRes.getIsSubsistent())) {
                checkResult = ExcelCheckResult.CODE_NOT_EXIST;
            } else if (Boolean.TRUE.equals(checkRes.getIsUnique())) {
                checkResult = ExcelCheckResult.CODE_NOT_EXIST;
            } else if (StringUtils.isBlank(siteId)) {
                checkResult = ExcelCheckResult.SITE_NAME_ERROR;
            } else if (StringUtils.isNotBlank(siteId)) {
                if (!redisIotReadService.getSiteEvses(siteId).contains(sim.getEvseNo())) {
                    checkResult = ExcelCheckResult.SITE_EVSE_MISMATCH;
                }
            } else if (StringUtils.isBlank(evseName)) {
                checkResult = ExcelCheckResult.EVSE_NOT_EXIST;
            }
            sim.setSiteId(siteId);
            sim.setDetail(checkResult.getDesc());
            if (checkResult.getCode() != ExcelCheckResult.SIM_VALID.getCode()) {
                invalid.add(sim);
            } else {
                valid.add(sim);
            }
        });
        simListVo.setValid(valid);
        simListVo.setInvalid(invalid);
        return simListVo;
    }
}
