package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.auth.user.po.SysUserPo;
import com.cdz360.biz.dc.client.InvoiceFeignClient;
import com.cdz360.biz.dc.client.reactor.OaFeignClient;
import com.cdz360.biz.dc.domain.InvoiceCallbackMsg;
import com.cdz360.biz.dc.service.invoice.InvoiceProcess;
import com.cdz360.biz.dc.service.invoice.UpdateCorpInvoiceRecordService;
import com.cdz360.biz.dc.service.invoice.callback.InvoiceCallbackService;
import com.cdz360.biz.dc.service.peek.invoice.PeekInvoiceService;
import com.cdz360.biz.ds.trading.ro.comm.ds.CommercialRoDs;
import com.cdz360.biz.ds.trading.ro.invoice.ds.CorpInvoiceRecordRoDs;
import com.cdz360.biz.ds.trading.ro.invoice.ds.InvoiceRecordOrderRefRoDs;
import com.cdz360.biz.ds.trading.ro.invoice.ds.InvoicedRecordBillRefRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderPayRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.PayBillRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.InvoiceRecordRoDs;
import com.cdz360.biz.ds.trading.ro.user.ds.TRUserRoDs;
import com.cdz360.biz.ds.trading.rw.invoice.ds.CorpInvoiceRecordRwDs;
import com.cdz360.biz.ds.trading.rw.invoice.ds.InvoiceRecordOrderRefRwDs;
import com.cdz360.biz.ds.trading.rw.invoice.ds.InvoicedRecordBillRefRwDs;
import com.cdz360.biz.ds.trading.rw.invoice.ds.InvoicedRecordContentRwDs;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargerOrderPayRwDs;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargerOrderRwDs;
import com.cdz360.biz.ds.trading.rw.order.ds.PayBillRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.InvoicedRecordRwDs;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.finance.type.InvoiceMode;
import com.cdz360.biz.model.finance.type.TaxStatus;
import com.cdz360.biz.model.finance.type.TaxType;
import com.cdz360.biz.model.invoice.param.InvoiceContentParam;
import com.cdz360.biz.model.invoice.param.InvoiceRecordParam;
import com.cdz360.biz.model.invoice.type.InvoiceCallbackStatus;
import com.cdz360.biz.model.invoice.type.InvoiceChannel;
import com.cdz360.biz.model.invoice.type.InvoiceType;
import com.cdz360.biz.model.invoice.type.InvoicedStatus;
import com.cdz360.biz.model.invoice.type.InvoicedType;
import com.cdz360.biz.model.invoice.type.ProductType;
import com.cdz360.biz.model.oa.constant.OaConstants;
import com.cdz360.biz.model.oa.param.PrepaidInvoicingEditParam;
import com.cdz360.biz.model.oa.vo.OaInvoicedVo;
import com.cdz360.biz.model.trading.invoice.dto.CorpInvoiceRecordDto;
import com.cdz360.biz.model.trading.invoice.dto.InvoiceRecordOrderRefDto;
import com.cdz360.biz.model.trading.invoice.dto.InvoicedRecordDto;
import com.cdz360.biz.model.trading.invoice.param.ListCorpInvoiceRecordParam;
import com.cdz360.biz.model.trading.invoice.param.ListInvoiceRecordOrderRefParam;
import com.cdz360.biz.model.trading.invoice.param.UserInvoiceRecordParam;
import com.cdz360.biz.model.trading.invoice.po.CorpInvoiceRecordPo;
import com.cdz360.biz.model.trading.invoice.po.InvoiceRecordOrderRefPo;
import com.cdz360.biz.model.trading.invoice.po.InvoicedRecordBillRefPo;
import com.cdz360.biz.model.trading.invoice.po.InvoicedRecordContentPo;
import com.cdz360.biz.model.trading.invoice.vo.CorpInvoiceInfoVo;
import com.cdz360.biz.model.trading.invoice.vo.CorpInvoiceRecordVo;
import com.cdz360.biz.model.trading.invoice.vo.CorpInvoicingContentVo;
import com.cdz360.biz.model.trading.order.dto.PayBillInvoiceBi;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.order.po.ChargerOrderPayPo;
import com.cdz360.biz.model.trading.order.po.PayBillPo;
import com.cdz360.biz.model.trading.order.vo.PayBillLinkChargeOrderVo;
import com.cdz360.biz.model.trading.order.vo.PayBillUsedDetail;
import com.cdz360.biz.model.trading.peek.invoice.dto.PeekInvoiceDto;
import com.cdz360.biz.model.trading.site.po.CommPo;
import com.cdz360.biz.model.trading.site.po.InvoicedRecordPo;
import com.chargerlinkcar.framework.common.domain.invoice.InvoicedRecordVo;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoiceApplyResultDTO;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoiceApplyResultOaDTO;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoiceCallBackDto;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoicedRecordDTO;
import com.chargerlinkcar.framework.common.domain.invoice.dto.UpdateIdDTO;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordAuditParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordManualParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordUpdateParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.SaveInvoiceRecordsParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.UserInvoiceRecordAuditParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceRecordDetail;
import com.chargerlinkcar.framework.common.domain.oa.NotifyResultParam;
import com.chargerlinkcar.framework.common.domain.peek.invoice.param.PeekInvoiceParam;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrder;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderSite;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderBiVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderTimeShareBiVo;
import com.chargerlinkcar.framework.common.feign.AuthCenterFeignClient;
import com.chargerlinkcar.framework.common.service.SettlementNoGenerator;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.OptionalUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.stream.Collectors;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class InvoiceService {

    private static final Executor executor = Executors.newFixedThreadPool(2, new ThreadFactory() {
        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r);
            t.setDaemon(Boolean.TRUE);
            return t;
        }
    });
    @Autowired
    private ChargerOrderRwDs chargerOrderRwDs;
    @Autowired
    private ChargerOrderPayRoDs chargerOrderPayRoDs;

    //    @Autowired
//    private PayBillBizService payBillBizService;
    @Autowired
    private ChargerOrderPayRwDs chargerOrderPayRwDs;
    @Autowired
    private PayBillRoDs payBillRoDs;

    //    @Autowired
//    private PayBillRoDs payBillRoDs;
    @Autowired
    private PayBillRwDs payBillRwDs;
    @Autowired
    private CorpInvoiceRecordRoDs corpInvoiceRecordRoDs;
    @Autowired
    private InvoicedRecordRwDs invoicedRecordRwDs;
    @Autowired
    private InvoicedRecordContentRwDs invoicedRecordContentRwDs;
    @Autowired
    private InvoiceRecordRoDs invoiceRecordRoDs;
    @Autowired
    private SettlementNoGenerator settlementNoGenerator;
    @Autowired
    private InvoicedRecordBillRefRwDs invoicedRecordBillRefRwDs;
    @Autowired
    private InvoicedRecordBillRefRoDs invoicedRecordBillRefRoDs;
    @Autowired
    private CorpInvoiceRecordRwDs corpInvoiceRecordRwDs;
    @Autowired
    private InvoiceRecordOrderRefRoDs invoiceRecordOrderRefRoDs;
    @Autowired
    private InvoiceRecordOrderRefRwDs invoiceRecordOrderRefRwDs;

    //    @Autowired
//    private UserFeignClient userFeignClient;
    @Autowired
    private PayBillBizService payBillBizService;
    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;
    @Autowired
    private UpdateCorpInvoiceRecordService updateCorpInvoiceRecordService;
    @Autowired
    private PeekInvoiceService peekInvoiceService;
    @Autowired
    private InvoiceFeignClient invoiceFeignClient;
    @Autowired
    private OaFeignClient oaFeignClient;
    @Autowired
    private PayBillBizService payBillService;
    @Autowired
    private InvoiceCallbackService invoiceCallbackService;
    @Autowired
    private InvoiceProcess invoiceProcess;
    @Autowired
    private CommercialRoDs commercialQueryDs;
    @Autowired
    private TRUserRoDs trUserRoDs;
    @Autowired
    private TRCommercialService trCommercialService;

    private static InvoicedRecordContentPo invoiceContentConvert(CorpInvoicingContentVo in) {
        InvoicedRecordContentPo out = new InvoicedRecordContentPo();
        BeanUtils.copyProperties(in, out);
//        if(StringUtils.isNotBlank(in.getProductType())) {
//            try {
//                out.setProductType(ProductType.valueOf(in.getProductType()));
//            } catch(Exception e) {
//                log.error("<< error={}, {}", e.getMessage(), e);
//            }
//        }
        return out;
    }

    private static InvoicedRecordContentPo invoiceContentConvert(InvoiceContentParam in) {
        InvoicedRecordContentPo out = new InvoicedRecordContentPo();
        BeanUtils.copyProperties(in, out);
        out.setEnable(true)
            .setCreateTime(new Date())
            .setUpdateTime(new Date());
        return out;
    }

    public void manualCorrectInvoiceData(PayAccountType accountType,
        Long recordId,
        String applyNo) {
        IotAssert.isNotNull(accountType, "入参缺失");
        if (PayAccountType.CREDIT.equals(accountType)) {
            IotAssert.isTrue(StringUtils.isNotBlank(applyNo), "入参缺失");
        } else {
            IotAssert.isNotNull(recordId, "入参缺失");
        }

        if (recordId != null) {
            long start = 0;
            final int size = 200;
            boolean loop;
            do {
                loop = this.correctByRecordIdAndRecordBillRef(accountType, recordId, start, size);
                start = start + size;
            } while (loop);
        } else if (StringUtils.isNotBlank(applyNo)) {
            long start = 0;
            final int size = 200;
            boolean loop;
            do {
                loop = this.correctByApplyNoAndRecordOrderRef(accountType, applyNo, start, size);
                start = start + size;
            } while (loop);
        }

    }

    /**
     * 修正recordId对应的充电单和充值单的开票金额（依赖t_invoiced_record_bill_ref表）
     *
     * @param recordId
     * @param start
     * @param size
     * @return Boolean 是否继续循环处理
     */
    public boolean correctByRecordIdAndRecordBillRef(final PayAccountType accountType,
        final long recordId,
        final long start, final int size) {
        if (!invoiceRecordRoDs.existById(recordId)) {
            log.error("无效recordId: {}", recordId);
            return false;
        }
        List<InvoicedRecordBillRefPo> refPoList = invoicedRecordBillRefRoDs.findByRecordId(
            recordId, start, size);
        if (CollectionUtils.isEmpty(refPoList)) {
            return false;
        }
        Optional<InvoicingMode> opt = refPoList.stream().map(InvoicedRecordBillRefPo::getType)
            .filter(Objects::nonNull).findFirst();
        if (opt.isEmpty()) {
            log.error("无有效type. recordId: {}, start: {}", recordId, start);
            return true;
        }

        List<String> orderNoList = refPoList.stream().map(InvoicedRecordBillRefPo::getOrderNo)
            .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());

        List<String> czOrderIdList = refPoList.stream().map(InvoicedRecordBillRefPo::getCzOrderId)
            .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(orderNoList)) {
            log.info("orderNoList.size: {}", orderNoList.size());
            // order_pay记录处理，分批操作
            Lists.partition(orderNoList, 100)
                .forEach(e -> chargerOrderPayRwDs.correctInvoicedAmount(e, recordId));
        }

        if (CollectionUtils.isNotEmpty(czOrderIdList)) {
            log.info("czOrderIdList.size: {}", czOrderIdList.size());
            if (InvoicingMode.PRE_PAY.equals(opt.get())) {
                // pay_bill记录处理，分批操作
                Lists.partition(czOrderIdList, 100)
                    .forEach(payBillRwDs::correctInvoicedAmount);

            } else if (InvoicingMode.POST_CHARGER.equals(opt.get())) {

                this.processingPayBillInvoicedAmount(czOrderIdList, accountType);

            } else {
                log.warn("暂不支持的type: {}", opt.get());
            }
        }

        return true;
    }

    /**
     * 修正applyNo对应的充电单和充值单的开票金额（依赖t_invoice_record_order_ref表）
     *
     * @param applyNo
     * @param start
     * @param size
     * @return Boolean 是否继续循环处理
     */
    public boolean correctByApplyNoAndRecordOrderRef(final PayAccountType accountType,
        final String applyNo,
        final long start, final int size) {
        CorpInvoiceRecordDto corpInvoiceRecord = corpInvoiceRecordRoDs.getRecordByApplyNo(applyNo,
            false);
        if (corpInvoiceRecord == null) {
            log.error("无效applyNo: {}", applyNo);
            return false;
        }
        List<InvoiceRecordOrderRefPo> refPoList = invoiceRecordOrderRefRoDs.findByApplyNo(
            applyNo, start, size);
        if (CollectionUtils.isEmpty(refPoList)) {
            return false;
        }
        Optional<InvoicingMode> opt = refPoList.stream().map(InvoiceRecordOrderRefPo::getInvoiceWay)
            .filter(Objects::nonNull).findFirst();
        if (opt.isEmpty()) {
            log.error("无有效invoiceWay. applyNo: {}, start: {}", applyNo, start);
            return true;
        }

        List<String> czOrderIdList = new ArrayList<>();
        List<String> orderNoList = refPoList.stream().map(InvoiceRecordOrderRefPo::getOrderNo)
            .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderNoList)) {
            log.error("无有效的单号. applyNo: {}, start: {}", applyNo, start);
            return true;
        }
        log.info("orderNoList.size: {}", orderNoList.size());

        if (InvoicingMode.PRE_PAY.equals(opt.get())) {
            // pay_bill记录处理，分批操作
            Lists.partition(orderNoList, 100)
                .forEach(payBillRwDs::correctInvoicedAmount);

        } else if (InvoicingMode.POST_CHARGER.equals(opt.get())) {
            Long recordId = invoiceRecordRoDs.queryIdByApplyNo(applyNo);
            // order_pay记录处理，分批操作
            Lists.partition(orderNoList, 100)
                .forEach(e -> chargerOrderPayRwDs.correctInvoicedAmount(e, recordId));

            czOrderIdList = payBillBizService.getCzOrderIdListByOrderNoList(
                PayAccountType.CREDIT,
                orderNoList,
                null,
                corpInvoiceRecord.getCorpId(),
                null
            );
        } else {
            log.warn("暂不支持的InvoicingMode: {}", opt.get());
        }

        this.processingPayBillInvoicedAmount(czOrderIdList, accountType);

        return true;
    }

    /**
     * 修正t_pay_bill的invoicedAmount
     *
     * @param czOrderIdList 充值单号
     * @param accountType
     */
    private void processingPayBillInvoicedAmount(@NonNull List<String> czOrderIdList,
        @NonNull PayAccountType accountType) {
        if (CollectionUtils.isEmpty(czOrderIdList)) {
            return;
        }
        czOrderIdList.stream().distinct().forEach(czOrderId -> {
            // 根据充值单号获取对应充电单实际消费金额
            Optional<Map<String, BigDecimal>> orderCostAmountMapOpt =
                payBillBizService.getOrderCostAmountMapByCzOrderId(czOrderId);

            orderCostAmountMapOpt.ifPresent(map -> {
                List<String> mapOrderNoList = map.keySet().stream()
                    .filter(StringUtils::isNotBlank).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(mapOrderNoList)) {
                    return;
                }

                // 若充电单存在开票关联关系，则累计金额
                List<String> alreadyInvoicedOrderNoList =
                    chargerOrderPayRoDs.getAlreadyInvoicedOrderNoList(mapOrderNoList);
                List<String> targetOrderNoList;
                if (PayAccountType.CREDIT.equals(accountType)) {
                    targetOrderNoList = invoiceRecordOrderRefRoDs.checkExist(
                        mapOrderNoList);
                } else {
                    targetOrderNoList = invoicedRecordBillRefRoDs.checkExist(
                        mapOrderNoList);
                }

                BigDecimal reduce = mapOrderNoList.stream().filter(
                        e -> alreadyInvoicedOrderNoList.contains(e)
                            || targetOrderNoList.contains(e))
                    .map(orderNo -> map.getOrDefault(orderNo, BigDecimal.ZERO))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                PayBillPo update = new PayBillPo();
                update.setOrderId(czOrderId)
                    .setInvoicedAmount(reduce);
                payBillRwDs.update(update);
            });

        });
    }

    public void manualNotifyEvent() {
        invoiceCallbackService.manualNotifyEvent();
    }

    public void manualCallback(InvoiceCallbackMsg msg) {
        invoiceCallbackService.addEventMsg(msg);
        invoiceCallbackService.notifyEvent();
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_UNCOMMITTED)
    public Integer invoiceCallback(InvoiceCallBackDto dto) {
        Long invoicedId = dto.getInvoicedId();
        InvoiceCallbackStatus status = dto.getStatus();
        log.info("发票回调: invoicedId={}, status={}", invoicedId, status);

        if (null == invoicedId) {
            log.warn("没有提供发票Id");
            return -1;
        }

        final InvoicedRecordDto invoicedRecordDto = invoiceRecordRoDs.getById(invoicedId);
        if (null == invoicedRecordDto) {
            log.warn("发票ID不存在");
            return -1;
        }

        // 处理长度超过255的失败原因
        if (dto.getFailMsg() != null && dto.getFailMsg().length() > 255){
            dto.setFailMsg(dto.getFailMsg().substring(0, 255));
        }

        // 审批流产生的开票申请
        final boolean isFlowable = StringUtils.isNotBlank(dto.getProcInstId());
        if (isFlowable
            && (InvoiceChannel.AUTO == invoicedRecordDto.getChannel()
            || InvoiceChannel.SEMI == invoicedRecordDto.getChannel())) {
//            if (StringUtils.isNotBlank(old.getProcInstId())
//                && InvoiceChannel.AUTO.equals(old.getChannel())) {
            NotifyResultParam req = new NotifyResultParam();
            req.setProcInstId(dto.getProcInstId())
                .setStatus(dto.getStatus())
                .setErrorMsg(dto.getFailMsg())
                .setInvoicePdfUrl(dto.getInvoicePdfUrl())
                .setAttachmentList(dto.getAttachmentList())
                .setProcInstNextStepEnabled(dto.getProcInstNextStepEnabled())
                .setProInstAdminContactEnabled(dto.getProInstAdminContactEnabled());
            log.info("通知biz-oa, 开票结果为 {}", JsonUtils.toJsonString(req));
            oaFeignClient.notifyResult(req)
                .subscribe();
        }

        if (isFlowable && OaConstants.PD_KEY_DEPOSIT_PROCESS.equals(getAOProcKey(invoicedId))) {

            // 开票记录是否需要更新
            boolean invoiceRecordNeedUpdate = false;

            if (status != null) {
                switch (status) {
                    case UNKNOWN:
                        log.error("回调状态无效");
                        return 0;
                    case RJ_SUCCEED:
                        if (!InvoicedStatus.COMPLETED.name()
                            .equals(invoicedRecordDto.getInvoicedStatus())) {
                            invoicedRecordDto.setInvoicedStatus(InvoicedStatus.COMPLETED.name());
                            invoiceRecordNeedUpdate = true;
                        }
                        // 成功，更新pay_bill的发票信息字段

                        final List<InvoicedRecordBillRefPo> payBillRecordList =
                            invoicedRecordBillRefRoDs.getPayBillByRecordId(
                                invoicedRecordDto.getId());
                        payBillRecordList.forEach(ref -> {
                            PayBillPo bill = new PayBillPo();
                            bill.setOrderId(ref.getCzOrderId())
                                .setTaxNo(dto.getInvoiceNumber())
                                .setTaxType(dto.getTaxType())
                                .setInvoiceMode(dto.getInvoiceMode());
                            payBillRwDs.update(bill);
                            bill = null;
                        });
                        break;
                    case RJ_RED:
                    case RJ_INVALID:
                    case RJ_FAILURE:
                        Map<InvoiceCallbackStatus, InvoicedStatus> statusMap =
                            Map.of(
                                InvoiceCallbackStatus.RJ_RED, InvoicedStatus.RED_DASHED,
                                InvoiceCallbackStatus.RJ_INVALID, InvoicedStatus.INVALID,
                                InvoiceCallbackStatus.RJ_FAILURE, InvoicedStatus.INVOICING_FAIL
                            );
                        invoicedRecordDto.setInvoicedStatus(statusMap.get(status).name());
                        invoicedRecordDto.setRejectReason(dto.getFailMsg());
                        invoiceRecordNeedUpdate = true;
                        break;
                }
            }

            // 通知oa，流转【自动开票】审批者的审批
//            if (InvoiceChannel.AUTO.equals(invoicedRecordDto.getChannel())) {
//                NotifyResultParam req = new NotifyResultParam();
//                req.setProcInstId(invoicedRecordDto.getProcInstId())
//                    .setStatus(status)
//                    .setErrorMsg(dto.getFailMsg())
//                    .setInvoicePdfUrl(dto.getInvoicePdfUrl())
//                    .setAttachmentList(dto.getAttachmentList());
//                oaFeignClient.notifyResult(req)
//                    .subscribe();
//            }

            if (invoiceRecordNeedUpdate) {
                log.info("数据更新到invoiced_record: {}",
                    invoicedRecordRwDs.updateInvoicedRecord(invoicedRecordDto));
            }

            return 1;
        }
        // 审批流产生的企客对账开票申请
        if (isFlowable && OaConstants.PD_KEY_BILLING.equals(getAOProcKey(invoicedId))) {
            if (InvoiceCallbackStatus.RJ_FAILURE.equals(status)) {
                // 开票失败，退回
                // 通知oa，流转【自动开票】退回者的审批
//                if (InvoiceChannel.AUTO.equals(invoicedRecordDto.getChannel())) {
//                    // 自动需要退回，手动不用管
//                    NotifyResultParam req = new NotifyResultParam();
//                    req.setProcInstId(invoicedRecordDto.getProcInstId())
//                        .setStatus(status)
//                        .setErrorMsg(dto.getFailMsg())
//                        .setInvoicePdfUrl(dto.getInvoicePdfUrl())
//                        .setAttachmentList(dto.getAttachmentList());
//                    oaFeignClient.notifyResult(req)
//                        .subscribe();
//                }
                return 1;
            }
        }

        List<String> orderNoList = null;
        if (StringUtils.isNotBlank(invoicedRecordDto.getProcInstId())) {
            if (StringUtils.isNotBlank(invoicedRecordDto.getApplyNo())) {
                orderNoList = invoiceRecordOrderRefRoDs.getOrderNoListByApplyNo(
                    InvoicingMode.POST_CHARGER,
                    invoicedRecordDto.getApplyNo());
            } else {
                orderNoList = invoicedRecordBillRefRoDs.getOrderNoListByRecordId(
                    invoicedId);
            }
        } else {
            // 通过发票Id查找订单信息
            List<ChargerOrder> orderList = chargerOrderRwDs.findByInvoiceId(invoicedId, false);
            orderNoList = CollectionUtils.isNotEmpty(orderList) ? orderList.stream().map(
                ChargerOrder::getOrderNo).distinct().collect(Collectors.toList()) : null;
        }
        if (StringUtils.isBlank(invoicedRecordDto.getProcInstId()) &&
            CollectionUtils.isEmpty(orderNoList)) {
            log.warn("发票Id没有对应的充电订单: invoicedId={}", invoicedId);
            return -1;
        }

        // 1. 回调成功情况
        if (InvoiceCallbackStatus.RJ_SUCCEED == status) {
            // 通知oa，流转【自动开票】审批者的审批
//            if (isFlowable && InvoiceChannel.AUTO.equals(invoicedRecordDto.getChannel())) {
//                NotifyResultParam req = new NotifyResultParam();
//                req.setProcInstId(invoicedRecordDto.getProcInstId())
//                    .setStatus(status)
//                    .setErrorMsg(dto.getFailMsg())
//                    .setInvoicePdfUrl(dto.getInvoicePdfUrl())
//                    .setAttachmentList(dto.getAttachmentList());
//                oaFeignClient.notifyResult(req)
//                    .subscribe();
//            }
        }
        // 2. 红冲操作或者作废
        // 9999 状态是开票失败返回码
        if (InvoiceCallbackStatus.RJ_RED.equals(status)
            || InvoiceCallbackStatus.RJ_INVALID.equals(status)
            || InvoiceCallbackStatus.RJ_FAILURE.equals(status)) {
//            // 找出对应的充值记录，看看需不需要调整充值记录的开票状态
//            orderList.forEach(order ->
//                    this.payBillBizService.rollbackPayBillTaxStatus(order.getOrderNo()));
//            List<String> orderNoList = orderList.stream().map(ChargerOrder::getOrderNo).collect(Collectors.toList());
//            this.chargerOrderInvoicedAmount(false, orderNoList, invoicedId);
//            this.emptyInvoicedIds(invoicedId);

            InvoiceCallbackMsg msg = new InvoiceCallbackMsg();
            msg.setAppend(false)
                .setOrderNoList(orderNoList)
                .setInvoiceId(invoicedId);
            invoiceCallbackService.addEventMsg(msg);
            invoiceCallbackService.notifyEvent();
        }

        return 1;
    }

    /**
     * 获取oa类型的简单实现
     *
     * @param invoicedId
     * @return
     */
    private String getAOProcKey(Long invoicedId) {

        final List<InvoicedRecordBillRefPo> refs =
            invoicedRecordBillRefRoDs.getByRecordId(invoicedId);

        if (CollectionUtils.isNotEmpty(refs)) {
            final InvoicedRecordBillRefPo invoicedRecordBillRefPo = refs.get(0);
            if (invoicedRecordBillRefPo.getType() == InvoicingMode.POST_CHARGER) {
                return OaConstants.PD_KEY_PREPAID_ORDER_INVOICING;
            } else if (invoicedRecordBillRefPo.getType() == InvoicingMode.PRE_PAY) {
                return OaConstants.PD_KEY_DEPOSIT_PROCESS;
            }
        }

        return null;
    }

    public ListResponse<CorpInvoiceRecordDto> findCorpInvoiceRecordList(
        ListCorpInvoiceRecordParam param) {
        // 分页信息
        Page<ChargerOrder> page = null;
        if (param.getTotal() != null && param.getTotal()) {
            if (null == param.getStart()) {
                param.setStart(0L);
            }

            if (null == param.getSize() || param.getSize() > 999) {
                param.setSize(999);
            }

            page = PageHelper.offsetPage(param.getStart().intValue(), param.getSize());
            log.info("分页: page = {}, size = {}", page.getPageNum(), page.getPageSize());
        }

        List<CorpInvoiceRecordDto> dtoList = corpInvoiceRecordRoDs.findRecordList(param);
        return RestUtils.buildListResponse(dtoList, page == null ? 0L : page.getTotal());
    }

    public CorpInvoiceRecordDto getRecordByProcInstId(String procInstId) {
        CorpInvoiceRecordDto dto = corpInvoiceRecordRoDs.getRecordByProcInstId(
            procInstId, false);
        if (dto != null) {
            Long count = invoiceRecordOrderRefRoDs.countByCondition(dto.getInvoiceWay(),
                dto.getApplyNo(), null);
            dto.setOrderCnt(count.intValue());
        }
        return dto;
    }

    public CorpInvoiceRecordVo corpInvoiceAppendOrder(CorpInvoiceRecordUpdateParam param) {
        if (null == param.getCorpInvoiceInfoVo()
            || param.getCorpInvoiceInfoVo().getInvoiceWay() == null) {
            throw new DcArgumentException("需要传入企业客户开票设置信息");
        }

        // 添加的订单是否有效
        if (StringUtils.isNotBlank(param.getInterimCode())) {
            InvoiceRecordOrderRefPo po = invoiceRecordOrderRefRoDs.getOneByInterimCode(
                param.getInterimCode(), true);
            if (null != po) {
                throw new DcArgumentException("订单已添加，请刷新后重试");
            }
        } else if (!Boolean.TRUE.equals(param.getOpAll())
            && CollectionUtils.isNotEmpty(param.getOrderNoList())) {
            ListInvoiceRecordOrderRefParam refParam = new ListInvoiceRecordOrderRefParam();
            refParam.setApplyNo(param.getApplyNo())
                .setOrderNoList(param.getOrderNoList());
            InvoiceRecordOrderRefPo po = invoiceRecordOrderRefRoDs.getOne(refParam);
            if (null != po) {
                throw new DcArgumentException("订单已添加，请刷新后重试");
            }
        }

        // 统计订单金额(充值/账单/充电订单)
        if (param.fromPrepaidOrderInvoicingProcess()) {
            String applyNo;
            if (StringUtils.isNotBlank(param.getApplyNo())) {
                applyNo = param.getApplyNo();
            } else {
                applyNo = settlementNoGenerator.next();
                CorpInvoiceRecordPo po = new CorpInvoiceRecordPo();
                po.setApplyNo(applyNo)
                    .setProcInstId(param.getProcInstId())
                    .setCorpId(param.getCorpId())
                    .setStatus(InvoicedStatus.NOT_SUBMITTED)
                    .setProductTempId(0L)
                    .setInvoiceWay(param.getCorpInvoiceInfoVo().getInvoiceWay())
                    .setCreateOpId(param.getOpId())
                    .setCreateOpType(param.getOpType())
                    .setCreateOpName(param.getOpName());
                corpInvoiceRecordRwDs.insertOrUpdate(po); // 先插入
            }
            param.setApplyNo(applyNo);
            updateCorpInvoiceRecordService.updateAsync(param, true); // 异步执行

            CorpInvoiceRecordVo res = new CorpInvoiceRecordVo();
            res.setApplyNo(param.getApplyNo());
            return res;
        } else {
            return updateCorpInvoiceRecordService.update(param, true);
        }
    }

    @Transactional
    public ObjectResponse<Long> prepaidInvoiceSubmit2Audit(PrepaidInvoicingEditParam param) {
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getInvoiceRecords()), "请传入发票信息");

//        InvoiceRecordParam invRecParamIn = param.getInvoiceRecords().get(0);  // 订单开票暂时只支持开1张
        for (var invRec : param.getInvoiceRecords()) {
            IotAssert.isTrue(CollectionUtils.isNotEmpty(invRec.getContents()),
                "请传入发票的商品行信息");
        }

        // STEP1.过滤出要追加或移除的订单
//        InvoicedRecordDTO originRecord = null;
        Optional<List<String>> appendOrderNoListOpt;
        Optional<List<String>> removeOrderNoListOpt;
        if (StringUtils.isNotBlank(param.getProcInstId())) {
            final List<String> refOrderNoList = invoicedRecordBillRefRoDs.getOrderNoListByOa(
                param.getProcInstId(), InvoicingMode.POST_CHARGER, null, null);
            if (CollectionUtils.isEmpty(refOrderNoList)) {
                appendOrderNoListOpt = OptionalUtils.ofEmptyListAble(param.getOrderNoList())
                    .map(e -> e.stream().distinct().collect(Collectors.toList()));
                removeOrderNoListOpt = Optional.empty();
            } else {

                appendOrderNoListOpt = OptionalUtils.ofEmptyListAble(refOrderNoList)
                    .map(origins -> param.getOrderNoList().stream()
                        .filter(e -> !origins.contains(e)).distinct()
                        .collect(Collectors.toList()))
                    .filter(CollectionUtils::isNotEmpty);
                removeOrderNoListOpt = OptionalUtils.ofEmptyListAble(refOrderNoList)
                    .map(t -> t.stream().filter(e -> !param.getOrderNoList().contains(e))
                        .distinct()
                        .collect(Collectors.toList()))
                    .filter(CollectionUtils::isNotEmpty);
            }
        } else {
            appendOrderNoListOpt = OptionalUtils.ofEmptyListAble(param.getOrderNoList())
                .map(e -> e.stream().distinct().collect(Collectors.toList()));
            removeOrderNoListOpt = Optional.empty();
        }

        appendOrderNoListOpt.ifPresent(e -> {
            // 判断要追加的订单是否有效
            Long count = invoicedRecordBillRefRoDs.countByOrderNoList(e,
                InvoicingMode.POST_CHARGER);
            IotAssert.isTrue(count == 0L, "订单已添加，请刷新后重试");

            count = chargerOrderPayRoDs.countByInvoiceAmountAndOrderNo(e);
            IotAssert.isTrue(count == e.size(), "订单已开票，请刷新后重试");
        });
        removeOrderNoListOpt.ifPresent(e -> {
            // 判断要移除的订单关联关系是否存在
            Long count = invoicedRecordBillRefRoDs.countByOrderNoList(e,
                InvoicingMode.POST_CHARGER);
            IotAssert.isTrue(count == e.size(), "订单关联关系异常，请刷新后重试");
        });

        if (param.getCommId() != null) {
            param.setTopCommId(commercialQueryDs.getTopCommId(param.getCommId()));
        } else if (param.getUserId() != null) {
            param.setTopCommId(trUserRoDs.getTopCommIdByUserId(param.getUserId()));
        }
        // STEP2.新增或编辑invoiced_record记录
        ObjectResponse<Long> response = invoiceFeignClient.disposeByPrepaidProcess(param);
        FeignResponseValidate.check(response);
        long invoiceId = response.getData();
        log.info("创建发票成功 invoiceId= {}", invoiceId);

//        int delCount = invoicedRecordContentRwDs.disableContent(invoiceId);
//        log.info("清除内容：{}", delCount);
//        this.addInvoiceContent2(invRecParamIn.getContents(), invoiceId);

        /**
         * STEP3.推入队列
         * a.开票相关金额处理（t_charger_order_pay表和t_pay_bill表）
         * b.关系表t_invoiced_record_bill_ref相关处理
         */
        appendOrderNoListOpt.ifPresent(e -> {
            log.info("appendOrderNoListOpt: {}", JsonUtils.toJsonString(e));
            InvoiceCallbackMsg msg = new InvoiceCallbackMsg();
            msg.setAppend(true)
                .setOrderNoList(e)
                .setInvoiceId(invoiceId)
                .setOaProcessDefinitionKey(OaConstants.PD_KEY_PREPAID_ORDER_INVOICING);
            invoiceCallbackService.addEventMsg(msg);
        });
        removeOrderNoListOpt.ifPresent(e -> {
            log.info("removeOrderNoListOpt: {}", JsonUtils.toJsonString(e));
            InvoiceCallbackMsg msg = new InvoiceCallbackMsg();
            msg.setAppend(false)
                .setOrderNoList(e)
                .setInvoiceId(invoiceId)
                .setOaProcessDefinitionKey(OaConstants.PD_KEY_PREPAID_ORDER_INVOICING);
            invoiceCallbackService.addEventMsg(msg);
        });
        invoiceCallbackService.notifyEvent();

        return RestUtils.buildObjectResponse(invoiceId);
    }

    @Transactional
    public BaseResponse abandonPrepaidInvoice(String procInstId) {
        IotAssert.isTrue(StringUtils.isNotBlank(procInstId), "缺少必要参数");

        // STEP1.获取要移除的订单
        ObjectResponse<InvoicedRecordDTO> response = invoiceFeignClient.getRecordByOa(procInstId);
        FeignResponseValidate.checkIgnoreData(response);
        if (response.getData() == null) {
            log.info("发票记录不存在");
            return RestUtils.success();
        }
        InvoicedRecordDTO originRecord = response.getData();

        List<InvoicedRecordBillRefPo> refList = invoicedRecordBillRefRoDs.getByRecordId(
            originRecord.getId(), InvoicingMode.POST_CHARGER);
        final List<String> refOrderNoList = refList.stream()
            .map(InvoicedRecordBillRefPo::getOrderNo)
            .filter(StringUtils::isNotBlank)
            .distinct()
            .collect(Collectors.toList());
        Optional<List<String>> removeOrderNoListOpt = OptionalUtils.ofEmptyListAble(refOrderNoList);

        // STEP2.编辑invoiced_record记录
        InvoicedRecordDTO update = new InvoicedRecordDTO();
        update.setId(originRecord.getId())
            .setInvoicedStatus(InvoicedStatus.DELETED);
        BaseResponse baseResponse = invoiceFeignClient.editRecordById(update);
        FeignResponseValidate.check(baseResponse);

        /**
         * STEP3.推入队列
         * a.开票相关金额处理（t_charger_order_pay表和t_pay_bill表）
         * b.关系表t_invoiced_record_bill_ref相关处理
         */
        removeOrderNoListOpt.ifPresent(e -> {
            InvoiceCallbackMsg msg = new InvoiceCallbackMsg();
            msg.setAppend(false)
                .setOrderNoList(e)
                .setInvoiceId(originRecord.getId())
                .setOaProcessDefinitionKey(OaConstants.PD_KEY_PREPAID_ORDER_INVOICING);
            invoiceCallbackService.addEventMsg(msg);
        });
        invoiceCallbackService.notifyEvent();

        return RestUtils.success();
    }

    /**
     * 预计算发票数值
     *
     * @param params
     * @return
     */
    public PeekInvoiceDto peekInvoice(PeekInvoiceParam params) {
        return peekInvoiceService.peekInvoice(params);
    }

    public CorpInvoiceRecordVo corpInvoiceRemoveOrder(CorpInvoiceRecordUpdateParam param) {
        if (null == param.getCorpInvoiceInfoVo()) {
            throw new DcArgumentException("需要传入企业客户开票设置信息");
        }

        // 移除的订单是否有效
        if (!Boolean.TRUE.equals(param.getOpAll())) {
            ListInvoiceRecordOrderRefParam refParam = new ListInvoiceRecordOrderRefParam();
            refParam.setApplyNo(param.getApplyNo())
                .setOrderNoList(param.getOrderNoList());
            List<InvoiceRecordOrderRefPo> list = invoiceRecordOrderRefRoDs.findList(refParam);
            if (list.size() != param.getOrderNoList().size()) {
                throw new DcArgumentException("开票记录关联的订单数不正确，请刷新后重试");
            }
        }

        // 统计订单金额(充值/账单/充电订单)
        return updateCorpInvoiceRecordService.update(param, false);
    }

    public CorpInvoiceRecordDetail corpInvoiceRecordDetail(String applyNo) {
        CorpInvoiceRecordDto dto = corpInvoiceRecordRoDs.getRecordByApplyNo(applyNo, false);
        if (null == dto) {
            throw new DcArgumentException("申请单号对应的申请记录不存在");
        }

        CorpInvoiceRecordDetail detail = new CorpInvoiceRecordDetail();
        BeanUtils.copyProperties(dto, detail);

//        List<InvoiceType> typeList = List.of(InvoiceType.PER_COMMON, InvoiceType.ENTER_COMMON);
        if (dto.getStatus() == InvoicedStatus.COMPLETED) {
            // -> invoice: 获取开票相关信息(pdf/发票号码/发票代码)
            ListResponse<InvoicedRecordVo> res = invoiceFeignClient.getInvoicedRecordByApplyNo(
                applyNo);
            FeignResponseValidate.check(res);
//            detail.setJpgUrl(res.getData().getJpgUrl())
//                .setPdfUrl(res.getData().getPdfUrl())
//                .setUrlList(res.getData().getPdfUrlList())
//                .setJpgUrlList(res.getData().getJpgUrlList())
//                .setInvoiceCode(res.getData().getInvoiceCode())
//                .setInvoiceNumber(res.getData().getInvoiceNumber());
        }

        return detail;
    }

    public Mono<Boolean> corpInvoiceRecordManual(CorpInvoiceRecordManualParam param) {
        CorpInvoiceRecordDto record = corpInvoiceRecordRoDs.getRecordByApplyNo(param.getApplyNo(),
            true);
        if (null == record) {
            throw new DcArgumentException("企业客户申请单号无效");
        }

        ObjectResponse<Boolean> response = invoiceFeignClient.invoiceRecordManual(param);
        FeignResponseValidate.check(response);

        // 更新失败的原因
        if (!param.getManualResult()) {
            // 开票失败原因
            Boolean b = corpInvoiceRecordRwDs.updateFailRemark(param.getApplyNo(),
                param.getRemark());
            if (!b) {
                throw new DcServiceException("手动处理开票失败");
            }
        }
        return Mono.just(response.getData());
    }

    public BaseResponse endCorpInvoiceRecordByOa(String procInstId) {
        CorpInvoiceRecordDto record = corpInvoiceRecordRoDs.getRecordByProcInstId(procInstId, true);
        if (null == record) {
            log.info("记录不存在");
            return RestUtils.success();
        }
        if (InvoicedStatus.REVIEWED.equals(record.getStatus())) {
            CorpInvoiceRecordPo update = new CorpInvoiceRecordPo();
            update.setApplyNo(record.getApplyNo())
                .setStatus(InvoicedStatus.COMPLETED);
            corpInvoiceRecordRwDs.updateByCondition(update);
        } else {
            log.info("不满足自动结束条件。applyNo: {}, invoicedStatus: {}", record.getApplyNo(),
                record.getStatus());
        }
        return RestUtils.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer corpInvoiceRecordAudit(CorpInvoiceRecordAuditParam param) {
        CorpInvoiceRecordDto record = corpInvoiceRecordRoDs.getRecordByApplyNo(param.getApplyNo(),
            true);
        if (null == record) {
            throw new DcArgumentException("企业客户申请单号无效");
        }
        return this.corpInvoiceRecordAuditProcess(param, record)
            .getLeft();
    }

//    @Transactional(rollbackFor = Exception.class)
//    public Mono<ListResponse<InvoicingContentVo>> setCorpInvoicingContent(Long invoiceId,
//        String procInstId) {
//        return oaFeignClient.getInvoicingContent(procInstId)
//            .doOnNext(FeignResponseValidate::check)
//            .map(e -> {
//                int delCount = invoicedRecordContentRwDs.disableContent(invoiceId);
//                log.info("清除内容：{}", delCount);
//                this.addInvoiceContent2(e.getData(), invoiceId);
//                return e;
//            });
//    }

    /**
     * 1、用于管理平台页面调用 2、用于企客对账开票流程调用
     */
    public Pair<Integer, Optional<List<InvoiceApplyResultDTO>>> corpInvoiceRecordAuditProcess(
        CorpInvoiceRecordAuditParam param, CorpInvoiceRecordDto record) {
        CorpInvoiceRecordPo po = new CorpInvoiceRecordPo();
        po.setApplyNo(param.getApplyNo())
            .setCorpId(record.getCorpId())
            .setProductTempId(record.getProductTempId())
            .setAuditRemark(param.getAuditRemark())
            .setAuditTime(new Date())
            .setAuditName(param.getOpName())
            .setUpdateOpId(param.getOpId())
            .setUpdateOpName(param.getOpName())
            .setUpdateOpType(param.getOpType())
            .setStatus(
                param.getAuditResult() ? InvoicedStatus.REVIEWED : InvoicedStatus.AUDIT_FAILED);
        int i = corpInvoiceRecordRwDs.insertOrUpdate(po);

        Optional<List<InvoiceApplyResultDTO>> resOptional = Optional.empty();
        List<InvoiceApplyResultDTO> results = new ArrayList<>();
        // 判断是否通过
        if (param.getAuditResult()) {
            // 企业客户所属商户
            ObjectResponse<CorpPo> corp = authCenterFeignClient.getCorp(record.getCorpId());
            FeignResponseValidate.check(corp);

            // 目前是使用顶级商户ID
            param.setCommId(corp.getData().getTopCommId());

            // -> invoice(开票记录),根据content的值是不是空的来决定要不要按照开票内容拆分为多张发票
            SaveInvoiceRecordsParam saveInvoiceRecordsParam = new SaveInvoiceRecordsParam();
            saveInvoiceRecordsParam.setInvoicedType(InvoicedType.ELECTRONIC) // 没找到原来这个字段从哪来，先默认为电子票
                .setInvoiceType(record.getInvoiceType())
                .setUserId(record.getUid())
                .setProcInstId(param.getProcInstId())
                .setApplyNo(param.getApplyNo())
                .setTempSalId(record.getTempSalId())
                .setProductTempId(record.getProductTempId())
                .setModelId(record.getModelId())
                .setInvoiceRecords(param.getInvoiceRecords());

//            switch (record.getInvoiceType()) {
//                case PER_COMMON:
//                case ENTER_COMMON:
//                    saveCorpInvoiceRecordParam.setInvoicedType(InvoicedType.ELECTRONIC);
//                    break;
//                case ENTER_PROFESSION:
//                    saveCorpInvoiceRecordParam.setInvoicedType(InvoicedType.PAPER);
//                    break;
//                default:
//                    log.error("开票种类不识别，数据异常: {}", record.getInvoiceType());
//                    throw new DcArgumentException("开票种类不识别，数据异常");
//            }

            if (CollectionUtils.isNotEmpty(param.getInvoiceRecords())) {
                for (InvoiceRecordParam contentsAndRemark : param.getInvoiceRecords()) {
                    CorpInvoiceRecordDto dto = new CorpInvoiceRecordDto();
                    BeanUtils.copyProperties(record, dto);
                    dto.setInvoicingRemark(contentsAndRemark.getRemark());
                    List<CorpInvoicingContentVo> corpInvoicingContentVoList = new ArrayList<>();
                    for (InvoiceContentParam contentParam : contentsAndRemark.getContents()) {
                        CorpInvoicingContentVo contentVo = new CorpInvoicingContentVo();
                        BeanUtils.copyProperties(contentParam, contentVo);
                        contentVo.setProductName(contentParam.getProductName());
                        contentVo.setCode(contentParam.getCode());
                        corpInvoicingContentVoList.add(contentVo);
                    }
                    dto.setInvoicingContent(corpInvoicingContentVoList);
//                    InvoiceRecordParam recordParam = this.toInvoiceRecordParam(dto,
//                        param);
//                    recordParam.setRemark(contentsAndRemark.getRemark());  // 票面备注
//                    saveCorpInvoiceRecordParam.getInvoiceRecords()
//                        .add(recordParam); // 将开票参数添加到列表

                }
            }
//            else {
////                saveCorpInvoiceRecordParam = this.toParam(saveCorpInvoiceRecordParam, record,
////                    param);
//                InvoiceRecordParam recordParam = this.toInvoiceRecordParam(record,
//                    param);
//                recordParam.setRemark(record.getInvoicingRemark());  // 票面备注
//                saveCorpInvoiceRecordParam.getInvoiceRecords().add(recordParam); // 将开票参数添加到列表
//            }

            // 先把这个procInstId里对应的之前的充值记录里的已开票金额释放掉
            List<InvoicedRecordPo> invoicedRecordPoList = invoicedRecordRwDs.getByProcInstId(param.getProcInstId(), false);
            for (InvoicedRecordPo invoicedRecordPo : invoicedRecordPoList) {
                // 企业用户开票，只支持红冲和开票失败状态来重新审核
                IotAssert.isTrue(
                    List.of(InvoicedStatus.RED_DASHED.name(),
                            InvoicedStatus.INVOICING_FAIL.name())
                        .contains(invoicedRecordPo.getInvoicedStatus()),
                    "开票申请当前状态不正确");
                // 回退充值单和充值单开票记录
                this.rollbackPayBillAndRecordRef(invoicedRecordPo.getId());
            }

            // 然后再去创建新的开票记录
            ObjectResponse<InvoiceApplyResultDTO> res = invoiceFeignClient.saveCorpInvoiceRecords(
                saveInvoiceRecordsParam);
            log.info("ObjectResponse<InvoiceApplyResultDTO>: {}",
                JsonUtils.toJsonString(res));
            FeignResponseValidate.checkIgnoreData(res);
            // 开票失败原因
            if (null == res.getData()) {
                corpInvoiceRecordRwDs.updateFailRemark(param.getApplyNo(), "系统错误");
            } else if (!res.getData().getSuccess()) {
                corpInvoiceRecordRwDs.updateFailRemark(param.getApplyNo(),
                    res.getData().getErrMsg());
            }
            results.add(res.getData());

            resOptional = Optional.of(results);

            // 处理新的充值信息
            if (res.getData() != null && Boolean.TRUE.equals(res.getData().getSuccess())
                && CollectionUtils.isNotEmpty(param.getPayBillChoiceList())) {
                final Map<String, PayBillInvoiceBi> payBillInvoiceBiMap = param.getPayBillChoiceList()
                    .stream()
                    .map(payBillChoice -> {
                        // 一个是trading里的vo，一个是oa里的vo，因此要转换一次
                        PayBillInvoiceBi payBillInvoiceBi = new PayBillInvoiceBi();
                        payBillInvoiceBi.setOrderId(payBillChoice.getOrderId())
                            .setAmount(payBillChoice.getAmount())
                            .setCanInvoiceAmount(payBillChoice.getCanInvoiceAmount())
                            .setPayTime(payBillChoice.getPayTime())
                            .setSourceType(payBillInvoiceBi.getSourceType())
                            .setOutAccountName(payBillChoice.getOutAccountName())
                            .setFreeAmount(payBillChoice.getFreeAmount())
                            .setProcInstId(payBillChoice.getProcInstId())
                            .setTotalAmount(payBillChoice.getTotalAmount());
                        return payBillInvoiceBi;
                    })
                    .collect(Collectors.toMap(PayBillInvoiceBi::getOrderId, o -> o));

                for (String orderId : payBillInvoiceBiMap.keySet()) {
                    // 处理充值单的信息
                    this.processPayBillInfo(orderId, payBillInvoiceBiMap,
                        res.getData().getRecordId());
                }
            }
        }

        return Pair.of(i, resOptional);
    }

    public Mono<ObjectResponse<InvoiceApplyResultOaDTO>> userInvoiceRecordAuditByOa(
        UserInvoiceRecordAuditParam param) {
        IotAssert.isNotNull(param, "请传入参");
        IotAssert.isNotBlank(param.getProcInstId(), "缺少流程实例ID");

        List<InvoicedRecordBillRefPo> billRefPos = invoicedRecordBillRefRoDs.findByOa(
            param.getProcInstId());
        IotAssert.isTrue(CollectionUtils.isNotEmpty(billRefPos), "开票记录和账单关联关系缺失");

        List<String> czOrderIds = billRefPos.stream().map(InvoicedRecordBillRefPo::getCzOrderId)
            .distinct().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<String> orderNos = billRefPos.stream().map(InvoicedRecordBillRefPo::getOrderNo)
            .distinct().filter(StringUtils::isNotBlank).collect(Collectors.toList());

        // 导入开票系统前，检查关联账单的开票数据正确性
        Long count = payBillRoDs.invoicingDataValidityCheck(czOrderIds);
        IotAssert.isTrue(count == czOrderIds.size(), "充值单开票数据异常");
        count = chargerOrderPayRoDs.invoicingDataValidityCheck(orderNos);
        IotAssert.isTrue(count == orderNos.size(), "充电单开票数据异常");

        return Mono.just(invoiceFeignClient.exportToInvoiceByOa(param.getProcInstId()));
    }

    @Transactional(rollbackFor = Exception.class)
    public Mono<ObjectResponse<InvoiceApplyResultOaDTO>> corpInvoiceRecordAuditByOa(
        CorpInvoiceRecordAuditParam param) {
        IotAssert.isTrue(StringUtils.isNotBlank(param.getProcInstId()), "缺少入参");
        CorpInvoiceRecordDto record = corpInvoiceRecordRoDs.getRecordByProcInstId(
            param.getProcInstId(), true);
        if (null == record) {
            throw new DcArgumentException("流程实例ID无效");
        }
        param.setApplyNo(record.getApplyNo());
        param.setAuditResult(Boolean.TRUE);
        if (param.getOpId() != null) {
            ObjectResponse<SysUserPo> response = authCenterFeignClient.getSysUserById(
                param.getOpId());
            FeignResponseValidate.check(response);
            param.setOpName(response.getData().getUsername());
        }
        Pair<Integer, Optional<List<InvoiceApplyResultDTO>>> pair = this.corpInvoiceRecordAuditProcess(
            param, record);
        log.info("Pair<Integer, Optional<InvoiceApplyResultDTO>>: {}, ee: {}",
            pair.getRight().isPresent(), JsonUtils.toJsonString(pair.getRight().get()));
        return Mono.just(pair)
            .filter(e -> e.getRight().isPresent())
            .map(e -> {
                InvoiceApplyResultOaDTO data = new InvoiceApplyResultOaDTO();
                data.setApplyNo(record.getApplyNo());
                data.setChannel(record.getChannel());
                e.getRight().ifPresent(t -> {
                    if (t.isEmpty() || t.get(0) == null) {
                        throw new DcServiceException("流程处理异常，请联系管理员");
                    }
                    data.setSuccess(t.get(0).getSuccess())
                        .setErrMsg(t.get(0).getErrMsg());
                });
                return data;
            })
            .map(RestUtils::buildObjectResponse)
            .switchIfEmpty(Mono.just("流程处理异常，请联系管理员").map(e -> {
                throw new DcServiceException(e);
            }));
    }

    /**
     * 构建
     */
//    private InvoiceRecordParam toInvoiceRecordParam(//SaveInvoiceRecordsParam saveInvoiceRecordsParam,
//        CorpInvoiceRecordDto dto,
//        CorpInvoiceRecordAuditParam param) {
//        // {"invoicedType":"ELECTRONIC","invoiceType":"PER_COMMON","userId":59328,"invoiceAmount":0.01,"servActualFee":0,"elecActualFee":0.01}
//        // "invoiceName":"test问", "email":"<EMAIL>"
//        // {"invoicedType":"ELECTRONIC","invoiceType":"ENTER_COMMON","userId":59328,"invoiceAmount":0.12,"servActualFee":0.01,"elecActualFee":0.11}
//        // "invoiceName":"企业普票","invoiceTin":"91310120312374490B","email":"<EMAIL>"
//        // {"invoicedType":"PAPER","invoiceType":"ENTER_PROFESSION","userId":59328,"invoiceAmount":0.06,"servActualFee":0.01,"elecActualFee":0.05}
//        // "invoiceAccount":"123", "invoiceName":"企业专票","invoiceTin":"91310120312374490B","invoiceBank":"中国银行","email":"<EMAIL>","receiverName":"鼎充","receiverMobilePhone":"123456","receiverProvince":"","receiverCity":"","receiverArea":"","receiverAddress":"上海棕榈广场"
//        InvoiceRecordParam saveInvoiceRecordParam = new InvoiceRecordParam();
//        saveInvoiceRecordParam
//            .setTempSalId(dto.getTempSalId())
//            .setProductTempId(dto.getProductTempId())
//            .setModelId(dto.getModelId());
////            .setProcInstId(dto.getProcInstId())
////            .setUserId(dto.getUid())
////            .setInvoiceType(dto.getInvoiceType())
////            .setInvoicingContent(dto.getInvoicingContent())
////            .setInvoicedStatus(InvoicedStatus.SUBMITTED);
////                .setCommercialId(param.getCommId());
//
//
//
////        BigDecimal parkFee = BigDecimal.ZERO;
//
//        // 拆分为多条后无法在此处校验
////        if (CollectionUtils.isNotEmpty(dto.getInvoicingContent())) {
////            // 此处直接filter存在转换问题所以用JSON先转换下（...LinkedHashMap cannot be cast to class ...CorpInvoicingContentVo）
////            List<CorpInvoicingContentVo> tempInvoicingContent = JSON.parseArray(
////                JSON.toJSONString(dto.getInvoicingContent()), CorpInvoicingContentVo.class);
////
////            BigDecimal elecFee = tempInvoicingContent.stream()
////                .filter(e -> ProductType.ELEC_ACTUAL_FEE.equals(e.getProductType()))
////                .findFirst()
////                .map(CorpInvoicingContentVo::getFixAmount)
////                .orElse(BigDecimal.ZERO);
////            BigDecimal servFee = tempInvoicingContent.stream()
////                .filter(e -> ProductType.SERV_ACTUAL_FEE.equals(e.getProductType()))
////                .findFirst()
////                .map(CorpInvoicingContentVo::getFixAmount)
////                .orElse(BigDecimal.ZERO);
////            parkFee = tempInvoicingContent.stream()
////                .filter(e -> ProductType.PARK_OUT_FEE.equals(e.getProductType()))
////                .findFirst()
////                .map(CorpInvoicingContentVo::getFixAmount)
////                .orElse(BigDecimal.ZERO);
////
////            boolean boo = DecimalUtils.eq(elecFee, dto.getFixElecFee())
////                || DecimalUtils.eq(servFee, dto.getFixServFee());
////            IotAssert.isTrue(boo, "开票金额异常");
////        }
//
////        saveInvoiceRecordParam//.setElecActualFee(dto.getFixElecFee())
//            //.setServActualFee(dto.getFixServFee())
////            .setParkActualFee(parkFee)
////            .setInvoiceAmount(
////                dto.getFixElecFee().add(dto.getFixServFee()).add(parkFee))
//            //.setCreatedDate(ZonedDateTime.now())
//            //.setCreatorName(param.getOpName())
////            .setSource("3"); // 临时使用来源(企业客户平台开票)
//
////        saveInvoiceRecordParam.setInvoiceDesc(dto.getApplyRemark());
////        saveInvoiceRecordParam.setCorpInvoicingContentVoList(dto.getInvoicingContent());
//
//        List<InvoiceRecordParam> saveRecordParamList = new ArrayList<>();
//
//        if (StringUtils.isNotBlank(saveInvoiceRecordsParam.getApplyNo())) {
//            saveRecordParamList = saveInvoiceRecordsParam.getInvoiceRecordList();
//        }
//        saveRecordParamList.add(saveInvoiceRecordParam);
////
////        // 计算record里的充电电费、服务费和占位费
////        saveRecordParamList.forEach(saveInvoiceRecordParamTemp -> {
////            BigDecimal elecFee = saveInvoiceRecordParamTemp.getCorpInvoicingContentVoList()
////                .stream()
////                .filter(e -> ProductType.ELEC_ACTUAL_FEE.equals(e.getProductType()))
////                .map(CorpInvoicingContentVo::getFixAmount)
////                .reduce(BigDecimal.ZERO, BigDecimal::add);
////            BigDecimal servFee = saveInvoiceRecordParamTemp.getCorpInvoicingContentVoList().stream()
////                .filter(e -> ProductType.SERV_ACTUAL_FEE.equals(e.getProductType()))
////                .map(CorpInvoicingContentVo::getFixAmount)
////                .reduce(BigDecimal.ZERO, BigDecimal::add);
////            BigDecimal parkFee = saveInvoiceRecordParamTemp.getCorpInvoicingContentVoList().stream()
////                .filter(e -> ProductType.PARK_OUT_FEE.equals(e.getProductType()))
////                .map(CorpInvoicingContentVo::getFixAmount)
////                .reduce(BigDecimal.ZERO, BigDecimal::add);
////            saveInvoiceRecordParamTemp.setElecActualFee(elecFee)
////                .setServActualFee(servFee)
////                .setParkActualFee(parkFee)
////                .setInvoiceAmount(elecFee.add(servFee).add(parkFee));
////        });
//
////        saveInvoiceRecordsParam//.setApplyNo(dto.getApplyNo())
////            .setInvoiceRecordList(saveRecordParamList);
//
////        return saveInvoiceRecordsParam;
//        return saveInvoiceRecordParam;
//    }
    @Transactional(rollbackFor = DcException.class)
    public Integer deleteCorpInvoiceRecordByApplyNo(String applyNo) {
        CorpInvoiceRecordDto dto = corpInvoiceRecordRoDs.getRecordByApplyNo(applyNo, true);
        if (null == dto) {
            throw new DcArgumentException("企业客户申请单号无效");
        }
        return this.deleteCorpInvoiceRecordProcess(dto, true);
    }

    /**
     * 1、用于管理平台页面调用 2、用于企客对账开票流程调用
     */
    public Integer deleteCorpInvoiceRecordProcess(CorpInvoiceRecordDto dto,
        boolean physicalDeletion) {
        // 找出所有的关联订单
        ListInvoiceRecordOrderRefParam refParam = new ListInvoiceRecordOrderRefParam();
        refParam.setApplyNo(dto.getApplyNo());
        List<InvoiceRecordOrderRefPo> refPoList = invoiceRecordOrderRefRoDs.findList(refParam);
        if (CollectionUtils.isNotEmpty(refPoList)) {
            CorpInvoiceRecordUpdateParam param = new CorpInvoiceRecordUpdateParam();
            param.setDeleteRecord(Boolean.TRUE);
            param.setOpAll(true);
            param.setOrderNoList(refPoList.stream()
                .map(InvoiceRecordOrderRefPo::getOrderNo)
                .collect(Collectors.toList()));
            CorpInvoiceRecordVo vo = updateCorpInvoiceRecordService.deleteRecord(param, dto);
        }

        if (physicalDeletion) {
            return corpInvoiceRecordRwDs.deleteByApplyNo(dto.getApplyNo());
        } else {
            CorpInvoiceRecordPo update = new CorpInvoiceRecordPo();
            update.setApplyNo(dto.getApplyNo());
            update.setStatus(InvoicedStatus.DELETED);
            return corpInvoiceRecordRwDs.updateByCondition(update);
        }
    }

    @Transactional(rollbackFor = DcException.class)
    public Integer deleteCorpInvoiceRecordByOa(String procInstId, Boolean physicalDeletion) {
        IotAssert.isNotNull(physicalDeletion, "缺少入参");
        CorpInvoiceRecordDto dto = corpInvoiceRecordRoDs.getRecordByProcInstId(procInstId, true);
        if (null == dto) {
            log.info("此OA流程无企业开票记录");
            return 0;
        }
        return this.deleteCorpInvoiceRecordProcess(dto, physicalDeletion);
    }

    public Integer updateCorpInvoiceRecordReturnFlag(CorpInvoiceRecordPo po) {
        CorpInvoiceRecordDto findDto = corpInvoiceRecordRoDs.getRecordByApplyNo(po.getApplyNo(),
            true);
        if (null == findDto) {
            throw new DcArgumentException("企业客户申请单号无效");
        }
        po.setReturnFlag(1);
        return corpInvoiceRecordRwDs.updateCorpInvoiceRecordReturnFlag(po);
    }

    public ListResponse<InvoiceRecordOrderRefDto> getInvoiceRecordOrderList(
        ListInvoiceRecordOrderRefParam param) {
        // 分页信息
        Page<ChargerOrder> page = null;
        if (param.getTotal() != null && param.getTotal()) {
            page = PageHelper.offsetPage(0, 10);
            if (null != param.getStart()
                && null != param.getSize() && param.getSize() > 0) {
                page = PageHelper.offsetPage(param.getStart().intValue(), param.getSize());
            }
            log.info("分页: page = {}, size = {}", page.getPageNum(), page.getPageSize());
        }

        List<InvoiceRecordOrderRefPo> list = invoiceRecordOrderRefRoDs.findList(param);
        if (CollectionUtils.isEmpty(list)) {
            return RestUtils.buildListResponse(new ArrayList<>(), 0);
        }

        return RestUtils.buildListResponse(list.stream().map(po -> {
            InvoiceRecordOrderRefDto dto = new InvoiceRecordOrderRefDto();
            BeanUtils.copyProperties(po, dto);
            return dto;
        }).collect(Collectors.toList()), page == null ? 0 : (int) page.getTotal());
    }

    public ListResponse<InvoiceRecordOrderRefPo> findListByOa(String procInstId) {
        return RestUtils.buildListResponse(invoiceRecordOrderRefRoDs.findListByOa(procInstId));
    }

    /**
     * 按照场站汇总订单
     *
     * @param param
     * @return
     */
    public ListResponse<ChargerOrderSite> recordOrderGroupBySite(
        ListInvoiceRecordOrderRefParam param) {
        // 分页信息
//        Page<ChargerOrder> page = null;
//        if (param.getTotal() != null && param.getTotal()) {
//            page = PageHelper.offsetPage(0, 10);
//            if (null != param.getStart()
//                    && null != param.getSize() && param.getSize() > 0) {
//                page = PageHelper.offsetPage(param.getStart().intValue(), param.getSize());
//            }
//            log.info("分页: page = {}, size = {}", page.getPageNum(), page.getPageSize());
//        }

        List<ChargerOrderSite> list = invoiceRecordOrderRefRoDs.recordOrderGroupBySite(param);
        return RestUtils.buildListResponse(list);
//        if (CollectionUtils.isEmpty(list)) {
//            return RestUtils.buildListResponse(new ArrayList<>(), 0);
//        }
//
//        return RestUtils.buildListResponse(list.stream().map(po -> {
//            InvoiceRecordOrderRefDto dto = new InvoiceRecordOrderRefDto();
//            BeanUtils.copyProperties(po, dto);
//            return dto;
//        }).collect(Collectors.toList()), page == null ? 0 : (int) page.getTotal());
    }

    /**
     * 获取企业开票记录关联的订单 金额数据
     *
     * @param param
     * @return
     */
    public ListResponse<OrderTimeShareBiVo> recordOrderGroupByTimeShareFee(
        ListInvoiceRecordOrderRefParam param) {
        List<OrderTimeShareBiVo> list = invoiceRecordOrderRefRoDs.recordOrderGroupByTimeShareFee(
            param);
        return RestUtils.buildListResponse(list);
    }

    public Integer corpInvoiceRecordSubmit2Audit(CorpInvoiceRecordDto dto) {
        CorpInvoiceRecordDto old = corpInvoiceRecordRoDs.getRecordByApplyNo(dto.getApplyNo(), true);
        if (null == old) {
            throw new DcArgumentException("企业客户申请单号无效");
        }

        CorpInvoiceRecordPo po = new CorpInvoiceRecordPo();
        BeanUtils.copyProperties(dto, po);
        po.setStatus(InvoicedStatus.SUBMITTED);
        po.setFixTotalFee(po.getFixElecFee().add(po.getFixServFee()));
        po.setAuditTime(null);
        po.setAuditRemark("");
        po.setAuditName("");
        po.setFailRemark("");
        po.setIssuedRemark("");
        return corpInvoiceRecordRwDs.insertOrUpdate(po);
    }

//    public void addInvoiceContent1(List<CorpInvoicingContentVo> list, Long invoiceId) {
//        if(CollectionUtils.isNotEmpty(list)) {
//            final int contentCount = invoicedRecordContentRwDs.batchInsert(list
//                .stream()
//                .map(InvoiceService::invoiceContentConvert)
//                .map(e -> e.setInvoiceId(invoiceId))
//                .collect(Collectors.toList()));
//            log.info("新增开票内容: {}", contentCount);
//        }
//    }

    /**
     * 个人商户会员开票记录提交到审核
     *
     * @param dto
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_UNCOMMITTED)
    public List<InvoicedRecordDto> userInvoiceRecordSubmit2Audit(UserInvoiceRecordParam dto) {

        IotAssert.isNotNull(dto, "请传入参");
        IotAssert.isNotNull(dto.getChannel(), "请传入开具方式");
//        IotAssert.isTrue(StringUtils.isNotBlank(dto.getProcInstId()), "参数错误,审批单号不能为空");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(dto.getInvoiceRecords()), "请传入发票信息");

        InvoiceRecordParam invRecParamIn = dto.getInvoiceRecords().get(0);  // 充值开票暂时只支持开1张
        for (var invRec : dto.getInvoiceRecords()) {
            IotAssert.isTrue(CollectionUtils.isNotEmpty(invRec.getContents()),
                "请传入发票的商品行信息");
        }

        InvoicedType invoicedType = null;
        if (InvoiceChannel.AUTO.equals(dto.getChannel())) {
            invoicedType = InvoicedType.ELECTRONIC;
        } else if (InvoiceChannel.MANUAL.equals(dto.getChannel())) {
            invoicedType = InvoicedType.PAPER;
        } else if (InvoiceChannel.SEMI.equals(dto.getChannel())) {
            invoicedType = InvoicedType.ELECTRONIC;
        }

        IotAssert.isTrue(
            List.of(PayAccountType.PERSONAL,
                    PayAccountType.COMMERCIAL)
                .contains(dto.getAccountType()),
            "不支持的支付类型" + dto.getAccountType());

        if (StringUtils.isNotBlank(dto.getApplyNo())) {
            //修改
            log.error("暂不支持修改. applyNo = {}", dto.getApplyNo());
            throw new DcServiceException("暂不支持修改");
//            return new ArrayList<>();
        }
        IotAssert.isTrue(CollectionUtils.isNotEmpty(dto.getOrderNoList()), "请传入充值单列表");

        if (StringUtils.isNotBlank(dto.getProcInstId())) {
            List<InvoicedRecordPo> originProcList = invoicedRecordRwDs.getByProcInstId(
                dto.getProcInstId(), true);
            for (InvoicedRecordPo originProc : originProcList) {
                InvoicedRecordPo updateRec = new InvoicedRecordPo();
                updateRec.setId(originProc.getId())
                    .setInvoicedEnabled(false);
                log.info("开票记录做逻辑删除 recId = {}", updateRec.getId());
                invoicedRecordRwDs.updateInvoicedRecord(updateRec);     // 开票记录做逻辑删除
                invoicedRecordContentRwDs.disableContent(updateRec.getId());    // 商品行明细做逻辑删除
            }
//            IotAssert.isTrue(CollectionUtils.isNotEmpty(originProcList),
//                "找不到原流程对应的开票申请");
        }
        List<InvoicedRecordDto> result = new ArrayList<>();
//        for (InvoicedRecordPo originProc : originProcList) {
        // 新增
        final Long byCzOrderIdCount = invoicedRecordBillRefRoDs.getByCzOrderIdCount(
            dto.getOrderNoList(), InvoicingMode.PRE_PAY);
        IotAssert.isTrue(byCzOrderIdCount == 0L, "订单已添加，请刷新后重试");

        long commId = 0L;
        if (dto.getCommId() != null) {
            commId = dto.getCommId();
            if (commId > 0) {
                // 商户会员，获取顶级商户
                final CommPo commercialById = trCommercialService.getCommercialById(commId);
                IotAssert.isNotNull(commercialById, "找不到商户信息: " + commId);
                commId = commercialById.getTopCommId();
            }
        }

//        final Optional<BigDecimal> servFeeSum = dto.getPayBillChoiceList()
//            .stream()
//            .filter(Objects::nonNull)
//            .map(PayBillInvoiceBi::getCanInvoiceAmount)
//            .filter(Objects::nonNull)
//            .reduce(BigDecimal::add);
        // 使用用户填写的开票内容来填充开票记录表
        final Optional<BigDecimal> servFeeSum = invRecParamIn.getContents()
            .stream()
            .filter(Objects::nonNull)
            .filter(content -> ProductType.SERV_ACTUAL_FEE.equals(content.getProductType()))
            .map(InvoiceContentParam::getFixAmount)
            .filter(Objects::nonNull)
            .reduce(BigDecimal::add);
        final Optional<BigDecimal> parkFeeSum = invRecParamIn.getContents()
            .stream()
            .filter(Objects::nonNull)
            .filter(content -> ProductType.PARK_OUT_FEE.equals(content.getProductType()))
            .map(InvoiceContentParam::getFixAmount)
            .filter(Objects::nonNull)
            .reduce(BigDecimal::add);
        final Optional<BigDecimal> prepaidCardFeeSum = invRecParamIn.getContents()
            .stream()
            .filter(Objects::nonNull)
            .filter(content -> ProductType.PREPAID_CARD_FEE.equals(content.getProductType()))
            .map(InvoiceContentParam::getFixAmount)
            .filter(Objects::nonNull)
            .reduce(BigDecimal::add);
        final Optional<BigDecimal> elecFeeSum = invRecParamIn.getContents()
            .stream()
            .filter(Objects::nonNull)
            .filter(content -> ProductType.ELEC_ACTUAL_FEE.equals(content.getProductType()))
            .map(InvoiceContentParam::getFixAmount)
            .filter(Objects::nonNull)
            .reduce(BigDecimal::add);
        InvoicedRecordPo invoicedRecordPo = new InvoicedRecordPo();
        invoicedRecordPo
            .setApproverDate(null)
            .setInvoicedType(invoicedType == null ? null : invoicedType.name())
            .setInvoiceType(dto.getInvoiceType() == null ? null : dto.getInvoiceType().name())
            .setUserId(dto.getUid())
            .setApplyNo(null)
            .setInvoicedStatus(dto.getStatus() == null ? null : dto.getStatus().name())
            .setInvoiceCode(dto.getInvoiceCode())
            .setInvoiceNumber(dto.getInvoiceNumber())
            .setInvoiceDate(null)
            .setIssuedTime(null)
            .setInvoiceAmount(dto.getFixTotalFee() == null ? null
                : dto.getFixTotalFee().movePointRight(2).intValue())
//            .setElecActualFee(BigDecimal.ZERO)// NOTE 充值单开票申请，无电费
//            .setServActualFee(servFeeSum.orElse(BigDecimal.ZERO))// NOTE 结合充值单可开票金额总和
//            .setParkActualFee(BigDecimal.ZERO)// NOTE 充值单开票申请，无停充超时实收金额
            // 使用用户填写的开票内容来填充开票记录表
            .setElecActualFee(elecFeeSum.orElse(BigDecimal.ZERO))
            .setServActualFee(servFeeSum.orElse(BigDecimal.ZERO))
            .setParkActualFee(parkFeeSum.orElse(BigDecimal.ZERO))
            .setPrepaidCardActualFee(prepaidCardFeeSum.orElse(BigDecimal.ZERO))
            .setInvoiceName(dto.getInvoiceName())
            .setInvoiceTin(dto.getInvoiceTin())
            .setInvoiceAddress(dto.getAddress())
            .setInvoiceTel(dto.getInvoiceTel())
            .setInvoiceBank(dto.getInvoiceBank())
            .setInvoiceAccount(dto.getInvoiceAccount())
            .setInvoiceDesc(invRecParamIn.getRemark())
            // 开始时间与别的地方存入ZonedDateTime有差别，因此在sql里处理
//            .setCreatedDate(new Date())
            .setReviewedDate(null)
            .setCreatorName(dto.getOpName())
            .setEmail(dto.getEmail())
            .setTrackingNumber(null)
            // 以下3个字段近年的记录都没在使用
//            .setProvince()
//            .setCity()
//            .setArea()
            .setAuditorDate(null)
            .setAuditorName(null)
            .setApproverDate(null)
            .setApproverName(null)
            .setTrackingOrderNo(null)
            .setReceiverName(dto.getReceiverName())
            .setReceiverMobilePhone(dto.getReceiverMobilePhone())
            .setReceiverProvince(dto.getReceiverProvince())
            .setReceiverCity(dto.getReceiverCity())
            .setReceiverArea(dto.getReceiverArea())
            .setReceiverAddress(dto.getReceiverAddress())
            .setSource("1")// TODO 暂定PC开票
            .setStationId(null)
            .setCommercialId(commId)
            .setHlhtApplyNo(null)
            .setPlatformSource(0)// TODO 暂定PC开票
            .setRejectReason(null)
            .setHlhtUserNo(null)
            .setProcInstId("")
            .setTempSalId(dto.getTempSalId())
            .setProductTempId(dto.getProductTempId())
            .setInvoicedEnabled(true);

        CorpInvoiceInfoVo invoiceInfoVo = dto.getCorpInvoiceInfoVo();
        if (invoiceInfoVo != null) {
            if (InvoiceType.PER_COMMON.equals(invoiceInfoVo.getInvoiceType())) {
                invoicedRecordPo.setInvoiceName(invoiceInfoVo.getName())
                    .setInvoiceTin("")
                    .setInvoiceAddress("")
                    .setInvoiceTel("")
                    .setInvoiceBank("")
                    .setInvoiceAccount("")
                    .setEmail(invoiceInfoVo.getEmail());
            } else if (InvoiceType.ENTER_COMMON.equals(invoiceInfoVo.getInvoiceType())) {
                invoicedRecordPo.setInvoiceName(invoiceInfoVo.getName())
                    .setInvoiceTin(invoiceInfoVo.getTin())
                    .setInvoiceAddress(invoiceInfoVo.getAddress())
                    .setInvoiceTel("")
                    .setInvoiceBank(invoiceInfoVo.getBank())
                    .setInvoiceAccount(invoiceInfoVo.getBankAccount())
                    .setEmail(invoiceInfoVo.getEmail());
            } else if (InvoiceType.ENTER_PROFESSION.equals(invoiceInfoVo.getInvoiceType())) {
                invoicedRecordPo.setInvoiceName(invoiceInfoVo.getName())
                    .setInvoiceTin(invoiceInfoVo.getTin())
                    .setInvoiceAddress(invoiceInfoVo.getAddress())
                    .setInvoiceTel(invoiceInfoVo.getTel())
                    .setInvoiceBank(invoiceInfoVo.getBank())
                    .setInvoiceAccount(invoiceInfoVo.getBankAccount())
                    .setEmail(invoiceInfoVo.getEmail())
                    .setReceiverName(invoiceInfoVo.getReceiverName())
                    .setReceiverMobilePhone(invoiceInfoVo.getReceiverMobilePhone())
                    .setReceiverProvince(invoiceInfoVo.getReceiverProvince())
                    .setReceiverCity(invoiceInfoVo.getReceiverCity())
                    .setReceiverArea(invoiceInfoVo.getReceiverArea())
                    .setReceiverAddress(invoiceInfoVo.getReceiverAddress());
            } else {
                log.warn("暂不支持的开票方式: {}", invoiceInfoVo.getInvoiceType());
            }
        }

//            if (originProc != null) {
        invoicedRecordPo.setId(invoicedRecordPo.getId())
            .setProcInstId(dto.getProcInstId());
        final boolean b = invoicedRecordRwDs.insertInvoicedRecord(invoicedRecordPo);
        IotAssert.isTrue(b, "新增发票记录失败");

//            } else {
//                final boolean b = invoicedRecordRwDs.insertInvoicedRecord(invoicedRecordPo);
//                IotAssert.isTrue(b, "新增发票记录失败");
//            }

        this.addInvoiceContent2(invRecParamIn.getContents(), invoicedRecordPo.getId()); // 发票的商品行数据

//        List<InvoicedRecordBillRefPo> invoicedRecordBillRefPoList = new ArrayList<>();

        final Map<String, PayBillInvoiceBi> payBillInvoiceBiMap = dto.getPayBillChoiceList()
            .stream()
            .collect(Collectors.toMap(PayBillInvoiceBi::getOrderId, o -> o));

        for (String orderId : dto.getOrderNoList()) {
            this.processPayBillInfo(orderId, payBillInvoiceBiMap, invoicedRecordPo.getId());
        }

//        if(!invoicedRecordBillRefPoList.isEmpty()) {
//            log.info("新增开票记录和账单关联表: {}",
//                invoicedRecordBillRefRwDs.batchInsert(invoicedRecordBillRefPoList)
//            );
//        }
        InvoicedRecordDto ret = new InvoicedRecordDto();
        BeanUtils.copyProperties(invoicedRecordPo, ret);
        result.add(ret);
//        }
        log.info("发票记录数量 = {}", result.size());
        return result;
    }

    private void processPayBillInfo(String orderId,
        Map<String, PayBillInvoiceBi> payBillInvoiceBiMap, Long recordId) {
        final PayBillUsedDetail payBillUsedDetail = payBillBizService.pointRecLog(orderId,
            null);

        // 充值单-已开票金额
        BigDecimal czInvoicedAmount;

        // 充值单-未开票金额
        BigDecimal czInvoiceAmount;

        if (CollectionUtils.isEmpty(payBillUsedDetail.getChargeOrderVoList())) {
            // 充值单无消费记录
            // 从t_pay_bill表获取开票信息
            PayBillPo po = payBillRwDs.findByOrderId(orderId, true);
            czInvoicedAmount = po.getInvoicedAmount();

            czInvoiceAmount = po.getAmount()
                .subtract(po.getInvoicedAmount())
                .subtract(po.getOutFlowAmount());
        } else {
            czInvoicedAmount = payBillUsedDetail.getUsedInfo() == null ?
                BigDecimal.ZERO : payBillUsedDetail.getUsedInfo().getTaxTotal();

            czInvoiceAmount = payBillUsedDetail.getUsedInfo() == null ?
                BigDecimal.ZERO : payBillUsedDetail.getUsedInfo().getUnTaxTotal();
        }

        IotAssert.isTrue(DecimalUtils.gt(czInvoiceAmount, BigDecimal.ZERO),
            "充值单无可开票金额，请刷新后再尝试" + orderId);
//            if(DecimalUtils.lte(czInvoiceAmount, BigDecimal.ZERO)) {
//                log.warn("该笔充值单已无可开票金额: {}", orderId);
//                continue;
//            }

        // 实时检查充值单，当前开票金额是否正确
        final PayBillInvoiceBi payBillInvoiceBi = payBillInvoiceBiMap.get(orderId);
        IotAssert.isNotNull(payBillInvoiceBi, "入参找不到充值单信息，请确认传参是否正确");
        IotAssert.isTrue(
            DecimalUtils.eq(payBillInvoiceBi.getCanInvoiceAmount(), czInvoiceAmount),
            "充值单可开票金额发生变更，请重新添加订单");

        // 插入1条特殊的记录
        InvoicedRecordBillRefPo invoicedRecordBillRefPoPayBill = new InvoicedRecordBillRefPo();
        invoicedRecordBillRefPoPayBill.setRecordId(recordId)
            .setOrderNo(null)
            .setCzOrderId(orderId)
            .setOrderBeforeInvoiceAmount(null)
            .setOrderBeforeInvoicedAmount(null)
            .setOrderInvoiceAmount(null)
            .setCzBeforeAmount(czInvoicedAmount)
            .setCzInvoiceAmount(czInvoiceAmount)
            .setType(InvoicingMode.PRE_PAY);
        invoicedRecordBillRefRwDs.insertInvoicedRecordBillRef(
            invoicedRecordBillRefPoPayBill);

        if (CollectionUtils.isEmpty(payBillUsedDetail.getChargeOrderVoList())) {
            // 充值单无消费记录
        } else {
            // FIXME 循环嵌套
//                Mono.zip(Mono.just(payBillUsedDetail.getChargeOrderVoList()),
//                        Mono.just(orderId),
//                        Mono.just(invoicedRecordPo.getId()),
//                        Mono.just(czInvoicedAmount),
//                        Mono.just(czInvoiceAmount))
//                    .doOnNext(e -> loopChargeOrderProc(e.getT1(),
//                        e.getT2(),
//                        e.getT3(),
//                        e.getT4(),
//                        e.getT5()))
//                    .subscribe();
            CompletableFuture.runAsync(() ->
                loopChargeOrderProc(payBillUsedDetail.getChargeOrderVoList(),
                    orderId,
                    recordId,
                    czInvoicedAmount,
                    czInvoiceAmount), executor);
        }

        // 充值订单-新的已开票金额
        final BigDecimal newDepositInvoicedAmount = czInvoicedAmount.add(czInvoiceAmount);

        PayBillPo bill = new PayBillPo();
        bill.setOrderId(orderId)
            .setInvoicedAmount(newDepositInvoicedAmount);

        log.info("更新t_pay_bill: {}, {}, {} -> {}",
            payBillRwDs.update(bill), orderId, czInvoicedAmount, newDepositInvoicedAmount);
    }

    private void loopChargeOrderProc(List<PayBillLinkChargeOrderVo> payBillLinkChargeOrderVoList,
        String orderId,
        Long recordId,
        BigDecimal czInvoicedAmount,
        BigDecimal czInvoiceAmount) {
        List<InvoicedRecordBillRefPo> invoicedRecordBillRefPoList = new ArrayList<>();

        for (PayBillLinkChargeOrderVo chargeOrderVo : payBillLinkChargeOrderVoList) {
            // NOTE 单笔充电订单的处理

            final String orderNo = chargeOrderVo.getOrderNo();

            // 充电单-已开票金额
            final BigDecimal invoicedAmount = chargeOrderVo.getInvoicedAmount();

            // 充电单-剩余可开票金额
            final BigDecimal invoiceAmount = chargeOrderVo.getInvoiceAmount();

            if (TaxStatus.YES.equals(chargeOrderVo.getTaxStatus())) {
                // 已开票，不理会
                continue;
            }

            if (TaxStatus.CANT.equals(chargeOrderVo.getTaxStatus())) {
                // 不可开票，不理会
                continue;
            }

            if (DecimalUtils.eq(invoiceAmount, DecimalUtils.ZERO)) {
                log.info("订单可开票金额为0: {}", orderNo);
                continue;
            }

            if (TaxStatus.PART.equals(chargeOrderVo.getTaxStatus())) {
                // 部分开票，具体分析
                // TODO
//                        continue;
            }

            // 实际开票金额 = 总金额 - 赠送金额
            final BigDecimal realAmount = chargeOrderVo.getTotalAmount()
                .subtract(chargeOrderVo.getFreeAmount());

            // 使用资金块的实际金额
            final BigDecimal amount = chargeOrderVo.getAmount();

            if (DecimalUtils.eq(realAmount, amount)) {
                // 资金块金额全覆盖，可全部开票
            } else if (DecimalUtils.gt(realAmount, amount)) {
                // 资金块无法全覆盖 充电金额，不可全开票
            } else {
                // 资金块超出充电金额，此处可能有问题
                log.warn("资金块超出充电金额，此处可能有问题");
            }
            InvoicedRecordBillRefPo invoicedRecordBillRefPo = new InvoicedRecordBillRefPo();
            invoicedRecordBillRefPo.setRecordId(recordId)
                .setOrderNo(orderNo)
                .setCzOrderId(orderId)
                .setOrderBeforeInvoiceAmount(invoiceAmount)
                .setOrderBeforeInvoicedAmount(invoicedAmount)
                .setOrderInvoiceAmount(amount)
                .setCzBeforeAmount(czInvoicedAmount)
                .setCzInvoiceAmount(czInvoiceAmount)
                .setType(InvoicingMode.PRE_PAY);

            invoicedRecordBillRefPoList.add(invoicedRecordBillRefPo);

            // 充电订单-新的已开票金额
            final BigDecimal newInvoicedAmount = invoicedAmount.add(amount);

            // 充电订单-新的可开票金额
            final BigDecimal newInvoiceAmount = invoiceAmount.subtract(amount);

            ChargerOrderPayPo chargerOrderPayPo = new ChargerOrderPayPo();
            chargerOrderPayPo.setOrderNo(orderNo)
                .setInvoiceAmount(newInvoiceAmount)
                .setInvoicedAmount(newInvoicedAmount);
            log.info("更新t_charger_order_pay: {}",
                chargerOrderPayRwDs.updateChargerOrderPay(chargerOrderPayPo));
        }
        if (!invoicedRecordBillRefPoList.isEmpty()) {
            // 一笔订单的资金块可能既含有充值开票，又含有充电开票部分，一个invoiced_id字段不够用
            // 所以若order表上invoiced_id已有值，则不填入
            List<String> updateOrderNoList = invoicedRecordBillRefPoList.stream()
                .map(InvoicedRecordBillRefPo::getOrderNo).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(updateOrderNoList)
                && recordId != null) {
                chargerOrderRwDs.fillInvoicedIdWhenEmpty(recordId,
                    updateOrderNoList);
                chargerOrderPayRwDs.fillInvoicedIdWhenEmpty(recordId,
                    updateOrderNoList);
            }

            log.info("新增开票记录和账单关联表: {}",
                invoicedRecordBillRefRwDs.batchInsert(invoicedRecordBillRefPoList)
            );
        }

    }

    public void addInvoiceContent2(List<InvoiceContentParam> list, Long invoiceId) {
        if (CollectionUtils.isNotEmpty(list)) {
            final int contentCount = invoicedRecordContentRwDs.batchInsert(list
                .stream()
                .map(InvoiceService::invoiceContentConvert)
                .map(e -> e.setInvoiceId(invoiceId))
                .collect(Collectors.toList()));
            log.info("新增开票内容: {}", contentCount);
        }
    }

    private List<InvoicedRecordPo> getInvoicedListByProcIdOrRecId(Long recordId,
        String procInstId) {
        IotAssert.isTrue(recordId != null || procInstId != null, "入参不能同时为空");
        List<InvoicedRecordPo> invRecPoList;
        if (StringUtils.isNotBlank(procInstId)) {
            invRecPoList = invoicedRecordRwDs.getByProcInstId(procInstId, true);
        } else {
            invRecPoList = new ArrayList<>();
            InvoicedRecordPo invRecPo = invoicedRecordRwDs.getById(recordId, true);
            if (invRecPo != null) {
                invRecPoList.add(invRecPo);
            }
        }
        return invRecPoList;
    }

    /**
     * 个人商户会员&企业客户开票记录提交到审核-审核驳回
     *
     * @param recordId invoiced_record.id
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_UNCOMMITTED)
    public Integer userInvoiceRecordDeny(Long recordId, String procInstId) {
        IotAssert.isTrue(recordId != null || procInstId != null, "入参不能同时为空");

        List<InvoicedRecordPo> invRecPoList = this.getInvoicedListByProcIdOrRecId(recordId,
            procInstId);
//        IotAssert.isTrue(CollectionUtils.isNotEmpty(invRecPoList), "未找到对应的开票记录");

        for (InvoicedRecordPo invoicedRecordPo : invRecPoList) {

//        IotAssert.isNotNull(invoicedRecordPo, "找不到开票申请");

            IotAssert.isTrue(
                List.of(InvoicedStatus.COMPLETED.name(),
                        InvoicedStatus.RED_DASHED.name(),
                        InvoicedStatus.SUBMITTED.name(),
                        InvoicedStatus.NOT_SUBMITTED.name(),
                        InvoicedStatus.INVOICING_FAIL.name())
                    .contains(invoicedRecordPo.getInvoicedStatus()),
                "开票申请当前状态不正确");

            // 发票当前已经开出，但未出事务，所以可能是已开具
            if (InvoicedStatus.COMPLETED.name().equals(invoicedRecordPo.getInvoicedStatus()) ||
                InvoicedStatus.RED_DASHED.name().equals(invoicedRecordPo.getInvoicedStatus())) {
                // 发票红冲
                invoicedRecordPo.setInvoicedStatus(InvoicedStatus.RED_DASHED.name());
            } else if (StringUtils.equals(InvoicedStatus.SUBMITTED.name(),
                invoicedRecordPo.getInvoicedStatus())) {
                invoicedRecordPo.setInvoicedStatus(
                    InvoicedStatus.NOT_SUBMITTED.name());   // 审批过程中，发票的状态为 NOT_SUBMITTED
            }
            log.info("更新invoiced_record: {}",
                invoicedRecordRwDs.updateInvoicedRecord(invoicedRecordPo));
            // 回退充值单
            this.rollbackPayBillAndRecordRef(invoicedRecordPo.getId());
        }
        return invRecPoList.size();
    }

    /**
     * 回退充值单和充值单的开票记录
     *
     * @param recordId
     */
    private void rollbackPayBillAndRecordRef(Long recordId) {
        List<InvoicedRecordBillRefPo> billRefs =
            invoicedRecordBillRefRoDs.getByRecordId(recordId,
                InvoicingMode.PRE_PAY);
        if (CollectionUtils.isNotEmpty(billRefs)) {

            for (InvoicedRecordBillRefPo billRefPo : billRefs) {
                if (StringUtils.isBlank(billRefPo.getOrderNo())) {

                    final String czOrderId = billRefPo.getCzOrderId();
                    final BigDecimal czBeforeAmount = billRefPo.getCzBeforeAmount();
                    final BigDecimal czInvoiceAmount = billRefPo.getCzInvoiceAmount();

                    final PayBillPo byOrderId = payBillRwDs.findByOrderId(czOrderId, false);
                    IotAssert.isNotNull(byOrderId, "找不到充值单:" + czOrderId);

                    // 充电单-已开票金额 回退
                    final BigDecimal rollbackPayBillInvoicedAmount =
                        byOrderId.getInvoicedAmount().subtract(czInvoiceAmount);

                    // 重置t_pay_bill的开票金额
                    PayBillPo bill = new PayBillPo();
                    bill.setOrderId(czOrderId)
                        .setInvoicedAmount(rollbackPayBillInvoicedAmount);

                    log.info("回退t_pay_bill>>: {}, {} -> {}, rollback: {}",
                        czOrderId,
                        czBeforeAmount,
                        rollbackPayBillInvoicedAmount,
                        czInvoiceAmount);

                    IotAssert.isTrue(
                        DecimalUtils.gte(rollbackPayBillInvoicedAmount, DecimalUtils.ZERO),
                        "回退的已开票金额不能为负数" + czOrderId);

                    log.info("<<回退t_pay_bill: {}, {}, {} -> {}, rollback: {}",
                        payBillRwDs.update(bill),
                        czOrderId,
                        czBeforeAmount,
                        rollbackPayBillInvoicedAmount,
                        czInvoiceAmount);
                    continue;
                }

                final ChargerOrderPayPo orderPayPo = chargerOrderPayRwDs.getByOrderNo(
                    billRefPo.getOrderNo(), false);

                // 充电单-开票金额 回退
                final BigDecimal rollbackInvoicedAmount = orderPayPo.getInvoicedAmount()
                    .subtract(billRefPo.getOrderInvoiceAmount());

                // 充电单-未开金额 回退
                final BigDecimal rollbackInvoiceAmount = orderPayPo.getInvoiceAmount()
                    .add(billRefPo.getOrderInvoiceAmount());

                ChargerOrderPayPo chargerOrderPayPo = new ChargerOrderPayPo();
                chargerOrderPayPo.setOrderNo(billRefPo.getOrderNo())
                    .setInvoiceAmount(rollbackInvoiceAmount)
                    .setInvoicedAmount(rollbackInvoicedAmount);

                log.info("回退t_charger_order_pay: {}",
                    chargerOrderPayRwDs.updateChargerOrderPay(chargerOrderPayPo));
            }
            // 一笔订单的资金块可能既含有充值开票，又含有充电开票部分，一个invoiced_id字段不够用
            // 所以若order表上invoiced_id与updateInvoiceId不一致，则不置空
            Long updateInvoiceId = recordId;
            List<String> updateOrderNoList = billRefs.stream()
                .map(InvoicedRecordBillRefPo::getOrderNo).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(updateOrderNoList)
                && updateInvoiceId != null) {
                chargerOrderRwDs.emptyInvoicedIdWhenEqual(updateInvoiceId,
                    updateOrderNoList);
                chargerOrderPayRwDs.emptyInvoicedIdWhenEqual(updateInvoiceId,
                    updateOrderNoList);
            }
            log.info("删除开票记录和账单关联表: {}",
                invoicedRecordBillRefRwDs.disableByRecordId(recordId));
        }
    }

    /**
     * 充值开票，个人商户会员，结束申请
     *
     * @param recordId
     * @param procInstId
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_UNCOMMITTED)
    public Integer userInvoiceRecordAbandon(Long recordId, String procInstId) {

        List<InvoicedRecordPo> invRecPoList = this.getInvoicedListByProcIdOrRecId(recordId,
            procInstId);
//        IotAssert.isTrue(CollectionUtils.isNotEmpty(invRecPoList), "未找到对应的开票记录");

        for (InvoicedRecordPo invoicedRecordPo : invRecPoList) {

            if (invoicedRecordPo != null) {
                InvoicedRecordPo po = new InvoicedRecordPo();
                po.setId(invoicedRecordPo.getId())
//            .setInvoicedStatus(InvoicedStatus.DELETED.name())
                    .setInvoicedEnabled(false);
                log.info("开票记录状态设为删除: {}", invoicedRecordRwDs.updateInvoicedRecord(po));
                return 0;
            }
        }
        return invRecPoList.size();
    }

    public Integer corpInvoiceCallback(InvoiceCallBackDto dto) {
        CorpInvoiceRecordDto old = corpInvoiceRecordRoDs.getRecordByApplyNo(dto.getApplyNo(), true);
        if (null == old) {
            throw new DcArgumentException("企业客户申请单号无效");
        }

        CorpInvoiceRecordPo po = new CorpInvoiceRecordPo();
        BeanUtils.copyProperties(old, po);
        if (po.getIssuedRemark() == null) {
            po.setIssuedRemark("");
        }
        switch (dto.getStatus()) {
            case RJ_FAILURE:
                if (StringUtils.isNotBlank(old.getProcInstId())) {
                    // 若是由开票流程发起的企业开票申请，不删除关系，不重置发票数据，不重置订单账单数据（放到企业开票记录删除时处理）
                    po.setStatus(InvoicedStatus.SUBMITTED);
                    po.setFailRemark(dto.getFailMsg());
                } else {
                    this.corpInvoiceFailureProcessing(dto, po);
                }
                break;
            case RJ_RED:
            case RJ_INVALID:
                this.corpInvoiceFailureProcessing(dto, po);
                break;
            case RJ_SUCCEED:
                this.corpInvoiceSuccessfulProcessing(dto, po);

                // 成功，更新pay_bill的发票信息字段
                final List<InvoicedRecordBillRefPo> payBillRecordList =
                    invoicedRecordBillRefRoDs.getPayBillByRecordId(
                        dto.getInvoicedId());
                payBillRecordList.forEach(ref -> {
                    PayBillPo bill = new PayBillPo();
                    bill.setOrderId(ref.getCzOrderId())
                        .setTaxNo(dto.getInvoiceNumber())
                        .setTaxType(dto.getTaxType())
                        .setInvoiceMode(dto.getInvoiceMode());
                    payBillRwDs.update(bill);
                    bill = null;
                });
                break;
            default:
                log.error("回调状态无效");
                return 0;
        }

        if (StringUtils.isNotBlank(old.getProcInstId())
            && (!InvoiceChannel.MANUAL.equals(old.getChannel()))) {
            // 自动和半自动的审批流均进行处理(即只有手动不处理）
            NotifyResultParam req = new NotifyResultParam();
            req.setProcInstId(old.getProcInstId())
                .setStatus(dto.getStatus())
                .setErrorMsg(dto.getFailMsg())
                .setInvoicePdfUrl(dto.getInvoicePdfUrl())
                .setAttachmentList(dto.getAttachmentList())
                .setProcInstNextStepEnabled(dto.getProcInstNextStepEnabled())
                .setProInstAdminContactEnabled(dto.getProInstAdminContactEnabled());
            oaFeignClient.notifyResult(req)
                .subscribe();
        }
        return corpInvoiceRecordRwDs.insertOrUpdate(po);
    }

    public void corpInvoiceFailureProcessing(InvoiceCallBackDto dto, CorpInvoiceRecordPo po) {
        po.setStatus(InvoiceCallbackStatus.RJ_FAILURE.equals(dto.getStatus())
            ? InvoicedStatus.INVOICING_FAIL : InvoicedStatus.INVALID);
        po.setActualServFee(BigDecimal.ZERO)
            .setActualElecFee(BigDecimal.ZERO)
            .setTotalFee(BigDecimal.ZERO)
            .setFixTotalFee(BigDecimal.ZERO)
            .setFixElecFee(BigDecimal.ZERO)
            .setFixServFee(BigDecimal.ZERO)
            .setFailRemark(dto.getFailMsg());

        ListInvoiceRecordOrderRefParam recordOrderRefParam = new ListInvoiceRecordOrderRefParam();
        recordOrderRefParam.setApplyNo(dto.getApplyNo());
        List<String> orderNoList = invoiceRecordOrderRefRoDs.findList(recordOrderRefParam)
            .stream().map(InvoiceRecordOrderRefPo::getOrderNo)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderNoList)) {
            log.error("发票数据不存在. applyNo = {}", dto.getApplyNo());
            return;
        }

        InvoiceCallbackMsg msg = new InvoiceCallbackMsg();
        msg.setAppend(false)
            .setOrderNoList(orderNoList)
            .setInvoiceId(dto.getInvoicedId())
            .setInvoiceWay(po.getInvoiceWay());
        invoiceCallbackService.addEventMsg(msg);
        invoiceCallbackService.notifyEvent();
        switch (po.getInvoiceWay()) {
            case PRE_PAY:
                ListInvoiceRecordOrderRefParam param = new ListInvoiceRecordOrderRefParam();
                param.setApplyNo(dto.getApplyNo());
                List<InvoiceRecordOrderRefPo> list = invoiceRecordOrderRefRoDs.findList(param);
                list.forEach(ref -> {
                    PayBillPo bill = new PayBillPo();
                    bill.setOrderId(ref.getOrderNo())
                        .setTaxNo("")
                        .setTaxType(TaxType.UNKNOWN)
                        .setInvoiceMode(InvoiceMode.UNKNOWN);
                    payBillRwDs.update(bill);
                    bill = null;
                });
                break;
            case POST_SETTLEMENT:
            case POST_CHARGER:
            default:
                break;
        }

        // 删除关系信息
        int delete = invoiceRecordOrderRefRwDs.delete(po.getInvoiceWay(), dto.getApplyNo(),
            orderNoList);

    }

    public void corpInvoiceSuccessfulProcessing(InvoiceCallBackDto dto, CorpInvoiceRecordPo po) {
        po.setStatus(InvoicedStatus.COMPLETED);

        switch (po.getInvoiceWay()) {
            case PRE_PAY:
                ListInvoiceRecordOrderRefParam param = new ListInvoiceRecordOrderRefParam();
                param.setApplyNo(dto.getApplyNo());
                List<InvoiceRecordOrderRefPo> list = invoiceRecordOrderRefRoDs.findList(param);
                list.forEach(ref -> {
                    PayBillPo bill = new PayBillPo();
                    bill.setOrderId(ref.getOrderNo())
                        .setTaxNo(dto.getInvoiceNumber())
                        .setTaxType(dto.getTaxType())
                        .setInvoiceMode(dto.getInvoiceMode());
                    payBillRwDs.update(bill);
                    bill = null;
                });
                break;
            case POST_SETTLEMENT:
            case POST_CHARGER:
            default:
                // nothing to do
                break;
        }
    }

    @Async
    public void updateInvoiceAmount(InvoiceCallBackDto dto) {
        log.info("处理发票申请结果: dto = {}", JsonUtils.toJsonString(dto));
        if (StringUtils.isNotBlank(dto.getApplyNo())) { // 企业客户开票申请
            CorpInvoiceRecordDto po = corpInvoiceRecordRoDs.getRecordByApplyNo(dto.getApplyNo(),
                true);
            if (null == po) {
                log.error("企业客户申请的开票记录不存在: applyNo = {}", dto.getApplyNo());
                return;
            }

            ListInvoiceRecordOrderRefParam recordOrderRefParam = new ListInvoiceRecordOrderRefParam();
            recordOrderRefParam.setApplyNo(dto.getApplyNo());
            List<String> orderNoList = invoiceRecordOrderRefRoDs.findList(recordOrderRefParam)
                .stream().map(InvoiceRecordOrderRefPo::getOrderNo)
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orderNoList)) {
                log.warn("发票下属订单数据不存在. applyNo = {}", dto.getApplyNo());
                return;
            }

            InvoiceCallbackMsg msg = new InvoiceCallbackMsg();
            msg.setAppend(true)
                .setApplyNo(dto.getApplyNo())
                .setOrderNoList(orderNoList)
                .setInvoiceId(dto.getInvoicedId())
                .setInvoiceWay(po.getInvoiceWay());
            invoiceCallbackService.addEventMsg(msg);
            invoiceCallbackService.notifyEvent();
//            switch (po.getInvoiceWay()) {
//                case POST_SETTLEMENT:
//                    this.settlementInvoicedAmount(true, orderNoList);
//                    break;
//                case PRE_PAY:
//                    // 获取充电
//                    this.payBillInvoicedAmount(true, orderNoList);
//                    break;
//                case POST_CHARGER:
//                    this.chargerOrderInvoicedAmount(true, orderNoList, dto.getInvoicedId());
//                    break;
//                default:
//                    break;
//            }
        } else { // 普通客户申请
            List<String> orderNoList = dto.getOrderNoList();
            if (CollectionUtils.isNotEmpty(orderNoList)) {
                invoiceProcess.chargerOrderInvoicedAmount(true, orderNoList, dto.getInvoicedId());

//                InvoiceCallbackMsg msg = new InvoiceCallbackMsg();
//                msg.setAppend(true)
//                        .setOrderNoList(orderNoList)
//                        .setInvoiceId(dto.getInvoicedId());
//                invoiceCallbackService.addEventMsg(msg);
//                invoiceCallbackService.notifyEvent();
            }
        }
    }

    public Integer updateCorpInvoiceRecordModelId(UpdateIdDTO data) {
        return corpInvoiceRecordRwDs.updateCorpInvoiceRecordModelId(data.getOldId(),
            data.getNewId());
    }

    public Mono<Integer> updateCorpInvoiceRecordProTempId(List<UpdateIdDTO> updateIdDTOList) {
        if (CollectionUtils.isEmpty(updateIdDTOList)) {
            return Mono.just(0);
        }

        // 第一个数据是开票主体的ID值
        UpdateIdDTO dto = updateIdDTOList.get(0);
        corpInvoiceRecordRwDs.updateCorpInvoiceRecordTempSalId(dto.getOldId(), dto.getNewId());

        updateIdDTOList.stream()
            .skip(1)
            .forEach(data ->
                corpInvoiceRecordRwDs.updateCorpInvoiceRecordProTempId(
                    data.getOldId(), data.getNewId()));
        return Mono.just(updateIdDTOList.size());
    }

    public Mono<Integer> updateCorpInvoiceRecord(CorpInvoiceRecordPo invoiceRecordPo) {
        IotAssert.isTrue(invoiceRecordPo != null
            && StringUtils.isNotBlank(invoiceRecordPo.getApplyNo()), "缺少入参");
        CorpInvoiceRecordDto old = corpInvoiceRecordRoDs.getRecordByApplyNo(
            invoiceRecordPo.getApplyNo(), true);
        if (null == old) {
            throw new DcArgumentException("企业客户申请单号无效");
        }
        return Mono.just(corpInvoiceRecordRwDs.updateByCondition(invoiceRecordPo));
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_UNCOMMITTED)
    public Mono<Integer> updateUserInvoiceRecord(InvoicedRecordDto invoicedRecordDto) {

        IotAssert.isNotNull(invoicedRecordDto, "缺少入参");

        IotAssert.isNotBlank(invoicedRecordDto.getProcInstId(), "缺少流程实例ID");

        IotAssert.isNotNull(invoicedRecordDto.getId(), "缺少id");

        final InvoicedRecordPo recordPo =
            invoicedRecordRwDs.getById(invoicedRecordDto.getId(), true);

        IotAssert.isNotNull(recordPo, "找不到开票记录: " + invoicedRecordDto.getId());

        InvoicedRecordPo poUpdate = new InvoicedRecordPo();
        poUpdate.setProcInstId(invoicedRecordDto.getProcInstId())
            .setId(invoicedRecordDto.getId());

        final boolean b = invoicedRecordRwDs.updateInvoicedRecord(poUpdate);
        log.info("修改个人&商户会员开票记录: {}", b);

        return Mono.just(0);
    }

//    public void emptyInvoicedIds(Long invoicedId) {
//        try {
//            List<Long> invoicedIdList = List.of(invoicedId);
//            Integer i = chargerOrderRwDs.emptyInvoicedIds(invoicedIdList);
//            Integer ii = chargerOrderPayRwDs.emptyInvoicedIds(invoicedIdList);
//            log.info("更新充电订单的开票Id: result = {} : {}", i, ii);
//        } catch (Exception e) {
//            log.error("清空开票ID err = {}", e.getMessage(), e);
//        }
//    }

    public Mono<Boolean> invoiceUsed(InvoicingMode invoiceWay, String orderNo) {
        return Mono.just(invoiceRecordOrderRefRoDs.invoiceUsed(invoiceWay, orderNo));
    }

    public Mono<ListResponse<ChargerOrderVo>> includeChargerOrderList(ListChargeOrderParam param) {
        return Mono.just(param)
            .doOnNext(p -> {
                if (null == param.getSize() || param.getSize() > 10000) {
                    param.setSize(9999);
                }
            })
            .map(this.invoiceRecordOrderRefRoDs::includeChargerOrderList)
            .map(RestUtils::buildListResponse)
            .doOnNext(res -> {
                if (null != param.getTotal() && param.getTotal()) {
                    res.setTotal(
                        this.invoiceRecordOrderRefRoDs.countChargerOrder(param).getOrderAmount());
                } else {
                    res.setTotal(0L);
                }
            });
    }

    public Mono<OrderBiVo> includeChargerOrderBi(ListChargeOrderParam param) {
        return Mono.just(param)
            .map(invoiceRecordOrderRefRoDs::countChargerOrder);
    }

    public Mono<ObjectResponse<OaInvoicedVo>> getInvoiceVo4PrepaidProcess(String procInstId) {

        OaInvoicedVo vo = invoiceRecordRoDs.getInvoiceVo4PrepaidProcess(
            procInstId);

        if (vo == null) {
            vo = corpInvoiceRecordRoDs.getInvoiceVo4PrepaidProcess(
                procInstId);
        }
        return Mono.just(RestUtils.buildObjectResponse(vo));
    }

}
