package com.cdz360.biz.dc.service;

import com.cdz360.biz.auth.zft.vo.ZftVo;
import com.cdz360.biz.dc.config.MchBillRegexConfig;
import com.chargerlinkcar.core.BaseMockTest;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
class ZftDailyBillServiceTest extends BaseMockTest {

    @Autowired
    private ZftDailyBillService zftDailyBillService;

    @Autowired
    private MchBillRegexConfig mchBillRegexConfig;

    @Test
    void testPattern() {
        String str = "新电途(上海鼎充:325616137220104234945240325)";
//        String regex = "^新电途\\(上海鼎充:.*$";
        String regex = mchBillRegexConfig.getRegList().get(0).getCorpMchInfoList().get(0).getRegex();
        boolean match = str.matches(regex);
        log.info("匹配结果: {} , dest = {}, regex = {}", match, str, regex);
    }

    @Test
    void aliDailyBillDownload() {
        Map<Long, List<ZftVo>> aliMchMap = new HashMap<>();
        aliMchMap.put(34474L, List.of(new ZftVo()
                .setTopCommId(34474L)
                .setId(1L)
                .setName("测试测试")
                .setCommId(34474L)
                .setAlipaySubMchId("XX12345678")));
        LocalDateTime billDate = LocalDate.now().minusDays(1).atStartOfDay();
        zftDailyBillService.aliDailyBillDownload(aliMchMap, billDate);
    }

    /**
     * 主函数
     */
    public static void main(String[] args) {
        String str = "新电途(上海鼎充:325616137220104234945240325)";
        String regex = "^新电途\\(上海鼎充:.*\\)$";
//        String reg = corpMchBillRegConfig.getRegList().get(0).getCorpMchInfoList().get(0).getReg();
        boolean match = str.matches(regex);
//        boolean match =  Pattern.matches(regex, str);
        log.info("匹配结果: {} , dest = {}, regex = {}", match, str, regex);
    }
}