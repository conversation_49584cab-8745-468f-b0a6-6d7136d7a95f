package com.chargerlinkcar.core.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.base.model.base.type.UserType;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.biz.dc.service.ChargerOrderBizService;
import com.cdz360.biz.dc.service.PayBillBizService;
import com.cdz360.biz.model.finance.type.*;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.cdz360.biz.model.trading.deposit.vo.DepositSourceType;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.cdz360.biz.model.trading.order.po.PayBillPo;
import com.cdz360.biz.model.trading.order.type.PayBillStatus;
import com.cdz360.biz.model.trading.order.vo.PayBillBi;
import com.cdz360.biz.model.trading.order.vo.PayBillVo;
import com.chargerlinkcar.core.BaseMockTest;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrder;
import com.chargerlinkcar.framework.common.service.PayBillIdGenerator;
import com.chargerlinkcar.framework.common.utils.IDUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

@Transactional
@Slf4j
public class IPayBillServiceTest extends BaseMockTest {

    @Autowired
    private PayBillIdGenerator payBillIdGenerator;

    private static final Long[] COMM_ID_LIST_3342 = {33421L, 34477L, 34481L, 34482L, 34484L, 34486L, 34487L, 34488L, 34492L, 34494L, 34495L, 34498L, 34500L, 34506L, 34507L, 34508L, 34510L, 34513L, 34514L, 34515L, 34516L};
    private static final Long[] COMM_ID_LIST_33490 = {33490L};
    // 充值来源
    private static final List<DepositSourceType> D_S_T = Arrays.asList(DepositSourceType.values());
    // 充值类型
    private static final List<DepositFlowType> D_F_T = Arrays.asList(DepositFlowType.values());
    // 充值账户
    private static final List<PayAccountType> P_A_T = Arrays.asList(PayAccountType.values());
    // 支付方式
    private static final List<PayChannel> P_C = Arrays.asList(PayChannel.values());
    // 到账账户
    private static final List<FlowInAccountType> F_I_A_T = Arrays.asList(FlowInAccountType.values());
    // 开票类型
    private static final List<TaxType> T_T = Arrays.asList(TaxType.values());
    // 寄送状态
    private static final List<ExpressStatus> E_S = Arrays.asList(ExpressStatus.values());
    private static final PayBillParam SEARCH_PARAM = new PayBillParam();

    // 操作对象
    private static final PayBillPo OPT_PO = new PayBillPo();

    @Autowired
    private PayBillBizService payBillService;

    @Autowired
    private ChargerOrderBizService chargerOrderBizService;

    @BeforeEach
    public void setUp() {
        OPT_PO.setId(IDUtil.getWorkerID()).setUserId(64008L).setCommId(33421L)
                .setOrderId("********************").setAmount(new BigDecimal(20.00))
                .setFreeAmount(new BigDecimal(10.00)).setFlowType(DepositFlowType.IN_FLOW)
                .setSourceType(DepositSourceType.MGM_WEB).setPayChannel(PayChannel.WXPAY)
                .setAccountType(PayAccountType.PERSONAL).setAccountCode(null)
                .setFlowInAccountType(FlowInAccountType.TENPAY).setRefBillNo(null)
                .setTaxType(TaxType.NORMAL_TAX).setInvoiceMode(InvoiceMode.ONLINE)
                .setTaxNo("**********9876543").setExpressStatus(ExpressStatus.SENT)
                .setExpressCompany("物流公司").setExpressNo("****************")
                .setAmountBefore(DecimalUtils.divide100(1000))
                .setAmountAfter(DecimalUtils.divide100(3000))
                .setSubject("充值：********************").setClientIp("*************")
                .setBody("充值：********************").setOrderId("****************")
                .setStatus(PayBillStatus.PAID.getCode()).setCreateTime(new Date())
                .setTradeNo("0987651**********").setPayTime(new Date())
                .setNotifyType(1).setTradeType("APP").setBankType(null).setOutRefundNo(null)
                .setChargeOrderNo("****************").setInAccountName("收款账户名称")
                .setInBankName("收款账户银行名称").setInAccountNo("12345678987654345678")
                .setFlowSeqNo("**************").setOpUserType(UserType.CUSTOMER)
                .setOpUid(64008L).setOpName("**********");
    }

    @Test
    public void test_payBillList() {
        String orderId = "********************";
//        String cusName = "180164";
//        String cusPhone = "180164";
        SEARCH_PARAM.setIndex(1);
        SEARCH_PARAM.setSize(1);
        SEARCH_PARAM.setCommIdList(Arrays.asList(COMM_ID_LIST_3342));
        SEARCH_PARAM.setOrderIdList(List.of(orderId));
        SEARCH_PARAM.setCusName("");
//        SEARCH_PARAM.setCusPhone(cusPhone);
//        SEARCH_PARAM.setStatus(PayBillStatus.PAID);
//        SEARCH_PARAM.setKeyword("2019");
        ListResponse<PayBillVo> responce = payBillService.payBillList(SEARCH_PARAM);
        if (null != responce.getData()) {
            List<PayBillVo> list = responce.getData();
            log.info("search result: size={}", list.size());
            if (list.size() != 0) {
                assertEquals(1, list.size());
//                assertTrue(list.get(0).getOrderId().contains(orderId));
//                assertTrue(list.get(0).getCusName().contains(cusName));
//                assertTrue(list.get(0).getCusPhone().contains(cusPhone));

//                boolean res = Arrays.asList(COMM_ID_LIST_3342).containsAll(list.stream().map(PayBillVo::getCommId).collect(Collectors.toList()));
//                assertTrue(res);
            }
        }
    }

    @Test
    public void test_updateById() {
        // 插入一条数据
        Long id = OPT_PO.getId();
        String orderId = OPT_PO.getOrderId();
        PayChannel payChannel = OPT_PO.getPayChannel();
        payBillService.insert(OPT_PO);

        // 查找
        PayBillPo po = payBillService.findById(id);
        assertNotNull(po);
        assertEquals(payChannel, po.getPayChannel());
        assertEquals(OPT_PO.getAccountCode(), po.getAccountCode());
        assertEquals(OPT_PO.getAmountBefore(), po.getAmountBefore());
        assertEquals(OPT_PO.getAmountAfter(), po.getAmountAfter());
        assertEquals(orderId, po.getOrderId());

        po = payBillService.findById(id);
        assertNotNull(po);
        assertEquals(payChannel, po.getPayChannel());
        assertEquals(OPT_PO.getAccountCode(), po.getAccountCode());
        assertEquals(OPT_PO.getAmountBefore(), po.getAmountBefore());
        assertEquals(OPT_PO.getAmountAfter(), po.getAmountAfter());
        assertEquals(orderId, po.getOrderId());

        // 更新
        assertNotEquals(payChannel, PayChannel.ALIPAY);
        po.setPayChannel(PayChannel.ALIPAY);
        payBillService.updateById(po);
        po = payBillService.findById(id);
        assertNotNull(po);

        assertEquals(PayChannel.ALIPAY, po.getPayChannel());
    }

    @Test
    public void test_updateByOrderId() {
        Long id = 20011713451209L;
        String orderId = "********************";
        PayBillPo po = payBillService.findById(id);
        po.setId(null)
                .setOrderId(orderId)
                .setTaxStatus(TaxStatus.YES)
                .setTaxType(TaxType.NORMAL_TAX);
        payBillService.updateByOrderId(po);
    }

    @Test
    public void test_payBillBi() {
        SEARCH_PARAM.setCommIdList(Arrays.asList(COMM_ID_LIST_3342));
        List<PayBillBi> res = payBillService.payBillBi(SEARCH_PARAM);
        log.info("result={}", res);
    }


    @Test
    public void test_pointRecLog() {
        String orderId = "CZ202004072011310244";
        payBillService.pointRecLog(orderId, null);
    }

    @Test
    public void test_id() {
        int first = new Random(10).nextInt(8) + 1;
        int randNo = UUID.randomUUID().toString().hashCode();
        if (randNo < 0) {
            randNo = -randNo;
        }
        log.info("randNo={}", randNo);
        Long aLong = Long.valueOf(first + String.format("%016d", randNo));
        log.info("aLong={}", aLong);
    }

    @Test
    public void test_ChargerOrder() {
        String[] noList = new String[]{"101912500336"};
        List<ChargerOrder> chargerOrder = chargerOrderBizService.findChargerOrder(Arrays.asList(noList));
        log.info("order list={}", chargerOrder);
    }

    @Test
    public void test_updateStatus() {
        PayBillStatus srcStatus = PayBillStatus.PAID;
        PayBillStatus targetStatus = PayBillStatus.EXHAUST;

        // 插入一条数据
        String orderId = OPT_PO.getOrderId();
        OPT_PO.setStatus(srcStatus.getCode());
        payBillService.insert(OPT_PO);

        PayBillPo po = payBillService.findById(OPT_PO.getId());
        assertEquals(srcStatus.getCode(), po.getStatus().intValue());

        Integer i = payBillService.updateStatus(orderId, targetStatus);
        assertEquals(1, i.intValue());

        po = payBillService.findById(OPT_PO.getId());
        assertEquals(targetStatus.getCode(), po.getStatus().intValue());
    }

    @Test
    public void test_payBillView() {
        String orderId = "CZ202003111326410978";
        payBillService.payBillView(orderId);
    }

    @Test
    public void test_orderPointRecLog() {
        String orderNo = "240915496046";
        payBillService.orderPointRecLog(orderNo, true);
    }
}
