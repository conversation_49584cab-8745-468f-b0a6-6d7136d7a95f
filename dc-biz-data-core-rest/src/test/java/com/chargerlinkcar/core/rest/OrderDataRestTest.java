package com.chargerlinkcar.core.rest;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.dc.rest.OrderDataRest;
import com.cdz360.biz.model.trading.order.param.ListCusOrderBiParam;
import com.chargerlinkcar.core.BaseMockTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@Slf4j
@WebAppConfiguration
class OrderDataRestTest extends BaseMockTest {

    private static final Long UID = 59109L;
    private static final String SITE_ID = "1062615860467757056";

    private MockMvc mockMvc;

    @Autowired
    private WebApplicationContext wac;

    @BeforeEach
    public void setUp() throws Exception {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    void getCusOrderBiList() throws Exception {
        String url = "/dataCore/orderData/getCusOrderBiList";

        MockHttpServletRequestBuilder rb = null;

        ListCusOrderBiParam param = new ListCusOrderBiParam();
        param.setUidList(List.of(UID));
        param.setSiteId(SITE_ID);
        param.setCommIdChain("34474,34482,34483,34477");

        // 查询是否存在
        rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(param));
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(OrderDataRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("getCusOrderBiList")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();
    }

    @Test
    void orderOvertimeParkingBi() throws Exception {
        String url = "/dataCore/orderData/orderOvertimeParkingBi";

        MockHttpServletRequestBuilder rb = null;

        // 查询是否存在
        rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON);
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(OrderDataRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("orderOvertimeParkingBi")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andDo(print())
                .andReturn();
    }
}