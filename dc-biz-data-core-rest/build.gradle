plugins {
    id 'java'
    id "com.gorylenko.gradle-git-properties"
}

apply plugin: 'org.springframework.boot'

sourceSets {
    main {
        resources {
            srcDirs "src/main/resources", "src/main/java"
        }
    }
}
springBoot {
    buildInfo()
}

dependencies {

    implementation project(':dc-biz-common-model')
    implementation project(':dc-biz-oa-model')
    implementation project(':dc-biz-auth-model')
    implementation project(':dc-biz-user-model')
    implementation project(':dc-biz-tj-model')
    implementation project(':dc-biz-trading-model')
    implementation project(':dc-biz-trading-ds')
    implementation project(':dc-biz-ess-model')
    implementation project(':dc-biz-ess-ds')
    implementation project(':dc-biz-utils')

    implementation("com.cdz360.cloud:dc-data-sync:${dcCloudVersion}")
    implementation("com.cdz360.cloud:dc-data-cache-reader:${dcCloudVersion}")
    implementation("com.cdz360.cloud:dc-data-cache-writer:${dcCloudVersion}")

    implementation('org.springframework.boot:spring-boot-starter-actuator')
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-data-redis")
    implementation("org.springframework.boot:spring-boot-starter-data-mongodb")
    implementation('org.springframework.cloud:spring-cloud-starter-config')
    implementation('org.springframework.cloud:spring-cloud-starter-netflix-eureka-client')

    implementation('org.springframework.cloud:spring-cloud-starter-openfeign')
    implementation('org.springframework.cloud:spring-cloud-bus')
    implementation('org.springframework.cloud:spring-cloud-starter-bus-amqp')
//    implementation('org.springframework.cloud:spring-cloud-starter-sleuth')
//    implementation('org.springframework.cloud:spring-cloud-sleuth-zipkin')
    implementation 'io.micrometer:micrometer-tracing-bridge-brave'
    implementation 'io.zipkin.reporter2:zipkin-reporter-brave'
    implementation 'io.github.openfeign:feign-micrometer'
//    implementation "io.springfox:springfox-boot-starter:${swaggerVersion}"
    implementation("org.springdoc:springdoc-openapi-ui:${springdocVersion}")
    implementation("org.springdoc:springdoc-openapi-webflux-ui:${springdocVersion}")


    implementation("com.google.guava:guava:${guavaVersion}")

    implementation("com.playtika.reactivefeign:feign-reactor-spring-cloud-starter:${feignReactiveVersion}")
    implementation("com.playtika.reactivefeign:feign-reactor-webclient:${feignReactiveVersion}")
    implementation("com.playtika.reactivefeign:feign-reactor-cloud:${feignReactiveVersion}")
    implementation("com.playtika.reactivefeign:feign-reactor-spring-configuration:${feignReactiveVersion}")


    implementation("org.mybatis.spring.boot:mybatis-spring-boot-starter:${mybatisSpringVersion}")
    implementation("com.github.pagehelper:pagehelper:${pagehelperVersion}")
    implementation("com.github.pagehelper:pagehelper-spring-boot-starter:${pageHelperStarterVersion}")
    implementation("org.apache.commons:commons-pool2:${commonsPoolVersion}")


    // logstash间接依赖于jaxb, 在java10+以上的环境, 缺少jaxb-api时, logstash无法正常启动
    implementation("org.glassfish.jaxb:jaxb-runtime")

    implementation("mysql:mysql-connector-java:${mysqlConnectorVersion}")



}
