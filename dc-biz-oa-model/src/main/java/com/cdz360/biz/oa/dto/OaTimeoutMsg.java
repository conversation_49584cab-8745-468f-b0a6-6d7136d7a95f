package com.cdz360.biz.oa.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@AllArgsConstructor
public class OaTimeoutMsg {

    @Schema(description = "流程实例ID")
    private String procInstId;

    @Schema(description = "流程实例超期时间")
    private LocalDateTime time;
}
