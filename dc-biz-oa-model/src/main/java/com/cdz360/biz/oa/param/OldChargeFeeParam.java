package com.cdz360.biz.oa.param;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

@Deprecated(since = "20230310")
@ApiModel(value = "电价下发(旧数据结构)")
@Data
@Accessors(chain = true)
@ToString
public class OldChargeFeeParam {

    private static final SimpleDateFormat DATE_FORMAT =
        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @ApiModelProperty(value = "计费模板ID", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long priceSchemeId;

    @ApiModelProperty(value = "操作对象", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private OpTarget target;

    @ApiModelProperty(value = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> siteIdList;

    @ApiModelProperty(value = "场站名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> siteNameList;

    @ApiModelProperty(value = "桩编号列表", notes = "下发对象是充电桩需要指定")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> evseNoList;

    @ApiModelProperty(value = "下发时间", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ExeTime exeTime;

    @ApiModelProperty(value = "执行时间", notes = "下发定时需要指定")
    @JsonInclude(JsonInclude.Include.NON_NULL)
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String exeDate;

    @ApiModelProperty(value = "备注")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String note;

    @Schema(description = "附件链接列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> attachmentLink;

    public enum OpTarget {
        SITE("整个站点"),
        EVSE("个别充电桩");

        private String desc;

        OpTarget(String desc) {
            this.desc = desc;
        }
    }

    public enum ExeTime {
        IN_TIME("立即"),
        SPECIAL_DATE("指定日期"),
        SPECIAL_TIME("指定时刻");

        private String desc;

        ExeTime(String desc) {
            this.desc = desc;
        }
    }

    public static void checkField(OldChargeFeeParam param) {
        if (null == param) {
            throw new DcArgumentException("入参无效");
        }

        if (null == param.priceSchemeId) {
            throw new DcArgumentException("请选择计费模板");
        }

        if (null == param.target) {
            throw new DcArgumentException("请选择操作对象");
        }

        if (null == param.exeTime) {
            throw new DcArgumentException("请选择下发时间");
        }

        if (OpTarget.EVSE.equals(param.target) && CollectionUtils.isEmpty(param.evseNoList)) {
            throw new DcArgumentException("请选择下发的桩");
        }
        if ((CollectionUtils.isEmpty(param.siteIdList) || CollectionUtils.isEmpty(
            param.siteNameList))) {
            throw new DcArgumentException("请选择场站");
        }

        if (ExeTime.SPECIAL_DATE.equals(param.exeTime) && null == param.exeDate) {
            try {
                DATE_FORMAT.parse(param.getExeDate());
            } catch (ParseException e) {
                throw new DcArgumentException("指定日期格式不对 格式yyyy-MM-dd");
            }
            throw new DcArgumentException("请选择指定日期");
        }

        if (ExeTime.SPECIAL_TIME.equals(param.exeTime) && null == param.exeDate) {
            try {
                DATE_FORMAT.parse(param.getExeDate());
            } catch (ParseException e) {
                throw new DcArgumentException("指定日期格式不对 格式yyyy-MM-dd");
            }
            throw new DcArgumentException("请选择指定时刻");
        }
    }
}
