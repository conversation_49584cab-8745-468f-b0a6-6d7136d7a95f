package com.cdz360.biz.oa.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "OA后付费账单概要信息")
public class OaSettlementBillDto {

    @Schema(description = "账单号")
    private String billNo;

    @Schema(description = "账单名称")
    private String billName;

    @Schema(description = "账期开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date startDateDay;

    @Schema(description = "账期结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date endDateDay;

    @Schema(description = "账单电量, 单位: kW·h")
    private BigDecimal elec;

    @Schema(description = "账单总额, 单位: 元")
    private BigDecimal totalFee;

    @Schema(description = "账单服务费, 单位: 元")
    private BigDecimal servFee;

    @Schema(description = "账单电费, 单位: 元")
    private BigDecimal elecFee;

    @Schema(description = "其他费用, 单位: 元")
    private BigDecimal otherFee;

    @Schema(description = "关联的场站名称")
    private List<String> siteNameList;

    @Schema(description = "关联的场站编号")
    private List<String> siteNoList;
}
