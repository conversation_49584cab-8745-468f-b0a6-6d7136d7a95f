package com.cdz360.biz.oa.dto;

import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.biz.model.cus.settlement.vo.SettlementVo;
import com.cdz360.biz.model.oa.param.BillingParam;
import com.cdz360.biz.oa.dto.account.CorpInvoiceDto;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class BillingOaDto extends BillingParam {

    private List<SettlementVo> billList;
    private InvoicingMode invoiceWay;
    private CorpInvoiceDto invoiceInfo;
    private String siteNameInfo;
//    private String corpDigest;


}
