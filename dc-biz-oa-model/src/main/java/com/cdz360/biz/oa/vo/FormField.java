package com.cdz360.biz.oa.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

@Data
@Accessors(chain = true)
public class FormField {

    private String id;
    private String name;
    private String type;
    private Object value;
    private boolean required;
    private boolean readOnly;
    private boolean overrideId;
    private String placeholder;
    private Map<String, Object> params;

}
