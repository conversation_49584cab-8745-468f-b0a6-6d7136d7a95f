package com.cdz360.biz.oa.param;

import com.cdz360.base.utils.JsonUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 余额充值流程相关参数
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class RechargeParam extends OaProcessBaseParam implements Serializable {

    private static final long serialVersionUID = 3253303514585081112L;

    /**
     * 申请记录 t_balance_application.id
     */
    private Long dataId;

    @Schema(description = "客户手机号")
    private String phone;

    @Schema(description = "客户名称")
    private String username;

    @Schema(description = "场站名称")
    private List<String> siteNameList;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}
