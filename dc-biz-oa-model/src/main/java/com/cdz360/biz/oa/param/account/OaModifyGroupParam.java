package com.cdz360.biz.oa.param.account;

import com.cdz360.base.utils.JsonUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OaModifyGroupParam {

    private String gid;

    private String name;

    private String type;

    @Schema(description = "操作人uid", required = true)
    private Long opUid;

    @Schema(description = "成员账号")
    private List<OaModifyAccountParam> userAccountList;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
