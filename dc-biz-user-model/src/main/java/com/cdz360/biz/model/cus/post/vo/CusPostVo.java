package com.cdz360.biz.model.cus.post.vo;

import com.cdz360.biz.model.cus.post.dto.CusPostDto;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "用户评论信息查看")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class CusPostVo extends CusPostDto {

    @Schema(description = "评论用户手机号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cusPhone;

    @Schema(description = "充电场站名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;
}
