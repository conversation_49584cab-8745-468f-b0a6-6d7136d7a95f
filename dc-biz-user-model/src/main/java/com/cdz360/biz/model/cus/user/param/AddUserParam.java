package com.cdz360.biz.model.cus.user.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class AddUserParam {

    private Long topCommId;

    @Schema(description = "商户id")
    private Long commercialId;

    @Schema(description = "手机号地区编码")
    private String nationalCode;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "密码")
    private String password;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "头像")
    private String avata;

    private Long sysUid;

    private String sysUserName;

    public AddUserParam() {
    }

    public AddUserParam(Long topCommId, Long commercialId, String nationalCode, String mobile,
        String password, String username, String email, String avata) {
        this.topCommId = topCommId;
        this.commercialId = commercialId;
        this.nationalCode = nationalCode;
        this.mobile = mobile;
        this.password = password;
        this.username = username;
        this.email = email;
        this.avata = avata;
    }

}
