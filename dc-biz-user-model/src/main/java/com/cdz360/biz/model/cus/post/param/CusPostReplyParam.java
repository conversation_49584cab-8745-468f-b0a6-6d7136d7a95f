package com.cdz360.biz.model.cus.post.param;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.vo.BaseObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "用户评论回复")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class CusPostReplyParam extends BaseObject {

    @Schema(description = "用户评论ID", required = true)
    @NotNull(message = "用户评论ID不能为空")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long id;

    @Schema(description = "充电订单号 系统操作日志使用", required = true)
    @NotNull(message = "充电订单号不能为空")
    private String orderNo;

    @Schema(description = "评论公开状态: true公开，false不公开", required = true)
    @NotNull(message = "open 不能为 null")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean open;

    @Schema(description = "评论回复内容", required = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @NotEmpty(message = "评论回复内容不能为空")
    private String replyContent;

    @Schema(description = "标签列表", required = true)
    @NotNull(message = "标签列表不能为空")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> tags;

    @Schema(description = "操作人ID. sys_user.id", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long opUid;

    @Schema(description = "操作人姓名", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String opName;

    public static void check(CusPostReplyParam param) {
        if (null == param.getId()) {
            throw new DcArgumentException("用户评论ID不能为空");
        }

        if (null == param.getOpen()) {
            throw new DcArgumentException("评论公开状态不能为空");
        }

//        if (StringUtils.isBlank(param.getReplyContent())) {
//            throw new DcArgumentException("评论回复内容不能为空");
//        }
    }
}
