package com.cdz360.biz.model.cus.corp.dto;

import com.cdz360.biz.model.cus.corp.type.LimitCycle;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "批量新增授信账户返回结果条目")
public class BatchAddCreditResultItem {

    private String phone;

    private String name;

    private LimitCycle durType;

    private BigDecimal limitMoney;

    private String error;
}
