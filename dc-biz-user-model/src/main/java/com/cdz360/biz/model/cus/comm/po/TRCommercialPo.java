package com.cdz360.biz.model.cus.comm.po;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2019/11/14 13:51
 */
@Data
@EqualsAndHashCode
@ToString(callSuper = true)
@Accessors(chain = true)
public class TRCommercialPo {
    @Schema(description = "商户Id")
    private Long id;

    @Schema(description = "父级商户Id")
    private Long pid;

    @Schema(description = "商户级别")
    private Integer commLevel;

    @Schema(description = "商户号")
    private String merchants;

    @Schema(description = "商户类型")
    private Integer commType;

    @Schema(description = "集团商户ID")
    private Long topCommId;

    @Schema(description = "商户全称")
    private String commName;

    @Schema(description = "商户简称")
    private String shortName;

    @Schema(description = "商户ID链", hidden = true)
    private String idChain;
}
