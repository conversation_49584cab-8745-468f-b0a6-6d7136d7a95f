package com.cdz360.biz.model.cus.app.po;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.biz.model.common.po.BasePo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppDeviceInfoPo extends BasePo {

    @Schema(description = "客户端类型. ANDROID_APP: 安卓APP; IOS_APP: iOS APP", example = "ANDROID_APP")
    private AppClientType appType;

    @Schema(description = "客户ID")
    private Long uid;

    @Schema(description = "推送的deviceToken")
    private String deviceToken;

}
