package com.cdz360.biz.model.cus.post.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum CusPostLevel implements DcEnum {
    UNKNOWN(0, "未知"),
    LEVEL_1(1, "1星，很不满意"),
    LEVEL_2(2, "2星，不满意"),
    LEVEL_3(3, "3星，一般"),
    LEVEL_4(4, "4星，满意"),
    LEVEL_5(5, "5星，非常满意")
    ;

    @JsonValue
    private int code;

    private String desc;

    CusPostLevel(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static CusPostLevel valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (CusPostLevel level : values()) {
            if (level.code == code) {
                return level;
            }
        }

        return CusPostLevel.UNKNOWN;
    }
}
