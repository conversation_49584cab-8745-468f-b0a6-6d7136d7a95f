package com.cdz360.biz.model.cus.site.vo;

import com.cdz360.biz.model.cus.site.po.SiteWhitelistPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * SiteWhitelistVo
 *
 * @since 12/25/2020 5:06 PM
 * <AUTHOR>
 */
@Schema(description = "场站白名单")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SiteWhitelistVo extends SiteWhitelistPo {
    private String corpName;
    private String corpPhone;
    private String userName;
    private String userPhone;
}