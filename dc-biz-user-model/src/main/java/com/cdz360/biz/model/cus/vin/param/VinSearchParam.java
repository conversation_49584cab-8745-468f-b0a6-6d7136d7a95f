package com.cdz360.biz.model.cus.vin.param;

import com.cdz360.base.model.base.param.BaseListParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import java.util.Locale;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * VinSearchParam
 *  vin搜索
 * @since 2019/5/15 9:11
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Schema(description = "VIN 列表查询茶树")
public class VinSearchParam extends BaseListParam {
    private String startTime;

    @Schema(description = "客户ID")
    private Long userId;

    private String endTime;

    @Schema(description = "VIN状态")
    private Integer status;

    @Schema(description = "VIN 查询,支持模糊查询")
    private String vin;

    @Schema(description = "车牌号查询,支持模糊查询")
    private String carNo;

//    private Integer pageNum;
//    private Integer pageSize;
    private String token;

    @Schema(description = "企业客户名称,支持模糊查询")
    private String corpName;

    @Schema(description = "客户名称,支持模糊查询")
    private String userName;//用于like查找客户名称,当不传入userId，使用

    @Schema(description = "子商户Id")
    private Long subCommId;

    @Schema(description = "商户ID链")
    private String commIdChain;

    @Schema(description = "场站名称")
    private String siteName;

    @Schema(description = "车队名称,支持模糊查询")
    private String carDepart;

    @Schema(description = "线路,支持模糊查询")
    private String carLineNum;

    @Schema(description = "车辆自编号,支持模糊查询")
    private String carNum;

    @Schema(description = "客户手机号,支持模糊查询")
    private String cusPhone;

    private Long leaderCommId;

    private List<String> vinList;

    /**
     * 区域语言信息
     */
    private Locale locale;
}