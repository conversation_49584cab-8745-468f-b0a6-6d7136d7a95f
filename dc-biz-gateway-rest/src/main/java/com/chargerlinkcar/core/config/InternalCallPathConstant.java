package com.chargerlinkcar.core.config;

//import com.cdz360.base.utils.JsonUtils;
//import java.util.HashMap;
//import java.util.Map;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
///**
// * <AUTHOR>
// */
//@Slf4j
//@Component
//public class InternalCallPathConstant {
//
//    public static Map<String, Boolean> path_map = new HashMap<String, Boolean>();
//
//    /**
//     * 只能内部服务调用的权限中心 接口列表
//     */
//    @Value("${auth.ctnter.internalCallPath}")
//    private String paths;
//
//    private void initMap() {
//        if (path_map.size() == 0) {
//            if (StringUtils.isNotBlank(paths)) {
//                String[] arrs = paths.split(",");
//                log.info("arrs:::" + JsonUtils.toJsonString(arrs));
//                for (String string : arrs) {
//                    String urlStr = string.trim();
//                    log.info("urlStr:::" + urlStr);
//                    path_map.put(urlStr, Boolean.TRUE);
//                }
//            }
//        }
//    }
//
//    /**
//     * 后缀地址
//     *
//     * @param path
//     * @return
//     */
//    public Boolean isInternalCallPath(String path) {
//        this.initMap();
//        log.info("isPayNotifyPath:::" + path);
//        Boolean isExc = path_map.get(path);
//        log.info("isPayNotifyPath boolean:::" + isExc);
//        return isExc == null ? false : isExc;
//    }
//
//}
