package com.chargerlinkcar.core.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.Objects;

public interface LogicDeletable {
    Integer getStatus();

    void setStatus(Integer status);

    @JsonIgnore
    default boolean isFreeze() {
        return Objects.equals(GlobalConst.FREEZE, getStatus());
    }

    default void setFreeze(Boolean freeze) {
        if (freeze != null)
            setStatus(freeze ? GlobalConst.FREEZE : GlobalConst.NORMAL);
    }
}
