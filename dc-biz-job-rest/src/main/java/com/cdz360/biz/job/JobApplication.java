package com.cdz360.biz.job;

import com.netflix.discovery.EurekaClient;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import reactivefeign.spring.config.EnableReactiveFeignClients;
import reactor.core.publisher.Hooks;

/**
 * <AUTHOR>
 */
@EnableDiscoveryClient
@SpringBootApplication
@EnableReactiveFeignClients(basePackages = {
    "com.cdz360.biz.utils.feign.*",
    "com.cdz360.biz.job.client.reactor"
})
@EnableFeignClients(basePackages = {
    "com.cdz360.biz.job.client",
    "com.chargerlinkcar.framework.common.feign",
    "com.cdz360.biz.utils.feign.iot"
})
@Slf4j
@ImportResource(locations = {"applicationcontext-xxl-job.xml"})
@ComponentScan(basePackages = {"com.cdz360.biz",
    "com.cdz360.charger",
    "com.chargerlinkcar.framework.common"})
@EnableAsync
@EnableScheduling
public class JobApplication {

    @Autowired
    private EurekaClient discoveryClient;

    /**
     * 主函数
     */
    public static void main(String[] args) {
        Hooks.enableAutomaticContextPropagation();  // 输出调用链traceId
        SpringApplication.run(JobApplication.class, args);
    }


    @PreDestroy
    public void destroy() {
        log.info("going to shutdown");

        //DiscoveryManager.getInstance().shutdownComponent();
        discoveryClient.shutdown();
        log.info("eureka de-registered.....");
    }
}
