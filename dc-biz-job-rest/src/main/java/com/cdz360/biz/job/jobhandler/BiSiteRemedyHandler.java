package com.cdz360.biz.job.jobhandler;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.job.client.DataCoreClient;
import com.cdz360.biz.job.domain.BiPlugSiteRequest;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.micrometer.tracing.Span;
import io.micrometer.tracing.Tracer;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * BiSiteRemedyHandler
 *
 * <AUTHOR>
 * @since 4/9/2020 8:14 AM
 */
@Component
@Slf4j
public class BiSiteRemedyHandler //extends IJobHandler
{

    @Autowired
    private Tracer tracer;

    @Autowired
    private DataCoreClient dataCoreClient;

    //    @Override
    @XxlJob(value = "biSiteRemedyHandler")
    //    public ReturnT<String> 
    public ReturnT<String> execute() throws Exception {
        Span span = this.tracer.nextSpan().name("xxlJob");
        ReturnT<String> result = ReturnT.SUCCESS;
        try (Tracer.SpanInScope ws = this.tracer.withSpan(span.start())) {
            String param = XxlJobHelper.getJobParam();
            log.info("场站统计补救job执行, param: {}", param);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date;
            String strSite = null;
            List<String> siteIds = null;

            if (StringUtils.isBlank(param)) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(new Date());
                calendar.clear(Calendar.MILLISECOND);
                calendar.clear(Calendar.SECOND);
                calendar.clear(Calendar.MINUTE);
                calendar.add(Calendar.HOUR, -1);
                date = calendar.getTime();

                log.info("统计前一小时的数据, date: {}", dateFormat.format(date));
            } else {
                BiPlugSiteRequest req = JsonUtils.fromJson(param, BiPlugSiteRequest.class);
                date = dateFormat.parse(req.getTime());
                strSite = req.getSite();
                siteIds = req.getSites();
            }

            IotAssert.isNotNull(date, "执行失败，无法转换日期");

            BaseResponse res = dataCoreClient.asyncBiSiteRemedyHourly(date, strSite, siteIds);
            if (res == null || res.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
                XxlJobHelper.handleFail(
                    "执行任务失败 失败. " + this.getClass().getSimpleName());
                result = ReturnT.FAIL;
            }
        } catch (Exception e) {
            log.error("执行任务失败. error= {}", e.getMessage());
            XxlJobHelper.log("执行任务失败. {}", this.getClass().getSimpleName(),
                e.getMessage());
            XxlJobHelper.handleFail(
                "执行任务失败. " + this.getClass().getSimpleName());
            result = ReturnT.FAIL;
        } finally {
            span.end();
        }
        return result;
    }
}
