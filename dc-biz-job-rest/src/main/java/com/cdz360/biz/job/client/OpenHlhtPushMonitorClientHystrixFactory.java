package com.cdz360.biz.job.client;


import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.RestUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class OpenHlhtPushMonitorClientHystrixFactory implements
    FallbackFactory<OpenHlhtPushMonitorClient> {

    @Override
    public OpenHlhtPushMonitorClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_OPEN_SIM,
            throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_OPEN_SIM,
            throwable.getStackTrace());

        return new OpenHlhtPushMonitorClient() {
            @Override
            public Mono<String> getDemandInfoList() {
                log.error("【服务熔断】 集成商获取需求指标信息（2.1）. Service = {}, api = refreshAllData.",
                    DcConstants.KEY_FEIGN_OPEN_HLHT_PUSH_MONITOR);
                return Mono.empty();
            }

            @Override
            public Mono<String> aueicPutRtuPowerCurve() {
                log.error("【服务熔断】 集成商负荷数据上报（2.3）. Service = {}, api = refreshAllData.",
                    DcConstants.KEY_FEIGN_OPEN_HLHT_PUSH_MONITOR);
                return Mono.empty();
            }

            @Override
            public Mono<String> aueicRtuForecastPowerCurve() {
                log.error("【服务熔断】 集成商日前预测负荷数据上报（2.4）. Service = {}, api = refreshAllData.",
                    DcConstants.KEY_FEIGN_OPEN_HLHT_PUSH_MONITOR);
                return Mono.empty();
            }

            @Override
            public Mono<String> aueicGetDemandConsInfoList() {
                log.error("【服务熔断】 集成商获取用户需求指标信息（2.11）. Service = {}, api = refreshAllData.",
                    DcConstants.KEY_FEIGN_OPEN_HLHT_PUSH_MONITOR);
                return Mono.empty();
            }
        };
    }
}
