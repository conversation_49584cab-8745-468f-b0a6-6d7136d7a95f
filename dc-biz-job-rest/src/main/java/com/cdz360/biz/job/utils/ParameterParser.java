package com.cdz360.biz.job.utils;

import com.cdz360.base.utils.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ParameterParser {

    public static Map<String, String> parse(String strParams) {
        if (StringUtils.isBlank(strParams)) {
            return Map.of();
        }
        Map<String, String> result = new HashMap<>();
        String[] kvs = strParams.split("&");
        for (String kv : kvs) {
            if (StringUtils.isBlank(kv)) {
                continue;
            }
            String[] toks = kv.strip().split("=");
            if (toks == null || toks.length < 1) {
                // pass
            }
            else if (toks.length > 1) {
                result.put(toks[0], toks[1]);
            } else {
                result.put(toks[0], null);
            }
        }
        return result;
    }


//    public static void main(String[] args) {
//        String str = "siteIds=11&startDate=&endDate=";
//        var params = ParameterParser.parse(str);
//        System.out.println("xxxxxxxxxxxxxxxxxxxxxxxxx");
//        System.out.println(params);
//        System.out.println("aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa");
//        List<String> siteIds =  null;
//        if (params.containsKey("siteIds")
//        ) {
//            siteIds = Arrays.stream(params.get("siteIds").split(",")).collect(Collectors.toList());
//        }
//        System.out.println(siteIds);
//        System.out.println("bbbbbbbbbbbbbbbbbbb");
//    }
}
