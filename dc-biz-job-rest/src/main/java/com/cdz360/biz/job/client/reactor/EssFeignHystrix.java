package com.cdz360.biz.job.client.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.RestUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@SuppressWarnings("ALL")
public class EssFeignHystrix implements FallbackFactory<EssFeignClient> {

    @Override
    public EssFeignClient apply(Throwable throwable) {
        log.error("err = {}", throwable.getMessage(), throwable);
        return new EssFeignClient() {
            @Override
            public Mono<BaseResponse> syncEssUserRtData2Mongo() {
                log.error("服务[{}]接口熔断 - 指定同步数据日期",
                    DcConstants.KEY_FEIGN_IOT_ESS);
                return Mono.just(RestUtils.serverBusy());
            }
        };
    }
}
