package com.cdz360.biz.job.jobhandler;

import com.cdz360.biz.utils.feign.auth.AuthCorpWxAppFeignClient;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.micrometer.tracing.Span;
import io.micrometer.tracing.Tracer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 刷新系统用户的团队标签(手动操作)
 */
@Slf4j
@Component
public class RefreshSysUserTeamCatalogHandler //extends IJobHandler
{

    @Autowired
    private Tracer tracer;

    @Autowired
    private AuthCorpWxAppFeignClient authCorpWxAppFeignClient;

    //    @Override
    @XxlJob(value = "refreshSysUserTeamCatalog")
    //    public ReturnT<String> 
    public ReturnT<String> execute() throws Exception {
        Span span = this.tracer.nextSpan().name("xxlJob");
        ReturnT<String> result = ReturnT.SUCCESS;
        String param = XxlJobHelper.getJobParam();
        try (Tracer.SpanInScope ws = this.tracer.withSpan(span.start())) {
            authCorpWxAppFeignClient.refreshTeamCatalog()
                .subscribe();
        } catch (Exception e) {
            log.error("执行任务失败. error= {}", e.getMessage());
            XxlJobHelper.log("执行任务失败. {}", this.getClass().getSimpleName(),
                e.getMessage());
            XxlJobHelper.handleFail(
                "执行任务失败. " + this.getClass().getSimpleName());
            result = ReturnT.FAIL;
        } finally {
            span.end();
        }
        return result;
    }
}
