package com.cdz360.biz.job.jobhandler;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.job.client.DataCoreClient;
import com.cdz360.biz.job.domain.BiPlugSiteRequest;
import com.cdz360.biz.model.trading.order.type.BiDependOnType;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.micrometer.tracing.Span;
import io.micrometer.tracing.Tracer;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * BiSiteHandler
 *
 * <AUTHOR>
 * @since 4/5/2020 4:26 PM
 */
@Component
@Slf4j
public class BiSiteHandler //extends IJobHandler
{

    @Autowired
    private Tracer tracer;

    @Autowired
    private DataCoreClient dataCoreClient;

    //    @Override
    @XxlJob(value = "biSiteHandler")
    //    public ReturnT<String> 
    public ReturnT<String> execute() throws Exception {
        Span span = this.tracer.nextSpan().name("xxlJob");
        ReturnT<String> result = ReturnT.SUCCESS;
        try (Tracer.SpanInScope ws = this.tracer.withSpan(span.start())) {
            String param = XxlJobHelper.getJobParam();
            log.info("场站统计job执行, param: {}", param);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String strUnit;
            String strDate;
            String strSite = null;
            List<String> siteIds = null;
            BiDependOnType type = null;

            if (StringUtils.isBlank(param)) {
                strUnit = "h";
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(new Date());
                calendar.add(Calendar.HOUR, -1);
                Date date = calendar.getTime();

                strDate = dateFormat.format(date);
                log.info("统计前一小时的数据, date: {}", strDate);
            } else {
                BiPlugSiteRequest req = JsonUtils.fromJson(param, BiPlugSiteRequest.class);
                strUnit = req.getUnit();
                strDate = req.getTime();
                strSite = req.getSite();
                type = req.getType();
                siteIds = req.getSites();
            }

            IotAssert.isNotNull(strUnit, "时间不正确");
            IotAssert.isNotNull(strDate, "单位不正确");

            Date date = dateFormat.parse(strDate);
            IotAssert.isNotNull(date, "执行失败，无法转换日期");
            BaseResponse res = null;
            if ("h".equalsIgnoreCase(strUnit)) {
                res = dataCoreClient.asyncBiSiteHourly(type, date, strSite, siteIds);
            } else if ("d".equalsIgnoreCase(strUnit)) {
                res = dataCoreClient.asyncBiSiteDaily(type, date, strSite, siteIds);
            } else if ("m".equalsIgnoreCase(strUnit)) {
                res = dataCoreClient.asyncBiSiteMonthly(type, date, strSite, siteIds);
            } else {
                IotAssert.isTrue(false, "不支持此统计单位: " + strUnit);
                XxlJobHelper.handleFail("执行任务失败 失败. " + this.getClass().getSimpleName());
            }
            if (res == null || res.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
                XxlJobHelper.handleFail(
                    "执行任务失败 失败. " + this.getClass().getSimpleName());
                result = ReturnT.FAIL;
            }
        } catch (Exception e) {
            log.error("执行任务失败. error= {}", e.getMessage());
            XxlJobHelper.log("执行任务失败. {}", this.getClass().getSimpleName(),
                e.getMessage());
            XxlJobHelper.handleFail(
                "执行任务失败. " + this.getClass().getSimpleName());
            result = ReturnT.FAIL;
        } finally {
            span.end();
        }
        return result;
    }
}