package com.cdz360.biz.job.jobhandler;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.job.client.DataCoreFeignClient;
import com.cdz360.biz.job.client.UserFeignClient;
import com.chargerlinkcar.framework.common.domain.OrderOvertimeParkingBi;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.micrometer.tracing.Span;
import io.micrometer.tracing.Tracer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 超停订单统计处理
 */
@Slf4j
@Component
//@JobHandler(value = "orderOvertimeParkingBi")
public class OrderOvertimeParkingBiHandler //extends IJobHandler
{

    @Autowired
    private Tracer tracer;


    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private UserFeignClient userFeignClient;


    @XxlJob(value = "orderOvertimeParkingBi")
    //    public ReturnT<String> 
    public ReturnT<String> execute() throws Exception {
        Span span = this.tracer.nextSpan().name("xxlJob");
        ReturnT<String> result = ReturnT.SUCCESS;
        String param = XxlJobHelper.getJobParam();
        try (Tracer.SpanInScope ws = this.tracer.withSpan(span.start())) {
            ListResponse<OrderOvertimeParkingBi> parkingBi = dataCoreFeignClient.orderOvertimeParkingBi();
            FeignResponseValidate.check(parkingBi);

            BaseResponse res = userFeignClient.overtimeParkingBi(parkingBi.getData());
            FeignResponseValidate.check(res);
        } catch (Exception e) {
            log.error("执行任务失败. error= {}", e.getMessage());
            XxlJobHelper.log("执行任务失败. {}", this.getClass().getSimpleName(),
                e.getMessage());
            XxlJobHelper.handleFail(
                "执行任务失败. " + this.getClass().getSimpleName());
            result = ReturnT.FAIL;
        } finally {
            span.end();
        }
        return result;
    }

}
