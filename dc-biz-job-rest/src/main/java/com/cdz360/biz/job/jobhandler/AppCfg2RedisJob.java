package com.cdz360.biz.job.jobhandler;

import com.cdz360.biz.job.client.AuthWebFeignClient;
import com.cdz360.biz.job.service.ITaskLogService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.micrometer.tracing.Span;
import io.micrometer.tracing.Tracer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2019/10/12
 */
@Component
@Slf4j
public class AppCfg2RedisJob //extends IJobHandler
{

    @Autowired
    private Tracer tracer;

    @Autowired
    private ITaskLogService taskLogService;

    @Autowired
    private AuthWebFeignClient authWebFeignClient;


    //    @Override
    @XxlJob(value = "appCfg2Redis")
    //    public ReturnT<String> 
    public ReturnT<String> execute() throws Exception {
        Span span = this.tracer.nextSpan().name("xxlJob");
        ReturnT<String> result = ReturnT.SUCCESS;
        String msg = "";
        try (Tracer.SpanInScope ws = this.tracer.withSpan(span.start())) {
            String param = XxlJobHelper.getJobParam();
            log.info("执行同步app信息到redis的任务. param = {}", param);
            XxlJobHelper.log("AppCfg2Redis==>APP缓存配置任务开始! param = {}", param);
            log.info("AppCfg2Redis==>开始APP缓存配置任务!");
            authWebFeignClient.appCfg2Redis();
            XxlJobHelper.log("AppCfg2Redis==>APP缓存配置任务完成!");

        } catch (Exception e) {
            msg = e.getMessage();
            log.error("执行任务失败. error= {}", e.getMessage());
            XxlJobHelper.log("执行任务失败. {}", this.getClass().getSimpleName(),
                e.getMessage());
            XxlJobHelper.handleFail(
                "执行任务失败. " + this.getClass().getSimpleName());
            result = ReturnT.FAIL;
        } finally {
            span.end();
        }
        taskLogService.insertTaskLog(msg, AppCfg2RedisJob.class.getName());
        return result;
    }
}