package com.cdz360.biz.job.jobhandler;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.utils.feign.iot.MeterFeignClient;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.micrometer.tracing.Span;
import io.micrometer.tracing.Tracer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * MeterStatusRefreshHandle
 *
 * <AUTHOR>
 * @since 9/24/2020 9:23 AM
 */
@Component
@Slf4j
public class MeterStatusRefreshHandle //extends IJobHandler
{

    private static final Integer TTL = 20 * 60;
    @Autowired
    private Tracer tracer;
    @Autowired
    private MeterFeignClient meterfeignClient;

    //    @Override
    @XxlJob(value = "meterStatusRefreshHandle")
    //    public ReturnT<String> 
    public ReturnT<String> execute() throws Exception {
        Span span = this.tracer.nextSpan().name("xxlJob");
        ReturnT<String> result = ReturnT.SUCCESS;
        try (Tracer.SpanInScope ws = this.tracer.withSpan(span.start())) {
            String param = XxlJobHelper.getJobParam();
            Integer ttl;
            try {
                ttl = Integer.valueOf(param);
            } catch (Exception e) {
                ttl = TTL;
            }
            ObjectResponse<Integer> integerObjectResponse = meterfeignClient.refreshMeterStatus(
                ttl);
        } catch (Exception e) {
            log.error("执行任务失败. error= {}", e.getMessage());
            XxlJobHelper.log("执行任务失败. {}", this.getClass().getSimpleName(),
                e.getMessage());
            XxlJobHelper.handleFail(
                "执行任务失败. " + this.getClass().getSimpleName());
            result = ReturnT.FAIL;
        } finally {
            span.end();
        }
        return result;

    }
}