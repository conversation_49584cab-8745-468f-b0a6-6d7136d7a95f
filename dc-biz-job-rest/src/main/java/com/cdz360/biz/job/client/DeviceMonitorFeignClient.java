package com.cdz360.biz.job.client;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.biz.job.domain.GwTimeoutMsgRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * DeviceMonitorFeignClient
 *  网关登录超时告警
 * <AUTHOR>
 * @since 2019/8/21 10:16
 *    <EMAIL>
 */
@FeignClient(DcConstants.KEY_FEIGN_IOT_MONITOR)
public interface DeviceMonitorFeignClient {
    @PostMapping("/api/alarm/sendSysLeveAlarm")
    BaseResponse sendSysLeveAlarm(@RequestBody GwTimeoutMsgRequest request);
}
