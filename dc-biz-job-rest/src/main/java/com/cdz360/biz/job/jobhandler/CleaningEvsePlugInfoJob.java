package com.cdz360.biz.job.jobhandler;

import com.cdz360.biz.job.client.DataCoreClient;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.micrometer.tracing.Span;
import io.micrometer.tracing.Tracer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CleaningEvsePlugInfoJob //extends IJobHandler
{

    @Autowired
    private Tracer tracer;

    @Autowired
    private DataCoreClient dataCoreClient;

    //    @Override
    @XxlJob(value = "CleaningEvsePlugInfoJob")
    //    public ReturnT<String> 
    public ReturnT<String> execute() throws Exception {
        Span span = this.tracer.nextSpan().name("xxlJob");
        ReturnT<String> result = ReturnT.SUCCESS;
        try (Tracer.SpanInScope ws = this.tracer.withSpan(span.start())) {
            String param = XxlJobHelper.getJobParam();

            XxlJobHelper.log("CleaningEvsePlugInfoJob==>管理平台2020-06版本场站桩枪数据清洗开始!");
            log.info("管理平台2020-06版本场站桩枪数据清洗开始!");
            dataCoreClient.upgradeCleaningEvsePlugInfo();
            XxlJobHelper.log("管理平台2020-06版本场站桩枪数据清洗完成!");
//        } catch (Exception e) {
//            log.error("管理平台2020-06版本场站桩枪数据清洗异常 err: {}", e.getMessage(), e);
//            XxlJobHelper.log("管理平台2020-06版本场站桩枪数据清洗异常", e);
//            XxlJobHelper.handleFail("执行任务失败 失败. " + this.getClass().getSimpleName());
//        }
        } catch (Exception e) {
            log.error("执行任务失败. error= {}", e.getMessage());
            XxlJobHelper.log("执行任务失败. {}", this.getClass().getSimpleName(),
                e.getMessage());
            XxlJobHelper.handleFail(
                "执行任务失败. " + this.getClass().getSimpleName());
            result = ReturnT.FAIL;
        } finally {
            span.end();
        }
        return result;
    }
}
