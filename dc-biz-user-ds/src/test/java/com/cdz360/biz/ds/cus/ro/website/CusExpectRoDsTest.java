package com.cdz360.biz.ds.cus.ro.website;

import com.cdz360.biz.ds.cus.CusDsTestBase;
import com.cdz360.biz.ds.cus.ro.website.ds.CusExpectRoDs;
import com.cdz360.biz.ds.cus.rw.website.ds.CusExpectRwDs;
import com.cdz360.biz.model.cus.website.po.CusExpectPo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;

@Slf4j
class CusExpectRoDsTest extends CusDsTestBase {

    private static Long ID = 111L;

    private static CusExpectPo CUS_EXPECT_PO;

    @Autowired
    private CusExpectRoDs cusExpectRoDs;

    @Autowired
    private CusExpectRwDs cusExpectRwDs;

    @BeforeEach
    public void init() {
        CUS_EXPECT_PO = new CusExpectPo();
        CUS_EXPECT_PO
                .setContext(List.of("123456", "789456"))
                .setMind("这是理想")
                .setName("这是姓名")
                .setPhone("12345678932")
                .setProvince("123456")
                .setCity("987456");
        cusExpectRwDs.insertCusExpect(CUS_EXPECT_PO);

        ID = CUS_EXPECT_PO.getId();
    }

    @Test
    void getById() {
        CusExpectPo expectPo = cusExpectRoDs.getById(ID);
        assertNotNull(expectPo, "初始化对象");
    }

}