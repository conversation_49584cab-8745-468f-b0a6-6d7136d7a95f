package com.cdz360.biz.ds.cus.rw.SiteAuthVin.ds;

import com.cdz360.biz.ds.cus.CusDsTestBase;
import com.cdz360.biz.ds.cus.ro.SiteAuthVin.ds.SiteAuthVinLogRoDs;
import com.cdz360.biz.model.vin.po.SiteAuthVinLogPo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * SiteAuthVinLogRwDsTest
 *
 * @since 8/17/2021 11:21 AM
 * <AUTHOR>
 */
public class SiteAuthVinLogRwDsTest extends CusDsTestBase  {

    @Autowired
    private SiteAuthVinLogRwDs siteAuthVinLogRwDs;

    @Autowired
    private SiteAuthVinLogRoDs siteAuthVinLogRoDs;

    @Test
    public void test_insertSiteAuthVinLog() {
        SiteAuthVinLogPo po = new SiteAuthVinLogPo();
        po.setSiteId("1234567")
                .setEvseId("987654309")
                .setStatus(1)
                .setVins(List.of("1111","33333"));
        assertEquals(true, siteAuthVinLogRwDs.insertSiteAuthVinLog(po), "插入数据失败");

        SiteAuthVinLogPo byId = siteAuthVinLogRoDs.getById(po.getId());
        assertEquals(2, byId.getVins().size(), "读取数据失败");
    }
}