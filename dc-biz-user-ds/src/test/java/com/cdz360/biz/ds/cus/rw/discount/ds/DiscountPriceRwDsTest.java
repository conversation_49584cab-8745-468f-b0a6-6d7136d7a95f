package com.cdz360.biz.ds.cus.rw.discount.ds;

import com.cdz360.biz.ds.cus.CusDsTestBase;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
class DiscountPriceRwDsTest extends CusDsTestBase {

    @Autowired
    private DiscountPriceRwDs discountPriceRwDs;

    @Test
    void getByPrimaryKey() {
    }

    @Test
    void upset() {
    }

    @Test
    void changeStatus() {
    }
}