package com.cdz360.biz.ds.cus.rw.wallet.ds;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ds.cus.rw.wallet.mapper.RefundOrderRwMapper;
import com.cdz360.biz.model.cus.wallet.po.RefundOrderPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class RefundOrderRwDs {

    @Autowired
    private RefundOrderRwMapper refundOrderRwMapper;


    public RefundOrderPo addRefundOrder(RefundOrderPo refundOrder) {
        if (StringUtils.isBlank(refundOrder.getSeqNo())) {
            throw new DcArgumentException("序号不能为空");
        }
        refundOrder.setEnable(true);
        this.refundOrderRwMapper.insertOrUpdate(refundOrder);
        return refundOrder;
    }


    public boolean updateRefundOrder(RefundOrderPo refundOrder) {
        if (StringUtils.isBlank(refundOrder.getSeqNo())) {
            throw new DcArgumentException("序号不能为空");
        }
        int ret = this.refundOrderRwMapper.updateRefundOrder(refundOrder);
        return ret > 0;
    }

    public RefundOrderPo getRefundOrder(String seqNo, boolean lock) {
        if (StringUtils.isBlank(seqNo)) {
            throw new DcArgumentException("序号不能为空");
        }
        return this.refundOrderRwMapper.getRefundOrder(seqNo, lock);
    }

    public int batchInsert(List<RefundOrderPo> refundOrderPoList) {
        if (CollectionUtils.isEmpty(refundOrderPoList)) {
            return 0;
        }

        return this.refundOrderRwMapper.batchInsert(refundOrderPoList);
    }
}
