package com.cdz360.biz.ds.cus.rw.siteAuthCard.ds;

import com.cdz360.biz.ds.cus.rw.siteAuthCard.mapper.SiteAuthCardRwMapper;
import com.cdz360.biz.model.cus.siteAuthCard.po.SiteAuthCardPo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SiteAuthCardRwDs {

	@Autowired
	private SiteAuthCardRwMapper siteAuthCardRwMapper;

	public SiteAuthCardPo getById(Long id, boolean lock) {
		return this.siteAuthCardRwMapper.getById(id, lock);
	}

	public boolean insertSiteAuthCard(SiteAuthCardPo siteAuthCardPo) {
		return this.siteAuthCardRwMapper.insertSiteAuthCard(siteAuthCardPo) > 0;
	}

	public boolean updateSiteAuthCard(SiteAuthCardPo siteAuthCardPo) {
		return this.siteAuthCardRwMapper.updateSiteAuthCard(siteAuthCardPo) > 0;
	}

	public int disableAll(String cardNo, List<String> cardNoList, Long commId) {
		return this.siteAuthCardRwMapper.disableAll(cardNo, cardNoList, commId);
	}

	public int insertOrUpdate(List<SiteAuthCardPo> list) {
		return this.siteAuthCardRwMapper.insertOrUpdate(list);
	}


}
