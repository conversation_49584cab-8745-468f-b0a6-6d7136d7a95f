package com.cdz360.biz.ds.cus.rw.siteAuthCard.mapper;

import com.cdz360.biz.model.card.po.SiteAuthCardLogPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SiteAuthCardLogRwMapper {
	SiteAuthCardLogPo getById(@Param("id") Long id, @Param("lock") boolean lock);

	int insertSiteAuthCardLog(SiteAuthCardLogPo siteAuthCardLogPo);

	int updateSiteAuthCardLog(SiteAuthCardLogPo siteAuthCardLogPo);

	int batchInsert(@Param("siteAuthCardLogPoList") List<SiteAuthCardLogPo> siteAuthCardLogPoList);

	int batchSetStatus(@Param("ids") List<Long> ids, @Param("status") int status);
}
