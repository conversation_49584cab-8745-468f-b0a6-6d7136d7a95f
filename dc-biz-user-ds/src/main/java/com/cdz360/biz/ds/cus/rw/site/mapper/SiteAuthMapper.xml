<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.cus.rw.site.mapper.SiteAuthMapper">

    <insert id="insertOrUpdate" parameterType="com.cdz360.biz.model.cus.site.po.SiteAuthPo">
        insert into t_site_auth (`type`, topCommId, account, siteId)
        values (#{type}, #{topCommId}, #{account}, #{siteId})
        on DUPLICATE key UPDATE
        <if test="type != null">
            `type` = #{type}
        </if>
        <if test="enable != null">
            , `enable` = #{enable}
        </if>
    </insert>

    <select id="findByAccountAndTopCommId" resultType="com.cdz360.biz.model.cus.site.vo.SiteAuthVo">
        select sa.*,
        s.name as siteName
        from t_site_auth sa
        left join t_site as s on sa.siteId = s.id
        where sa.`type`=#{type} and sa.account=#{account}
        <if test="topCommId != null">
            and sa.topCommId=#{topCommId}
        </if>
        <if test="enable != null">
            and sa.enable = #{enable}
        </if>
        <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteStatusList )">
            and s.`status` in
            <foreach collection="siteStatusList" item="siteStatus" open="("
                     separator="," close=")">
                #{siteStatus.code}
            </foreach>
        </if>
    </select>

    <select id="findByCondition" resultType="com.cdz360.biz.model.cus.site.po.SiteAuthPo">
        select
            *
        from
            d_card_manager.t_site_auth
        <where>
            <if test="type != null">
                type = #{type}
            </if>
            <if test="topCommId">
                and `topCommId` = #{topCommId}
            </if>
            <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( accountList )">
                and account in
                <foreach collection="accountList" index="index" item="item"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="enable != null">
                and enable = #{enable}
            </if>
        </where>
    </select>

    <update id="disableAll">
        update t_site_auth set `enable`=false
        where `type`=#{type}
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( account )">
            and account=#{account}
        </if>
        <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( accountList )">
            and account in
            <foreach collection="accountList" item="item" index="index"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="topCommId != null">
            and topCommId=#{topCommId}
        </if>
    </update>


    <update id="batchDisable">
        update t_site_auth set `enable`=false
        where `type`=#{type}
        and topCommId=#{topCommId}
        and account in
        <foreach collection="accountList" item="acc" open="(" close=")" separator=",">
            #{acc}
        </foreach>
    </update>

    <update id="moveCorpCardByCorpId">
        update
            t_site_auth as auth
            LEFT JOIN t_site AS site ON site.id = auth.siteId
            LEFT JOIN t_r_commercial AS trc ON trc.id = site.commId
            LEFT JOIN t_card AS tc ON tc.card_no = auth.account
        set auth.enable = FALSE
        where
            tc.corpId = #{corpId}
            AND auth.topCommId = #{topCommId}
            AND auth.type = 1
            AND site.id IS NOT NULL
            AND auth.ENABLE = TRUE
            AND trc.idChain IS NOT NULL
            and trc.idChain not like concat(#{idChain}, '%')
    </update>

    <update id="moveCorpVINByCorpId">
        update
            t_site_auth as auth
            LEFT JOIN t_site AS site ON site.id = auth.siteId
            LEFT JOIN t_r_commercial AS trc ON trc.id = site.commId
            LEFT JOIN t_vin AS tv ON tv.vin = auth.account
        set auth.enable = FALSE
        where
            tv.corpId = #{corpId}
            AND auth.topCommId = #{topCommId}
            AND auth.type = 2
            AND site.id IS NOT NULL
            AND auth.ENABLE = TRUE
            AND trc.idChain IS NOT NULL
            and trc.idChain not like concat(#{idChain}, '%')
    </update>
</mapper>