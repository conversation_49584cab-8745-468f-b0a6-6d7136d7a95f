<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.cus.ro.comm.mapper.CommCusRefRoMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ccr.id, ccr.commId, ccr.cusId as userId, ccr.score, ccr.`enable`, ccr.createTime, ccr.updateTime
    </sql>

    <select id="findByCondition" resultType="com.chargerlinkcar.framework.common.domain.vo.CommCusRef">
        select
            <include refid="Base_Column_List"/>,
            u.username as userName,
            u.comm_id as userCommId,
            u.phone as userPhone,
            u.debt as userDebt,
            comm.comm_name as commName
        from
            d_card_manager.t_comm_cus_ref ccr
        inner join d_card_manager.t_user u on
            ccr.`cusId`=u.id
        inner join d_card_manager.t_r_commercial comm on
            ccr.`commId` = comm.id
        <where>
            <if test="enable != null">
                ccr.enable = #{enable}
            </if>
            <if test="commId != null">
                and ccr.`commId`= #{commId}
            </if>
            <if test="userId != null">
                and ccr.`cusId` = #{userId}
            </if>
            <if test="userPhone != null">
                and u.phone = #{userPhone}
            </if>
            <if test="userName != null">
                and u.username like concat('%',#{userName},'%')
            </if>
            <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(idList)">
                and ccr.id in
                <foreach collection="idList" index="index" item="item" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        <if test="start != null and size != null">
            limit #{start}, #{size}
        </if>
    </select>

    <select id="findById" resultType="com.chargerlinkcar.framework.common.domain.vo.CommCusRef">
        select
            <include refid="Base_Column_List"/>,
            comm.comm_name as commName,
            u.username as userName,
            u.comm_id as userCommId,
            u.debt as userDebt,
            u.phone as userPhone
        from
            d_card_manager.t_comm_cus_ref ccr
        inner join d_card_manager.t_r_commercial comm on
            ccr.`commId` = comm.id
        inner join d_card_manager.t_user u on
            ccr.`cusId` = u.id
        where
            ccr.id = #{id}
    </select>

    <select id="findByCommIdAndCusId" resultType="com.chargerlinkcar.framework.common.domain.vo.CommCusRef">
        select
            <include refid="Base_Column_List"/>,
            comm.comm_name as commName,
            u.username as userName,
            u.comm_id as userCommId,
            u.debt as userDebt,
            u.phone as userPhone
        from
            t_comm_cus_ref ccr
        inner join t_r_commercial comm on
            ccr.`commId` = comm.id
        inner join t_user u on
            ccr.`cusId` = u.id
        where
            ccr.commId = #{commId}
            and ccr.cusId = #{cusId}
        order by
            createTime desc
        limit 1
        <if test="lock == true">
            for update
        </if>
    </select>
    
    <select id="listCommIdByCusId" resultType="java.lang.Long">
        select
        distinct commId
        from t_comm_cus_ref
        where  cusId=#{userId} and enable = #{enable}
    </select>

    <select id="checkCommCusRefExists" resultType="java.lang.Boolean">
        select
        case when count(1)>0 then 1 else 0 end
        from t_comm_cus_ref
        where  cusId=#{userId} and commId = #{commId}
    </select>

    <select id="checkCommCusRefDisabled" resultType="java.lang.Boolean">
        select
        case when count(1)>0 then 1 else 0 end
        from t_comm_cus_ref
        where  cusId=#{userId} and commId = #{commId} and enable = 0
    </select>

    <select id="countByCondition" parameterType="com.chargerlinkcar.framework.common.domain.vo.CommCusRef"
            resultType="java.lang.Long">
        select
            count(tr.id)
        from
            t_comm_cus_ref tr
        left join t_user u on
            u.id = tr.cusId
        <where>
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( userPhone )">
                u.phone = #{userPhone}
            </if>
        </where>
    </select>

</mapper>