package com.cdz360.biz.ds.cus.ro.score.ds;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.ds.cus.ro.score.mapper.ScoreSettingRoMapper;
import com.cdz360.biz.model.cus.score.dto.ScoreSettingDto;
import com.cdz360.biz.model.cus.score.dto.ScoreSettingSiteGidDto;
import com.cdz360.biz.model.cus.score.param.SearchScoreSettingParam;
import com.cdz360.biz.model.cus.score.po.ScoreSettingPo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ScoreSettingRoDs {

	@Autowired
	private ScoreSettingRoMapper scoreSettingRoMapper;

	public ScoreSettingPo getById(Long id) {
		return this.scoreSettingRoMapper.getById(id);
	}

	public ScoreSettingPo getByName(String name) {
		return this.scoreSettingRoMapper.getByName(name);
	}

	public Long getScoreSettingCount(SearchScoreSettingParam param) {
		return scoreSettingRoMapper.getScoreSettingCount(param);
	}

	public List<ScoreSettingDto> getScoreSettingList(SearchScoreSettingParam param) {
		return scoreSettingRoMapper.getScoreSettingList(param);
	}

	public List<Long> getExcludeSettingList(Long userId) {
		return scoreSettingRoMapper.getExcludeSettingList(userId);
	}

	public List<ScoreSettingSiteGidDto> getBySiteGid(List<String> siteGidList, List<Long> excludeSettingList) {
		if(CollectionUtils.isEmpty(siteGidList)) {
			return List.of();
		}
		return scoreSettingRoMapper.getBySiteGid(siteGidList, excludeSettingList);
	}
}
