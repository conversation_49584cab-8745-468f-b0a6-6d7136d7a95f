package com.cdz360.biz.ds.cus.rw.soc.mapper;

import com.cdz360.biz.model.cus.soc.po.SocMainStrategyPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.math.BigDecimal;
import java.util.Date;

@Mapper
public interface SocMainStrategyRwMapper {
	SocMainStrategyPo getById(@Param("id") Long id, @Param("lock") boolean lock);

	int insertSocMainStrategy(SocMainStrategyPo socMainStrategyPo);

	int updateSocMainStrategy(SocMainStrategyPo socMainStrategyPo);

    int deleteById(@Param("id") Long id);
}
