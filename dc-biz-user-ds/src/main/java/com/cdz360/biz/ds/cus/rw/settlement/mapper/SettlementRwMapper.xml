<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.cus.rw.settlement.mapper.SettlementRwMapper">
  <insert id="insertOrUpdate" parameterType="com.cdz360.biz.model.cus.settlement.po.SettlementPo">
    insert into t_settlement
    <trim prefix="(" suffix=")" suffixOverrides=",">
      billNo,
      billName,
      corpId,
      billStatus,
      settlementType,
      cfgId,
      guaranteeWay,
      guaranteeKwh,
      guaranteeElecFee,
      guaranteeServFee,
      subSettlementType,
      subSettlement,
      settlementTotalFee,
      settlementServFee,
      settlementElecFee,
      orderNum,
      orderKwh,
      kwhOther,
      kwh<PERSON>ian,
      kwhF<PERSON>,
      kwhP<PERSON>,
      kwhGu,
      orderElecFee,
      orderServFee,
      siteNameList,
      siteNoList,
      <if test="opType != null">
        opType,
      </if>
      <if test="opId != null">
        opId,
      </if>
      <if test="opName != null">
        opName,
      </if>
      <if test="settStartDate != null">
        settStartDate,
      </if>
      <if test="settEndDate != null">
        settEndDate,
      </if>
      <if test="settStartDateDay != null">
        settStartDateDay,
      </if>
      <if test="settEndDateDay != null">
        settEndDateDay,
      </if>
      <if test="orderElecProfit != null">
        orderElecProfit,
      </if>
      <if test="orderServProfit != null">
        orderServProfit,
      </if>
      <if test="orderTotalProfit != null">
        orderTotalProfit,
      </if>
      createTime,
      updateTime
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{billNo},
      #{billName},
      #{corpId},
      #{billStatus},
      #{settlementType.code},
      #{cfgId},
      #{guaranteeWay},
      #{guaranteeKwh},
      #{guaranteeElecFee},
      #{guaranteeServFee},
      #{subSettlementType.code},
      #{subSettlement},
      #{settlementTotalFee},
      #{settlementServFee},
      #{settlementElecFee},
      #{orderNum},
      #{orderKwh},
      #{kwhOther},
      #{kwhJian},
      #{kwhFeng},
      #{kwhPing},
      #{kwhGu},
      #{orderElecFee},
      #{orderServFee},
      #{siteNameList, typeHandler=com.cdz360.biz.ds.cus.ListTypeHandler},
      #{siteNoList, typeHandler=com.cdz360.biz.ds.cus.ListTypeHandler},
      <if test="opType != null">
        #{opType.code},
      </if>
      <if test="opId != null">
        #{opId},
      </if>
      <if test="opName != null">
        #{opName},
      </if>
      <if test="settStartDate != null">
        #{settStartDate},
      </if>
      <if test="settEndDate != null">
        #{settEndDate},
      </if>
      <if test="settStartDateDay != null">
        #{settStartDateDay},
      </if>
      <if test="settEndDateDay != null">
        #{settEndDateDay},
      </if>
      <if test="orderElecProfit != null">
        #{orderElecProfit},
      </if>
      <if test="orderServProfit != null">
        #{orderServProfit},
      </if>
      <if test="orderTotalProfit != null">
        #{orderTotalProfit},
      </if>
      now(),
      now()
    </trim>

    on DUPLICATE key UPDATE
    <if test="guaranteeWay != null">
      guaranteeWay = #{guaranteeWay},
    </if>
    <if test="guaranteeKwh != null">
      guaranteeKwh = #{guaranteeKwh},
    </if>
    <if test="guaranteeElecFee != null">
      guaranteeElecFee = #{guaranteeElecFee},
    </if>
    <if test="guaranteeServFee != null">
      guaranteeServFee = #{guaranteeServFee},
    </if>
    <if test="subSettlementType != null">
      subSettlementType = #{subSettlementType.code},
    </if>
    <if test="subSettlement != null">
      subSettlement = #{subSettlement},
    </if>
    <if test="settlementTotalFee != null">
      settlementTotalFee = #{settlementTotalFee},
    </if>
    <if test="settlementServFee != null">
      settlementServFee = #{settlementServFee},
    </if>
    <if test="settlementElecFee != null">
      settlementElecFee = #{settlementElecFee},
    </if>
    <if test="orderNum != null">
      orderNum = #{orderNum},
    </if>
    <if test="orderKwh != null">
      orderKwh = #{orderKwh},
    </if>
    <if test="kwhOther != null">
      kwhOther = #{kwhOther},
    </if>
    <if test="kwhJian != null">
      kwhJian = #{kwhJian},
    </if>
    <if test="kwhFeng != null">
      kwhFeng = #{kwhFeng},
    </if>
    <if test="kwhPing != null">
      kwhPing = #{kwhPing},
    </if>
    <if test="kwhGu != null">
      kwhGu = #{kwhGu},
    </if>
    <if test="orderElecFee != null">
      orderElecFee = #{orderElecFee},
    </if>
    <if test="orderServFee != null">
      orderServFee = #{orderServFee},
    </if>
    <if test="settStartDate != null">
      settStartDate = #{settStartDate},
    </if>
    <if test="settEndDate != null">
      settEndDate = #{settEndDate},
    </if>
    <if test="settStartDateDay != null">
      settStartDateDay = #{settStartDateDay},
    </if>
    <if test="settEndDateDay != null">
      settEndDateDay = #{settEndDateDay},
    </if>
    <if test="orderElecProfit != null">
      orderElecProfit = #{orderElecProfit},
    </if>
    <if test="orderServProfit != null">
      orderServProfit = #{orderServProfit},
    </if>
    <if test="orderTotalProfit != null">
      orderTotalProfit = #{orderTotalProfit},
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteNoList )">
      siteNoList = #{siteNoList, typeHandler=com.cdz360.biz.ds.cus.ListTypeHandler},
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteNameList )">
      siteNameList = #{siteNameList, typeHandler=com.cdz360.biz.ds.cus.ListTypeHandler},
    </if>
    updateTime = now()
  </insert>

  <update id="updateByBillNo" parameterType="com.cdz360.biz.model.cus.settlement.po.SettlementPo">
    update t_settlement
    set
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(billName)">
      billName = #{billName},
    </if>
    <if test="billStatus != null">
      billStatus = #{billStatus},
    </if>
    <if test="settlementType != null">
      settlementType = #{settlementType.code},
    </if>
    <if test="kwhOther != null">
      kwhOther = #{kwhOther},
    </if>
    <if test="kwhJian != null">
      kwhJian = #{kwhJian},
    </if>
    <if test="kwhFeng != null">
      kwhFeng = #{kwhFeng},
    </if>
    <if test="kwhPing != null">
      kwhPing = #{kwhPing},
    </if>
    <if test="kwhGu != null">
      kwhGu = #{kwhGu},
    </if>
    <if test="orderElecFee != null">
      orderElecFee = #{orderElecFee},
    </if>
    <if test="orderServFee != null">
      orderServFee = #{orderServFee},
    </if>
    <if test="settStartDate != null">
      settStartDate = #{settStartDate},
    </if>
    <if test="settEndDate != null">
      settEndDate = #{settEndDate},
    </if>
    <if test="settStartDateDay != null">
      settStartDateDay = #{settStartDateDay},
    </if>
    <if test="settEndDateDay != null">
      settEndDateDay = #{settEndDateDay},
    </if>
    <if test="orderElecProfit != null">
      orderElecProfit = #{orderElecProfit},
    </if>
    <if test="orderServProfit != null">
      orderServProfit = #{orderServProfit},
    </if>
    <if test="orderTotalProfit != null">
      orderTotalProfit = #{orderTotalProfit},
    </if>
    <if test="procInstId != null">
      procInstId = #{procInstId},
    </if>
    updateTime = now()
    where billNo = #{billNo}
  </update>

  <update id="updateBatch" parameterType="com.cdz360.biz.model.cus.settlement.po.SettlementPo">
    update t_settlement
    set
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(billName)">
      billName = #{billName},
    </if>
    <if test="billStatus != null">
      billStatus = #{billStatus},
    </if>
    <if test="settlementType != null">
      settlementType = #{settlementType.code},
    </if>
    <if test="kwhOther != null">
      kwhOther = #{kwhOther},
    </if>
    <if test="kwhJian != null">
      kwhJian = #{kwhJian},
    </if>
    <if test="kwhFeng != null">
      kwhFeng = #{kwhFeng},
    </if>
    <if test="kwhPing != null">
      kwhPing = #{kwhPing},
    </if>
    <if test="kwhGu != null">
      kwhGu = #{kwhGu},
    </if>
    <if test="orderElecFee != null">
      orderElecFee = #{orderElecFee},
    </if>
    <if test="orderServFee != null">
      orderServFee = #{orderServFee},
    </if>
    <if test="settStartDate != null">
      settStartDate = #{settStartDate},
    </if>
    <if test="settEndDate != null">
      settEndDate = #{settEndDate},
    </if>
    <if test="settStartDateDay != null">
      settStartDateDay = #{settStartDateDay},
    </if>
    <if test="settEndDateDay != null">
      settEndDateDay = #{settEndDateDay},
    </if>
    <if test="orderElecProfit != null">
      orderElecProfit = #{orderElecProfit},
    </if>
    <if test="orderServProfit != null">
      orderServProfit = #{orderServProfit},
    </if>
    <if test="orderTotalProfit != null">
      orderTotalProfit = #{orderTotalProfit},
    </if>
    <if test="procInstId != null">
      procInstId = #{procInstId},
    </if>
    updateTime = now()
    where billNo in
    <foreach collection="billNoList" index="index" item="billNo"
      open="(" separator="," close=")">
      #{billNo}
    </foreach>
  </update>

  <update id="updateSettlementInvoicedAmount"
    parameterType="com.cdz360.biz.model.cus.settlement.param.UpdateSettlementInvoicedAmountParam">
    update t_settlement ts
    left join t_bloc_user tbu on ts.corpId = tbu.id
    left join t_r_commercial comm on tbu.comm_id = comm.id
    set
    <choose>
      <when test='"FIXED".equals(opType.name())'>
        ts.invoicedAmount = ts.settlementTotalFee,
      </when>
      <when test='"ROLL_BACK".equals(opType.name())'>
        ts.invoicedAmount = 0,
      </when>
    </choose>
    <if test="applyNo != null">
      applyNo = #{applyNo},
    </if>
    <if test="procInstId != null">
      procInstId = #{procInstId},
    </if>
    updateTime = now()
    where 1=1
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(commIdChain)">
      AND comm.`idChain` like CONCAT(#{commIdChain}, '%')
    </if>
    <if test="billNoList!=null and billNoList.size() > 0">
      AND ts.billNo in
      <foreach collection="billNoList" index="index" item="item" open="("
        separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="corpId != null">
      and ts.corpId = #{corpId}
    </if>
  </update>

  <delete id="removeByBillNo">
    delete from t_settlement where billNo = #{billNo}
  </delete>
</mapper>