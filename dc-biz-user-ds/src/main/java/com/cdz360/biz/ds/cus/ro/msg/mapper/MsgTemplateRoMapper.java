package com.cdz360.biz.ds.cus.ro.msg.mapper;

import com.chargerlinkcar.framework.common.domain.MsgTemplatePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MsgTemplateRoMapper {

    MsgTemplatePO getMsgTemplate(@Param("key") String key,
                                 @Param("topCommId") Long topCommId);

    List<MsgTemplatePO> getMsgTemplateList(@Param("keys") List<String> keys,
                                           @Param("topCommId") Long topCommId);


}
