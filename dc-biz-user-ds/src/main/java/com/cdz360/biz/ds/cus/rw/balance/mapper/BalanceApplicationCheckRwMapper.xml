<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.cus.rw.balance.mapper.BalanceApplicationCheckRwMapper">

	<resultMap id="RESULT_BALANCE_APPLICATION_CHECK_PO" type="com.cdz360.biz.model.cus.balance.po.BalanceApplicationCheckPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="applicationId" jdbcType="BIGINT" property="applicationId" />
		<result column="type" jdbcType="JAVA_OBJECT" property="type" />
		<result column="result" jdbcType="JAVA_OBJECT" property="result" />
		<result column="remark" jdbcType="VARCHAR" property="remark" />
		<result column="operatorId" jdbcType="BIGINT" property="operatorId" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_BALANCE_APPLICATION_CHECK_PO">
		select * from t_balance_application_check where id = #{id}
		<if test="lock == true">
			for update
		</if>
	</select>

	<insert id="insertBalanceApplicationCheck" useGeneratedKeys="true" keyProperty="id"
		keyColumn="id" parameterType="com.cdz360.biz.model.cus.balance.po.BalanceApplicationCheckPo">
		insert into t_balance_application_check (`applicationId`,
			`type`,
			`result`,
			`remark`,
			`operatorId`,
			`createTime`,
			`updateTime`)
		values (#{applicationId},
			#{type},
			#{result},
			#{remark},
			#{operatorId},
			now(),
			now())
	</insert>

	<update id="updateBalanceApplicationCheck" parameterType="com.cdz360.biz.model.cus.balance.po.BalanceApplicationCheckPo">
		update t_balance_application_check set
		<if test="applicationId != null">
			applicationId = #{applicationId},
		</if>
		<if test="type != null">
			type = #{type},
		</if>
		<if test="result != null">
			result = #{result},
		</if>
		<if test="remark != null">
			remark = #{remark},
		</if>
		<if test="operatorId != null">
			operatorId = #{operatorId},
		</if>
		updateTime = now()
		where id = #{id}
	</update>

</mapper>
