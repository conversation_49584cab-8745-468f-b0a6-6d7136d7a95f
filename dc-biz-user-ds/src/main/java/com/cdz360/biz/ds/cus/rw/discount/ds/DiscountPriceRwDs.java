package com.cdz360.biz.ds.cus.rw.discount.ds;


import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.biz.ds.cus.rw.discount.mapper.DiscountPriceRwMapper;
import com.cdz360.biz.model.cus.corp.vo.CorpSimpleVo;
import com.cdz360.biz.model.cus.discount.po.DiscountPricePo;
import com.cdz360.biz.model.cus.discount.po.DiscountPricePrimaryKey;
import com.cdz360.biz.model.cus.discount.type.DiscountStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j

@Service

public class DiscountPriceRwDs {



	@Autowired

	private DiscountPriceRwMapper discountPriceRwMapper;



	public DiscountPricePo getByPrimaryKey(DiscountPricePrimaryKey primaryKey, Boolean enable, boolean lock) {

		return this.discountPriceRwMapper.getByPrimaryKey(primaryKey, enable, lock);

	}



	public boolean upset(DiscountPricePo discountPricePo) {

		return this.discountPriceRwMapper.upset(discountPricePo) > 0;

	}


	public int changeStatus(
			Long uid, PayAccountType accountType, Long accountCode,
			DiscountStatus oldStatus, DiscountStatus newStatus) {

		return this.discountPriceRwMapper.changeStatus(
				uid, accountType, accountCode, oldStatus, newStatus);
	}

	public boolean disableUserSite(
			Long uid, PayAccountType accountType, Long accountCode, List<String> siteIdList) {
		return this.discountPriceRwMapper.disableUserSite(uid, accountType, accountCode, siteIdList) > 0;
	}

	public boolean disableCorpUser(String siteId, List<CorpSimpleVo> removeList) {
		return this.discountPriceRwMapper.disableCorpUser(siteId, removeList) > 0;
	}
}

