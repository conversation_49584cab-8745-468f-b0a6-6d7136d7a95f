package com.cdz360.biz.ds.cus.ro.card.mapper;

import com.cdz360.biz.model.cus.card.param.CardAmountSyncParam;
import com.cdz360.biz.model.cus.card.po.CardAmountSyncPo;
import com.cdz360.biz.model.cus.card.vo.CardAmountSyncVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CardAmountSyncRoMapper {

	CardAmountSyncPo getById(@Param("id") Long id);

	List<CardAmountSyncVo> getList(CardAmountSyncParam param);

	Long getCount(CardAmountSyncParam param);
}
