package com.cdz360.biz.ds.cus.ro.settlement.ds;

import com.cdz360.biz.ds.cus.ro.settlement.mapper.SettlementCfgRoMapper;
import com.cdz360.biz.model.cus.settlement.po.SettlementCfgPo;
import com.cdz360.biz.model.cus.settlement.type.SettlementCfgStatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class SettlementCfgRoDs {

    @Autowired
    private SettlementCfgRoMapper settlementCfgRoMapper;

    public SettlementCfgPo getById(Long id) {
        return settlementCfgRoMapper.getById(id);
    }

    public SettlementCfgPo findByCorpId(Long corpId, SettlementCfgStatusEnum status) {
        return settlementCfgRoMapper.findByCorpId(corpId, status);
    }

    public List<SettlementCfgPo> needActiveCfgList(Date now) {
        return settlementCfgRoMapper.needActiveCfgList(now);
    }

    public List<SettlementCfgPo> getNeedAutoGenSettlement(int day) {
        return settlementCfgRoMapper.getNeedAutoGenSettlement(day);
    }

    public Long checkDuplicateBillName(@NonNull Long corpId,
                                       @NonNull List<String> nameList) {
        return settlementCfgRoMapper.checkDuplicateBillName(corpId, nameList);
    }

}
