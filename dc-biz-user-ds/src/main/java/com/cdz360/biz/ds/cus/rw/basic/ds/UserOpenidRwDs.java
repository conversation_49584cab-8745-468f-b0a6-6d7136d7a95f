package com.cdz360.biz.ds.cus.rw.basic.ds;

import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ds.cus.rw.basic.mapper.UserOpenidRwMapper;
import com.cdz360.biz.model.cus.user.po.UserOpenidPo;
import com.cdz360.biz.model.cus.user.po.UserPo;
import com.cdz360.biz.model.cus.user.type.UserOpenidType;
import jakarta.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UserOpenidRwDs {

    @Autowired
    private UserOpenidRwMapper userOpenidRwMapper;

    public void addUserOpenid(UserOpenidPo data) {
        this.userOpenidRwMapper.addUserOpenid(data);
    }

    public void deleteUserOpenid(Long topCommId, Long uid, String type, @Nullable String openid,
        @Nullable String appId) {
        log.info("第三方帐号解绑 topCommId = {}, uid = {}, type = {}, openid = {}, appId = {}",
            topCommId, uid, type, openid, appId);
        this.userOpenidRwMapper.deleteUserOpenid(topCommId, uid, type, openid, appId);
    }

    public void updateUserOpenId(UserOpenidPo param) {
        log.info("更新第三方帐号信息 param = {}", param);
        this.userOpenidRwMapper.updateUserOpenId(param);
    }

    public void updateWxOpenid(UserPo user, String openid, String appId) {
        if (StringUtils.equalsIgnoreCase("none", openid)) {
            this.deleteUserOpenid(user.getCommId(), user.getId(),
                UserOpenidType.WX_OPENID, null, null);
        } else if (StringUtils.isNotBlank(openid)) {
            UserOpenidPo open = new UserOpenidPo();
            open.setTopCommId(user.getCommId())
                .setUid(user.getId())
                .setOpenid(openid)
                .setType(UserOpenidType.WX_OPENID)
                .setAppId(appId);
            this.addUserOpenid(open);
        }
    }

    public void updateWxUnionid(UserPo user, String unionid) {
        if (StringUtils.equalsIgnoreCase("none", unionid)) {
            this.deleteUserOpenid(user.getCommId(), user.getId(),
                UserOpenidType.WX_UNIONID, null, null);
        } else if (StringUtils.isNotBlank(unionid)) {
            UserOpenidPo open = new UserOpenidPo();
            open.setTopCommId(user.getCommId())
                .setUid(user.getId())
                .setOpenid(unionid)
                .setType(UserOpenidType.WX_UNIONID)
                .setAppId("");
            this.addUserOpenid(open);
        }
    }

    public void updateAlipayOpenid(UserPo user, String openid, String appId) {
        log.info("更新支付宝信息 topCommId= {}, appId= {}, openId= {}, userId= {}",
                user.getCommId(), appId, openid, user.getId());
        if (StringUtils.equalsIgnoreCase("none", openid)) {
            this.deleteUserOpenid(user.getCommId(), user.getId(),
                UserOpenidType.ALIPAY_OPENID, null, null);
        } else if (StringUtils.isNotBlank(openid)) {
            UserOpenidPo open = new UserOpenidPo();
            open.setTopCommId(user.getCommId())
                    .setUid(user.getId())
                    .setOpenid(openid)
                    .setType(UserOpenidType.ALIPAY_OPENID)
                    .setAppId(appId);
            this.addUserOpenid(open);
        }
    }

    /**
     * 保存支付宝即插即充客户信息，type 放 alipay_vin, openid字段放vin
     */
    public void updateAlipayVin(Long topCommId, String appId, Long uid, String vin, String carNo,
        Boolean unbind) {
        log.info(
            "新增/更新 支付宝即插即充 vin 信息. topCommId= {}, appId= {}, uid= {}, vin= {}, carNo= {}, unbind= {}",
            topCommId, appId, uid, vin, carNo, unbind);
        if (Boolean.TRUE.equals(unbind)) {
            this.deleteUserOpenid(topCommId, uid,
                UserOpenidType.ALIPAY_VIN, vin, null);
        } else if (StringUtils.isNotBlank(vin)) {
            UserOpenidPo open = new UserOpenidPo();
            open.setTopCommId(topCommId)
                .setUid(uid)
                .setOpenid(vin)
                .setType(UserOpenidType.ALIPAY_VIN)
                .setAppId(appId)
                .setExtraA(carNo);
            this.addUserOpenid(open);
        }
    }
}
