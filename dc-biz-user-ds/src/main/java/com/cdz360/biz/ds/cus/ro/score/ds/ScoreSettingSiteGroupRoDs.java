package com.cdz360.biz.ds.cus.ro.score.ds;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.model.cus.score.po.ScoreSettingSiteGroupPo;
import com.cdz360.biz.ds.cus.ro.score.mapper.ScoreSettingSiteGroupRoMapper;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.lang.Double;
import java.util.Date;

@Slf4j
@Service
public class ScoreSettingSiteGroupRoDs {

	@Autowired
	private ScoreSettingSiteGroupRoMapper scoreSettingSiteGroupRoMapper;

	public ScoreSettingSiteGroupPo getById(Long id) {
		return this.scoreSettingSiteGroupRoMapper.getById(id);
	}

	public List<ScoreSettingSiteGroupPo> getByScoreSettingIds(List<Long> idList) {
		if(CollectionUtils.isEmpty(idList)) {
			return List.of();
		}
		return this.scoreSettingSiteGroupRoMapper.getByScoreSettingIds(idList);
	}

	public List<ScoreSettingSiteGroupPo> getByGids(List<String> gidList) {
		if(CollectionUtils.isEmpty(gidList)) {
			return List.of();
		}
		return this.scoreSettingSiteGroupRoMapper.getByGids(gidList);
	}
}
