package com.cdz360.biz.ds.cus.ro.mechant.ds;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.biz.ds.cus.ro.mechant.mapper.CommercialRoMapper;
import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Slf4j
@Service
public class CommercialRoDs {

    @Autowired
    private CommercialRoMapper commercialRoMapper;


    public CommercialSimpleVo getCommerial(long id) {
        return this.commercialRoMapper.getCommerial(id);
    }

    public List<CommercialSimpleVo> getCommerialList(List<Long> idList) {
        return this.commercialRoMapper.getCommerialList(idList);
    }

//    /**
//     * 查询ID对应的所有子商户ID列表. 含 commercialId
//     *
//     * @param commercialId
//     * @return
//     */
//    public List<Long> listCommercialIds(long commercialId) {
//        List<Long> ret = this.commercialRoMapper.listSubCommercialIds(commercialId);
//        ret.add(commercialId);
//        return ret;
//    }

    public List<Long> listSubCommercialIds2(long commId) {
        return this.commercialRoMapper.listSubCommercialIds2(commId);
    }


//    /**
//     * 递归获取顶级商户，
//     *
//     * @param id
//     * @return
//     */
//    public CommercialSimpleVo getTopCommercial(Long id, int retry) {
//        log.info("查找商户 {} 的集团商户. retry = {}", id, retry);
//        CommercialSimpleVo rCommercialSimpleVo = commercialRoMapper.getCommerial(id);
//        if (rCommercialSimpleVo == null) {
//            log.error("找不到对应商户: {}", id);
//            return null;
//        }
//        if (rCommercialSimpleVo.getPid() == null || rCommercialSimpleVo.getPid().longValue() == 0L) {
//            return rCommercialSimpleVo;
//        } else if (retry == 0) {
//            log.error("查找集团商户失败,已达最大重试次数. commId = {}", id);
//            return null;
//        } else {
//            return getTopCommercial(rCommercialSimpleVo.getPid(), retry - 1);
//        }
//    }

    /**
     * 根据商户ID查找集团商户ID
     *
     * @param commId 商户ID
     * @return 集团商户ID
     */
    public Long getTopCommId(Long commId) {
        CommercialSimpleVo comm = commercialRoMapper.getCommerial(commId);
        if (comm == null) {
            log.warn("商户不存在. commId = {}", commId);
            throw new DcArgumentException("商户不存在");
        }
        return comm.getTopCommId();
    }

    /**
     * 获取商户的上级id集合（含当前商户id）
     *
     * @param id
     * @return
     */
    public List<Long> getCommercialPidList(Long id) {
        List<Long> list = new ArrayList<>();
        CommercialSimpleVo rCommercialSimpleVo = commercialRoMapper.getCommerial(id);
        if (rCommercialSimpleVo == null) {
            log.error("找不到对应商户: {}", id);
            return null;
        }
        while (rCommercialSimpleVo.getPid() != null) {
            list.add(rCommercialSimpleVo.getId());
            rCommercialSimpleVo = commercialRoMapper.getCommerial(rCommercialSimpleVo.getPid());
        }
        //把顶级商户加进去
        if (rCommercialSimpleVo.getPid() == null) {
            list.add(rCommercialSimpleVo.getId());
        }
        return list;
    }

    public List<CommercialSimpleVo> getCommercialSimpleVoList() {
        return this.commercialRoMapper.getCommercialSimpleVoList();
    }

}
