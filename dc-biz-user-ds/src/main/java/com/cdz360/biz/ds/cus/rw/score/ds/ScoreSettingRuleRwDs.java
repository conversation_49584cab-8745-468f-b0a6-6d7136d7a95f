package com.cdz360.biz.ds.cus.rw.score.ds;

import com.cdz360.biz.model.cus.score.po.ScoreSettingRulePo;
import com.cdz360.biz.ds.cus.rw.score.mapper.ScoreSettingRuleRwMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.lang.Double;
import java.util.Date;

@Slf4j
@Service
public class ScoreSettingRuleRwDs {

	@Autowired
	private ScoreSettingRuleRwMapper scoreSettingRuleRwMapper;

	public ScoreSettingRulePo getById(Long id, boolean lock) {
		return this.scoreSettingRuleRwMapper.getById(id, lock);
	}

	public boolean insertScoreSettingRule(ScoreSettingRulePo scoreSettingRulePo) {
		return this.scoreSettingRuleRwMapper.insertScoreSettingRule(scoreSettingRulePo) > 0;
	}

	public boolean updateScoreSettingRule(ScoreSettingRulePo scoreSettingRulePo) {
		return this.scoreSettingRuleRwMapper.updateScoreSettingRule(scoreSettingRulePo) > 0;
	}

	public int deleteByScoreSettingId(Long scoreSettingId) {
		return this.scoreSettingRuleRwMapper.deleteByScoreSettingId(scoreSettingId);
	}


}
