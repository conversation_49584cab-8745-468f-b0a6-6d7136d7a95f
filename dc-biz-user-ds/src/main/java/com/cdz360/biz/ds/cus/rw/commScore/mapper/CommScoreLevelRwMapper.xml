<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.cus.rw.commScore.mapper.CommScoreLevelRwMapper">
    <insert id="insertBatch" parameterType="java.util.List">
        insert into d_card_manager.t_comm_score_level
        (commId,minScore,maxScore,discount,`level`,enable)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.commId},#{item.minScore},#{item.maxScore},#{item.discount},#{item.level},#{item.enable})
        </foreach>
        on DUPLICATE key UPDATE
        minScore = values(minScore),
        maxScore = values(maxScore),
        discount = values(discount),
        enable = values(enable)
    </insert>
    <update id="updateBatch">
        update d_card_manager.t_comm_cus_ref set score = 0
        where
        id in
        <foreach collection="list" index="index" item="item" open="("
                 separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="commUserScoreModify">
        update d_card_manager.t_comm_cus_ref
       set score = #{score}
       where commId=#{commId} and cusId=#{cusId}
    </update>

    <select id="updateCommLevelByCommId" resultType="java.lang.Long">
       update d_card_manager.t_comm_score_level set enable = 0 where commId = #{commId}
    </select>

</mapper>