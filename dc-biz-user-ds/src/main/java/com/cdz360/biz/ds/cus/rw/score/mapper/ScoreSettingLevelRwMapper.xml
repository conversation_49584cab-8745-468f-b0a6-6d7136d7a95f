<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.cus.rw.score.mapper.ScoreSettingLevelRwMapper">

	<resultMap id="RESULT_SCORE_SETTING_LEVEL_PO" type="com.cdz360.biz.model.cus.score.po.ScoreSettingLevelPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="scoreSettingId" jdbcType="BIGINT" property="scoreSettingId" />
		<result column="level" jdbcType="INTEGER" property="level" />
		<result column="minScore" jdbcType="BIGINT" property="minScore" />
		<result column="maxScore" jdbcType="BIGINT" property="maxScore" />
		<result column="discount" jdbcType="DECIMAL" property="discount" />
		<result column="totalPrice" jdbcType="DECIMAL" property="totalPrice" />
		<result column="enable" jdbcType="BOOLEAN" property="enable" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_SCORE_SETTING_LEVEL_PO">	
		select * from t_score_setting_level where id = #{id}
		<if test="lock == true">
			for update
		</if>
	</select>

	<insert id="insertScoreSettingLevel" useGeneratedKeys="true" keyProperty="id"
		keyColumn="id" parameterType="com.cdz360.biz.model.cus.score.po.ScoreSettingLevelPo">
		insert into t_score_setting_level (`scoreSettingId`,
			`level`,
			`minScore`,
			`maxScore`,
			`discount`,
			`totalPrice`,
			`enable`,
			`createTime`,
			`updateTime`)
		values (#{scoreSettingId},
			#{level},
			#{minScore},
			#{maxScore},
			#{discount},
			#{totalPrice},
			#{enable},
			now(),
			now())
	</insert>

	<insert id="batchInsert" parameterType="com.cdz360.biz.model.cus.score.po.ScoreSettingLevelPo">
		insert into t_score_setting_level (`scoreSettingId`,
		`level`,
		`minScore`,
		`maxScore`,
		`discount`,
		`totalPrice`,
		`enable`,
		`createTime`,
		`updateTime`)
		values
		<foreach collection="list" item="item" index="index" separator=",">
			(#{item.scoreSettingId},
			#{item.level},
			#{item.minScore},
			#{item.maxScore},
			#{item.discount},
			#{item.totalPrice},
			#{item.enable},
			now(),
			now())
		</foreach>
	</insert>

	<update id="updateScoreSettingLevel" parameterType="com.cdz360.biz.model.cus.score.po.ScoreSettingLevelPo">
		update t_score_setting_level set
		<if test="scoreSettingId != null">
			scoreSettingId = #{scoreSettingId},
		</if>
		<if test="level != null">
			level = #{level},
		</if>
		<if test="minScore != null">
			minScore = #{minScore},
		</if>
		<if test="maxScore != null">
			maxScore = #{maxScore},
		</if>
		<if test="discount != null">
			discount = #{discount},
		</if>
		<if test="totalPrice != null">
			totalPrice = #{totalPrice},
		</if>
		<if test="enable != null">
			enable = #{enable},
		</if>
		updateTime = now()
		where id = #{id}
	</update>

	<update id="deleteByScoreSettingId">
		update t_score_setting_level set
			enable = false,
			updateTime = now()
		where
			scoreSettingId = #{scoreSettingId}
	</update>

</mapper>
