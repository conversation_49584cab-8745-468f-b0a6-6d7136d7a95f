package com.cdz360.biz.model.tj.kc.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "孙类型")
@Getter
public enum MaterialCostGrandchildType implements DcEnum {


    UNKNOWN(999, "未知"),
    VOLTAGE_CABLE_COPPER(2001001, "铜缆"),
    VOLTAGE_CABLE_ALUMINIUM(2001002, "铝缆"),
    LOWER_COLUMN_SWITCH(5001001, "下柱开关"),
    NUMBER_OF_RING_NETWORK_INTERVALS(5002001, "环网间隔数"),
    GALVANIZED_CABLE_TRAY_AND_INSTALLATION(5003001, "镀锌桥架及安装"),
    ORDINARY_SOIL_EXCAVATION(5004001, "普通土开挖"),
    EXCAVATION_OF_PEDESTRIAN_WALKWAY(5004002, "人行道开挖"),
    CONCRETE_EXCAVATION(5004003, "混凝土开挖"),
    DESIGN_HIGH_VOLTAGE_PROJECT(5005001, "高压项目"),
    DESIGN_LOW_VOLTAGE_PROJECT(5005002, "低压项目"),
    OTHER_TYPE(5006001, "施工围栏、垃圾清运、防火封堵、垃圾箱等"),
    ;
    @JsonValue
    private final int code;
    private final String desc;

    MaterialCostGrandchildType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @JsonCreator
    public static MaterialCostGrandchildType valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (MaterialCostGrandchildType flag : values()) {
            if (flag.code == code) {
                return flag;
            }
        }
        return MaterialCostGrandchildType.UNKNOWN;
    }

}
