package com.cdz360.biz.model.tj.kc.po;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "勘察站-高压信息")
public class TjSurveyHighVoltagePo {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "id 不能为 null")
    private Long id;

    @ApiModelProperty(value = "勘察站的no")
    private String surveyNo;

    @ApiModelProperty(value = "箱变容量 有箱变必填")
    private Integer capacity;

    @ApiModelProperty(value = "高压距离")
    private Integer cableDistance;

    @ApiModelProperty(value = "柱开数量")
    private Integer poleTopSwitch;

    @ApiModelProperty(value = "环网柜间隔数")
    private Integer ringMainUnit;

    @ApiModelProperty(value = "是否删除")
    private Boolean enable;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}

