package com.cdz360.biz.model.tj.kc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "勘察站初次投入金额")
public class TjInitCashVo {

    @Schema(description = "高压投资额")
    private BigDecimal tzHighVoltageFee;

    @Schema(description = "低压投资额")
    private BigDecimal tzLowVoltageFee;

    @Schema(description = "设备投资额")
    private BigDecimal tzEquipmentsFee;

}

