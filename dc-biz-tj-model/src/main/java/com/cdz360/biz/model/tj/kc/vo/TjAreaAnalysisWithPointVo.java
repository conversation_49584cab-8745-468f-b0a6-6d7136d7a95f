package com.cdz360.biz.model.tj.kc.vo;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.tj.kc.po.TjAreaAnalysisPointPo;
import com.cdz360.biz.model.tj.kc.po.TjAreaPo;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "带投建分析点的投建分析区域")
@EqualsAndHashCode(callSuper = true)
public class TjAreaAnalysisWithPointVo extends TjAreaAnalysisVo {

    @Schema(description = "关联投建区域")
    private TjAreaPo tjArea;

    @Schema(description = "投建分析划分点", requiredMode = RequiredMode.REQUIRED)
    private List<TjAreaAnalysisPointPo> analysisPointList;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}

