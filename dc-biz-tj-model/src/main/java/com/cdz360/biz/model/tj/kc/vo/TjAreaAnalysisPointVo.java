package com.cdz360.biz.model.tj.kc.vo;

import com.cdz360.biz.model.tj.kc.po.TjAreaAnalysisPointPo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "投建分析划分点查看信息")
@EqualsAndHashCode(callSuper = true)
public class TjAreaAnalysisPointVo extends TjAreaAnalysisPointPo {

    @Schema(description = "辐射区已勘查站点")
    private Integer surveyNum;

    @Schema(description = "辐射区已运营站点")
    private Integer operateNum;

    @Schema(description = "辐射区竞争对手运营站点信息")
    private List<CompetitorSiteBi> competitorSiteBiList;
}

