package com.cdz360.biz.model.tj.kc.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "竞争对手场站")
public class TjCompetitorSiteVo {

    @NotNull(message = "id 不能为 null")
    private Long id;

    @Schema(description = "竞争对手ID(t_tj_competitor.id)")
    @JsonInclude(Include.NON_NULL)
    private Long competitorId;

    @Schema(description = "竞争对手名称")
    @JsonInclude(Include.NON_EMPTY)
    private String competitorName;

    @Schema(description = "高德ID")
    @JsonInclude(Include.NON_NULL)
    private String gdId;

    @Schema(description = "场站名称")
    @JsonInclude(Include.NON_EMPTY)
    private String name;

    @Schema(description = "省行政编码")
    @JsonInclude(Include.NON_EMPTY)
    private String pCode;

    @Schema(description = "省名")
    @JsonInclude(Include.NON_EMPTY)
    private String pName;

    @Schema(description = "市级行政编码")
    @JsonInclude(Include.NON_EMPTY)
    private String cityCode;

    @Schema(description = "城市名称")
    @JsonInclude(Include.NON_EMPTY)
    private String cityName;

    @Schema(description = "区县行政编码")
    @JsonInclude(Include.NON_EMPTY)
    private String adCode;

    @Schema(description = "区县名称")
    @JsonInclude(Include.NON_EMPTY)
    private String adName;

    @Schema(description = "站点地址")
    @JsonInclude(Include.NON_EMPTY)
    private String address;

    @Schema(description = "经纬度")
    @JsonInclude(Include.NON_EMPTY)
    private String location;

    @Schema(description = "直流桩数量")
    @JsonInclude(Include.NON_NULL)
    private Integer dcEvseNum;

    @Schema(description = "直流枪头数量")
    @JsonInclude(Include.NON_NULL)
    private Integer dcPlugNum;

    @Schema(description = "交流桩数量")
    @JsonInclude(Include.NON_NULL)
    private Integer acEvseNum;

    @Schema(description = "交流枪头数量")
    @JsonInclude(Include.NON_NULL)
    private Integer acPlugNum;

    @Schema(description = "总功率,单位kW")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal power;

    @Schema(description = "最小电费单价，单位：元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal minElecFee;

    @Schema(description = "最大电费单价，单位：元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal maxElecFee;

    @Schema(description = "最小服务费单价，单位：元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal minServFee;

    @Schema(description = "最大服务费单价，单位：元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal maxServFee;

    @Schema(description = "附加信息更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date attachUpdateTime;

}

