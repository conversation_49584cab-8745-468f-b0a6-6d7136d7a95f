package com.cdz360.biz.model.tj.kc.po;

import com.cdz360.biz.model.tj.kc.type.PointZoneStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "投建分析划分点")
public class TjAreaAnalysisPointPo {

    @Schema(description = "投建区域ID(t_tj_area_analysis.id)")
    @NotNull(message = "analysisId 不能为 null")
    private Long analysisId;

    @Schema(description = "投建区域标号")
    @NotNull(message = "num 不能为 null")
    private Integer num;

    @Schema(description = "辐射区状态")
    @NotNull(message = "status 不能为 null")
    private PointZoneStatus status;

    @Schema(description = "中心点")
    @NotNull(message = "location 不能为 null")
    private String location;

    @Schema(description = "圆半径，shape为圆赋值，其他情况为0(单位: 米)")
    private Integer radius;

    @Schema(description = "通过经纬度获取详细地址")
    private String address;

    // 前端直接获取颜色使用
    public String getColor() {
        return status != null ? status.getColor() : null;
    }
}

