package com.cdz360.biz.model.tj.kc.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "子类型")
@Getter
public enum MaterialCostChildType implements DcEnum {


    UNKNOWN(999, "未知"),

    BOX_TRANSFORMATION(1001, "箱变"),
    HIGH_VOLTAGE_CABLE(2001, "高压电缆"),
    SITE_TO_POWER_SUPPLY_WIRING(2002, "充电站到电源走线"),
    SINGLE_DC_CABLE(2003, "充电区域单台直流充电桩电缆"),
    SINGLE_AC_CABLE(2004, "充电区域单台交流充电桩电缆"),
    EVSE(3001, "充电桩"),
    BIG_CAR_GEAR(4001, "大车车挡"),
    SMALL_CAR_GEAR(4002, "小车车挡"),
    INTELLIGENT_LOCK(4003, "智能地锁"),
    EPOXY_FLOORING(4004, "环氧地坪"),
    GROUND_HARDENING(4005, "地面硬化"),
    FIRE_PROTECTION_SYSTEM(4006, "消防系统"),
    GROUND_LIGHTING(4007, "地上照明"),
    UNDERGROUND_LIGHTING(4008, "地下照明"),
    CCTV_MONITORING_SYSTEM(4009, "CCTV监控系统"),
    FLAGSHIP_STORE(4010, "品牌形象RVI旗舰店"),
    ORDINARY_STATION(4011, "品牌形象RVI普通站"),
    BARRIER_GATE_SYSTEM(4012, "道闸系统"),
    BARRIER_GATE_SYSTEM_EQUIPMENT(4013, "道闸系统对接"),
    CANOPY(4014, "雨棚"),
    POLE_TOP_SWITCH_AND_INSTALLATION(5001, "柱开及安装"),
    RING_MAIN_UNIT_AND_INSTALLATION(5002, "环网柜及安装"),
    BRIDGE_STRUCTURE_AND_INSTALLATION(5003, "桥架及安装"),
    CABLE_DIRECT_BURIAL_EXCAVATION(5004, "电缆直埋开挖"),
    DESIGN_FEE(5005, "设计费"),
    OTHER(5006, "其他"),
    ;
    @JsonValue
    private final int code;
    private final String desc;

    MaterialCostChildType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @JsonCreator
    public static MaterialCostChildType valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (MaterialCostChildType flag : values()) {
            if (flag.code == code) {
                return flag;
            }
        }
        return MaterialCostChildType.UNKNOWN;
    }

}
