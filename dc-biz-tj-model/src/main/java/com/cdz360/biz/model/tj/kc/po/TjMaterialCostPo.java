package com.cdz360.biz.model.tj.kc.po;

import com.cdz360.biz.model.tj.kc.type.CableMaterial;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "投建-数据字典-物料成本")
public class TjMaterialCostPo {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "id 不能为 null")
    private Long id;

    @Schema(description = "类型（见MaterialCostType）")
    @NotNull(message = "type 不能为 null")
    private Integer type;

    @Schema(description = "子类型（见MaterialCostChildType）")
    private Integer childType;

    @Schema(description = "容量（315、500、630、800、1000、1250、1600）kVA")
    private Integer capacity;

    @Schema(description = "单价")
    private BigDecimal price;

    @Schema(description = "安装费用")
    private BigDecimal installationFee;

    @Schema(description = "孙类型（见MaterialCostGrandchildType）")
    private Integer grandchildType;

    @Schema(description = "充电桩型号ID")
    private Long evseModelId;

    @Schema(description = "功率")
    private Integer power;

    @Schema(description = "终端（1：250A终端单枪，2：250A终端双枪）")
    private Integer terminal;

    @Schema(description = "充电桩基础费用")
    private BigDecimal evseBaseFee;

    @Schema(description = "true有效;false删除")
    private Boolean enable;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

}

