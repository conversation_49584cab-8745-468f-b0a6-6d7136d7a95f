package com.cdz360.biz.model.tj.kc.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "是否需要分支箱")
@Getter
public enum BranchBoxType implements DcEnum {


    UNKNOWN(999, "未知"),

    BRANCH_BOX_USE(1, "使用分支箱"),
    BRANCH_BOX_NO(2, "无分支箱"),
    BRANCH_BOX_NO_NEED(3, "不需要"),
    ;
    @JsonValue
    private final int code;
    private final String desc;

    BranchBoxType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @JsonCreator
    public static BranchBoxType valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (BranchBoxType flag : values()) {
            if (flag.code == code) {
                return flag;
            }
        }
        return BranchBoxType.UNKNOWN;
    }

}
