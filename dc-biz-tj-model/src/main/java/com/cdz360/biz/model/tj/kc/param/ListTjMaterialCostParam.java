package com.cdz360.biz.model.tj.kc.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "获取物料成本列表参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListTjMaterialCostParam extends BaseListParam {

    @Schema(description = "物料成本id")
    private Long id;

    @Schema(description = "子类型")
    private Integer childType;

    @Schema(description = "是否删除")
    private Boolean enable;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
