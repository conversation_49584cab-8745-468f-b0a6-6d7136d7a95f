package com.cdz360.biz.device.business.config;


import com.cdz360.base.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;

public class FastJsonRedisSerializer implements RedisSerializer<Object> {

    private static final Logger log = LoggerFactory.getLogger(FastJsonRedisSerializer.class);
    private Class<Object> clazz = Object.class;
    private JdkSerializationRedisSerializer jdkSerializationRedisSerializer = new JdkSerializationRedisSerializer();

    public FastJsonRedisSerializer() {
    }

    public byte[] serialize(Object t) throws SerializationException {
        return t == null ? new byte[0]
            : JsonUtils.toJsonString(t)
                .getBytes(RedisConfig.DEFAULT_CHARSET);
    }

    public Object deserialize(byte[] bytes) throws SerializationException {
        if (bytes != null && bytes.length > 0) {
            String str = new String(bytes, RedisConfig.DEFAULT_CHARSET);

            try {
                Object obj = JsonUtils.fromJson(str, this.clazz);
                return obj;
            } catch (Exception var5) {
                log.error("JSON反序列化失败:{}", var5.getMessage());
                return str;
            }
        } else {
            return null;
        }
    }
}
