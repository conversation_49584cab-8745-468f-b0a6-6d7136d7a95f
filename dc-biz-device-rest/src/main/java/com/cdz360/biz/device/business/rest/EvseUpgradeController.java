package com.cdz360.biz.device.business.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.device.business.client.AuthCoreClient;
import com.cdz360.biz.device.business.client.IotEvseClient;
import com.cdz360.biz.device.business.constant.Constant;
import com.cdz360.biz.device.business.entity.request.ListEvseRequest;
import com.cdz360.biz.device.business.entity.request.TokenRequest;
import com.cdz360.biz.device.business.entity.request.UpgradeTaskDetailRequest;
import com.cdz360.biz.device.business.entity.request.UpgradeTaskInfoRequest;
import com.cdz360.biz.device.business.entity.request.UpgradeTaskListRequest;
import com.cdz360.biz.device.business.entity.request.UpgradeTaskRequest;
import com.cdz360.biz.device.business.entity.result.EvseVo;
import com.cdz360.biz.device.business.entity.result.SysUserVo;
import com.cdz360.biz.device.business.entity.result.UpgradeTaskDetailVo;
import com.cdz360.biz.device.business.entity.result.UpgradeTaskInfoVo;
import com.cdz360.biz.device.business.entity.result.UpgradeTaskVo;
import com.cdz360.biz.device.business.entity.request.*;
import com.cdz360.biz.device.business.entity.result.*;
import com.cdz360.biz.device.business.rest.base.BaseController;
import com.cdz360.biz.device.business.service.UpdateTaskService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Classname EvseUpdateController
 * @Description TODO
 * @Date 9/12/2019 4:00 PM
 * @Created by Rafael
 */

@Slf4j
//@FeignClient(value = "device-business-rest")
@RequestMapping("/api/upgrade")
@RestController
@ApiOperation(value = "桩升级记录、升级操作接口")
@Deprecated
public class EvseUpgradeController extends BaseController {

    @Autowired
    private AuthCoreClient authCoreClient;

    @Autowired
    private UpdateTaskService updateTaskService;
    @Autowired
    private IotEvseClient iotEvseClient;

    /**
     * 分页获取 获取场站下升级记录列表
     * @return
     */
    @ApiOperation(value = "分页获取 获取场站下升级记录列表")
    @PostMapping("/getUpgradeTaskListBySite")
    public ListResponse<UpgradeTaskVo> getUpgradeTaskListBySite(@RequestBody UpgradeTaskListRequest upgradeTaskListRequest) {
        log.info(">> 获取场站下升级记录列表: req={}", upgradeTaskListRequest);
        ListResponse<UpgradeTaskVo> ret = updateTaskService.getUpgradeTaskListBySite(upgradeTaskListRequest);
        log.info("<< {}", JsonUtils.toJsonString(ret));
        return ret;
    }

    /**
     * 获取升级记录详情列表，不分页
     * @return
     */
    @ApiOperation(value = "获取升级记录详情列表，不分页")
    @PostMapping("/getUpgradeTaskDetailListByTaskId")
    public ListResponse<UpgradeTaskDetailVo> getUpgradeTaskDetailListByTaskId(@RequestBody UpgradeTaskDetailRequest upgradeTaskDetailRequest) {
        log.info(">> 获取升级记录详情列表: req = {}", upgradeTaskDetailRequest);
        ListResponse<UpgradeTaskDetailVo> list = updateTaskService.getUpgradeTaskDetailListByTaskId(upgradeTaskDetailRequest);
        log.info("<< {}", JsonUtils.toJsonString(list));
        return list;
    }

    @ApiOperation(value = "获取升级信息")
    @PostMapping("/getUpgradeTaskInfo")
    public ObjectResponse<UpgradeTaskInfoVo> getUpgradeTaskInfo(
            @RequestBody UpgradeTaskInfoRequest upgradeTaskInfoRequest) {
        log.info(">> 获取升级信息: req={}", JsonUtils.toJsonString(upgradeTaskInfoRequest));
        ObjectResponse<UpgradeTaskInfoVo> obj =  updateTaskService
                .getUpgradeTaskInfo(upgradeTaskInfoRequest);
        log.info("<< {}", JsonUtils.toJsonString(obj));
        return obj;
    }

    @ApiOperation(value = "发起桩升级")
    @PostMapping("/startTask")
    public BaseResponse startTask(@RequestBody UpgradeTaskRequest upgradeTaskRequest) {
        log.info(">> 发起桩升级请求: {}", JsonUtils.toJsonString(upgradeTaskRequest));

        // 获取用户登陆信息
        String token = request.getHeader(Constant.CURRENT_USER_TOKEN);
        Assert.isTrue(StringUtils.isNotBlank(token), "请传入token");
//        ListResponse<Long> res = merchantFeignClient.getCommIdListByToken(token);
        TokenRequest tokenRequest = new TokenRequest();
        tokenRequest.setToken(token);
        ObjectResponse<SysUserVo> res =  authCoreClient.getLoginUserByToken(tokenRequest);
        Assert.isTrue(res.getStatus() == 0 && res.getData() != null, res.getError());
        upgradeTaskRequest.setOpId(res.getData().getId());
        upgradeTaskRequest.setOpName(res.getData().getUsername());

        Assert.isTrue(upgradeTaskRequest.getEvseIds() != null && !upgradeTaskRequest.getEvseIds().isEmpty(),
                "请传入待升级的桩列表。");
        if(upgradeTaskRequest.getTaskId() == null || upgradeTaskRequest.getTaskId() <= 0) {
            Assert.isTrue(upgradeTaskRequest.getBundleId() != null, "请传入升级包编号。");
            Assert.isTrue(StringUtils.isNotBlank(upgradeTaskRequest.getSiteId()), "请传入siteId");
        }

        BaseResponse ret = updateTaskService.startTask(upgradeTaskRequest);

        log.info("<< 发起桩升级请求完成");

        return ret;
    }

    /**
     * 根据站点id获取站点下全部桩信息
     *
     * @param listEvseRequest
     * @return
     * @throws DcServiceException
     */
    @PostMapping("/listBySiteId")
    public ListResponse<EvseVo> listBySiteId(@RequestBody ListEvseRequest listEvseRequest) throws DcServiceException {
        ListResponse<EvseVo> evseVoList = iotEvseClient.listBySiteIdForUpgrade(listEvseRequest);
        return evseVoList;
    }


}