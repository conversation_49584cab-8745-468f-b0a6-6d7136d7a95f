package com.cdz360.biz.device.business.rest;


import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.biz.device.business.entity.request.SiteGeoListRequest;
import com.cdz360.biz.device.business.entity.result.SiteDetailInfoVo;
import com.cdz360.biz.device.business.rest.base.BaseController;
import com.cdz360.biz.device.business.service.SiteQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2018年8月30日
 */
@RequestMapping("/api/site")
@RestController
@Slf4j
@Deprecated
public class SiteQueryController extends BaseController {



    @Autowired
    private SiteQueryService siteQueryService;

    /**
     * 根据站点ID获取站点信息 (运营支撑平台使用)
     *
     * @param siteId
     * @return
     * @throws DcServiceException
     */
    @PostMapping("/getSiteDetailBySiteId")
    public ObjectResponse<SiteDetailInfoVo> getSiteDetailBySiteId(@RequestParam("siteId") String siteId,
                                                                  @RequestParam(value = "latitude", required = false) Double latitude,
                                                                  @RequestParam(value = "longitude", required = false) Double longitude)
            throws DcServiceException {
        log.info("siteId = {}", siteId);
        Assert.isTrue(com.cdz360.base.utils.StringUtils.isNotBlank(siteId), "站点id不能为空");
        SiteGeoListRequest siteGeoListRequest = new SiteGeoListRequest();
        siteGeoListRequest.setSiteId(siteId);
        siteGeoListRequest.setLatitude(latitude);
        siteGeoListRequest.setLongitude(longitude);
        SiteDetailInfoVo vo = siteQueryService.getSiteDetailBySiteId(siteGeoListRequest);
        return new ObjectResponse<>(vo);
    }



}
