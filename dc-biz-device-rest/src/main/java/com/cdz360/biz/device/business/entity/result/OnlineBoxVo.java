package com.cdz360.biz.device.business.entity.result;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Classname OnlineBoxVo
 * @Description TODO
 * @Date 2019/7/11 17:28
 * @Created by JLei
 * @Email <EMAIL>
 */
@Data
public class OnlineBoxVo implements Serializable {
    /**
     * 站点ID
     */
    private String siteId;
    /**
     * 站点下离线桩ID集合
     */
    private List<String> offlineEvseIds = new ArrayList<>();
    /**
     * 站点下在线桩列表
     */
    private List<BoxInfoVo> onlineBoxInfoVos = new ArrayList<>();

}
