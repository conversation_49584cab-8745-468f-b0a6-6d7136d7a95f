<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.device.business.mapper.SubTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cdz360.biz.device.business.entity.po.SubTemplate">
        <id column="id" property="id"/>
        <result column="template_id" property="templateId"/>
        <result column="code" property="code"/>
        <result column="num" property="num"/>
        <result column="version" property="version"/>
        <result column="start_power" property="startPower"/>
        <result column="stop_power" property="stopPower"/>
        <result column="start_time" property="startTime"/>
        <result column="stop_time" property="stopTime"/>
        <result column="price" property="price" jdbcType="DECIMAL"/>
        <result column="scale" property="scale"/>
        <result column="service_price" property="servicePrice"/>
        <result column="service_scale" property="serviceScale"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="tariff_tag" property="tariffTag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        template_id AS templateId,
        code,
        version,
        start_power AS startPower,
        stop_power AS stopPower,
        start_time AS startTime,
        stop_time AS stopTime,
        price,
        `scale`,
        service_price AS servicePrice,
        service_scale AS serviceScale,
        remark,
        create_time AS createTime,
        tariff_tag AS tariffTag
    </sql>

    <select id="findAllForClean" resultType="java.lang.Long">
        SELECT template_id from t_sub_template where num is NULL GROUP BY template_id ORDER BY template_id limit 0,10
    </select>

    <select id="findByTemplateId" resultMap="BaseResultMap">
        SELECT * from t_sub_template where template_id = #{templateId}
    </select>

    <update id="updateByCondition">
        UPDATE t_sub_template
        <set>
            <if test="num != null">
                num = #{num}
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>
