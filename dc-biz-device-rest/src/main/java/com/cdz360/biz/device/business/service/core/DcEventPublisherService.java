package com.cdz360.biz.device.business.service.core;
//
//import com.cdz360.data.sync.service.DcEventPublisher;
//import com.cdz360.biz.device.business.entity.po.Site;
//import com.cdz360.biz.device.business.mapper.SiteManageMapper;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
///**
// * @Classname DcEventPublisherService
// * @Description 推送MQ消息
// * @Date 2019/11/1
// * @Created by wangzheng
// */
//@Service
//public class DcEventPublisherService {
//    @Autowired
//    private DcEventPublisher dcEventPublisher;
//    @Autowired
//    private SiteManageMapper siteManageMapper;
//
//    public void publishSiteInfo(String siteId) {
//        Site site = this.siteManageMapper.selectById(siteId);
//        com.cdz360.data.sync.model.Site siteEvent =
//                new com.cdz360.data.sync.model.Site();
//        siteEvent.setSiteId(site.getId())
//                .setName(site.getName())
//                .setTopCommId(site.getTopCommId())
//                .setCommId(site.getOperateId())
//                .setStatus(site.getStatus())
//                .setProvince(String.valueOf(site.getProvince()))
//                .setCity(String.valueOf(site.getCity()))
//                .setArea(String.valueOf(site.getArea()))
//                .setAddress(site.getAddress())
//                .setLon(site.getLongitude())
//                .setLat(site.getLatitude())
//                .setPriceCode(site.getTemplateId())
//                .setSiteType(site.getType())
//                .setCommPhone(site.getPhone())
//                .setPhone(site.getContactsPhone());
//        this.dcEventPublisher.publishSiteInfo(siteEvent);
//    }
//}