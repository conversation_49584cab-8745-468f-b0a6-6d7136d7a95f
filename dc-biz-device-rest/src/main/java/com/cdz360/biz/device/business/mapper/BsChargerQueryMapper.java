package com.cdz360.biz.device.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdz360.biz.device.business.entity.po.BsCharger;
import com.cdz360.biz.device.business.entity.request.ChargerListRequest;
import com.cdz360.biz.device.business.entity.result.ChargerInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 基础设备枪头数据表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-07-06
 */
@Mapper
public interface BsChargerQueryMapper extends BaseMapper<BsCharger> {

    /**
     * 获取枪头信息列表
     *
     * @param request
     * @return
     */
    List<ChargerInfoVo> getChargerInfoList(ChargerListRequest request);

    List<ChargerInfoVo> selectChargerNamesByBcIds(Map<String, Object> map);

    List<ChargerInfoVo> selectChargerNamesByBusinessIds(Map<String, Object> map);

    Long getCommIdByPlugNo(Map<String, Object> map);

    List<ChargerInfoVo> getChargerListOfSite(@RequestParam("siteId") String siteId);
}
