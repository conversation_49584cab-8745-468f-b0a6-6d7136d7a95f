package com.cdz360.biz.device.common.dingchong;

import com.cdz360.base.model.base.vo.BaseObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @Classname ChargeV2
 * @Description TODO
 * @Date 11/9/2019 2:01 PM
 * @Created by Rafael
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ChargeV2 extends BaseObject {

    @ApiModelProperty(value = "价格编码, 取值范围 0 ~ 255, 不能重复")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private int code;//价格编码, 取值范围 0 ~ 255, 不能重复

    @ApiModelProperty(value = "计价开始时间, 格式为 HH:MM, 从 00:00 开始, 开始时间取闭区间.")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String startTime;//计价开始时间, 格式为 HH:MM, 从 00:00 开始, 开始时间取闭区间.

    @ApiModelProperty(value = "计价结束时间, 格式为 HH:MM, 至 24:00 为止, 结束时间取开区间")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String stopTime;//计价结束时间, 格式为 HH:MM, 至 24:00 为止, 结束时间取开区间

    @ApiModelProperty(value = "电费单价, 单位'元'")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal elecPrice;//电费单价, 单位'元'

    @ApiModelProperty(value = "服务费单价, 单位'元'")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal servPrice;//服务费单价, 单位'元'
}