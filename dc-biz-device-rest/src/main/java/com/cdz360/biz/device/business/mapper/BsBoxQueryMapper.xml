<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.device.business.mapper.BsBoxQueryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cdz360.biz.device.business.entity.po.BsBox">
        <id column="box_code" property="boxCode"/>
        <result column="box_out_factory_code" property="boxOutFactoryCode"/>
        <result column="box_charger_type" property="boxChargerType"/>
        <result column="business_id" property="businessId"/>
        <result column="station_code" property="stationCode"/>
        <result column="status" property="status"/>
        <result column="charger_num" property="chargerNum"/>
        <result column="current_type" property="currentType"/>
        <result column="product_name" property="productName"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="last_modify_time" property="lastModifyTime"/>
        <result column="charging_way" property="chargingWay"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        box_code AS boxCode,
        box_out_factory_code AS boxOutFactoryCode,
        box_charger_type AS boxChargerType,
        business_id AS businessId,
        station_code AS stationCode,
        status AS status,
        charger_num AS chargerNum,
        current_type AS currentType,
        product_name AS productName,
        remark AS remark,
        create_time AS createTime,
        last_modify_time AS lastModifyTime,
        charging_way AS chargingWay
    </sql>

    <!--设备简单信息查询结果-->
    <sql id="select_simple_column_list">
        box.box_code AS deviceId,
        box.box_out_factory_code AS serialNumber,
        box.box_charger_type AS deviceType,
        box.business_id AS businessId,
        box.station_code AS siteId,
        box.`status` AS `status`,
        box.charger_num AS validateConnectorCount,
        box.current_type AS currentType,
        box.product_name AS productName,
        box.box_name AS boxName,
        box.remark AS remark,
        box.is_associate_site_template AS isAssociateSiteTemplate,
        box.template_id AS templateId,
        box.template_name AS templateName,
        box.isUseSiteDefaultSetting AS isUseSiteDefaultSetting
    </sql>

    <!--根据桩号获取设备ID-->
    <select id="getDeviceIdBySerialNumber" resultType="java.lang.String">
        SELECT box_code FROM bs_box WHERE box_out_factory_code = #{serialNumber}
    </select>

    <!--根据站点id获取站点下设备id列表-->
    <select id="getBoxCodeListBySiteId" resultType="java.lang.String">
        SELECT box_code AS deviceId FROM bs_box WHERE station_code = #{siteId}
    </select>

    <!--获取设备简单列表-->
    <select id="getBoxSimpleList" resultType="com.cdz360.biz.device.business.entity.result.BoxInfoVo">
        SELECT
        <include refid="select_simple_column_list"/>,
        bbs.STATUS AS sendStatus
        FROM
        bs_box box LEFT JOIN t_bs_box_setting bbs ON
        box.box_code = bbs.boxCode
        <where>
            <if test="siteId != null and siteId != ''">
                AND box.station_code = #{siteId}
            </if>
            <if test="commercialIds != null and commercialIds.size() >0">
                AND box.business_id in
	            <foreach collection="commercialIds" item="item" index="index" separator="," open="(" close=")">
		            #{item}
	            </foreach>
            </if>
            <if test="keywords != null and keywords != ''">
                AND (
                box.box_out_factory_code = #{keywords}
                OR box.box_out_factory_code like CONCAT('%',#{keywords},'%')
                OR LOCATE(#{keywords}, box.remark)
                OR LOCATE(#{keywords},box.box_name)
                )
            </if>
            <if test="deviceIdList !=null and deviceIdList.size() >0">
                AND box.box_code in
                <foreach collection="deviceIdList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" lastQueryTime != null and lastQueryTime != ''">
                    AND  <![CDATA[ box.last_modify_time >= #{lastQueryTime} ]]>
            </if>
        </where>
        ORDER BY box.create_time DESC
    </select>

    <!--获取设备简单列表-->
    <select id="BoxSettingSimpleList" resultType="com.cdz360.biz.device.business.entity.result.BoxSettingInfoVo">
        SELECT
        <include refid="select_simple_column_list"/>
        ,bbs.id AS settingId
        FROM
        bs_box box RIGHT JOIN t_bs_box_setting bbs
        ON
        bbs.boxCode=box.box_code
        <where>
            1=1
            <if test="isUseSiteDefaultSetting != null">
                AND box.isUseSiteDefaultSetting = #{isUseSiteDefaultSetting}
            </if>
            <if test="siteId != null and siteId != ''">
                AND box.station_code = #{siteId}
            </if>
            <if test="commercialIds != null and commercialIds.size() >0">
                AND box.business_id in
                <foreach collection="commercialIds" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="keywords != null and keywords != ''">
                AND (
                box.box_out_factory_code = #{keywords}
                OR LOCATE(#{keywords}, box.remark)
                OR LOCATE(#{keywords},box.box_name)
                )
            </if>
            <if test="deviceIdList !=null and deviceIdList.size() >0">
                AND box.box_code in
                <foreach collection="deviceIdList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test=" lastQueryTime != null and lastQueryTime != ''">
                AND  <![CDATA[ box.last_modify_time >= #{lastQueryTime} ]]>
            </if>
        </where>
        ORDER BY box.create_time DESC
    </select>

    <!--临时接口-->
    <select id="getDeviceStatistics" resultType="java.util.Map">
        SELECT
            count(0) AS boxCount,
            IFNULL(SUM(box.charger_num),0) AS chargerCount,
            IFNULL(SUM(CASE WHEN box.current_type = '0' THEN 1 ELSE 0 END ),0) AS boxCountOfAc,
            IFNULL(SUM(CASE WHEN box.current_type = '1' THEN 1 ELSE 0 END ),0) AS boxCountOfDc,
            IFNULL(SUM(CASE WHEN box.current_type = '0' THEN box.charger_num ELSE 0 END ),0) AS chargerCountOfAc,
            IFNULL(SUM(CASE WHEN box.current_type = '1' THEN box.charger_num ELSE 0 END ),0) AS chargerCountOfDc
        FROM
            bs_box box
        WHERE
            station_code = #{siteId}
    </select>

    <!--根据计费模板Id获取所有与此计费模板绑定的设备-->
    <select id="getBoxIOnfoListByTemplateId" resultType="com.cdz360.biz.device.business.entity.result.BoxInfoVo">
        SELECT
        <include refid="select_simple_column_list"/>,
        site.name AS siteName
        FROM
        bs_box box
        LEFT JOIN t_site site ON site.id = box.station_code
        WHERE box.template_id IN
        <foreach item="item" index="index" collection="templateIdList"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="siteId != null and siteId != ''">
            AND box.station_code = #{siteId}
            AND box.is_associate_site_template = 1
            AND box.yx_bz = '1'
        </if>
    </select>

    <!--根据siteId获取默认采用场站计费模板的设备-->
    <select id="getBoxIOnfoListBySiteId" resultType="com.cdz360.biz.device.business.entity.result.BoxInfoVo">
        SELECT
        <include refid="select_simple_column_list"/>,
        site.name AS siteName
        FROM
        bs_box box
        LEFT JOIN t_site site ON site.id = box.station_code
        WHERE
            box.station_code = #{siteId}
            AND box.is_associate_site_template = 1
            AND box.yx_bz = '1'
    </select>

    <!-- 查询桩名-->
    <select id="selectEvseNamesByDeviceIds" resultType="com.cdz360.biz.device.business.entity.result.BoxInfoVo">
        select box_code deviceId, box_name boxName
        from bs_box
        where 1=1
        <if test="deviceIds!=null and deviceIds.size()>0">
            AND box_code in
            <foreach collection="deviceIds" index="index" item="item" open="("
                     separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getAllBoxBySiteId" resultType="java.lang.String">
        SELECT box_out_factory_code from bs_box where station_code=#{siteId} and isUseSiteDefaultSetting=1
    </select>
</mapper>
