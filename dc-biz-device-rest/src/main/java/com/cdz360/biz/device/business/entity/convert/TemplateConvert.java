package com.cdz360.biz.device.business.entity.convert;

import com.cdz360.biz.device.business.constant.TariffTagEnum;
import com.cdz360.biz.device.business.constant.TemplateCalculateUnit;
import com.cdz360.biz.device.business.entity.po.SubTemplate;
import com.cdz360.biz.device.business.entity.po.Template;
import com.cdz360.biz.device.business.entity.result.SubTemplateInfoVo;
import com.cdz360.biz.device.business.entity.result.TemplateFullInfoVo;
import com.cdz360.biz.device.business.entity.result.TemplateInfoVo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date Create on 2018/12/21 19:20
 */
public class TemplateConvert {


    /**
     * @param template
     * @return
     */
    public static TemplateInfoVo convertToTemplateInfoVo(Template template) {
        if (template == null) {
            return null;
        }
        TemplateInfoVo vo = new TemplateInfoVo();
        // 计费模板ID
        vo.setId(template.getId());
        // 计费模板编号**唯一标识**
        vo.setCode(template.getCode());
        // 版本号
        vo.setVersion(template.getVersion());
        // 模板名称
        vo.setName(template.getName());
        // 代理商ID
        vo.setCommercialId(template.getCommercialId());
        // 充电计费单位{@link com.chargerlink.device.business.constant.TemplateCalculateUnit}
        vo.setCalculateUnit(template.getCalculateUnit());
        // 充电计费方式{@link com.chargerlink.device.business.constant.TemplateCalculateTypeEnum}
        vo.setCalculateType(template.getCalculateType());
        // 充电收费方式{@link com.chargerlink.device.business.constant.TemplateChargingTypeEnum}
        vo.setChargingType(template.getChargingType());
        // 充电费备注
        vo.setRemarkCharge(template.getRemarkCharge());
        // 服务费备注
        vo.setRemarkService(template.getRemarkService());
        // 创建时间
        vo.setCreateTime(template.getCreateTime());
        // 是否免费
        vo.setFreeChargeFlag(template.getFreeChargeFlag());
        return vo;
    }

    /**
     * @param template
     * @return
     */
    public static TemplateFullInfoVo convertToTemplateFullInfoVo(Template template) {
        if (template == null) {
            return null;
        }
        TemplateFullInfoVo vo = new TemplateFullInfoVo();
        // 计费模板ID
        vo.setId(template.getId());
        // 计费模板编号**唯一标识**
        vo.setCode(template.getCode());
        // 版本号
        vo.setVersion(template.getVersion());
        // 模板名称
        vo.setName(template.getName());
        // 代理商ID
        vo.setCommercialId(template.getCommercialId());
        // 充电计费单位{@link com.chargerlink.device.business.constant.TemplateCalculateUnit}
        vo.setCalculateUnit(template.getCalculateUnit());
        // 充电计费方式{@link com.chargerlink.device.business.constant.TemplateCalculateTypeEnum}
        vo.setCalculateType(template.getCalculateType());
        // 充电收费方式{@link com.chargerlink.device.business.constant.TemplateChargingTypeEnum}
        vo.setChargingType(template.getChargingType());
//        // 充电费备注
//        vo.setRemarkCharge(template.getRemarkCharge());
//        // 服务费备注
//        vo.setRemarkService(template.getRemarkService());
        // 创建时间
        vo.setCreateTime(template.getCreateTime());
        // 是否免费
        vo.setFreeChargeFlag(template.getFreeChargeFlag());
        return vo;
    }

    /**
     * 生成服务费备注
     *
     * @param price
     * @param scale
     * @param calculateUnit
     * @return
     */
    public static String generatePriceDesc(BigDecimal price, Integer scale, Integer calculateUnit) {
        if (price == null || calculateUnit == null) {
            return "免费";
        }
        StringBuilder remark = new StringBuilder();
        switch (calculateUnit) {
            case TemplateCalculateUnit.UNIT_TIME: {
                //按时长计费
                remark.append(price).append("元").append("/");
                if ((scale / 60f) == 0f) {
                    remark.append("小时");
                } else {
                    remark.append(scale / 60f).append("小时");
                }
            }
            break;
            case TemplateCalculateUnit.UNIT_KWH: {
                //按度数计费
                remark.append(price).append("元").append("/");
                if (scale == 1) {
                    remark.append("度");
                } else {
                    remark.append(scale).append("度");
                }
            }
            break;
            default:
                break;
        }
        return remark.toString();
    }

    /**
     * @param subTemplate
     * @return
     */
    public static SubTemplateInfoVo convertToSubTemplateInfoVo(SubTemplate subTemplate) {
        if (subTemplate == null) {
            return null;
        }
        SubTemplateInfoVo vo = new SubTemplateInfoVo();
        // 开始时间**单位：分钟**
        vo.setStartTime(subTemplate.getStartTime());
        // 结束时间**单位：分钟**
        vo.setStopTime(subTemplate.getStopTime());
        vo.setNum(subTemplate.getNum());
        // 充电费价格-分子**单位：分**
        vo.setPrice(subTemplate.getPrice());
        // 充电费单位-分母**分钟或度数**
        vo.setScale(subTemplate.getScale());
        // 服务费价格-分子**单位：分**
        vo.setServicePrice(subTemplate.getServicePrice());
        // 服务费单位-分母**分钟或度数**
        vo.setServiceScale(subTemplate.getServiceScale());
        // 备注
        vo.setRemark(subTemplate.getRemark());
        //设置尖峰平谷TG

        for (TariffTagEnum tariffTagEnum : TariffTagEnum.values()) {
            if (tariffTagEnum.getValue().equals(subTemplate.getTariffTag())) {
                vo.setTariffTagName(tariffTagEnum.getValueName());
                vo.setTariffTag(tariffTagEnum.getValue() + "");
                break;
            }
        }
        vo.setTariffTag(subTemplate.getTariffTag() + "");
        return vo;
    }
}
