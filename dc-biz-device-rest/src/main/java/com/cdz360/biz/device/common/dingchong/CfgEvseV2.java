package com.cdz360.biz.device.common.dingchong;

import com.cdz360.base.model.base.vo.BaseObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * @Classname CfgEvseV2
 * @Description TODO
 * @Date 11/9/2019 1:59 PM
 * @Created by Rafael
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CfgEvseV2 extends BaseObject {

    @ApiModelProperty(value = "桩端长效秘钥版本号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer evsePasscodeVer;

    @ApiModelProperty(value = "桩端长效秘钥")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String evsePasscode;

    @ApiModelProperty(value = "桩端一级管理员登录密码, 数字型, 6~8位. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String adminCodeA;

    @ApiModelProperty(value = "桩端二级管理员登录密码, 数字型, 6~8位. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String adminCodeB;

    @ApiModelProperty(value = "是否支持 VIN 码充电. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean vin;

    @ApiModelProperty(value = "是否支持充电记录查询. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean queryChargeRecord;

    @ApiModelProperty(value = "是否支持扫码充电. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean qrCharge;

    @ApiModelProperty(value = "是否支持刷卡充电. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean cardCharge;

    @ApiModelProperty(value = "是否支持无卡充电. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean noCardCharge;

    @ApiModelProperty(value = "是否支持定时充电. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean timedCharge;

    @ApiModelProperty(value = "白天音量. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer dayVolume;

    @ApiModelProperty(value = "夜间音量. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer nightVolume;

    @ApiModelProperty(value = "桩端显示的二维码 URL. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String qrUrl;

    @ApiModelProperty(value = "充电停止方式, 不传表示不做变更")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private ChargeStopMode stopMode;

    @ApiModelProperty(value = "紧急充电卡列表. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<WhiteCardV2> whiteCards;

    @ApiModelProperty(value = "计费模板ID. 不传表示不修改价格")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer priceCode;

    @ApiModelProperty(value = "计费方案. 不传表示不做修改")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<ChargeV2> price;

    @ApiModelProperty(hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    @JsonIgnore
    private String cfgVer;
}