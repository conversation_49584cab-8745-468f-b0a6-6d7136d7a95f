package com.cdz360.biz.device.business.entity.result;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;


/**
 * @ClassName： UpdatePackageVo
 * @Description: // 桩升级包管理
 * @Email: <EMAIL>
 * @Author: JLEI
 * @CreateDate: 2019/9/11 10:59
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpdatePackageVo {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 包编码
     */
    @ApiModelProperty(value = "包编码")
    private String version;

    /**
     * 包名字
     */
    @ApiModelProperty(value = "包名字")
    private String name;

    /**
     * 升级要点
     */
    @ApiModelProperty(value = "升级要点")
    private String comment;

    /**
     * 上传时间
     */
    @ApiModelProperty(value = "上传时间")
    private Date uploadtime;

    /**
     * 新增时间
     */
    @ApiModelProperty(value = "新增时间")
    private Date createtime;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String operator;

    /**
     * 删除标记,1-删除,0-未删除
     */
    @ApiModelProperty(value = "删除标记,1-删除,0-未删除")
    private Integer delflag;

    private List<UpdatePackageModuleVo> updPkgModList;

    private static final long serialVersionUID = 1L;
}
