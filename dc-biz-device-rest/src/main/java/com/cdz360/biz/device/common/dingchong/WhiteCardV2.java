package com.cdz360.biz.device.common.dingchong;

import com.cdz360.base.model.base.vo.BaseObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @Classname WhiteCardV2
 * @Description TODO
 * @Date 11/9/2019 2:02 PM
 * @Created by Rafael
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WhiteCardV2 extends BaseObject {

    @ApiModelProperty(value = "物理卡号, 对用户可见")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String visibleNumber;

    @ApiModelProperty(value = "逻辑卡号, 对用户不可见, 仅用于鉴权")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cardNumber;

    @ApiModelProperty(value = "充电密码")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String passcode;
}