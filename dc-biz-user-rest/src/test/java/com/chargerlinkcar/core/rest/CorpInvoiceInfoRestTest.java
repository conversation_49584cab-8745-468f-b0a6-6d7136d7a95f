package com.chargerlinkcar.core.rest;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.handler;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.cus.rest.CorpInvoiceInfoRest;
import com.cdz360.biz.model.cus.corp.dto.CorpInvoiceInfoDto;
import com.chargerlinkcar.core.BaseMockTest;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

class CorpInvoiceInfoRestTest extends BaseMockTest {

    @Test
    void getCorpInvoiceInfo() throws Exception {
        String url = "/api/invoice/getCorpInvoiceInfo";

        MockHttpServletRequestBuilder rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("corpId", String.valueOf(289L));

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(CorpInvoiceInfoRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("getCorpInvoiceInfo")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();
    }

    @Test
    void saveCorpInvoiceInfo() throws Exception {
        String url = "/api/invoice/saveCorpInvoiceInfo";
        CorpInvoiceInfoDto dto = new CorpInvoiceInfoDto();
        dto.setCorpId(1L)
                .setUid(67949L)
                .setInvoiceWay(InvoicingMode.POST_SETTLEMENT)
                .setTempSalId(1L)
                .setProductTempId(1L);
        String req = JsonUtils.toJsonString(dto); // 请求体

        MockHttpServletRequestBuilder rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON).content(req);

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(CorpInvoiceInfoRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("saveCorpInvoiceInfo")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").doesNotExist())
                .andDo(print())
                .andReturn();
    }
}