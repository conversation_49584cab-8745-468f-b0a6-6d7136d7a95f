// package com.chargerlinkcar.core.balance.service;
//
// import com.chargerlinkcar.core.BaseMapperTest;
// import com.chargerlinkcar.core.balance.constant.BalanceType;
// import com.chargerlinkcar.core.balance.constant.FrozenType;
// import com.chargerlinkcar.framework.common.constant.UnFrozenType;
// import org.junit.jupiter.api.Test;
// import org.springframework.beans.factory.annotation.Autowired;
//
// import java.text.ParseException;
// import java.text.SimpleDateFormat;
//
// /**
//  * <AUTHOR>
//  *  //TODO
//  * @since 2019/7/17
//  **/
// public class PersonalBalanceTest extends BaseMapperTest {
//     @Autowired
//     private BalanceFactroy balanceFactroy;
//
//     @Test
//     public void payWithBalance() {
//     }
//
//     @Test
//     public void getBalanceRecord() {
//     }
//
//     @Test
//     public void initBalance() throws ParseException {
//         SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
//         SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//         System.out.println(sdf2.format(sdf.parse("2019-09-24T07:08:41.843+0000")));
//     }
//
//     @Test
//     public void frozenAmount() {
//         IBalance balance = balanceFactroy.createBalance(BalanceType.PERSONAL);
//         balance.frozenAmount(BalanceType.PERSONAL, FrozenType.INIT, 61902, "100001",true);
//     }
//
//     @Test
//     public void unFrozenAmount(){
//         IBalance balance = balanceFactroy.createBalance(BalanceType.PERSONAL);
//         balance.unFrozenAmount(BalanceType.PERSONAL, UnFrozenType.FULL, 61902L, 90L, 200L, "100001");
//     }
// }