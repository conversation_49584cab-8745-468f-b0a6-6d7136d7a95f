<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.cus.repository.CollectSiteMapper">

    <!--    &lt;!&ndash; 批量收藏充电点 &ndash;&gt;-->
    <!--    <insert id="saveCollect" parameterType="java.util.List" useGeneratedKeys="true">-->
    <!--        <selectKey resultType="long" keyProperty="id" order="AFTER">-->
    <!--            SELECT LAST_INSERT_ID()-->
    <!--        </selectKey>-->
    <!--        INSERT INTO collect (pid,uid,type,status,ctime) VALUES-->
    <!--        <foreach collection="list" item="list" index="index" separator=",">-->
    <!--        (#{list.pid},#{list.uid},#{list.type},#{list.status},#{list.ctime})-->
    <!--        </foreach>-->
    <!--    </insert>-->

    <!--    &lt;!&ndash;批量取消收藏充电点&ndash;&gt;-->
    <!--    <update id="delCollect" parameterType="java.util.List">-->
    <!--        <foreach collection="list" item="item" index="index" open="" close="" separator=";">-->
    <!--            UPDATE collect-->
    <!--            <set>-->
    <!--                status=#{item.status},mtime=#{item.mtime}-->
    <!--            </set>-->
    <!--            WHERE pid = #{item.pid}-->
    <!--        </foreach>-->
    <!--    </update>-->

    <!--    &lt;!&ndash;校验用户是否收藏充电点&ndash;&gt;-->
    <!--    <select id="repeatCollectCheck" resultType="java.lang.Integer">-->
    <!--        SELECT count(1) FROM collect WHERE uid=#{userId} AND status=1 AND type=1 AND pid=#{spotId}-->
    <!--    </select>-->

    <!--    &lt;!&ndash;获取用户关联的充电点id&ndash;&gt;-->
    <!--    <select id="getFavoriteSpotList" resultType="String">-->
    <!--        SELECT pid FROM collect  WHERE uid=#{uid} AND status=1 AND type=1 order by ctime desc-->
    <!--    </select>-->
</mapper>