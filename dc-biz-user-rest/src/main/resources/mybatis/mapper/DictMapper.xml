<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.cus.repository.DictMapper">

    <sql id="dictColumns">
        a.id AS "id",
        a.value AS "value",
        a.label AS "label",
        a.type AS "type",
        a.description AS "description"
    </sql>

    <sql id="dictColumnsList">
        a.id AS "id",
        a.value AS "value",
        a.label AS "label",
        a.type AS "type",
        a.description AS "description"
    </sql>

    <sql id="dictJoins">
    </sql>

    <select id="get" resultType="com.chargerlinkcar.framework.common.domain.Dict">
        SELECT
        <include refid="dictColumns"/>
        FROM d_card_manager.t_sys_dict a
        <include refid="dictJoins"/>
        WHERE a.id = #{id}
    </select>

    <!--根据类型查询字典-->
    <select id="findDictDataByType" resultType="com.chargerlinkcar.framework.common.domain.Dict">
        SELECT
        <include refid="dictColumnsList"/>
        FROM d_card_manager.t_sys_dict a
        WHERE
         a.type IN
        <foreach collection="typeList" index="index" item="item" open="("
          separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findList" resultType="com.chargerlinkcar.framework.common.domain.Dict">
        SELECT
        <include refid="dictColumnsList"/>
        FROM d_card_manager.t_sys_dict a
        <where>

            <if test="id != null and id != ''">
                AND a.id = #{id}
            </if>
            <if test="value != null and value != ''">
                AND a.value LIKE concat('%',#{value},'%')
            </if>
            <if test="label != null and label != ''">
                AND a.label LIKE concat('%',#{label},'%')
            </if>
            <if test="type != null and type != ''">
                AND a.type LIKE #{type}
            </if>
            <if test="description != null and description != ''">
                AND a.description LIKE concat('%',#{description},'%')
            </if>
        </where>
    </select>


    <select id="findAllList" resultType="com.chargerlinkcar.framework.common.domain.Dict">
        SELECT
        <include refid="dictColumnsList"/>
        FROM d_card_manager.t_sys_dict a


    </select>

    <insert id="insert">
        INSERT INTO d_card_manager.t_sys_dict(
        id,
        value,
        label,
        type,
        description
        ) VALUES (
        #{id},
        #{value},
        #{label},
        #{type},
        #{description}
        )
    </insert>

    <update id="update">
        UPDATE d_card_manager.t_sys_dict SET
        value = #{value},
        label = #{label},
        type = #{type},
        description = #{description}
        WHERE id = #{id}
    </update>


    <!--物理删除-->
    <update id="delete">
        DELETE FROM d_card_manager.t_sys_dict
        WHERE id = #{id}
    </update>

    <!-- 图片配置 -->
    <select id="getPictureConfig" resultType="com.cdz360.biz.cus.domain.PictureConfig">
        SELECT picture_size AS pictureSize,picture_measurement AS pictureMeasurement,picture_format AS pictureFormat
        FROM d_card_manager.t_picture_config
    </select>
</mapper>