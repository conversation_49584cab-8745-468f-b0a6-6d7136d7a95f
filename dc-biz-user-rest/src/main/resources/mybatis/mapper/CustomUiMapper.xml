<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.cus.repository.CustomUiMapper">

    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        id, merchants AS merchants, logo, use_commercial_logo as useCommercialLogo ,menu_color AS menuColor,
        domain_name AS domainName,use_commercial_domain as useCommercialDomain, btn_color AS btnColor,platformName
    </sql>

    <!--根据商户号查询界面设置 -->
    <select id="queryByMerchants" resultType="com.cdz360.biz.cus.domain.CustomUi">
        select
        <include refid="Base_Column_List"/>
        from t_custom_ui
        where merchants= #{merchants}
        order by id desc
        limit 1
    </select>
    <!--根据商户号查询界面设置 -->
    <select id="queryCustomUi" parameterType="com.cdz360.biz.cus.domain.CustomUi"
            resultType="com.cdz360.biz.cus.domain.CustomUi">
        select
        <include refid="Base_Column_List"/>
        from t_custom_ui
        <where>
            <if test="merchants != null and  merchants!= ''">
                merchants = #{merchants}
            </if>
            <if test="domainName != null and  domainName!= ''">
                domain_name = #{domainName}
            </if>
        </where>
        order by id desc
    </select>

    <insert id="insertCustomUi" parameterType="com.cdz360.biz.cus.domain.CustomUi">
        insert into t_custom_ui
        <trim prefix="(" suffix=")" suffixOverrides=",">
            merchants,
            <if test="logo != null">
                logo,
            </if>
            <if test="platformName != null">
                platformName,
            </if>
            <if test="useCommercialLogo != null">
                use_commercial_logo,
            </if>
            <if test="menuColor != null">
                menu_color,
            </if>
            <if test="domainName != null">
                domain_name,
            </if>
            <if test="btnColor != null">
                btn_color,
            </if>
            <if test="useCommercialDomain != null">
                use_commercial_domain,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{merchants,jdbcType=VARCHAR},
            <if test="logo != null">
                #{logo,jdbcType=VARCHAR},
            </if>
            <if test="platformName != null">
                #{platformName,jdbcType=VARCHAR},
            </if>
            <if test="useCommercialLogo != null">
                #{useCommercialLogo,jdbcType=TINYINT},
            </if>
            <if test="menuColor != null">
                #{menuColor,jdbcType=VARCHAR},
            </if>
            <if test="domainName != null">
                #{domainName,jdbcType=VARCHAR},
            </if>
            <if test="btnColor != null">
                #{btnColor,jdbcType=VARCHAR},
            </if>
            <if test="useCommercialDomain != null">
                #{useCommercialDomain,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.cdz360.biz.cus.domain.CustomUi">
        update t_custom_ui
        <set>
            <if test="logo != null">
                logo =#{logo},
            </if>
            <if test="platformName != null">
                platformName = #{platformName},
            </if>
            <if test="useCommercialLogo != null">
                use_commercial_logo =#{useCommercialLogo},
            </if>
            <if test="menuColor != null">
                menu_color =#{menuColor},
            </if>
            <if test="domainName != null">
                domain_name =#{domainName},
            </if>
            <if test="btnColor != null">
                btn_color =#{btnColor},
            </if>
            <if test="useCommercialDomain != null">
                use_commercial_domain =#{useCommercialDomain},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>