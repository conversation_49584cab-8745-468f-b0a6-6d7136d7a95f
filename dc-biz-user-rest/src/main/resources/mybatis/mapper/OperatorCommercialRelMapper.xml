<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.cus.repository.OperatorCommercialRelMapper">

    <!--根据客户运营商获取对应的设备运营商的商户信息-->
    <select id="queryCommercialIdByOperatorId" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT
        t1.commercial_id
        FROM
        op_operator_commercial_rel t1
        LEFT JOIN op_operator_rel t2 ON t1.operator_id = t2.device_operator_id
        WHERE
        t2.client_operator_id = #{operatorId}
    </select>
</mapper>

