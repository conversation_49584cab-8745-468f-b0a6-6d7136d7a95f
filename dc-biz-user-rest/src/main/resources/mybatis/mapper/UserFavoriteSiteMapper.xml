<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.cus.repository.UserFavoriteSiteMapper">

    <insert id="addToFavorites">
        INSERT IGNORE INTO t_user_favorite_site (uid, siteId, createTime)
        VALUE (#{uid}, #{siteId}, NOW())
    </insert>

    <delete id="removeTheCollection">
        DELETE FROM t_user_favorite_site WHERE uid = #{uid} AND siteId = #{siteId}
    </delete>

    <select id="ifFavorite" resultType="java.lang.Integer">
    SELECT COUNT(*) from t_user_favorite_site where uid = #{uid} and siteId = #{siteId}
  </select>

  <select id="getByUid" parameterType="com.chargerlinkcar.framework.common.domain.request.UserFavorSiteReq"
          resultType="com.chargerlinkcar.framework.common.domain.UserFavoriteSite">
    SELECT
        ufs.*
    FROM
        t_user_favorite_site ufs INNER JOIN t_site s on ufs.siteId = s.id
    <where>
      <if test="displayStatusList != null and displayStatusList.size() > 0">
        s.`status` in
        <foreach collection="displayStatusList" index="index" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="uid != null and uid != 0">
        AND uid = #{uid}
      </if>
    </where>
    ORDER BY createTime DESC
    LIMIT #{start},#{size}
  </select>
</mapper>