package com.cdz360.biz.cus.service.impl;
//
//import com.cdz360.biz.cus.domain.UserLevel;
//import com.cdz360.biz.cus.repository.UserLevelMapper;
//import com.cdz360.biz.cus.service.UserLevelService;
//import com.cdz360.biz.cus.service.UserServiceImpl;
//import com.chargerlinkcar.framework.common.domain.vo.UserVo;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.Date;
//import java.util.List;
//
///**
// * <AUTHOR>
// * @since 2018/11/25.
// */
//@Slf4j
//@Service
//public class UserLevelServiceImpl implements UserLevelService {
//
//    @Autowired
//    private UserLevelMapper userLevelMapper;
//
//    @Autowired
//    private UserServiceImpl iUserService;
//
//    /**
//     * 新增用户级别
//     *
//     * @param userLevel
//     * @return
//     */
//    @Override
//    public int insertUserLevel(UserLevel userLevel) {
//        userLevel.setCreateTime(new Date());
//        return userLevelMapper.insertUserLevel(userLevel);
//    }
//
//    /**
//     * 验证用户级别是否重复
//     *
//     * @param userLevel
//     * @return
//     */
//    @Override
//    public List<UserLevel> validateUserLevel(UserLevel userLevel) {
//        return userLevelMapper.validateUserLevel(userLevel);
//    }
//
//    /**
//     * 更新用户级别
//     *
//     * @param userLevel
//     * @return
//     */
//    @Override
//    public int updateUserLevelById(UserLevel userLevel) {
//        return userLevelMapper.updateUserLevelById(userLevel);
//    }
//
//    /**
//     * 根据客户id查询用户级别列表(APP)
//     *
//     * @param userId
//     * @return
//     */
//    @Override
//    public List<UserLevel> queryUserLevelListByid(Long userId) {
//        //先查询用户
//        UserVo user = iUserService.findInfoByUid(userId, null);
//        //组装查询条件
//        UserLevel userLevel = new UserLevel();
//        userLevel.setCommId(user.getCommId());
//        return queryUserLevelListByConditions(userLevel);
//    }
//
//    /**
//     * 根据条件查询用户级别列表
//     * <p>
//     * 按照阈值（到达阈值升级为本等级升序排列
//     *
//     * @return
//     */
//    @Override
//    public List<UserLevel> queryUserLevelList(UserLevel userLevel) {
//        return userLevelMapper.queryUserLevelList(userLevel);
//    }
//
//    /**
//     * 根据条件，查询用户级别列表
//     *
//     * @param userLevel
//     * @return
//     */
//    @Override
//    public List<UserLevel> queryUserLevelListByConditions(UserLevel userLevel) {
//        return userLevelMapper.queryUserLevelListByConditions(userLevel);
//    }
//
//    /**
//     * 根据id查询用户级别
//     *
//     * @param id
//     * @return
//     */
//    @Override
//    public UserLevel queryUserLevelById(Long id) {
//        return userLevelMapper.queryUserLevelById(id);
//    }
//}
