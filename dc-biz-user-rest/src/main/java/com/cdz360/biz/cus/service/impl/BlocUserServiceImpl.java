package com.cdz360.biz.cus.service.impl;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.cus.client.AuthCenterFeignClient;
import com.cdz360.biz.cus.client.DataCoreFeignClient;
import com.cdz360.biz.cus.client.TradingFeignClient;
import com.cdz360.biz.cus.domain.BlocUser;
import com.cdz360.biz.cus.domain.request.CardRequest;
import com.cdz360.biz.cus.domain.vo.BlocUserLoginVo;
import com.cdz360.biz.cus.domain.vo.BlocUserVo;
import com.cdz360.biz.cus.repository.BlocUserMapper;
import com.cdz360.biz.cus.repository.RBlocUserMapper;
import com.cdz360.biz.cus.repository.UserMapper;
import com.cdz360.biz.cus.service.IBlocUserService;
import com.cdz360.biz.cus.service.ICardService;
import com.cdz360.biz.cus.service.MerchantService;
import com.cdz360.biz.cus.service.RBlocUserService;
import com.cdz360.biz.cus.service.SiteDutyService;
import com.chargerlinkcar.framework.common.domain.BlocUserDto;
import com.chargerlinkcar.framework.common.domain.type.CardStatus;
import com.chargerlinkcar.framework.common.domain.vo.Card;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import jakarta.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * BlocUserServiceImpl
 * 
 * @since 2019/7/17
 * <AUTHOR>
 */
@Slf4j
@Service
public class BlocUserServiceImpl implements IBlocUserService {

    @Autowired
    private BlocUserMapper blocUserMapper;
    @Autowired
    private MerchantService merchantFeignClient;

    @Autowired
    private TradingFeignClient tradingFeignClient;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private RBlocUserMapper rBlocUserMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private RBlocUserService rBlocUserService;

    @Autowired
    @Lazy
    private ICardService cardService;

    @Autowired
    @Lazy
    private SiteDutyService siteDutyService;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Override
    public BlocUserDto findById(Long id) {
        return blocUserMapper.findById(id);
    }

    @Override
    public BlocUserDto findByUid(Long uid) {
        return blocUserMapper.findByUid(uid);
    }

    @Override
    public ListResponse<BlocUserDto> findByCondition(BlocUser blocUser) {
        return new ListResponse<>(blocUserMapper.findByCondition(blocUser));
    }

    /**
     * 通过commIdChain查询本身及下属商户的集团客户
     *
     * @return
     */
    public ListResponse<BlocUserDto> selectSubBlocUserByCommIdChain(@Nullable String commIdChain,
        @Nullable Boolean includedHlhtCorp) {
        List<BlocUserDto> result = blocUserMapper.selectByCommIdChainX(commIdChain,
            includedHlhtCorp);
        return RestUtils.buildListResponse(result);
    }

    /**
     * 通过commIdChain查询本身及上级商户的企业
     *
     * @param commIdChain
     * @param includedHlhtCorp
     * @return
     */
    public ListResponse<BlocUserDto> selectUpperBlocUserByCommIdChain(@Nullable String commIdChain,
        @Nullable Boolean includedHlhtCorp) {
        if (StringUtils.isBlank(commIdChain)) {
            return RestUtils.buildListResponse(List.of());
        }
        List<Long> ids = Arrays.stream(commIdChain.split(",")).map(Long::valueOf)
            .collect(Collectors.toList());
        List<BlocUserDto> result = blocUserMapper.selectUpperByCommIdChain(ids, includedHlhtCorp);
        return RestUtils.buildListResponse(result);
    }

    /**
     * 查询商户的集团客户 用于运营支撑平台
     *
     * @return
     */
    public ListResponse<BlocUserDto> selectSubBlocUserByTokenOnOperate() {
        List<BlocUserDto> result = blocUserMapper.selectByCommIdChain(null, null);
        return new ListResponse<>(result);
    }

    /**
     * 根据授信客户ID查询企业客户信息
     *
     * @param rBlocUserId
     * @return
     */
    @Override
    public ObjectResponse<BlocUserDto> getByRBlocUserId(Long rBlocUserId) {
        return RestUtils.buildObjectResponse(blocUserMapper.getByRBlocUserId(rBlocUserId));
    }

    /**
     * 根据集团Id查询集团基础信息(新增余额字段)
     *
     * @param blocUserId
     * @return
     */
    @Override
    public Map getBlocUserByBlocUserId(Long blocUserId) {
        Map map = blocUserMapper.getBlocUserByBlocUserId(blocUserId);
        if (map.size() > 0) {
            return map;
        } else {
            return null;
        }
    }


    /**
     * 更新集团信息
     *
     * @param blocUser
     * @return
     */
    @Override
    public BaseResponse updateBlocUser(BlocUser blocUser) {
//        int status = 0;
        blocUser.setUpdateTime(new Date());

        // 校验集团重复 暂对账户account/集团名称blocUserName做重复校验

        IotAssert.isTrue(
            blocUserMapper.countAccountSinId(blocUser.getId(), blocUser.getAccount()) == 0,
            "登录用户名已存在");

        IotAssert.isTrue(
            blocUserMapper.countCorpNameSinId(blocUser.getId(), blocUser.getBlocUserName()) == 0,
            "集团名称已存在");

        IotAssert.isTrue(blocUserMapper.updateBlocUserById(blocUser) > 0, "更新失败");

        return BaseResponse.success();
    }

    /**
     * 删除集团
     *
     * @param blocUserId
     * @return
     */
    @Override
    public BlocUserDto deleteBlocUserById(Long blocUserId) {
        Boolean status = false;
        BlocUserDto dto = blocUserMapper.findById(blocUserId);
        // 校验该集团下是否存在集团客户
        List<RBlocUser> result = rBlocUserService.findRBlocUserByBlocUserId(blocUserId);
        // java8 的新特性 stream表达式，从一个对象集合获取某个值返回新集合（相对普通foreach效率更高）
        List<Long> rBlocUserIds = result.stream().map(RBlocUser::getId)
            .collect(Collectors.toList());

        log.info("rBlocUserIds：{}", JsonUtils.toJsonString(rBlocUserIds));
        if (!rBlocUserIds.isEmpty()) {
            //判断授信账户下 是否有“充电中”、“待支付”状态的订单，如果存在这两种状态的订单，则不允许删除集团客户
//            ObjectResponse<Boolean> res = tradingFeignClient.queryChargingFlagByRblocUserIds(rBlocUserIds);
            ObjectResponse<Boolean> res = tradingFeignClient.queryChargingOrderByCorpId(blocUserId);
            FeignResponseValidate.check(res);
            if (res.getData()) {
                throw new DcServiceException("当前存在未结算订单，无法禁用集团账户");
            }
        }
        try {
            if (!rBlocUserIds.isEmpty()) {

                // 批量删除所属集团下的客户(逻辑删除)
                List<RBlocUser> users = rBlocUserMapper.findByBlocUserId(blocUserId);
                log.info("批量删除所属集团下的客户，相关的客户的默认支付账户将改为个人账户: {}",
                    JsonUtils.toJsonString(users));
                if (users != null && !users.isEmpty()) {
                    users.forEach(e -> {
                        Long userId = e.getUserId();
                        Long balanceId = e.getId();
                        userMapper.deleteBlocUserByBalanceId(userId, balanceId);
                    });
                }
//                userFeignClient.logicDeleteBatchIds(rBlocUserList.getData()); //BUG1118-281 不对用户进行逻辑删除，只是让用户不能用授信账户开启充电
            }
            // 集团逻辑删除
            int i = blocUserMapper.logicDeleteBlocUserById(blocUserId);
            if (i > 0) {
                status = true;
            }
            //将下列状态的卡排除在外
            List<String> excludeCardStatusList = new ArrayList<>();
            excludeCardStatusList.add(CardStatus.INACTIVE.getCode());
            excludeCardStatusList.add(CardStatus.LOCK.getCode());
            excludeCardStatusList.add(CardStatus.FAILURE.getCode());
            excludeCardStatusList.add(CardStatus.EXPIRED.getCode());
            excludeCardStatusList.add(CardStatus.DELETED.getCode());
            CardRequest cardRequest = new CardRequest();
            cardRequest.setCorpId(blocUserId);
            cardRequest.setExcludeCardStatusList(excludeCardStatusList);
            //查询出集团客户名下有效的紧急卡
            ListResponse<Card> response = cardService.queryCardByCondition(cardRequest);
            FeignResponseValidate.check(response);
            List<Card> cardList = response.getData();
            //多场站重新下发紧急卡，卡状态下发后会回写成已删除(异步调用)
            siteDutyService.sendBatchBySiteList(cardList);
            //修改d_charger.t_r_corp
            blocUserMapper.updateCorpEnable(blocUserId);
            //修改auth_center.t_corp中enable
            authCenterFeignClient.updateCorpEnable(blocUserId);
            //修改d_charger.t_site_defult_setting中   无卡充电结算账户信息
            dataCoreFeignClient.updateNoCardSetting(blocUserId);

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        if (!status) {
            throw new DcServiceException("禁用失败");
        }
        return dto;
    }

    @Override
    public BlocUserDto enableBlocUserById(Long blocUserId) {
        Boolean flag = false;
        BlocUserDto dto = blocUserMapper.findById(blocUserId);
        try {
            int i = blocUserMapper.enableBlocUserById(blocUserId);
            //修改d_charger中的  t_r_corp
            blocUserMapper.updateCorpEnable(blocUserId);
            //修改auth_center.t_corp中enable
            authCenterFeignClient.updateCorpEnable(blocUserId);
            if (i > 0) {
                flag = true;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        if (!flag) {
            throw new DcServiceException("启用失败");
        }
        return dto;
    }

    /**
     * 校验集团重复 暂对账户account/集团名称blocUserName做重复校验
     *
     * @param account
     * @param blocUserName
     * @return
     */
    @Override
    public int queryBlocUserByCondition(String account, String blocUserName) {
        int i = blocUserMapper.queryBlocUserByCondition(account, blocUserName);
        return i;
    }

    /**
     * 登录集团账户
     *
     * @return
     */
    @Override
    public ObjectResponse<BlocUserVo> loginBlocUser(BlocUserLoginVo blocUserLoginVo) {
        //根据用户名查询
        BlocUserVo blocUserVo = blocUserMapper.getBlocUserByAccount(blocUserLoginVo.getAccount());
        if (blocUserVo == null || blocUserVo.getEnable() == false) {
            throw new DcServiceException("用户名不存在或已被禁用");
        } else {
            if (!blocUserLoginVo.getPassword().equals(blocUserVo.getPassword())) {
                throw new DcServiceException("用户名或密码错误");
            }
        }
        //密码不返回
        blocUserVo.setPassword("");
        // 登录后具体返回对象需要与前端再次确认后实现
        return new ObjectResponse<>(blocUserVo);
    }

    @Override
    public ObjectResponse<BlocUserVo> getBlocUserByAccount(String username) {
        BlocUserVo blocUserVo = blocUserMapper.getBlocUserByAccount(username);
        if (blocUserVo == null || blocUserVo.getEnable() == false) {
            throw new DcServiceException("用户名不存在或已被禁用");
        }
        return new ObjectResponse<>(blocUserVo);
    }

    public ListResponse<Long> getBlocUserIdsByCorpAndUser(Long blocUserId, Long corpOrgId) {
        List<Long> list = rBlocUserMapper.getBlocUserIdsByCorpAndUser(blocUserId, corpOrgId);
        ListResponse<Long> ret = new ListResponse<>(list, (long) list.size());
        return ret;
    }

}