package com.cdz360.biz.cus.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.cus.client.AuthCenterFeignClient;
import com.cdz360.biz.cus.constant.CardConstants;
import com.cdz360.biz.cus.domain.User;
import com.cdz360.biz.cus.domain.vo.UserBalanceVo;
import com.cdz360.biz.cus.domain.vo.UserSampleVo;
import com.cdz360.biz.cus.repository.CardMapper;
import com.cdz360.biz.cus.repository.UserMapper;
import com.cdz360.biz.cus.service.impl.BlocUserServiceImpl;
import com.cdz360.biz.cus.service.impl.VinServiceImpl;
import com.cdz360.biz.cus.utils.RedisUtil;
import com.cdz360.biz.ds.cus.ro.basic.ds.UserOpenidRoDs;
import com.cdz360.biz.ds.cus.ro.basic.ds.UserRoDs;
import com.cdz360.biz.ds.cus.ro.comm.ds.CommCusRefRoDs;
import com.cdz360.biz.ds.cus.ro.comm.ds.TRCommercialRoDs;
import com.cdz360.biz.ds.cus.ro.commScore.ds.CommScoreLevelRoDs;
import com.cdz360.biz.ds.cus.ro.mechant.ds.CommercialRoDs;
import com.cdz360.biz.ds.cus.ro.site.ds.SiteRoDs;
import com.cdz360.biz.ds.cus.rw.basic.ds.UserOpenidRwDs;
import com.cdz360.biz.ds.cus.rw.basic.ds.UserRwDs;
import com.cdz360.biz.model.cus.basic.type.BizUserConstants;
import com.cdz360.biz.model.cus.comm.po.TRCommercialPo;
import com.cdz360.biz.model.cus.commScore.vo.CommScoreVo;
import com.cdz360.biz.model.cus.site.po.SitePo;
import com.cdz360.biz.model.cus.user.dto.CusSampleDto;
import com.cdz360.biz.model.cus.user.param.AddUserParam;
import com.cdz360.biz.model.cus.user.param.ListCustomerParam;
import com.cdz360.biz.model.cus.user.param.ListCustomerSiteParam;
import com.cdz360.biz.model.cus.user.param.ModifyCusInfoParam;
import com.cdz360.biz.model.cus.user.po.UserOpenidPo;
import com.cdz360.biz.model.cus.user.po.UserPo;
import com.cdz360.biz.model.cus.user.type.UserOpenidType;
import com.cdz360.biz.model.cus.vin.dto.UserCarTinyDto;
import com.cdz360.biz.model.merchant.dto.CommercialDto;
import com.cdz360.biz.model.merchant.param.ListCommercialParam;
import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import com.cdz360.biz.utils.feign.pcp.PcpAsyncFeignClient;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import com.chargerlinkcar.framework.common.domain.BlocUserDto;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.PointPo;
import com.chargerlinkcar.framework.common.domain.pay.PcpQueryVinStatusParam;
import com.chargerlinkcar.framework.common.domain.vo.CardVo;
import com.chargerlinkcar.framework.common.domain.vo.CheckPhoneBindVo;
import com.chargerlinkcar.framework.common.domain.vo.CommCusRef;
import com.chargerlinkcar.framework.common.domain.vo.UserPropVO;
import com.chargerlinkcar.framework.common.domain.vo.UserVo;
import com.chargerlinkcar.framework.common.domain.vo.VinDto;
import com.chargerlinkcar.framework.common.domain.vo.VinParam;
import com.chargerlinkcar.framework.common.service.DcCusBalanceService;
import com.chargerlinkcar.framework.common.utils.BCryptHasher;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class UserBizService //implements IUserService
{


    @Autowired
    RedisUtil redisDao;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserRoDs userRoDs;

    @Autowired
    private UserRwDs userRwDs;


    @Autowired
    private UserOpenidRwDs userOpenidRwDs;

    @Autowired
    private UserOpenidRoDs userOpenidRoDs;


    @Autowired
    private MerchantService merchantService;

    @Autowired
    private CommercialService commercialService;

    @Autowired
    private CardMapper cardMapper;

    @Autowired
    private DcCusBalanceService dcCusBalanceService;
    @Autowired
    private UserBizService userService;
    @Autowired
    private CommercialRoDs commercialRoDs;
    @Autowired
    private CommScoreLevelRoDs commScoreLevelRoDs;

    @Autowired
    private PublishCusInfoService publishCusInfoService;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private VinServiceImpl vinService;

    @Autowired
    private BlocUserServiceImpl blocUserService;

    @Autowired
    private TRCommercialRoDs trCommercialRoDs;

    @Autowired
    private SiteRoDs siteRoDs;

    @Autowired
    private CommCusRefRoDs commCusRefRoDs;
    @Autowired
    private PcpAsyncFeignClient pcpAsyncFeignClient;

    @Autowired
    private IManageUserService manageUserService;

    public User getUserByWxOpenId(Long topCommId, String openid) {
        return userMapper.findByWechatopenId(openid, topCommId);
    }

    /**
     * 判断手机号是否存在
     *
     * @param commericalId 商户id
     * @param mobile       手机号
     * @param nationalCode 手机区域编码
     * @return
     */

    public User findByCommIdAndPhone(Long commericalId, String mobile, String nationalCode) {
        User user = new User();
        if (StringUtils.isBlank(mobile) || StringUtils.isBlank(nationalCode)) {
            log.info("客户—根据手机号查询信息—参数有误");
            return null;
        }
        user.setCommId(commericalId);
        user.setPhone(mobile);
        user.setNationalCode(nationalCode);
        return userMapper.findByCommIdAndPhone(commericalId, mobile, nationalCode);
    }

    /**
     * 用户登录后，修改最后登录时间，如果是第一次登录，进行标记。
     *
     * @param userId    用户id
     * @param lastLogin 用户最后登录时间
     * @return
     */

    public Integer updateUserForLoginAfter(Long userId, Date lastLogin) {
        if (userId == null) {
            return null;
        }
        User user = new User();
        if (lastLogin == null) {
            user.setIsFirstLogin(CardConstants.YES);
        } else {
            user.setIsFirstLogin(CardConstants.NO);
        }

        user.setId(userId);

        user.setLastLogin(new Date());

        return userMapper.updateByUidSelective(user);
    }

    /**
     * 用户手机号绑定 微信公众号openId（wxOpenId）
     *
     * @param userId   用户id
     * @param wxOpenId 微信公众号openId
     * @return
     */

    public Integer updateUserWxOpenIdForLoginAfter(Long userId, String wxOpenId) {
        if (userId == null || wxOpenId == null) {
            return null;
        }
        User user = new User();

        user.setId(userId);

        user.setWxOpenId(wxOpenId);

        return userMapper.updateByUidSelective(user);
    }

    public void updateAliUserIdForLoginAfter(UserPo user, String aliUserId, String appId) {
        if (user == null || aliUserId == null) {
            return;
        }
        userOpenidRwDs.updateAlipayOpenid(user, aliUserId, appId);
    }

    /**
     * 更新 t_user_openid 表的 extraX 字段
     */
    @Transactional
    public void addOrUpdateUserOpenid(UserOpenidPo param) {
        Assert.notNull(param.getUid(), "参数错误, uid 不能为空");
        Assert.notNull(param.getTopCommId(), "参数错误,topCommId不能为空");
        Assert.notNull(param.getType(), "参数错误,type不能为空");
        Assert.notNull(param.getOpenid(), "参数错误,openid不能为空");
        UserOpenidPo userOpenid = userOpenidRoDs.getUserOpenid(param.getTopCommId(),
            param.getType(),
            param.getOpenid(),
            param.getAppId());
        if (userOpenid == null) {
            userOpenidRwDs.addUserOpenid(param);
        } else if (!StringUtils.equals(param.getExtraA(), userOpenid.getExtraA())
            || !StringUtils.equals(param.getExtraB(), userOpenid.getExtraB())) {
            UserOpenidPo updatePo = new UserOpenidPo();
            updatePo.setId(userOpenid.getId());
            if (param.getExtraA() != null) {
                updatePo.setExtraA(param.getExtraA());
            }
            if (param.getExtraB() != null) {
                updatePo.setExtraB(param.getExtraB());
            }
            userOpenidRwDs.updateUserOpenId(updatePo);
        } else {
            log.warn("客户的第三方帐号信息不需要修改. param= {}, userOpenid= {}",
                param,
                JsonUtils.toJsonString(userOpenid));
        }
    }

    /**
     * 更新 t_user_openid 表的 extraX 字段
     */
    @Transactional
    public void updateAlipayOpenid(UserOpenidPo param) {
//        Assert.notNull(param.getUid(), "参数错误, uid 不能为空");
        Assert.notNull(param.getTopCommId(), "参数错误,topCommId不能为空");
        Assert.notNull(param.getType(), "参数错误,type不能为空");
        Assert.notNull(param.getOpenid(), "参数错误,openid不能为空");
        Assert.isTrue(StringUtils.equals(UserOpenidType.ALIPAY_OPENID, param.getType()),
            "参数错误,仅支持修改alipay_openid");
//        Assert.notNull(param.getOpenid(), "参数错误,openid不能为空");
        UserOpenidPo userOpenid = userOpenidRoDs.getUserOpenid(param.getTopCommId(),
            param.getType(),
            param.getOpenid(),
            param.getAppId());
        if (userOpenid == null) {
            log.warn("参数错误,openid对应的账号不存在. param= {}", JsonUtils.toJsonString(param));
            throw new DcArgumentException("参数错误,openid对应的账号不存在");
        } else if (!StringUtils.equals(param.getExtraA(), userOpenid.getExtraA())
            || !StringUtils.equals(param.getExtraB(), userOpenid.getExtraB())) {
            UserOpenidPo updatePo = new UserOpenidPo();
            updatePo.setId(userOpenid.getId());
            if (param.getExtraA() != null) {
                updatePo.setExtraA(param.getExtraA());
            }
            if (param.getExtraB() != null) {
                updatePo.setExtraB(param.getExtraB());
            }
            userOpenidRwDs.updateUserOpenId(updatePo);
        } else {
            log.info("账号信息不需要变更. param = {}， userOpenid= {}", param,
                JsonUtils.toJsonString(userOpenid));
        }
    }

    /**
     * 新增/更新 支付宝即插即充 vin 信息
     *
     * @param vin none 表示关闭
     */
    @Transactional
    public void addOrUpdateAlipayVin(Long topCommId,
        String alipayAppId,
        String alipayOpenid,
        String vin,
        String phone,
        String carNo,
        Boolean unbind) {
        log.info(
            ">> topCommId= {}, alipayAppId= {}, alipayOpenid= {}, vin= {}, phone= {}, carNo= {}, unabind= {}",
            topCommId, alipayAppId, alipayOpenid, vin, phone, carNo, unbind);
        Assert.notNull(topCommId, "参数错误, topCommId 不能为空");
        Assert.notNull(alipayOpenid, "参数错误, alipayOpenid 不能为空");
        Assert.notNull(vin, "参数错误, vin 不能为空");

        // 判断手机号对应的账号是否存在，如果不存在则创建新t_user用户， 判断openid是否有绑定支付宝应用，如果没有就将openid与phone对应的用户绑定。
        if (StringUtils.isNotBlank(phone)) {
            UserPropVO user = userService.findByPhone(phone, topCommId);
            if (Objects.isNull(user)) {
                AddUserParam param = new AddUserParam(topCommId, topCommId, "86", phone,
                    phone, phone, null, "");
                manageUserService.addUser(param);
            }
        }

        //直接去除appId降级查，对于支付宝来讲，appid字段不在使用
        UserOpenidPo alipayOpenidPo = userOpenidRoDs.getUserOpenid(topCommId,
                UserOpenidType.ALIPAY_OPENID,
                alipayOpenid,
                null);

//        UserOpenidPo alipayOpenidPo = userOpenidRoDs.getUserOpenid(topCommId,
//            UserOpenidType.ALIPAY_OPENID,
//            alipayOpenid,
//            alipayAppId);
//
//        if (alipayOpenidPo == null) {    // 去掉appId降级查
//            alipayOpenidPo = userOpenidRoDs.getUserOpenid(topCommId,
//                    UserOpenidType.ALIPAY_OPENID,
//                    alipayOpenid,
//                    null);
//        }

        if (alipayOpenidPo == null && StringUtils.isNotBlank(phone)) {
            UserPropVO user = userService.findByPhone(phone, topCommId);
            if (Objects.nonNull(user)) {
                UserOpenidPo open = new UserOpenidPo();
                open.setTopCommId(topCommId)
                        .setUid(user.getUserId())
                        .setOpenid(alipayOpenid)
                        .setType(UserOpenidType.ALIPAY_OPENID)
                        .setAppId("");
                userOpenidRwDs.addUserOpenid(open);
                alipayOpenidPo = open;
            }
        }

        if (alipayOpenidPo == null) {
            // 支付宝 openid 不存在
            log.warn(
                "客户的支付宝openid信息不存在. topCommId = {}, alipayOpenid = {}, vin = {}",
                topCommId, alipayOpenid, vin);
            throw new DcServiceException("客户的支付宝openid信息不存在,客户需要先做支付宝帐号绑定");
        }

        if (Boolean.TRUE.equals(unbind)) {
            // VIN 停用
            VinDto vinDto = vinService.getVinByUid(alipayOpenidPo.getTopCommId(),
                alipayOpenidPo.getUid(), vin);
            if (vinDto == null) {
                // vin 不存在， 忽略
            } else if (vinDto.getCorpId() == null || vinDto.getCorpId() < 1L) {
                // vin 不属于企业授信，可停用
                vinService.inactiveVin(vinDto.getId());
                log.info("已将VIN停用. vinDto= {}", vinDto);
            } else {
                log.info("vin 有绑定企业授信账户，不做停用操作. vinDto= {}", vinDto);
            }
        } else {
            VinDto vinWithSameCarNo = null; // 通过车牌号查找车辆信息
            if (StringUtils.isNotBlank(carNo)) {
                vinWithSameCarNo = vinService.selectByCarNo(carNo, alipayOpenidPo.getUid())
                    .getData();
            }
            if (vinWithSameCarNo == null) {
                log.info("创建新的 t_vin 记录. uid= {}, vin= {}, carNo= {}",
                    alipayOpenidPo.getUid(), vin, carNo);
                VinParam addVinParam = new VinParam();
                addVinParam.setVin(vin)
                    .setCommId(topCommId)
                    .setSubCommId(topCommId)
                    .setUserId(alipayOpenidPo.getUid())
                    .setCarNo(carNo);
                vinService.create(addVinParam); // 创建或更新 t_vin 表记录
            } else if (!StringUtils.equalsIgnoreCase(vinWithSameCarNo.getVin(), vin)) {
                log.info(
                    "客户名下有相同车牌号的 t_vin 记录, 仅修改vin信息. t_vin.id= {}, uid= {}, 原vin= {}, 新vin= {}, carNo= {}",
                    vinWithSameCarNo.getId(), alipayOpenidPo.getUid(),
                    vinWithSameCarNo.getVin(),
                    vin, carNo);
                vinService.updateVin(vinWithSameCarNo.getId(), vin);
            } else if (!NumberUtils.equals(1, vinWithSameCarNo.getEnable())
                || !NumberUtils.equals(1, vinWithSameCarNo.getStatus())) {
//                log.info(
//                    "重新启用客户车辆 vin 记录, 仅修改vin信息. t_vin.id= {}, uid= {}, vin= {}, carNo= {}, 原status= {}, 原enable= {}",
//                    vinWithSameCarNo.getId(), alipayOpenidPo.getUid(), vin, carNo,
//                    vinWithSameCarNo.getStatus(), vinWithSameCarNo.getEnable());
                VinParam enableVinParam = new VinParam();
                enableVinParam.setId(vinWithSameCarNo.getId())
                    .setUserId(vinWithSameCarNo.getUserId())
                    .setVin(vin)
                    .setCommId(vinWithSameCarNo.getCommId())
                    .setIsPrimary(vinWithSameCarNo.getIsPrimary());
                log.info(
                    "重新启用客户车辆 vin 记录, 仅修改vin信息. t_vin.id= {}, uid= {}, vin= {}, carNo= {}, 原status= {}, 原enable= {}, enableVinParam= {}",
                    vinWithSameCarNo.getId(), alipayOpenidPo.getUid(), vin, carNo,
                    vinWithSameCarNo.getStatus(), vinWithSameCarNo.getEnable(),
                    JsonUtils.toJsonString(enableVinParam));
                vinService.activeVin(enableVinParam);
            }
        }

        this.userOpenidRwDs.updateAlipayVin(topCommId, alipayAppId, alipayOpenidPo.getUid(), vin,
            carNo, unbind);

    }

    @Transactional
    public void syncAlipayVin(Long topCommId, Long uid, String appId, String openid) {
        UserOpenidPo alipayOpenid = userOpenidRoDs.getByUid(topCommId, UserOpenidType.ALIPAY_OPENID,
            uid, null);
        if (alipayOpenid == null) {
            log.info("查询用户openid失败");
            return;
        } else if (!StringUtils.equalsIgnoreCase(openid, alipayOpenid.getOpenid())) {
            log.info("查询到的openid不匹配. 传入openid= {}, alipayOpenidPo= {}",
                openid, JsonUtils.toJsonString(alipayOpenid));
            return;
        }
        PcpQueryVinStatusParam queryVinStatusParam = new PcpQueryVinStatusParam();
        queryVinStatusParam.setAppId(appId)
            .setTopCommId(topCommId)
            .setUid(uid)
            .setOpenid(alipayOpenid.getOpenid());
        log.info("调用pcp接口查询即插即充服务开通状态. param= {}", queryVinStatusParam);
        ListResponse<UserCarTinyDto> res = pcpAsyncFeignClient.queryUserVinStatus(
                queryVinStatusParam)
            .block(Duration.ofSeconds(50L));
        if (res.getData() == null) {
            return;
        }
        for (UserCarTinyDto car : res.getData()) {
            this.addOrUpdateAlipayVin(topCommId, appId, alipayOpenid.getOpenid(),
                car.getVin(), "", car.getCarNo(), null);
        }
    }

    /**
     * 用户手机号绑定 微信小程序openid（wechatOpenid）
     *
     * @param user
     * @param wxOpenid 微信小程序openid
     */

    public void updateUserWechatOpenidForLoginAfter(UserPo user, String wxOpenid,
        String wxUnionId, String appId) {
        if (user == null || (wxOpenid == null && wxUnionId == null)) {

            return;
        }
//        User user = new User();
//
//        user.setId(userId);

        if (StringUtils.isNotBlank(wxOpenid)) {
//            user.setWechatOpenid(wxOpenid);
            userOpenidRwDs.updateWxOpenid(user, wxOpenid, appId);
        }
        if (StringUtils.isNotBlank(wxUnionId)) {
//            user.setWxUnionId(wxUnionId);
            userOpenidRwDs.updateWxUnionid(user, wxUnionId);
        }

//        return userMapper.updateByUidSelective(user);
    }

    /**
     * 用户手机号绑定 高德小程序openid（gaodeOpenid）
     *
     * @param userId
     * @param gaodeOpenid 高德小程序openid
     * @return
     */

    public Integer updateUserGaodeOpenidForLoginAfter(Long userId, String gaodeOpenid) {
        if (userId == null || gaodeOpenid == null) {
            return null;
        }
        User user = new User();

        user.setId(userId);

        user.setGaodeOpenid(gaodeOpenid);

        return userMapper.updateByUidSelective(user);
    }

    @Transactional
    public Integer updateUsername(Long userId, String username, String image) {
        if (userId == null || username == null) {
            return null;
        }
        User user = new User();

        user.setId(userId);

        user.setUsername(username);

        user.setImage(image);

        int ret = userMapper.updateByUidSelective(user);
        if (ret > 0) {
            this.publishCusInfoService.publishCusInfo(userId);
        }
        return ret;
    }

    /**
     * 根据客户ID 与商户id 查询客户信息
     *
     * @param uid    用户id
     * @param commId 商户id
     * @return
     */

    public UserVo queryByUidAndCommId(Long uid, Long commId) {
        UserVo userVo = userMapper.queryByUidAndCommId(uid, commId);

        if (ObjectUtils.isEmpty(userVo)) {
            return null;
        }

        List<UserOpenidPo> openids = userOpenidRoDs.getUserOpenidList(userVo.getCommId(), uid,
            null);
        for (UserOpenidPo open : openids) {
            if (UserOpenidType.WX_UNIONID.equalsIgnoreCase(open.getType())) {
                userVo.setWxUnionId(open.getOpenid());
            } else if (UserOpenidType.WX_OPENID.equalsIgnoreCase(open.getType())) {
                userVo.setWxOpenId(open.getOpenid());
            } else if (UserOpenidType.ALIPAY_OPENID.equalsIgnoreCase(open.getType())) {
                userVo.setAliUserId(open.getOpenid());
            }
        }
        return userVo;
    }


    /**
     * 通过手机号和手机验证码找回密码
     *
     * @param commercialId 商户id
     * @param mobile       用户手机号
     * @param nationalCode 手机区域编码
     * @param verifyCode   手机验证码
     * @param password     密码
     * @return
     */

    public ObjectResponse retrievePasswordByPhone(Long commercialId, String mobile,
        String nationalCode, String verifyCode, String password) {

        log.info(
            "找回密码接口:retrievePassword接口开始。commercialId: {}, mobile: {}, nationalCode: {}, verifyCode: {}, password: {}",
            commercialId, mobile, nationalCode, verifyCode, password);

        if (commercialId == null || commercialId <= 0 || StringUtils.isEmpty(mobile)
            || StringUtils.isEmpty(verifyCode) || StringUtils.isEmpty(nationalCode)
            || StringUtils.isEmpty(password)) {
            throw new DcArgumentException("参数错误");
        }

        // 判断验证码是否正确
        String key = mobile.trim() + CardConstants.FIND_PASSWORD;
        String code = redisDao.get(key);
        User user = userMapper.findByCommIdAndPhone(commercialId, mobile, nationalCode);

        if (user == null || user.getId() == null) {
            throw new DcServiceException("设置新密码失败");
        }

        if (!verifyCode.equals(code)) {
            throw new DcServiceException("验证码错误");
        }

        Integer updatePassordStatus = this.retrievePasswordCommon(user.getId(), password);

        if (updatePassordStatus == null || updatePassordStatus <= 0) {
            throw new DcServiceException("设置新密码失败");
        }

        return new ObjectResponse<>("设置新密码成功");
    }


    /**
     * 修改密码
     *
     * @param uid      用户id
     * @param password 密码
     * @return
     */
    public Integer retrievePasswordCommon(Long uid, String password) {
        if (uid == null) {
            return null;
        }
        User updateUser = new User();
        updateUser.setId(uid);
        updateUser.setPwd(BCryptHasher.hashPassword(password));
        int updateStatus = userMapper.updateByUidSelective(updateUser);

        if (updateStatus <= 0) {
            return null;
        }
        return updateStatus;
    }

    /**
     * 根据客户ID查询客户信息
     *
     * @param userId 用户id
     * @return
     */

    public UserVo findInfoByUid(Long userId, @Nullable Long topCommId) {
        UserVo user = userMapper.findUserInfoByUid(userId, topCommId);
        if (user == null) {
            return null;
        }
        user.setWxOpenId("");
        user.setWxUnionId("");
        user.setAliUserId(user.getAliUserId() == null ? ""
            : user.getAliUserId()); //有些t_user_openid没有支付宝userid 首次注册登录
        List<UserOpenidPo> openids = userOpenidRoDs.getUserOpenidList(user.getCommId(), userId,
            null);
        for (UserOpenidPo open : openids) {
            if (UserOpenidType.WX_UNIONID.equalsIgnoreCase(open.getType())) {
                user.setWxUnionId(open.getOpenid());
            } else if (UserOpenidType.WX_OPENID.equalsIgnoreCase(open.getType())) {
                user.setWxOpenId(open.getOpenid());
            } else if (UserOpenidType.ALIPAY_OPENID.equalsIgnoreCase(open.getType())) {
                user.setAliUserId(open.getOpenid());
            }
        }
        return user;
    }

    /**
     * 根据客户手机号，commId获取商户会员信息
     *
     * @param phone 手机号
     * @return
     */

    public UserVo findUserByPhone(List<Long> commIdList, String phone) {
        return userMapper.findUserByPhone(commIdList, phone);
    }

    public UserPo getByOpenid(Long topCommId, String openid, String openidType) {
        return userRoDs.getByOpenid(topCommId, openid, openidType);
    }

//    public UserPo getByAlipayUserId(Long topCommId, String alipayUserId) {
//        return userRoDs.getByAlipayUserId(topCommId, alipayUserId);
//    }


    /**
     * 根据客户ID修改客户名称
     *
     * @param userId   客户ID
     * @param userName 用户名称
     * @param commId   商户ID
     * @return
     */
    public int updateUserNameById(Long userId, String userName, Long commId) {
        return userMapper.updateUserNameById(userId, userName, commId);
    }

    /**
     * 修改客户信息
     *
     * @param param
     * @return
     */
    @Transactional
    public ObjectResponse<String> updateCusInfo(ModifyCusInfoParam param) {

        log.info("修改客户信息接口: param: {}", param);
        if (param == null || param.getCusId() == null) {
            return null;
        }
        UserPo dbUser = userRoDs.getCusById(param.getCusId());  // 当前数据库中存储的用户信息
        User updateUser = new User();
        updateUser.setId(param.getCusId());
        boolean isChanged = false;
        if (StringUtils.isNotBlank(param.getNickname())) {
            updateUser.setUsername(param.getNickname());
            isChanged = true;
        }
        if (param.getSex() != null) {
            updateUser.setSex(param.getSex().getCode());
            isChanged = true;
        }
        if (StringUtils.isNotBlank(param.getCityCode())) {
            updateUser.setCityCode(param.getCityCode());
            isChanged = true;
        }
        if (param.getBirthday() != null) {
            updateUser.setBrithday(DateUtil.date2Str(param.getBirthday(), "yyyy-MM-dd"));
            isChanged = true;
        }
        if (param.getDegree() != null) {
            updateUser.setDegree(param.getDegree());
            isChanged = true;
        }
        if (param.getSalaryLevel() != null) {
            updateUser.setSalaryLevel(param.getSalaryLevel());
            isChanged = true;
        }
        if (StringUtils.isNotBlank(param.getHeadIconUrl())) {
            updateUser.setImage(param.getHeadIconUrl());
            isChanged = true;
        }
        if (StringUtils.isNotBlank(param.getPhone())) {
            updateUser.setPhone(param.getPhone());
            isChanged = true;
        }
        userOpenidRwDs.updateWxOpenid(dbUser, param.getWxOpenId(), param.getAppId());
        userOpenidRwDs.updateWxUnionid(dbUser, param.getWxUnionId());
        userOpenidRwDs.updateAlipayOpenid(dbUser, param.getAlipayUserId(), param.getAppId());

        if (isChanged) {
            log.info("updateUser = {}", JsonUtils.toJsonString(updateUser));
            Integer updateStatus = userMapper.updateByUidSelective(updateUser);

            if (updateStatus == null || updateStatus <= 0) {
                log.info("修改客户信息接口失败，updateStatus：{}", updateStatus);
                throw new DcServiceException("修改客户信息失败");
            }
            this.publishCusInfoService.publishCusInfo(param.getCusId());
        }
        log.info("修改客户信息成功");
        return new ObjectResponse<>("修改客户信息成功");
    }

    /**
     * 根据客户ID查询客户信息
     *
     * @param userIds 用户id
     * @return
     */

    public Map<Long, UserVo> findInfoByUids(List<Long> userIds) {
        List<UserVo> list = userMapper.queryUsersByIds(userIds);
        list.stream().forEach(o -> {
            CommercialSimpleVo rtCommercial = commercialRoDs.getCommerial(o.getCommId());
            o.setCommIdName(rtCommercial.getCommName());
        });
        Map<Long, UserVo> map = list.stream().collect(Collectors.toMap(UserVo::getId, o -> o));
        return map;
    }


    /**
     * 获取能分组的客户信息
     *
     * @param userlist   用户列表
     * @param commIdList 商户及子商户列表
     * @return
     */

    public ListResponse<UserSampleVo> queryNegationUsers(List<Long> userlist,
        List<Long> commIdList) {
        if (CollectionUtils.isEmpty(commIdList)) {
            throw new DcArgumentException("commIdList不能为空");
        }
        List<UserSampleVo> userSampleVos = userMapper.queryNegationUsers(userlist, commIdList);
        return new ListResponse<>(userSampleVos);
    }

    /**
     * 根据手机号和商户id查询客户信息
     *
     * @param phone
     * @param commId
     * @return
     */

    public UserPropVO findByPhone(String phone, Long commId) {
        return userMapper.findByPhone(phone, commId);
    }

    /**
     * 根据邮箱和商户id查询客户信息
     *
     * @param email
     * @param commId
     * @return
     */

    public UserPropVO findByEmail(String email, Long commId) {
        return userMapper.findByEmail(email, commId);
    }

    /**
     * 根据商户、邮箱获取客户信息
     * @param email
     * @param commId
     * @return
     */
    public UserPo getByEmail(String email, Long commId) {
        return userMapper.getByEmail(email, commId);
    }


    /**
     * 支付保证金后，修改该用户的保证金状态为已缴纳。
     *
     * @param uid 用户id
     * @return
     */

    public ObjectResponse updateUserForAfterPayBond(Long uid) {
        User user = new User();
        user.setIsBond(CardConstants.YES);
        user.setId(uid);
        Integer updateStatus = userMapper.updateByUidSelective(user);
        if (updateStatus == null || updateStatus <= 0) {
            throw new DcServiceException("缴纳保证金失败");
        }
        return new ObjectResponse<>("缴纳保证金成功");
    }

    /**
     * 取消保证金后，修改保证金状态为未缴纳
     *
     * @param uid
     * @return
     */

    public ObjectResponse updateUserForAfterCancelBond(Long uid) {
        User user = new User();
        user.setIsBond(CardConstants.NO);
        user.setId(uid);
        Integer updateStatus = userMapper.updateByUidSelective(user);
        if (updateStatus == null || updateStatus <= 0) {
            throw new DcServiceException("取消保证金失败");
        }
        return new ObjectResponse<>("取消保证金成功");
    }

    /**
     * 根据用户id更新退保证金的失败原因
     *
     * @param msg 失败原因
     * @param uid 用户id
     */

    public int updateRefundsFailReasonstMsg(Long uid, String msg) {
        //根据用户id更新退保证金的失败原因，并标记为未读
        return userMapper.updateRefundsFailReasonstMsg(uid, msg);
    }


    /**
     * 根据客户ID查询客户基本信息(commId弃废)
     */

    public UserBalanceVo findByUserIdAndCommercialId(Long userId, Long commId) {
        UserBalanceVo vo = userMapper.queryUserById(userId, commId);
        if (vo != null) {
            List<CardVo> list = cardMapper.findByUserId(userId);
            vo.setCards(list);
        }
        return vo;
    }

    /**
     * 设置用户资料信息
     *
     * @param user 用户信息
     * @return
     */
    @Transactional
    public ObjectResponse<User> setBasicInfo(User user) {
        if (ObjectUtils.isEmpty(user)) {
            throw new DcArgumentException("参数错误");
        }
        if (user.getId() == null || user.getId() <= 0) {
            throw new DcArgumentException("userId不可为空");
        }

        User newUser = new User();
        newUser.setId(user.getId());
        //newUser.setPhone(user.getPhone());
        if (user.getCommId() != null) {
            newUser.setCommId(user.getCommId());
        }
        if (StringUtils.isNotBlank(user.getPartnerCode())) {
            newUser.setPartnerCode(user.getPartnerCode());
        }
        if (user.getUsername() != null && StringUtils.isNotBlank(user.getUsername())) {
            newUser.setUsername(user.getUsername());
        }
        if (user.getName() != null && StringUtils.isNotBlank(user.getName())) {
            newUser.setName(user.getName());
        }

        if (user.getSex() != null) {
            newUser.setSex(user.getSex());
        }
        if (user.getImage() != null && StringUtils.isNotBlank(user.getImage())) {
            newUser.setImage(user.getImage());
        }
        if (user.getMicroblog() != null && StringUtils.isNotBlank(user.getMicroblog())) {
            newUser.setMicroblog(user.getMicroblog());
        }
        //newUser.setWechant(user.getWechant());
        if (user.getQq() != null && StringUtils.isNotBlank(user.getQq())) {
            newUser.setQq(user.getQq());
        }
        if (user.getAddress() != null && StringUtils.isNotBlank(user.getAddress())) {
            newUser.setAddress(user.getAddress());
        }
        if (user.getPostCode() != null) {
            newUser.setPostCode(user.getPostCode());
        }
        if (user.getVin() != null && StringUtils.isNotBlank(user.getVin())) {
            newUser.setVin(user.getVin());
        }
        if (user.getPhone() != null && StringUtils.isNotBlank(user.getPhone())) {
            newUser.setPhone(user.getPhone());
        }
        if (user.getMovePhone() != null && StringUtils.isNotBlank(user.getMovePhone())) {
            newUser.setMovePhone(user.getMovePhone());
        }
        if (user.getAllowOperator() != null) {
            newUser.setAllowOperator(user.getAllowOperator());
        }
        if (user.getCity() != null) {
            newUser.setCity(user.getCity());
        }
        if (user.getDefaultPayType() != null) {
            newUser.setDefaultPayType(user.getDefaultPayType());
        }
        if (user.getBalanceId() != null && user.getBalanceId() > 0) {
            newUser.setBalanceId(user.getBalanceId());
        }

        if (StringUtils.isNotEmpty(user.getPwd())) {
            newUser.setPwd(user.getPwd());
        }
        int updateStatus = userMapper.updateByUidSelective(newUser);

        if (updateStatus <= 0) {
            throw new DcServiceException("更新用户资料失败");
        }
        this.publishCusInfoService.publishCusInfo(user.getId());
        return new ObjectResponse<>(newUser);
    }

    /**
     * 更新 绑定的手机号
     *
     * @param appCommId    平台商户id
     * @param nationalCode 国码
     * @param phoneNum     手机号
     * @param password     密码
     * @return
     */
    @Transactional
    public ObjectResponse updateBindMobile(Long appCommId, Long userId, String nationalCode,
        String phoneNum, String password) {

        if (userId == null || userId <= 0) {
            throw new DcArgumentException("userId不可为空");
        }
        if (StringUtils.isBlank(nationalCode) || StringUtils.isBlank(phoneNum)) {
            throw new DcArgumentException("参数错误");
        }

        User userInfo = userMapper.findByCommIdAndPhone(appCommId, phoneNum, nationalCode);

        if (!ObjectUtils.isEmpty(userInfo)) {
            throw new DcServiceException("手机号已被使用");
        }

        User newUser = new User();
        newUser.setId(userId);
        newUser.setNationalCode(nationalCode);
        newUser.setPhone(phoneNum);
        if (StringUtils.isNotBlank(password)) {
            newUser.setPwd(BCryptHasher.hashPassword(password));
        }

        int updateStatus = userMapper.updateByUidSelective(newUser);

        if (updateStatus <= 0) {
            throw new DcServiceException("更新用户资料失败");
        }
        this.publishCusInfoService.publishCusInfo(userId);
        return new ObjectResponse<>(updateStatus);
    }


    public UserBalanceVo findByUserId(Long userId) {
        UserBalanceVo vo = userMapper.queryUserByUid(userId);

        if (vo == null || vo.getCommId() == null) {
            throw new DcServiceException("查询用户信息失败");
        }

        List<Long> uidList = new ArrayList<>();
        uidList.add(userId);

        ListResponse<PointPo> res = dcCusBalanceService.queryPointPo(PayAccountType.PERSONAL,
            uidList, vo.getCommId(), vo.getCommId(), true, null);

//        if (res == null || res.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
//            throw new DcServiceException("查询个人现金账户表出错");
//        }
        if (res == null || res.getStatus() != ResultConstant.RES_SUCCESS_CODE
            || res.getData() == null || res.getData().size() == 0) {
            vo.setBalance(DecimalUtils.ZERO);
        } else {
            PointPo pointPo = res.getData().get(0);
            vo.setBalance(pointPo.getPoint());
        }

        return vo;
    }

    /**
     * 查询商户用户关联表
     *
     * @param page
     * @param userPhone  用户手机号
     * @param userId     用户id
     * @param commId     商户id
     * @param enable     状态（1启用，0禁用）
     * @param commIdList 当前商户及子商户id列表
     * @return
     */

    public ListResponse<CommCusRef> queryCommCusRefs(OldPageParam page, String userPhone,
        Long userId, String cusName, Long commId, Boolean enable, String commIdChain,
        List<Long> commIdList) {
        log.info(
            "查询商户用户关联表参数 page: {}, userPhone: {}，commId: {}, enable: {}, commIdList: {}",
            page,
            userPhone, commId, enable, commIdList);

        Page<CommCusRef> pageInfo = PageHelper.startPage(page.getPageNum(), page.getPageSize(),
            true, false, null);

        Page<CommCusRef> commCusRefs = userMapper.queryCommCusRefs(userPhone, userId, cusName,
            commId, enable, commIdChain, commIdList);
        log.info("查询商户用户关联表结果 commCusRefs.size(): {}", commCusRefs.size());
        List<Long> commIds = commCusRefs.stream().map(CommCusRef::getCommId)
            .collect(Collectors.toList());

        //通过调用auth获取商户的直付通信息
        if (commIds.size() > 0) {
            List<CommScoreVo> commScoreVoList = commScoreLevelRoDs.findVoByCommCusIdList(
                commCusRefs.stream().map(CommCusRef::getId).collect(Collectors.toList()));
            Map<Long, CommScoreVo> commScoreVoMap = commScoreVoList.stream()
                .collect(Collectors.toMap(CommScoreVo::getCommCusId, e -> e));

            ListCommercialParam param = new ListCommercialParam();
            param.setCommIdList(commIds);
            ListResponse<CommercialDto> commercialInfoList = authCenterFeignClient.getCommList(
                param);
            List<CommercialDto> commercialList = commercialInfoList.getData();
            commCusRefs.forEach(e -> {

                CommScoreVo temp = commScoreVoMap.get(e.getId());
                if (temp != null) {
                    e.setLevel(temp.getLevel().longValue());
                }

                List<CommercialDto> res = commercialList.stream()
                    .filter(filter -> (filter.getId().equals(e.getCommId())))
                    .collect(Collectors.toList());

                if (res.size() > 0) {
                    e.setEnableUseZft(res.get(0).getEnableUseZft());
                    e.setZftId(res.get(0).getZftId() == null ? 0L : res.get(0).getZftId());
                    e.setEnableCommRefund(res.get(0).getEnableCommRefund() == null ? false
                        : res.get(0).getEnableCommRefund());
                    e.setEnableZft(
                        res.get(0).getEnableZft() == null ? false : res.get(0).getEnableZft());
                    e.setWxMchId(
                        res.get(0).getWxMchId() == null ? "" : res.get(0).getWxMchId());
                    e.setWxSubMchId(
                        res.get(0).getWxSubMchId() == null ? "" : res.get(0).getWxSubMchId());
                    e.setAlipayMchId(res.get(0).getAlipayMchId() == null ? ""
                        : res.get(0).getAlipayMchId());
                    e.setAlipaySubMchId(res.get(0).getAlipaySubMchId() == null ? ""
                        : res.get(0).getAlipaySubMchId());
                    e.setEnableOnlinePay(res.get(0).getEnableOnlinePay() == null ? false
                        : res.get(0).getEnableOnlinePay());
                } else {
                    e.setEnableUseZft(false);
                    e.setZftId(0L);
                    e.setEnableCommRefund(false);
                    e.setEnableZft(false);
                    e.setEnableOnlinePay(false);
                    e.setWxMchId("");
                    e.setWxSubMchId("");
                    e.setAlipayMchId("");
                    e.setAlipaySubMchId("");
                }

            });

        }
        return new ListResponse<>(commCusRefs, pageInfo.getTotal());
    }

    /**
     * 查询商户用户关联表（不分页）
     *
     * @param userPhone   用户手机号
     * @param userId      用户id
     * @param commId      商户id
     * @param enable      状态（1启用，0禁用）
     * @param commIdChain 当前商户及子商户id列表
     * @return
     */

    public ListResponse<CommCusRef> queryCommCusRefsNoPage(String userPhone, Long userId,
        String cusName, Long commId, Boolean enable, String commIdChain) {
        log.info("查询商户用户关联表参数 userPhone: {}，commId: {}, enable: {}, commIdChain: {}",
            userPhone,
            commId, enable, commIdChain);

        Page<CommCusRef> commCusRefs = userMapper.queryCommCusRefs(userPhone, userId, cusName,
            commId, enable, commIdChain, null);
        log.info("查询商户用户关联表结果 commCusRefs.size(): {}", commCusRefs.size());

        if (CollectionUtils.isNotEmpty(commCusRefs)) {
            List<CommScoreVo> commScoreVoList = commScoreLevelRoDs.findVoByCommCusIdList(
                commCusRefs.stream().map(CommCusRef::getId).collect(Collectors.toList()));
            Map<Long, CommScoreVo> commScoreVoMap = commScoreVoList.stream()
                .collect(Collectors.toMap(CommScoreVo::getCommCusId, e -> e));

            commCusRefs.forEach(e -> {

                CommScoreVo temp = commScoreVoMap.get(e.getId());
                if (temp != null) {
                    e.setLevel(temp.getLevel().longValue());
                }
            });
        }

        return new ListResponse<>(commCusRefs);
    }

    /**
     * 查询商户用户关联表
     *
     * @param userId 用户id
     * @param enable 状态（1启用，0禁用）
     * @return
     */

    public long queryCommCusRefsCount(Long userId, Boolean enable) {
        return userMapper.queryCommCusRefsCount(userId, enable);
    }


    public ObjectResponse<CheckPhoneBindVo> checkPhoneWxBindAndUserStatus(Long commId, String phone,
        String nationalCode, String openId) {
        CheckPhoneBindVo checkPhoneBindVo = new CheckPhoneBindVo();
        User userInfo = userService.findByCommIdAndPhone(commId, phone, nationalCode);

        if (null != userInfo && (userInfo.getStats() == null || userInfo.getStats() == 0
            || userInfo.getStatus() == null
            || userInfo.getStatus() != BizUserConstants.USER_STATUS_OK)) {
            checkPhoneBindVo.setSend(false);
            if (NumberUtils.equals(userInfo.getStatus(), BizUserConstants.USER_STATUS_DEREGISTER)) {
                checkPhoneBindVo.setMsg("您已申请账号注销,账号已冻结,如有疑问请联系客服。");
            } else {
                checkPhoneBindVo.setMsg("您已被禁止使用，如有疑问请联系客服或站点工作人员。");
            }
        } else {
            checkPhoneBindVo.setSend(true);
        }
        return new ObjectResponse<>(checkPhoneBindVo);
    }

    public ObjectResponse<CusSampleDto> getCusSampleDtoById(Long userId) {
        IotAssert.isNotNull(userId, "入参缺失");
        return RestUtils.buildObjectResponse(userRoDs.getCusSampleDtoById(userId));
    }

    public ListResponse<CusSampleDto> getCusSampleList(ListCustomerParam param) {
        ListResponse<CusSampleDto> cusRes = this.userRoDs.getCusSampleList(param);

        if (CollectionUtils.isNotEmpty(cusRes.getData())) {
            List<VinDto> carNoListByUserIds = vinService.getCarNoListByUserIds(
                cusRes.getData().stream().map(CusSampleDto::getId).collect(Collectors.toList()),
                param.getTopCommId());

            if (CollectionUtils.isNotEmpty(carNoListByUserIds)) {
                Map<Long, List<VinDto>> userCarNoMap = carNoListByUserIds.stream()
                    .collect(Collectors.groupingBy(VinDto::getUserId));

                cusRes.getData().forEach(e -> {
                    if (CollectionUtils.isNotEmpty(userCarNoMap.get(e.getId()))) {
                        e.setCarNoList(userCarNoMap.get(e.getId()).stream().map(VinDto::getCarNo)
                            .filter(StringUtils::isNotBlank).collect(Collectors.toList()));
                    }
                });
            }

        }

        if (Boolean.TRUE.equals(param.getBalance()) && CollectionUtils.isNotEmpty(
            cusRes.getData())) {
            List<Long> uidList = cusRes.getData().stream().map(CusSampleDto::getId)
                .collect(Collectors.toList());
            ListResponse<PointPo> cusBalanceListRes = this.dcCusBalanceService.queryPointPo(
                PayAccountType.PERSONAL, uidList, param.getTopCommId(), param.getTopCommId(), true,
                null);
            FeignResponseValidate.check(cusBalanceListRes);
            Map<Long, PointPo> cusBalanceMap = cusBalanceListRes.getData().stream().collect(
                Collectors.toMap(o1 -> Long.parseLong(o1.getUid()), o -> o, (v1, v2) -> v1));
            cusRes.getData().stream().forEach(u -> {
                PointPo balance = cusBalanceMap.get(u.getId());
                if (balance != null) {
                    u.setBalance(balance.getAvailable());
                }
            });
        }
        return cusRes;
    }

    public BaseResponse setCouponAutoDeduct(Long userId, Boolean autoDeduct) {
        UserPo po = userRoDs.getCusById(userId);
        IotAssert.isNotNull(po, "客户不存在");
        User record = new User();
        record.setId(userId);
        record.setCouponAutoDeduct(autoDeduct);
        int res = userMapper.updateByUidSelective(record);
        IotAssert.isTrue(res == 1, "修改失败");
        return RestUtils.success();
    }

    public Mono<BaseResponse> cusOrderDebt(Long cusId, boolean debt) {
        boolean b = userRwDs.cusOrderDebt(cusId, debt);
        log.info("更新用户欠费状态结果: b = {}", b);
        return Mono.just(RestUtils.success());
    }

    public Mono<ObjectResponse<String>> getNextPartnerUserPhone() {
        return Mono.just(RestUtils.buildObjectResponse(userRoDs.getNextPartnerUserPhone()));
    }

    public Mono<Integer> getUserCount(String idChain, Long topCommId) {

        List<Long> commIdList =new ArrayList<>();
        List<Long> disabledUserList= new ArrayList<>();
        disabledUserList.add(0L); // t_user_comm_ref存在uid=0的数据
        if (StringUtils.isNotEmpty(idChain)) {
            commIdList = trCommercialRoDs.getCommercialByChain(
                idChain);
        }

        if (topCommId != null) {
             List<Long> disableUser = userRoDs.getDisableUser(topCommId);
             if (CollectionUtils.isNotEmpty(disableUser)) {
                 disabledUserList.addAll(disableUser);
             }
        }
//        return Mono.just(userRoDs.getUserCount(idChain, topCommId));

        return Mono.just(userRoDs.getUserAmount(disabledUserList,commIdList));
    }

    public Mono<Boolean> setLatestOrder(Long userId, String orderNo) {
        return Mono.just(userRwDs.setLatestOrder(userId, orderNo));
    }

    public ListResponse<CusSampleDto> getCusBySiteId(ListCustomerSiteParam param) {
        ListResponse<CusSampleDto> cusRes = this.userRoDs.getCusBySiteId(param);
        return cusRes;
    }

    public ListResponse<BlocUserDto> getUpperCorpBySiteId(ListCustomerSiteParam param) {
        IotAssert.isNotNull(param, "请传入参数");
        IotAssert.isNotBlank(param.getSiteId(), "请传入场站id");

        SitePo siteBySiteId = siteRoDs.getSiteBySiteId(param.getSiteId());
        IotAssert.isNotNull(siteBySiteId, "找不到场站");

        TRCommercialPo commercialById = trCommercialRoDs.getCommercialById(
            siteBySiteId.getCommId());
        IotAssert.isNotNull(commercialById, "找不到商户");

        return blocUserService.selectUpperBlocUserByCommIdChain(commercialById.getIdChain(), true);
    }

    /**
     * 用户账号注销
     */
    @Transactional
    public void cusDeregister(long uid, Long opUid) {
        log.info("用户账号注销. uid = {}, opUid = {}", uid, opUid);
        UserPo user = this.userRwDs.getCusById(uid, true);
        if (NumberUtils.equals(user.getStatus(), BizUserConstants.USER_STATUS_DEREGISTER)) {
            return;
        }
        user.setStatus(BizUserConstants.USER_STATUS_DEREGISTER);
        UserPo cusUpdate = new UserPo();
        cusUpdate.setId(user.getId()).setStatus(BizUserConstants.USER_STATUS_DEREGISTER);
        boolean ret = this.userRwDs.updateCus(cusUpdate);
        if (ret) {
            this.publishCusInfoService.publishCusInfo(user.getId());
        }
    }

    /**
     * 用户主动退出
     */
    public void cusLogout(long uid, Long opUid) {
        log.info("用户主动退出. uid = {}, opUid = {}", uid, opUid);
        UserPo user = this.userRwDs.getCusById(uid, true);
        IotAssert.isNotNull(user, "用户ID无效");
        try {
            userOpenidRwDs.deleteUserOpenid(
                user.getCommId(), uid, UserOpenidType.WX_OPENID, null, null);
            userOpenidRwDs.deleteUserOpenid(
                user.getCommId(), uid, UserOpenidType.WX_UNIONID, null, null);
        } catch (Exception e) {
            log.error("用户退出收尾工作异常: {} - {}", uid, e.getMessage(), e);
        }
    }

    /**
     * 判断账号状态
     *
     * @param accountType
     * @param commId
     * @param userId
     * @return true：状态正常，false：状态异常
     */
    public boolean checkAccountStatus(PayAccountType accountType, Long commId, Long userId) {

//        return true;

        if (accountType == null) {
            return false;
        }

        switch (accountType) {
            case CORP:
                final BlocUserDto byUid = blocUserService.findByUid(userId);
                IotAssert.isNotNull(byUid, "找不到企业信息");
                return Boolean.TRUE.equals(byUid.getEnable());
            case PERSONAL:
                final UserVo userVo = userService.queryByUidAndCommId(userId, commId);
                IotAssert.isNotNull(userVo, "找不到个人帐户信息");
                // 帐户状态异常
                return Integer.valueOf(10001).equals(userVo.getStatus());
            case COMMERCIAL:
                // 会员帐户当前不可用
                return !commCusRefRoDs.checkCommCusRefDisabled(commId, userId);
            case UNKNOWN:
            case CREDIT:
            case PREPAY:
            case E_CNY:
            case WX_CREDIT:
            case ALIPAY_CREDIT:
            case OTHER:
                // 不支持的充值类型检查
                return false;
        }

        return false;
    }

    public UserOpenidPo getUserOpenid(UserOpenidPo param) {
        return userOpenidRoDs.getUserOpenid(param.getTopCommId(),
            param.getType(),
            param.getOpenid(),
            param.getAppId());
    }

    public UserOpenidPo getUserOpenidByUid(UserOpenidPo param) {
        return userOpenidRoDs.getByUid(param.getTopCommId(),
            param.getType(),
            param.getUid(),
            param.getAppId());
    }
}
