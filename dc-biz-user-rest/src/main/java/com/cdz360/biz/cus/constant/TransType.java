package com.cdz360.biz.cus.constant;

/**
 * 交易类型
 * 初始化、充值、提现、增加、减少、冻结、解冻、支付、退款
 *
 * <AUTHOR>
 */
public enum TransType {

    /**
     * 初始化
     */
    INIT("INIT", "初始化"),
    /**
     * 充值
     */
    DEPOSIT("DEPOSIT", "充值、提现"),
    /**
     * 增加
     */
    REDUCE("REDUCE", "增加、扣减"),
    /**
     * 冻结
     */
    FROZEN("FROZEN", "冻结、解冻"),
    /**
     * 充电退款
     */
    PAY("PAY", "支付、充电退款");

    private final String code;
    private final String name;

    TransType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static TransType valueOfCode(String code) {
        for (TransType type : TransType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

}
