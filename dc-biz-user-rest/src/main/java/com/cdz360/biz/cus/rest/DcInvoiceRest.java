package com.cdz360.biz.cus.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.cus.service.IDcInvoiceService;
import com.cdz360.biz.model.cus.invoice.DcInvoice;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since Created on 18:06 2019/3/6.
 */

@Slf4j
@Tag(name = "鼎充发票功能相关接口", description = "鼎充发票")
@RestController
public class DcInvoiceRest {

    @Autowired
    private IDcInvoiceService interfaceService;

    /**
     * 查询用户列表
     *
     * @param commIdChain
     * @param keyword
     * @param status
     * @param index
     * @param size
     * @return
     */
    @PostMapping("/api/invoiced/user")
    public ListResponse<DcInvoice> queryUserList(
            @RequestParam(value = "topCommId", required = false) Long topCommId,
            @RequestParam(value = "commIdChain", required = false) String commIdChain,
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "index", required = true) Integer index,
            @RequestParam(value = "size", required = true) Integer size) {
        Map<String, Object> map = new HashMap<>();
        //用户名
        map.put("keyword", keyword);
        //状态
        map.put("status", status);
        //当前页
        map.put("start", (index - 1) * size);
        //每页显示条数
        map.put("size", size);
        map.put("topCommId", topCommId);
        map.put("commIdChain", commIdChain);
        return interfaceService.queryUserList(map);
    }

    /**
     * 更新用户发票信息接口
     *
     * @param userId
     * @param invoicedAmount
     * @param monthDay
     * @param auto
     * @return
     */
    @PostMapping("/api/invoiced/saveInvoicedUserAutoAmount")
    public ObjectResponse saveInvoicedUserAutoAmount(@RequestParam("userId") Long userId,
                                                     @RequestParam("invoicedAmount") long invoicedAmount,
                                                     @RequestParam("monthDay") Integer monthDay,
                                                     @RequestParam("auto") Integer auto) {
        Map<String, Object> map = new HashMap<>();
        //用户ID
        map.put("userId", userId);
        //开票起始金额（单位分）
        map.put("invoicedAmount", invoicedAmount);
        //每月几号
        map.put("monthDay", monthDay);
        //是否开票
        map.put("auto", auto);
        ObjectResponse ObjectResponse = interfaceService.saveInvoicedUserAutoAmount(map);
        return ObjectResponse;
    }

    /**
     * 获取企业开票使用的模板
     * @param productId
     * @return
     */
    @GetMapping("/api/invoiced/getCorpInfoByProduct")
    public ObjectResponse getCorpInfoByProductId(@RequestParam("productId") Long productId) {
        int count = interfaceService.getCorpInfoByProductId(productId);
        return new ObjectResponse<>(count);
    }
}
