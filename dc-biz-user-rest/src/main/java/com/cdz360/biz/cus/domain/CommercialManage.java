package com.cdz360.biz.cus.domain;

//
//@Data
//public class CommercialManage implements Serializable {
//    /**
//     * t_commercial_manage
//     */
//    private static final long serialVersionUID = 1L;
//    /**
//     *
//     */
//    private Long id;
//    /**
//     *
//     */
//    private Long comId;
//    /**
//     * 短信验证码模板编号
//     */
//    private String smsModelVerifyCodeNo;
//    /**
//     *
//     */
//    private String smsModelNationNo;
//    /**
//     *
//     */
//    private String smsModelAppNo;
//    /**
//     *短信推送个人账户充值模板
//     */
//    private String smsModelPersonCharge;
//    /**
//     * 短信推送商户会员充值模板
//     */
//    private String smsModelMerchantCharge;
//    /**
//     * 短信推送授信账户充值模板
//     */
//    private String smsModelCreditCharge;
//    /**
//     * 短信apikey(集成sdk为云片apikey或者消息中心为消息中心的apikey)
//     */
//    private String smsApiKey;
//    /**
//     * 微信消息通知apikey
//     */
//    private String wxMsgApiKey;
//    /**
//     *
//     */
//    private String wxMsgTemplateRecharge;
//    /**
//     *
//     */
//    private String wxMsgTemplateRefund;
//    /**
//     *
//     */
//    private String wxMsgTemplateChargingEnd;
//    /**
//     *
//     */
//    private String wxMsgTemplateChargingStartAfter;
//    /**
//     *
//     */
//    private String wxMsgTemplateChargingStartPre;
//    /**
//     * 支付apikey
//     */
//    private String payApiKey;
//    /**
//     * 微信appId
//     */
//    private String wxAppid;
//    /**
//     * 微信appSecret
//     */
//    private String wxAppSecret;
//    /**
//     * 微信wxGrantType
//     */
//    private String wxGrantType;
//    /**
//     * 定时充电 发送短信模板id
//     */
//    private String smsModelTimerChargeNo;
//
//}