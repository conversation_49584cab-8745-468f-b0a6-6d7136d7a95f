package com.cdz360.biz.cus.repository;

import com.chargerlinkcar.framework.common.domain.UserFavoriteSite;
import com.chargerlinkcar.framework.common.domain.request.UserFavorSiteReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * UserFavoriteSite
 *
 * @since 2019/9/18
 * <AUTHOR>
 */
@Mapper
public interface UserFavoriteSiteMapper {
    /**
     * 加入收藏
     *
     * @return
     */
    int addToFavorites(@Param("uid") long uid, @Param("siteId") String siteId);

    /**
     * 移除收藏
     *
     * @param uid
     * @param siteId
     * @return
     */
    int removeTheCollection(@Param("uid") long uid, @Param("siteId") String siteId);

    /**
     * 场站是否被用户收藏
     *
     * @return
     */
    int ifFavorite(@Param("uid") long uid, @Param("siteId") String siteId);

    /**
     * 获取用户收藏的场站列表
     *
     * @param req
     * @return
     */
    List<UserFavoriteSite> getByUid(UserFavorSiteReq req);
}