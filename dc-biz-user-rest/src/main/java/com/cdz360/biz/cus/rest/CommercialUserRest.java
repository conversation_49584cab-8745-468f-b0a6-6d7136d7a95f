package com.cdz360.biz.cus.rest;


import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.cus.service.CommercialUserService;
import com.cdz360.biz.model.commercialUser.param.UserRegisterParam;
import com.cdz360.biz.model.cus.user.dto.UserAndBalanceAndTokenVo;
import com.chargerlinkcar.framework.common.rest.BaseController;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 海外平台用户
 */
@Slf4j
@Tag(name = "海外平台用户相关接口")
@RestController
@RequestMapping("/api/CommercialUser")
public class CommercialUserRest extends BaseController {

    @Autowired
    private CommercialUserService commercialUserService;


    /**
     * 海外用户邮箱注册
     */
    @PostMapping(value = "/registerByEmail")
    public ObjectResponse<UserAndBalanceAndTokenVo> registerByEmail(
        @RequestBody UserRegisterParam param) {
        //获取前端客户端类型,统计注册用户来源
        log.info("海外用户邮箱注册。 param: {}", JsonUtils.toJsonString(param));
        return commercialUserService.registerByEmail(param);
    }


    /**
     * 邮箱登录
     */
    @PostMapping(value = "/loginByEmail")
    public ObjectResponse<UserAndBalanceAndTokenVo> loginByEmail(
        @RequestBody UserRegisterParam param) {
        log.info("海外用户邮箱登录。 param: {}", JsonUtils.toJsonString(param));
        return commercialUserService.loginByEmail(param);
    }

    /**
     * 修改密码
     */
    @PostMapping(value = "/modifyByEmail")
    public ObjectResponse<UserAndBalanceAndTokenVo> modifyByEmail(
        @RequestBody UserRegisterParam param) {
        log.info("海外用户修改密码。 param: {}", JsonUtils.toJsonString(param));
        return commercialUserService.modifyByEmail(param);
    }


}
