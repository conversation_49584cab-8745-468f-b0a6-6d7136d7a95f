package com.cdz360.biz.cus.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.biz.cus.service.IBankCardService;
import com.cdz360.biz.model.cus.wallet.param.AddBankCardParam;
import com.cdz360.biz.model.cus.wallet.vo.BankCardVo;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@Tag(name = "客户银行卡相关接口", description = "客户银行卡")
@RequestMapping("/api/bankCard")
public class CusBankCardRest {

    @Autowired
    private IBankCardService bankCardService;

    @GetMapping("/getBankCardList")
    @Operation( summary = "获取银行卡列表")
    public ListResponse<BankCardVo> getBankCardList(ServerHttpRequest request,
                                                    @Parameter(name = "用户id") @RequestParam(value = "cusId", required = false) Long cusId,
                                                    @Parameter(name = "是否有效") @RequestParam(value = "enable", required = false) Boolean enable,
                                                    @Parameter(name = "卡号") @RequestParam(value = "cardNo", required = false) String cardNo) {
        log.debug(LoggerHelper2.formatEnterLog(request));

        if (cusId == null || cusId < 1L) {
            log.error("获取银行卡列表信息失败;cusId:{}", cusId);
            throw new DcServiceException("获取用户信息失败");
        }
        log.info("【获取银行卡列表】,cusId:{},enable:{},cardNo:{}", cusId, enable, cardNo);

        ListResponse<BankCardVo> list = bankCardService.listBankCard(cusId, cardNo, enable);
        FeignResponseValidate.check(list);

        log.info("获取银行卡列表。request: {}, result: {}", request, list.getData().size());

        log.debug(LoggerHelper2.formatLeaveLog(request));
        return list;
    }

    @PostMapping("/addBankCard")
    @Operation( summary = "添加银行卡")
    public BaseResponse addBankCard(ServerHttpRequest request,
                                    @RequestBody AddBankCardParam param) {
        log.info(LoggerHelper2.formatEnterLog(request) + "param = {}", param);

        if (param == null) {
            throw new DcArgumentException("参数错误");
        }

        if (param.getCardNo() == null || param.getCusId() == null) {
            throw new DcArgumentException("参数错误");
        }

        BankCardVo bankCardVo = new BankCardVo();
        BeanUtils.copyProperties(param, bankCardVo);

        BaseResponse res = bankCardService.addBankCard(bankCardVo);

        log.info(LoggerHelper2.formatLeaveLog(request));
        return res;
    }

    @PostMapping("/updateBankCard")
    @Operation(summary = "修改银行卡")
    public BaseResponse updateBankCard(ServerHttpRequest request,
                                       @RequestBody AddBankCardParam param) {
        log.info(LoggerHelper2.formatEnterLog(request) + "param = {}", param);
        if (param == null) {
            throw new DcArgumentException("参数错误");
        }

        if (param.getId() == null || param.getCardNo() == null || param.getCusId() == null) {
            throw new DcArgumentException("参数错误");
        }
        BankCardVo bankCardVo = new BankCardVo();
        BeanUtils.copyProperties(param, bankCardVo);

        BaseResponse res = bankCardService.updateBankCard(bankCardVo);

        log.info(LoggerHelper2.formatLeaveLog(request));
        return res;

    }

    @PostMapping("/deleteBankCard")
    @Operation(summary = "删除银行卡", description = "逻辑删除")
    public BaseResponse deleteBankCard(ServerHttpRequest request,
                                       @Parameter(name = "卡号", example = "**************") @RequestParam String cardNo,
                                       @Parameter(name = "用户id", example = "111") @RequestParam Long userId) {
        log.info(LoggerHelper2.formatEnterLog(request));

        log.info("【删除银行卡】,userId:{},cardNo:{}", userId, cardNo);

        BaseResponse res = bankCardService.deleteBankCard(userId, cardNo);

        log.info(LoggerHelper2.formatLeaveLog(request));
        return res;
    }
}
