package com.cdz360.biz.cus.service.impl;


import com.cdz360.biz.cus.repository.AnalysisMapper;
import com.cdz360.biz.cus.service.IAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 数据分析
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AnalysisServiceImpl implements IAnalysisService {

    @Autowired
    private AnalysisMapper analysisMapper;


//    /**
//     * 获取运营概况
//     *
//     * @param map
//     * @return
//     */
//    @Override
//    public AnalysisVo getOperationData(Map map) {
//        AnalysisVo data = new AnalysisVo();
//
//        // 客户统计概览
//        List<KVAnalysisVo> khtjgl = analysisMapper.getkhtjgl(map);
//        data.setKhtjgl(khtjgl);
//
//        // 客户增长走势
//        List<KVAnalysisVo> khzzzs = analysisMapper.getkhzzzs(map);
//        dataZsInit(khzzzs);
//        data.setKhzzzs(khzzzs);
//
//
//        return data;
//    }
//
//
//    /**
//     * 运营拆分接口 --  客户统计概览
//     *
//     * @param map
//     * @return
//     */
//    @Override
//    public List<KVAnalysisVo> getkhtjgl(Map map) {
//        // 客户统计概览
//        List<KVAnalysisVo> khtjgl = analysisMapper.getkhtjgl(map);
//        return khtjgl;
//    }
//
//    /**
//     * 运营拆分接口 -- 客户增长走势
//     *
//     * @param map
//     * @return
//     */
//    @Override
//    public List<KVAnalysisVo> getkhzzzs(Map map) {
//        // 客户增长走势
//        List<KVAnalysisVo> khzzzs = analysisMapper.getkhzzzs(map);
//        dataZsInit(khzzzs);
//        return khzzzs;
//    }
//
//    /**
//     * 运营拆分接口 -- 客户统计柱状图,本年度
//     *
//     * @param map
//     * @return
//     */
//    @Override
//    public List<Map> getkhtjzzt(Map map) {
//        //客户统计柱状图,本年度
//        List<Map> khtjzzt = analysisMapper.getkhtjzzt(map);
//        //处理数据，补全查询数据库缺少的月份
//        Map months = MonthOnMapUtil.getDataByMonthMap(khtjzzt, "content");
//        List<Map> data = new ArrayList<>();
//        data.add(months);
//        return data;
//    }


//    /**
//     * 数据处理  填補+排序
//     *
//     * @return
//     */
//    private void dataZsInit(List<KVAnalysisVo> dataList) {
//        //获取当前日期
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//        Date today = new Date();
//        //获取三十天前日期
//        Calendar theCa = Calendar.getInstance();
//        Date start;
//        String startDate;
//        boolean flag;
//        KVAnalysisVo info;
//
//        for (int i = 0; i <= 30; i++) {
//            flag = false;
//            theCa.setTime(today);
//            /**
//             * 最后一个数字30可改，30天的意思
//             */
//            theCa.add(Calendar.DATE, -i);
//            start = theCa.getTime();
//            /**
//             * 三十天之前日期
//             */
//            startDate = sdf.format(start);
//            for (KVAnalysisVo data : dataList) {
//                if (StringUtils.equals(data.getTitle(), startDate)) {
//                    flag = true;
//                }
//            }
//            if (!flag) {
//                info = new KVAnalysisVo();
//                info.setContent("0");
//                info.setTitle(startDate);
//                dataList.add(info);
//            }
//        }
//
//        // 排序  java8 特性
//        dataList.sort((KVAnalysisVo h1, KVAnalysisVo h2) -> h1.getTitle().compareTo(h2.getTitle()));
//    }
}