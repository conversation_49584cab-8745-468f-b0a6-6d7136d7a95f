package com.cdz360.biz.cus.service.impl;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.model.corp.type.CorpType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.cus.client.AuthCenterFeignClient;
import com.cdz360.biz.cus.client.BizBiFeignClient;
import com.cdz360.biz.cus.client.TradingFeignClient;
import com.cdz360.biz.cus.domain.User;
import com.cdz360.biz.cus.repository.CardMapper;
import com.cdz360.biz.cus.repository.CorpOrgMapper;
import com.cdz360.biz.cus.repository.RBlocUserMapper;
import com.cdz360.biz.cus.repository.UserMapper;
import com.cdz360.biz.cus.repository.VinMapper;
import com.cdz360.biz.cus.service.CorpSettlementService;
import com.cdz360.biz.cus.service.IManageUserService;
import com.cdz360.biz.cus.service.IVinService;
import com.cdz360.biz.cus.service.PublishCorpUserInfoService;
import com.cdz360.biz.cus.service.PublishCusInfoService;
import com.cdz360.biz.cus.service.RBlocUserService;
import com.cdz360.biz.cus.service.UserBizService;
import com.cdz360.biz.cus.service.UserCommRefService;
import com.cdz360.biz.ds.cus.ro.mechant.ds.CommercialRoDs;
import com.cdz360.biz.ds.cus.rw.corp.ds.CorpRwDs;
import com.cdz360.biz.model.cus.corp.param.ListCorpParam;
import com.cdz360.biz.model.cus.corp.po.CorpOrgPo;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.corp.type.LimitCycle;
import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import com.cdz360.biz.model.order.type.OrderPayType;
import com.cdz360.biz.model.sys.vo.CorpGroupTinyVo;
import com.cdz360.biz.model.trading.hlht.po.PartnerAccountPo;
import com.cdz360.biz.model.trading.hlht.po.PartnerPo;
import com.cdz360.biz.model.trading.site.vo.CorpOrderCountVo;
import com.chargerlinkcar.framework.common.constant.OrderStatus;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.OrderCountParam;
import com.chargerlinkcar.framework.common.domain.PayAccount;
import com.chargerlinkcar.framework.common.domain.PointPo;
import com.chargerlinkcar.framework.common.domain.UserCommRef;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import com.chargerlinkcar.framework.common.domain.vo.Card;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDataVo;
import com.chargerlinkcar.framework.common.domain.vo.CorpCreditAccountEx;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUserVo;
import com.chargerlinkcar.framework.common.domain.vo.UserPropVO;
import com.chargerlinkcar.framework.common.domain.vo.UserVo;
import com.chargerlinkcar.framework.common.domain.vo.VinDto;
import com.chargerlinkcar.framework.common.service.DcCusBalanceService;
import com.chargerlinkcar.framework.common.utils.DcAssertUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.UUIDUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.Duration;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @since 2018/12/10.
 */
@Slf4j
@Service
public class RBlocUserServiceImpl implements RBlocUserService {

    @Autowired
    private RBlocUserMapper rBlocUserMapper;
    // @Autowired
    // private BlocUserMapper blocUserMapper;

    @Autowired
    private CorpRwDs corpRwDs;

    @Autowired
    private UserBizService iUserService;

    @Autowired
    private IVinService vinService;

    @Autowired
    private IManageUserService manageUserService;
    // @Autowired
    // private ICardService cardService;

    // @Autowired
    // private MerchantFeignClient merchantFeignClient;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private TradingFeignClient tradingFeignClient;
    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;
    @Autowired
    private CommercialRoDs commercialRoDs;

    @Autowired
    private DcCusBalanceService dcCusBalanceService;

    @Autowired
    private PublishCorpUserInfoService publishCorpUserInfoService;

    @Autowired
    private CardMapper cardMapper;

    @Autowired
    private VinMapper vinMapper;
    @Autowired
    private CorpOrgMapper corpOrgMapper;

    @Autowired
    private UserCommRefService userCommRefService;

    @Autowired
    private PublishCusInfoService publishCusInfoService;

    @Autowired
    private CorpSettlementService corpSettlementService;

    @Autowired
    private BizBiFeignClient bizBiFeignClient;

    /**
     * 根据条件查询 固定条件(status=1)
     *
     * @param rBlocUser
     * @return
     */
    public List<RBlocUser> findByCondition(RBlocUser rBlocUser) {
        rBlocUser.setStatus(1);
        return rBlocUserMapper.findByCondition(rBlocUser);
    }

    /**
     * 分页查询  该企业的 所有组织下的用户列表
     * @param page
     * @param userParams
     * @param keyword
     * @param corpId
     * @param corpOrgId
     * @return
     */
//    @Override
//    public ListResponse<RBlocUser> queryCorpUser(Page<UserAndDynamicVo> page,
//                                                 Integer userParams,
//                                                 String keyword,
//                                                 Long corpId,
//                                                 Long corpOrgId) {
//
//        Page<UserAndDynamicVo> pageInfo = PageHelper.startPage(page.getPageNum(),
//                page.getPageSize(),
//                true,
//                false,
//                null);
//
//        Page<RBlocUser> list = rBlocUserMapper.queryCorpUser(userParams, keyword, corpId, corpOrgId);
//
//        return new ListResponse<>(list.stream().collect(Collectors.toList()), list.getTotal());
//    }


    /**
     * 根据条件更新 id必传，其他字段至少传一个
     *
     * @param rBlocUser
     * @return
     */
    public BaseResponse modifyByCondition(RBlocUser rBlocUser) {
        DcAssertUtil.isNotNull(rBlocUser, "请求参数为空");
        if (rBlocUser.getId() == null || rBlocUser.getId() <= 0) {
            throw new DcArgumentException("RBlocUser.id为空");
        }
        if (rBlocUserMapper.modifyByCondition(rBlocUser) > 0) {
            publishCorpUserInfoService.publishCorpUserInfo(rBlocUser.getId());
            return new BaseResponse();
        } else {
            throw new DcServiceException("修改RBlocUser失败");
        }
    }

    /**
     * 新增集团用户信息
     *
     * @param rBlocUser
     * @return
     */
    @Override
    public int insertRBlocUser(RBlocUser rBlocUser) {
        log.info("rBlocUser：{}", JsonUtils.toJsonString(rBlocUser));
        IotAssert.isNotNull(rBlocUser.getLimitCycle(), "未指定授信客户限额周期");
        int status = 0;
        rBlocUser.setCreateDate(new Date());
        // 校验集团客户表是否已经添加
        RBlocUser rBlocUsers = rBlocUserMapper.selectRBlocUserByPhone(rBlocUser);
        if (rBlocUsers != null) {
            // 手机号存在，则校验是否禁用
            IotAssert.isTrue(rBlocUsers.getStatus() == 0, "此客户已添加，请重新添加！");
            // 已禁用时，重新执行更新操作
            rBlocUser.setStatus(1);
            rBlocUser.setId(rBlocUsers.getId());
            if (rBlocUserMapper.updateRBlocUserStatus(rBlocUser) > 0) {
                publishCorpUserInfoService.publishCorpUserInfo(rBlocUser.getId());
                log.info("新增集团用户信息已存在，更新操作，数据为：{}",
                    JsonUtils.toJsonString(rBlocUser));
                return 1;
            } else {
                return 2;
            }
        }
        // 校验邮箱不可重复
//        UserPropVO emailResult = iUserService.findByEmail(rBlocUser.getEmail(), rBlocUser.getCommId());
//        if (emailResult != null) {
//            log.error("此邮箱已添加，请重新添加！");
//            return 3;
//        }
        // 校验用户表
        //CommercialSimpleVo topCommercial = commercialRoDs.getTopCommercial(rBlocUser.getCommId(), 10);
        CommercialSimpleVo comm = commercialRoDs.getCommerial(rBlocUser.getCommId());
        boolean modifyUser = this.touchUserForRBlocUser(rBlocUser, comm);

        return this.bindUserToRBlocUser(rBlocUser, comm, modifyUser);
    }

    /**
     * 尝试创建关联用户t_user
     *
     * @param rBlocUser
     * @param comm
     * @return 创建了新的_user将返回true，否则返回false
     */
    private boolean touchUserForRBlocUser(RBlocUser rBlocUser, CommercialSimpleVo comm) {
//        CommercialSimpleVo comm = commercialRoDs.getCommerial(rBlocUser.getCommId());
        UserPropVO userPropVO = iUserService.findByPhone(rBlocUser.getPhone(), comm.getTopCommId());
        if (userPropVO == null) {
            ObjectResponse<Long> user = manageUserService.addUser(comm.getTopCommId(),
                rBlocUser.getCommId(), "86",
                rBlocUser.getPhone(), null,
                null, rBlocUser.getEmail(),
                null);
            rBlocUser.setUserId(user.getData());
            return true;
        } else {
            rBlocUser.setUserId(userPropVO.getUserId());

            // 层级关系调整
            Set<Long> relativeCommIdSet = new HashSet<>();
            relativeCommIdSet.add(rBlocUser.getCommId());
            log.info("尝试创建商户-客户关系: {}", relativeCommIdSet);

            Long uid = rBlocUser.getUserId();
            relativeCommIdSet.forEach(commId -> {
                UserCommRef userCommRef = new UserCommRef();
                userCommRef.setUid(uid)
                    .setCommId(commId)
                    .setCreateTime(new Date());

                // FIXME: 这里存在多级商户添加问题
                userCommRefService.addUserCommRef(userCommRef);
            });
            return false;
        }
    }

    /**
     * 创建t_r_bloc_user并设置与t_user的关系
     *
     * @param rBlocUser
     * @param comm
     * @param modifyUser
     * @return
     */
    private int bindUserToRBlocUser(RBlocUser rBlocUser, CommercialSimpleVo comm,
        boolean modifyUser) {
        if (rBlocUser.getFrozenAmount() == null) {
            rBlocUser.setFrozenAmount(BigDecimal.ZERO);
        }
        if (rBlocUser.getBalance() == null) {
            rBlocUser.setBalance(BigDecimal.ZERO);
        }
        int blocUserId = rBlocUserMapper.insertRBlocUser(rBlocUser);
        if (blocUserId > 0) {
            if (modifyUser) {
                //新增成功  修改t_user中的 defaultPayType,balanceId
                User user = new User();
                user.setDefaultPayType(2);
                user.setBalanceId(rBlocUser.getId());
                user.setId(rBlocUser.getUserId());
                userMapper.updateByUidSelective(user);
            }
            publishCusInfoService.publishCusInfo(comm.getTopCommId(), rBlocUser.getPhone());
            publishCorpUserInfoService.publishCorpUserInfo(rBlocUser.getId());
            return 1;
        }
        return 0;
    }

    /**
     * 根据集团id查询所属集团的客户列表
     *
     * @param blocUserId
     * @return
     */
    @Override
    public List<RBlocUser> findRBlocUserByBlocUserId(Long blocUserId) {
        return rBlocUserMapper.findRBlocUserByBlocUserId(blocUserId);
    }

    /**
     * 批量删除集团中的客户信息(物理删除)
     *
     * @param rBlocUserIds
     * @return
     */
//    @Override
//    public Boolean deleteBatchRBlocUser(List<Long> rBlocUserIds) {
//        Boolean status = false;
//        try {
//            if (rBlocUserMapper.deleteBatchIds(rBlocUserIds) > 0) {
//                status = true;
//            }
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//        }
//        return status;
//    }

    /**
     * 批量删除集团中的客户信息(逻辑删除)
     *
     * @param rBlocUserIds
     * @return
     */
    @Override
    public Boolean logicDeleteBatchIds(List<Long> rBlocUserIds) {
        Boolean status = false;
        try {
            if (rBlocUserMapper.logicDeleteBatchIds(rBlocUserIds) > 0) {

                String traceId = UUID.randomUUID().toString();
                log.info("同步数据. traceId = {}", traceId);

                publishCorpUserInfoService.publishCorpUserInfo(rBlocUserIds, traceId);
                status = true;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return status;
    }


    /**
     * 更新集团用户信息
     *
     * @param rBlocUser
     * @return
     */
    @Override
    public int updateRBlocUser(RBlocUser rBlocUser) {
        RBlocUser rBlocUserVo = this.findRBlocUserById(rBlocUser.getId(), true);
        IotAssert.isNotNull(rBlocUserVo, "找不到该用户,id: " + rBlocUser.getId());
        if (StringUtils.isNotBlank(rBlocUser.getEmail())) {
            // 校验邮箱不可重复
            IotAssert.isNotNull(rBlocUser.getCommId(), "commId不能为空");
            UserPropVO emailResult = iUserService.findByEmail(rBlocUser.getEmail(),
                rBlocUser.getCommId());
            // 排除自身的email可能
            IotAssert.isTrue(
                rBlocUser.getEmail().equals(rBlocUserVo.getEmail()) || emailResult == null,
                "此邮箱已添加，请重新添加！");
//        if (!rBlocUser.getEmail().equals(rBlocUserVo.getEmail()) && emailResult != null) {
//            log.error("此邮箱已添加，请重新添加！");
//            return 2;
//        }
        }
        int ret = rBlocUserMapper.updateRBlocUser(rBlocUser);
        IotAssert.isTrue(ret > 0, "更新失败");
//        if (rBlocUserMapper.updateRBlocUser(rBlocUser) > 0) {
        publishCorpUserInfoService.publishCorpUserInfo(rBlocUser.getId());
//            return 1;
//        } else {
//            return 0;
//        }
        return ret;
    }

    /**
     * 禁用集团授信关系(逻辑删除)
     *
     * @param rBlocUserId
     * @return
     */
    @Override
    public Boolean deleteRBlocUserById(Long rBlocUserId) {
        Boolean status = false;
        RBlocUser rBlocUser = rBlocUserMapper.findRBlocUserById(rBlocUserId, false);
        if (rBlocUser.getPhone().startsWith("2")) {
            //在禁用集团授信关系时，手机号以2开头的user下才会有紧急卡，但不支持解绑，所以无需考虑紧急卡的弃用
            throw new DcServiceException("集团默认个人客户不支持解绑");
        }

        //检查是否有正在进行中的订单
        List<Long> rBlocUserIds = new ArrayList<>();
        rBlocUserIds.add(rBlocUserId);
        ObjectResponse<Boolean> res = tradingFeignClient.queryChargingFlagByRblocUserIds(
            rBlocUserIds);
        FeignResponseValidate.check(res);
        if (res.getData()) {
            throw new DcServiceException("当前存在未处理订单，无法执行当前操作");
        }

        if (rBlocUserMapper.deleteRBlocUserById(rBlocUserId) > 0) {

            log.info("尝试删除商户-客户关系");
            RBlocUser deleteBlocUser = rBlocUserMapper.findRBlocUserById(rBlocUserId, false);
            userCommRefService.delUserCommRef(deleteBlocUser.getUserId(),
                deleteBlocUser.getCommId());

            publishCorpUserInfoService.publishCorpUserInfo(rBlocUserId);
            log.info("重置集团授信用户的默认支付方式为【个人账户】, balanceId: {}", rBlocUserId);
            userMapper.resetByBalanceId(rBlocUser.getId());
            // TODO: 这里要同步数据......
            status = true;
        }

        return status;
    }

    /**
     * 集团客户id查询集团客户详情
     *
     * @param id
     * @return
     */
    @Override
    public RBlocUser findRBlocUserById(Long id, @Nullable Boolean lock) {

        return rBlocUserMapper.findRBlocUserById(id, lock == null ? false : lock);
    }

    @Override
    public RBlocUserVo findRBlocUserVoById(Long rBlocUserId) {

        RBlocUserVo bu = rBlocUserMapper.findRBlocUserVOById(rBlocUserId);
        if (bu == null) {
            log.warn("参数错误,对应的账户不存在. rBlocUserId = {}", rBlocUserId);
            throw new DcArgumentException("参数错误,对应的账户不存在");
        }

        PointPo corpAcc = dcCusBalanceService.getPoint(PayAccountType.PERSONAL,
            bu.getCorpTopCommId(),
            bu.getCorpTopCommId(),
            bu.getCorpUid());
        if (corpAcc != null) {
            bu.setBlocAvailableAccount(corpAcc.getAvailable());
            bu.setBlocAccountBalance(corpAcc.getPoint());
            bu.setBlocFrozenAccount(corpAcc.getFrozen());
            bu.setBlocUsedAccount(corpAcc.getUsed());
        }
        return bu;
    }

    @Override
    public RBlocUser selectRBlocUserByPhone(RBlocUser rBlocUser) {
        return rBlocUserMapper.selectRBlocUserByPhone(rBlocUser);
    }

    /**
     * 查询某集团客户是否有xxx开头手机号的集团授信关系
     *
     * @param blocUserId
     * @param phone
     * @return
     */
    public List<RBlocUser> selectRBlocUserByStartWithPhone(Long blocUserId, String phone) {
        return rBlocUserMapper.selectRBlocUserByStartWithPhone(blocUserId, phone);
    }

    /**
     * C端客户id 查询集团客户详情(包含集团账户余额)
     *
     * @param userId C端用户id
     * @return
     */
    @Override
    public ListResponse<RBlocUserVo> findRBlocUserByUserId(Long userId, Boolean obtainGids,
        String commIdChain) {

        List<RBlocUserVo> blocUser = rBlocUserMapper.findRBlocUserVOByUserId(userId, null,
            commIdChain);

        if (CollectionUtils.isEmpty(blocUser)) {
            return RestUtils.buildListResponse(List.of());
        }

        val corpIdList = blocUser.stream().map(RBlocUser::getBlocUserId).distinct()
            .collect(Collectors.toList());

        Map<Long, List<Card>> cardsMap = new HashMap<>();
        Map<Long, List<VinDto>> vinsMap = new HashMap<>();
        Map<Long, List<String>> corpGidsMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(blocUser)) {
//            List<Long> corpIds =
//                blocUser.stream()
//                    .map(RBlocUserVo::getBlocUserId)
//                    .filter(Objects::nonNull)
//                    .collect(Collectors.toList());
            List<Card> cards = cardMapper.getCardsByUserIdAndCorpIds(userId, corpIdList);
            if (CollectionUtils.isNotEmpty(cards)) {
                cardsMap = cards.stream()
                    .collect(Collectors.groupingBy(Card::getCorpId, Collectors.toList()));
            }
            List<VinDto> vins = vinMapper.getCardsByUserIdAndCorpIds(userId, corpIdList);
            if (CollectionUtils.isNotEmpty(vins)) {
                vinsMap = vins.stream()
                    .collect(Collectors.groupingBy(VinDto::getCorpId, Collectors.toList()));
            }

            if (BooleanUtils.isTrue(obtainGids)) {
                ListResponse<CorpGroupTinyVo> response = authCenterFeignClient.getCorpGroups(
                    corpIdList);
                FeignResponseValidate.checkIgnoreData(response);
                corpGidsMap = CollectionUtils.isNotEmpty(response.getData())
                    ? response.getData().stream()
                    .collect(Collectors.toMap(CorpGroupTinyVo::getCorpId, CorpGroupTinyVo::getGids))
                    : new HashMap<>();
            }
        }

        final Map<Long, List<Card>> finalCardsMap = cardsMap;
        final Map<Long, List<VinDto>> finalVinsMap = vinsMap;
        final Map<Long, List<String>> finalCorpGidsMap = corpGidsMap;

        blocUser.forEach(bu -> {
            PointPo corpAcc = dcCusBalanceService.getPoint(PayAccountType.PERSONAL,
                bu.getCorpTopCommId(),
                bu.getCorpTopCommId(),
                bu.getCorpUid());
            if (corpAcc != null) {
                bu.setBlocAccountBalance(corpAcc.getPoint());
                bu.setBlocFrozenAccount(corpAcc.getFrozen());
                bu.setBlocUsedAccount(corpAcc.getUsed());
                bu.setBlocAvailableAccount(corpAcc.getAvailable());
            } else {
                bu.setBlocAccountBalance(BigDecimal.ZERO);
                bu.setBlocFrozenAccount(BigDecimal.ZERO);
                bu.setBlocUsedAccount(BigDecimal.ZERO);
                bu.setBlocAvailableAccount(BigDecimal.ZERO);
            }

//            预付费 + 无周期: min(企业可用余额，授信金额 - 冻结 - 已使用)
//            预付费 + 无限制: 企业可用余额
//            后付费 + 无周期: 授信金额 - 冻结 - 已使用
//            后付费 + 无限制: null (前端显示--)
            if (bu.getLimitCycle().equals(LimitCycle.UNLIMITED)) {
                if (SettlementType.POSTPAID.equals(bu.getBlocSettlementType())) {
                    bu.setAvailableMoney(null);
                } else {
                    bu.setAvailableMoney(bu.getBlocAvailableAccount());
                }
            } else if (bu.getLimitCycle().equals(LimitCycle.NONCYCLE)) {
                // 无周期  余额 = 授信金额 - 冻结金额 - 已使用金额
                OrderCountParam orderCountParam = new OrderCountParam();
                orderCountParam.setCorpId(bu.getBlocUserId())
                    .setPayAccountIdList(List.of(bu.getId()));
                ListResponse<CorpOrderCountVo> orderPayCountRes = bizBiFeignClient.corpOrderCount(
                    orderCountParam);
                FeignResponseValidate.check(orderPayCountRes);
                BigDecimal paidFee = BigDecimal.ZERO;
                if (orderPayCountRes != null && orderPayCountRes.getData() != null
                    && CollectionUtils.isNotEmpty(orderPayCountRes.getData())) {
                    paidFee = orderPayCountRes.getData().get(0).getPaidFee();
                }
                BigDecimal blocAccountPersonMoney = bu.getLimitMoney()
                    .subtract(bu.getFrozenAmount() == null ? BigDecimal.ZERO
                        : bu.getFrozenAmount())
                    .subtract(paidFee);

                if (SettlementType.POSTPAID.equals(bu.getBlocSettlementType())
                    || DecimalUtils.gt(bu.getBlocAvailableAccount(),
                    blocAccountPersonMoney)) {
                    bu.setAvailableMoney(blocAccountPersonMoney);
                } else {
                    bu.setAvailableMoney(bu.getBlocAvailableAccount());
                }
            } else {
                //集团授信个人可用余额= 授信限额- 冻结金额 - 已用金额
                BigDecimal blocAccountPersonMoney = bu.getLimitMoney()
                    .subtract(bu.getFrozenAmount())
                    .subtract(bu.getBalance());
                bu.setAvailableMoney(
                    DecimalUtils.lt(bu.getBlocAvailableAccount(),
                        blocAccountPersonMoney) ? bu.getBlocAvailableAccount()
                        : blocAccountPersonMoney);//取2者之间小的
            }

//            if (DecimalUtils.ltZero(bu.getAvailableMoney())) {
//                bu.setAvailableMoney(BigDecimal.ZERO);
//            }

            bu.setCards(CollectionUtils.isNotEmpty(finalCardsMap.get(bu.getBlocUserId())) ?
                finalCardsMap.get(bu.getBlocUserId()) :
                null);

            bu.setVins(CollectionUtils.isNotEmpty(finalVinsMap.get(bu.getBlocUserId())) ?
                finalVinsMap.get(bu.getBlocUserId()) :
                null);

            bu.setCorpGids(finalCorpGidsMap.get(bu.getBlocUserId()));

        });
        return new ListResponse<>(blocUser);
    }

    /**
     * C端客户id 查询集团客户详情列表(包含集团账户余额)
     *
     * @param userId
     * @param page
     * @return
     */
    @Override
    public ListResponse<RBlocUserVo> queryRBlocUserByUserId(Long userId, OldPageParam page) {
        Page<RBlocUserVo> pageInfo = PageHelper.startPage(page.getPageNum(), page.getPageSize(),
            true, false, null);
        // 根据条件查询数据库
        Page<RBlocUserVo> blocUserList = rBlocUserMapper.queryRBlocUserVOByUserId(userId);

        List<RBlocUserVo> list = blocUserList.getResult();

        val corpIdList = list.stream().map(RBlocUser::getBlocUserId).distinct()
            .collect(Collectors.toList());

        Map<Long, CorpPo> corpMap = null;
        if (CollectionUtils.isNotEmpty(corpIdList)) {
            corpMap = corpRwDs.getCorpList(new ListCorpParam().setIdList(corpIdList))
                .stream().collect(Collectors.toMap(CorpPo::getId, o -> o));
        } else {
            corpMap = new HashMap<>();
        }
        for (RBlocUserVo rBlocUserVo : list) {
            PointPo corpAcc = dcCusBalanceService.getPoint(PayAccountType.PERSONAL,
                rBlocUserVo.getCorpTopCommId(),
                rBlocUserVo.getCorpTopCommId(),
                rBlocUserVo.getCorpUid());
            if (corpAcc != null) {
                rBlocUserVo.setBlocAccountBalance(corpAcc.getPoint());
                rBlocUserVo.setBlocFrozenAccount(corpAcc.getFrozen());
                rBlocUserVo.setBlocUsedAccount(corpAcc.getUsed());
                rBlocUserVo.setBlocAvailableAccount(
                    null == corpAcc.getAvailable() ? BigDecimal.ZERO : corpAcc.getAvailable());
            } else {
                rBlocUserVo.setBlocAccountBalance(BigDecimal.ZERO);
                rBlocUserVo.setBlocFrozenAccount(BigDecimal.ZERO);
                rBlocUserVo.setBlocUsedAccount(BigDecimal.ZERO);
                rBlocUserVo.setBlocAvailableAccount(BigDecimal.ZERO);
            }

//            预付费 + 无周期: min(企业可用余额，授信金额 - 冻结 - 已使用)
//            预付费 + 无限制: 企业可用余额
//            后付费 + 无周期: 授信金额 - 冻结 - 已使用
//            后付费 + 无限制: null (前端显示--)
            if (rBlocUserVo.getLimitCycle().equals(LimitCycle.UNLIMITED)) {
                val corp = corpMap.get(rBlocUserVo.getBlocUserId());
                if (null != corp && SettlementType.POSTPAID.equals(corp.getSettlementType())) {
                    rBlocUserVo.setAvailableMoney(null);
                } else {
                    rBlocUserVo.setAvailableMoney(rBlocUserVo.getBlocAvailableAccount());
                }
            } else if (rBlocUserVo.getLimitCycle().equals(LimitCycle.NONCYCLE)) {
                // 无周期  余额 = 授信金额 - 冻结金额 - 已使用金额
                OrderCountParam orderCountParam = new OrderCountParam();
                orderCountParam.setCorpId(rBlocUserVo.getBlocUserId())
                    .setPayAccountIdList(List.of(rBlocUserVo.getId()));
                ListResponse<CorpOrderCountVo> orderPayCountRes = bizBiFeignClient.corpOrderCount(
                    orderCountParam);
                FeignResponseValidate.check(orderPayCountRes);
                BigDecimal paidFee = BigDecimal.ZERO;
                if (orderPayCountRes != null && orderPayCountRes.getData() != null
                    && CollectionUtils.isNotEmpty(orderPayCountRes.getData())) {
                    paidFee = orderPayCountRes.getData().get(0).getPaidFee();
                }
                BigDecimal blocAccountPersonMoney = rBlocUserVo.getLimitMoney()
                    .subtract(rBlocUserVo.getFrozenAmount() == null ? BigDecimal.ZERO
                        : rBlocUserVo.getFrozenAmount())
                    .subtract(paidFee);

                val corp = corpMap.get(rBlocUserVo.getBlocUserId());
                if (null != corp && SettlementType.POSTPAID.equals(corp.getSettlementType())
                    || DecimalUtils.gt(rBlocUserVo.getBlocAvailableAccount(),
                    blocAccountPersonMoney)) {
                    rBlocUserVo.setAvailableMoney(blocAccountPersonMoney);
                } else {
                    rBlocUserVo.setAvailableMoney(rBlocUserVo.getBlocAvailableAccount());
                }
            } else {
                //集团授信个人可用余额= 授信限额- 冻结金额 - 已用金额
                BigDecimal blocAccountPersonMoney = rBlocUserVo.getLimitMoney()
                    .subtract(rBlocUserVo.getFrozenAmount())
                    .subtract(rBlocUserVo.getBalance());
                rBlocUserVo.setAvailableMoney(
                    DecimalUtils.lt(rBlocUserVo.getBlocAvailableAccount(),
                        blocAccountPersonMoney) ? rBlocUserVo.getBlocAvailableAccount()
                        : blocAccountPersonMoney);//取2者之间小的
            }

//            if (DecimalUtils.ltZero(rBlocUserVo.getAvailableMoney())) {
//                rBlocUserVo.setAvailableMoney(BigDecimal.ZERO);
//            }
        }

        ListResponse<RBlocUserVo> res = new ListResponse<>(blocUserList, pageInfo.getTotal());
        return res;
    }

    /**
     * 查询所属集团下的客户列表
     *
     * @param blocUserId
     * @param keyWord
     * @param page
     * @return
     */
    @Override
    public ListResponse<RBlocUser> queryRBlocUser(OldPageParam page, String keyWord,
        Long blocUserId, String commIdChain) {
        Page<RBlocUser> pageInfo = PageHelper.startPage(page.getPageNum(), page.getPageSize(), true,
            false, null);
        // 根据条件查询数据库
        Page<RBlocUser> rBlocUsersList = rBlocUserMapper.queryRBlocUser(blocUserId, keyWord,
            commIdChain);

        ListResponse<RBlocUser> res = new ListResponse<>(rBlocUsersList, pageInfo.getTotal());
        return res;

    }


    public List<RBlocUser> getCorpUserList(Integer userParams,
//按用户名/用户ID查询条件的判断(0.用户名,1.用户ID，2:手机号,3:按特殊客户搜索)
        String sk,
        Long corpId,
        Long corpOrgId,
        List<Long> uidList) {
        return this.rBlocUserMapper.queryCorpUser(userParams, sk, corpId, corpOrgId, uidList, null);
    }

    /**
     * 分页查询  该企业的 所有组织下的用户列表
     *
     * @param page
     * @param userParams
     * @param keyword
     * @param corpId
     * @param corpOrgId
     * @return
     */
    @Override
    public ListResponse<RBlocUser> getRBlocUserList(OldPageParam page,
        Integer userParams,
        String keyword,
        Long corpId,
        Long corpOrgId,
        List<String> corpOrgIds) {
        Page<RBlocUser> pageInfo = PageHelper.startPage(page.getPageNum(),
            page.getPageSize(),
            true,
            false,
            null);
        List<RBlocUser> list = rBlocUserMapper.queryCorpUser(userParams, keyword, corpId, corpOrgId,
            null, corpOrgIds);
        list.stream().forEach(rb -> {
            if (rb.getLimitMoney() == null) {
                rb.setLimitMoney(BigDecimal.ZERO);
            }
            if (rb.getBalance() == null) {
                rb.setBalance(BigDecimal.ZERO);
            }
            if (rb.getFrozenAmount() == null) {
                rb.setFrozenAmount(BigDecimal.ZERO);
            }
            rb.setAvailableMoney(
                rb.getLimitMoney().subtract(rb.getBalance()).subtract(rb.getFrozenAmount()));
            if (DecimalUtils.ltZero(rb.getAvailableMoney())) {
                rb.setAvailableMoney(BigDecimal.ZERO);
            }
        });
        return new ListResponse(list, pageInfo.getTotal());
    }

    /**
     * 新增修改删除授信账户统一接口
     *
     * @param corpCreditAccountEx
     * @return
     */
    @Override
    public ObjectResponse<Long> upserdelRBlocUser(CorpCreditAccountEx corpCreditAccountEx) {
        IotAssert.isNotNull(corpCreditAccountEx.getCorpId(), "请传入集团客户Id");
        if (corpCreditAccountEx.getId() == null) {
            return new ObjectResponse<>(this.createRBlocUser(corpCreditAccountEx));
        } else {
            return new ObjectResponse<>(this.modifyRBlocUser(corpCreditAccountEx));
        }
    }

    private Long createRBlocUser(CorpCreditAccountEx corpCreditAccountEx) {

        log.info("尝试复用已删除的旧账户");
        RBlocUser rBlocUserQueryParam = new RBlocUser();
        rBlocUserQueryParam.setPhone(corpCreditAccountEx.getPhone())
            .setBlocUserId(corpCreditAccountEx.getCorpId())
            .setStatus(0);
        RBlocUser recoveryAccount = selectRBlocUserByPhone(rBlocUserQueryParam);
        if (recoveryAccount != null) {
            log.info("存在已删除的账号，复用此账号: {}", JsonUtils.toJsonString(recoveryAccount));
            //Long oldCommId = retQuery.getCommId();
            recoveryAccount.setCommId(corpCreditAccountEx.getCommId())
                .setLabelName(recoveryAccount.getLabelName())
                .setStatus(1)
                .setName(corpCreditAccountEx.getUserName())
                .setEmail(recoveryAccount.getEmail())
                .setLimitMoney(corpCreditAccountEx.getLimitMoney())
                .setLimitCycle(corpCreditAccountEx.getLimitCycle())
                .setId(recoveryAccount.getId());
            if (rBlocUserMapper.updateRBlocUserStatus(recoveryAccount) > 0) {

                log.info("尝试重建商户-客户关系");
                //userCommRefService.delUserCommRef(retQuery.getUserId(), oldCommId);
                UserCommRef userCommRef = new UserCommRef();
                userCommRef.setCommId(recoveryAccount.getCommId())
                    .setUid(recoveryAccount.getUserId())
                    .setCreateTime(new Date());
                userCommRefService.addUserCommRef(userCommRef);

                publishCorpUserInfoService.publishCorpUserInfo(recoveryAccount);
                log.info("新增集团用户信息已存在，更新操作，数据为：{}",
                    JsonUtils.toJsonString(recoveryAccount));
                return recoveryAccount.getId();
            } else {
                log.warn("更新失败, sql执行返回为0");
            }

        } else {
            log.info("找不到旧账户");
        }

        // insert
        RBlocUser rBlocUser = new RBlocUser();
        rBlocUser.setName(corpCreditAccountEx.getUserName());
        rBlocUser.setCorpOrgId(corpCreditAccountEx.getCorpOrgId());
        rBlocUser.setLimitMoney(corpCreditAccountEx.getLimitMoney());
        rBlocUser.setLimitCycle(corpCreditAccountEx.getLimitCycle());
        rBlocUser.setBlocUserId(corpCreditAccountEx.getCorpId());
        rBlocUser.setPhone(corpCreditAccountEx.getPhone());
        rBlocUser.setCommId(corpCreditAccountEx.getCommId());
        rBlocUser.setStatus(1);

        int ret = this.insertRBlocUser(rBlocUser);//rBlocUserMapper.insertRBlocUser(rBlocUser);!!
        corpCreditAccountEx.setId(rBlocUser.getId());
        return ret == 0 ? 0 : rBlocUser.getId();
    }

    private RBlocUser initRBlocUser(CorpCreditAccountEx corpCreditAccountEx,
        RBlocUser originalRBlocUser) {
        RBlocUser paramOut = new RBlocUser();

        paramOut.setName(corpCreditAccountEx.getUserName() == null ?
            originalRBlocUser.getName() :
            corpCreditAccountEx.getUserName());

        paramOut.setCorpOrgId(corpCreditAccountEx.getCorpOrgId() == null ?
            originalRBlocUser.getCorpOrgId() :
            corpCreditAccountEx.getCorpOrgId());

        paramOut.setLimitMoney(corpCreditAccountEx.getLimitMoney() == null ?
            originalRBlocUser.getLimitMoney() :
            corpCreditAccountEx.getLimitMoney());

        paramOut.setLimitCycle(corpCreditAccountEx.getLimitCycle() == null ?
            originalRBlocUser.getLimitCycle() :
            corpCreditAccountEx.getLimitCycle());

        paramOut.setBlocUserId(corpCreditAccountEx.getCorpId() == null ?
            originalRBlocUser.getBlocUserId() :
            corpCreditAccountEx.getCorpId());

        paramOut.setPhone(corpCreditAccountEx.getPhone() == null ?
            originalRBlocUser.getPhone() :
            corpCreditAccountEx.getPhone());

        paramOut.setCommId(corpCreditAccountEx.getCommId() == null ?
            originalRBlocUser.getCommId() :
            corpCreditAccountEx.getCommId());

        paramOut.setStatus(corpCreditAccountEx.getStatus() == null ?
            originalRBlocUser.getStatus() :
            (corpCreditAccountEx.getStatus().equals(Boolean.TRUE) ? 1 : 0));

        paramOut.setId(corpCreditAccountEx.getId());
        paramOut.setUserId(originalRBlocUser.getUserId());
        paramOut.setCreateDate(originalRBlocUser.getCreateDate());

        return paramOut;
    }

    private boolean checkLimitCycleChange(RBlocUser infoFromDb, RBlocUser update) {
        // true  检查是否有未结算的订单
        // false 不检查是否有未结算的订单

        if (infoFromDb.getLimitCycle() != update.getLimitCycle()) {
            // 用户修改了授信周期
            if (infoFromDb.getLimitCycle() == LimitCycle.UNLIMITED
                && update.getLimitCycle() != LimitCycle.UNLIMITED) {
                // 从 无限额 改成 周期限额
                return true;
            } else if (infoFromDb.getLimitCycle() != LimitCycle.UNLIMITED
                && update.getLimitCycle() == LimitCycle.UNLIMITED) {
                // 从 周期限额 改成 无限额
                return true;
            }
        }
        return false;
    }

    private ListResponse<CorpOrderCountVo> getOrderPayCount(OrderCountParam orderCountParam) {
        ListResponse<CorpOrderCountVo> corpOrderCountVoListResponse = bizBiFeignClient.corpOrderCount(
            orderCountParam);
        FeignResponseValidate.check(corpOrderCountVoListResponse);
        return corpOrderCountVoListResponse;
    }

    private Long modifyRBlocUser(CorpCreditAccountEx corpCreditAccountEx) {
        // update or ban
        RBlocUser originalRBlocUser = rBlocUserMapper.findRBlocUserById(corpCreditAccountEx.getId(),
            false);
        IotAssert.isNotNull(originalRBlocUser, "找不到授信账户。id：" + corpCreditAccountEx.getId());

        if (LimitCycle.NONCYCLE.equals(corpCreditAccountEx.getLimitCycle())) {
            BigDecimal availableMoney = originalRBlocUser.getLimitMoney()
                .subtract(originalRBlocUser.getBalance())
                .subtract(originalRBlocUser.getFrozenAmount());

            OrderCountParam param = new OrderCountParam();
            param.setCorpId(originalRBlocUser.getBlocUserId())
                .setPayAccountIdList(List.of(originalRBlocUser.getId()));
            ListResponse<CorpOrderCountVo> corpOrderCountVoListResponse = this.getOrderPayCount(
                param);

            List<CorpOrderCountVo> data = corpOrderCountVoListResponse.getData();
            IotAssert.isTrue(CollectionUtils.isNotEmpty(data) && data.size() == 1,
                "企业订单数据不正确");
            CorpOrderCountVo corpOrderCountVo = data.get(0);

            IotAssert.isTrue(corpCreditAccountEx.getLimitMoney()
                    .compareTo(originalRBlocUser.getFrozenAmount().add(corpOrderCountVo.getPaidFee()))
                    >= 0,
                "无周期限额不得低于冻结金额与历史金额之和");
        }

        RBlocUser paramIn = this.initRBlocUser(corpCreditAccountEx, originalRBlocUser);

        boolean checkNoSettlementCreditAccount = false;
        if (paramIn.getStatus() == 0) { //授信账户删除
            checkNoSettlementCreditAccount = true;

            int cardCount = cardMapper.getCreditAccountCardCount(corpCreditAccountEx.getId());
            int vinCount = vinMapper.getCreditAccountVinCount(corpCreditAccountEx.getId());
            IotAssert.isTrue(cardCount == 0 && vinCount == 0,
                "删除失败：该账户存在已配置的在线卡或VIN码");
        }
        if (checkNoSettlementCreditAccount
            || this.checkLimitCycleChange(originalRBlocUser, paramIn) // 从周期限额切换到无限制(包括相反))
        ) {
            // 检查是否有未结算的订单
            BaseResponse res = tradingFeignClient.checkNoSettlementCreditAccount(
                corpCreditAccountEx.getId());
            FeignResponseValidate.check(res);
        }
//            rBlocUser.setEmail(rBlocUserExists.getEmail());

        // WARNING
        // 若修改授信客户(d_card.manager.t_r_bloc_user)的手机号，需同时修改用户表(d_card_manager.t_user)的手机号
        // 如果被变更手机号在t_user中已存在，则不能变更（防止账户错乱）
        if (!paramIn.getPhone().equals(originalRBlocUser.getPhone())) {

            log.info("手机号变更{}->{}", originalRBlocUser.getPhone(), paramIn.getPhone());

            // ？变更手机号，判断变更后的手机号是否在该企业上配置了授信账户
            RBlocUser samePhoneAccount = rBlocUserMapper.selectRBlocUserByPhone(new RBlocUser() {{
                setBlocUserId(paramIn.getBlocUserId()).setPhone(paramIn.getPhone());
            }});

            Long newRBlocUserId;
            final long OriginalId = corpCreditAccountEx.getId();
            if (samePhoneAccount != null) {

                // 如果在该企业上配置了授信账户，则报错

                IotAssert.isTrue(samePhoneAccount.getStatus() == 0,
                    "该企业下存在相同手机号且使用中的授信账户：" + samePhoneAccount.getPhone());

//                CommercialSimpleVo comm = commercialRoDs.getCommerial(paramIn.getCommId());
//                UserPropVO userPropVO = iUserService.findByPhone(samePhoneAccount.getPhone(), comm.getTopCommId());
                //  复用此授信账户
//                samePhoneAccount.setPhone(paramIn.getPhone())
//                        .setUserId(userPropVO.getUserId())
//                        .setStatus(1);
//                rBlocUserMapper.updateRBlocUser(samePhoneAccount);
                newRBlocUserId = this.createRBlocUser(corpCreditAccountEx);


            } else {
                // 在该企业下创建授信账户
                newRBlocUserId = this.createRBlocUser(corpCreditAccountEx);

            }

            // 转移card和vin到新授信账户下
            if (newRBlocUserId > 0) {
                log.info("尝试转移card和vin到新授信账户下: {}->{}", OriginalId, newRBlocUserId);
                RBlocUser newRBlocUser = rBlocUserMapper.findRBlocUserById(newRBlocUserId, false);

                Long newUserId = newRBlocUser.getUserId();

                int cardSwapCount = cardMapper.swapCreditAccountCard(OriginalId, newUserId,
                    paramIn.getPhone());
                log.info("转移 {} 张在线卡", cardSwapCount);

                int vinSwapCount = vinMapper.swapCreditAccountVin(OriginalId, newUserId);
                log.info("转移 {} 个VIN", vinSwapCount);
            }

            // 逻辑删除旧手机号授信账户
            if (rBlocUserMapper.updateRBlocUser(originalRBlocUser.setStatus(0)) > 0) {
                paramIn.setStatus(0);
                publishCorpUserInfoService.publishCorpUserInfo(originalRBlocUser.getId());
            }

            // ？用户是否存在
            // 不存在，创建新用户
            // 修改授信账户t_r_bloc_user字段phone、user_id为新用户的对应字段

            // ？？t_r_bloc_user关联的t_vin[userId]、t_card[mobile|user_id]到新手机号

            // 同步数据库表

            //此处不应修改t_user
            if (false) {
                CommercialSimpleVo comm = commercialRoDs.getCommerial(paramIn.getCommId());
                UserPropVO userPropVO = iUserService.findByPhone(paramIn.getPhone(),
                    comm.getTopCommId());
                if (userPropVO != null && userPropVO.getStatus() != null
                    && NumberUtils.equals(userPropVO.getStatus(), 10001)) {
                    throw new DcServiceException("手机号重复，如需修改请联系客服");
                }
                User user = new User();
                user.setId(paramIn.getUserId());
                user.setPhone(paramIn.getPhone());
                iUserService.setBasicInfo(user);//修改t_user,并推送同步消息
            }


        } else {

            this.setNewBalance(originalRBlocUser, paramIn);

            this.updateRBlocUser(paramIn);//修改t_r_bloc_user,并推送同步消息
        }

        return corpCreditAccountEx.getId();//TODO 换成新id?

    }

    private void setNewBalance(RBlocUser infoFromDb, RBlocUser update) {
        // STEP 1. 检查是否更新授信周期
        if (infoFromDb.getLimitCycle().equals(update.getLimitCycle())) {
            return;
        }
        // STEP 2. 根据修改后的授信周期，获取该授信周期内已结算订单的消费金额
        Long startTimestamp = Long.MIN_VALUE;
        Long endTimestamp = System.currentTimeMillis();
        switch (update.getLimitCycle()) {
            case DAILY:
                startTimestamp = LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant()
                    .toEpochMilli();
                break;

            case WEEKLY:
                startTimestamp = LocalDate.now().with(DayOfWeek.MONDAY)
                    .atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                break;

            case MONTHLY:
                startTimestamp = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth())
                    .atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                break;

            case YEARLY:
                startTimestamp = LocalDate.now().with(TemporalAdjusters.firstDayOfYear())
                    .atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                break;

            case UNLIMITED:
                startTimestamp = null;
                endTimestamp = null;
                break;
        }
        ChargerOrderParam param = new ChargerOrderParam();
        param.setStopTimeFrom(startTimestamp == null ? "" : startTimestamp.toString());
        param.setStopTimeTo(endTimestamp == null ? "" : endTimestamp.toString());
        param.setStatusList(List.of(OrderStatus.ORDER_STATUS_RECEIVE_MONEY));
        param.setExcludeCancelOrder(true);
        param.setPayAccountList(
            List.of(new PayAccount().setPayAccountId(infoFromDb.getId())
                .setDefaultPayType(OrderPayType.BLOC)));
        param.setPayStatus(2);
        param.setBlocUserId(infoFromDb.getBlocUserId());
        param.setCorpOrgIds(List.of(infoFromDb.getCorpOrgId()));
        ObjectResponse<ChargerOrderDataVo> dataVoObjectResponse = tradingFeignClient.getChargerOrderData(
            param);
        FeignResponseValidate.check(dataVoObjectResponse);
        ChargerOrderDataVo dataVo = dataVoObjectResponse.getData();

        // STEP 3. 已用额 = 消费金额
        update.setBalance(dataVo.getOrderPriceAmount());
    }

    @Override
    @Transactional
    public void batchDisableCreditAccount(List<Long> accountIds) {

        this.rBlocUserMapper.batchDisableCreditAccount(accountIds);
    }


    @Override
    public void batchAddCreditAccount(List<RBlocUser> buList) {
        if (CollectionUtils.isEmpty(buList)) {
            log.warn("buList is empty. do nothing");
            return;
        }
        this.rBlocUserMapper.batchAddCreditAccount(buList);
    }

    @Override
    public BaseResponse refreshRBlocUserBalance() {
        log.info("refreshRBlocUserBalance start");
        // TODO 可能需要推送MQ去修改同步表,但目前没必要
        // publishCorpUserInfoService...
        LocalDate currDate = LocalDate.now();
        LocalDate firstDayOfWeek = currDate.with(DayOfWeek.MONDAY);
        LocalDate firstDayOfMonth = currDate.with(TemporalAdjusters.firstDayOfMonth());
        LocalDate firstDayOfYear = currDate.with(TemporalAdjusters.firstDayOfYear());

        long start = 0;
        final int size = 100;

        while (true) {
            RBlocUser temp = new RBlocUser();
            temp.setStart(start)
                .setSize(size);
            List<RBlocUser> rBlocUsers = rBlocUserMapper.findByCondition(temp);
            if (CollectionUtils.isEmpty(rBlocUsers)) {
                break;
            }
            Map<LimitCycle, List<Long>> dailyLimitCycle = rBlocUsers.stream().collect(
                Collectors.groupingBy(RBlocUser::getLimitCycle,
                    Collectors.mapping(RBlocUser::getId, Collectors.toList())));

            List<Long> modifiedIdList = new ArrayList<>();

            List<Long> dailyList = dailyLimitCycle.get(LimitCycle.DAILY);
            if (CollectionUtils.isNotEmpty(dailyList)) {
                modifiedIdList.addAll(dailyList);
            }

            List<Long> weeklyList = dailyLimitCycle.get(LimitCycle.WEEKLY);
            if (currDate.isEqual(firstDayOfWeek) && CollectionUtils.isNotEmpty(weeklyList)) {
                modifiedIdList.addAll(weeklyList);
            }

            List<Long> monthlyList = dailyLimitCycle.get(LimitCycle.MONTHLY);
            if (currDate.isEqual(firstDayOfMonth) && CollectionUtils.isNotEmpty(monthlyList)) {
                modifiedIdList.addAll(monthlyList);
            }

            List<Long> yearlyList = dailyLimitCycle.get(LimitCycle.YEARLY);
            if (currDate.isEqual(firstDayOfYear) && CollectionUtils.isNotEmpty(yearlyList)) {
                modifiedIdList.addAll(yearlyList);
            }

            if (CollectionUtils.isNotEmpty(modifiedIdList)) {
                rBlocUserMapper.updateBalanceEveryMouth(modifiedIdList);
            }
            start += size;
        }
        log.info("刷新授信客户已用额完成");
        return RestUtils.success();
    }


    /**
     * 增加冻结金额
     *
     * @param deltaFrozen 变更金额
     * @return
     */
    @Override
    public int freezeAmount(long id, BigDecimal deltaFrozen) {

        return rBlocUserMapper.freezeAmount(id, deltaFrozen);
    }

    /**
     * 解冻
     *
     * @param id
     * @param deltaFrozen
     * @return
     */
    @Override
    public int unfreezeAmount(long id, BigDecimal deltaFrozen) {

        return rBlocUserMapper.unfreezeAmount(id, deltaFrozen);
    }

    @Override
    public int updateFrozenAmountAndBalance(RBlocUser rBlocUser) {
        // TODO 可能需要推送MQ去修改同步表,但目前没必要
        // publishCorpUserInfoService...
        return rBlocUserMapper.updateFrozenAmountAndBalance(rBlocUser);
    }

    public ListResponse<RBlocUser> queryRBlocUserByIds(List<Long> ids) {
        List<RBlocUser> result = rBlocUserMapper.queryRBlocUserByIds(ids);
        return new ListResponse<>(result);
    }

    @Override
    public List<Long> queryIdsByBlocUserId(Long blocUserId) {
        return rBlocUserMapper.queryIdsByBlocUserId(blocUserId);
    }

    @Override
    public List<Long> queryIdsByBlocUserName(String blocUserName, Boolean isSureBlocUserName) {
        return rBlocUserMapper.queryIdsByBlocUserName(blocUserName, isSureBlocUserName);
    }


    @Override
    public List<Long> getUidList(Long corpId, List<Long> idList) {
        return this.rBlocUserMapper.getUidList(corpId, idList);
    }

    @Override
    @Transactional
    public int moveCorp(Long corpId, Long commId) {

        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");

        List<RBlocUser> blocUserList = rBlocUserMapper.findRBlocUserByBlocUserId(corpId);
        int ret = 0;
        if (CollectionUtils.isNotEmpty(blocUserList)) {
            ret = rBlocUserMapper.moveCorp(corpId, commId);

            blocUserList.stream().forEach(e -> {
                publishCorpUserInfoService.publishCorpUserInfo(e.getId());
            });

        }

        return ret;
    }
    @Transactional
    public ListResponse<PartnerAccountPo> getPartnerAccountData(List<PartnerPo> poList) {
        HlhlData data = new HlhlData();

        return Mono.just(poList)
            .map(partnerPoList -> {
                data.setPartnerPoList(partnerPoList);
                data.setUserVoMap(iUserService.findInfoByUids(partnerPoList.stream()
                    .map(PartnerPo::getUid).collect(Collectors.toList())));
                return data;
            })
            .map(d -> {
                d.getPartnerPoList()
                    .forEach(partnerPo -> {
                        // STEP 1、根据d_open.t_partner.name+"互联企业" 为企业名称，新建企业。
                        UserVo userVo = d.getUserVoMap().get(partnerPo.getUid());
                        CorpPo corp = new CorpPo();
                        corp.setTopCommId(partnerPo.getTopCommId())
                            .setCommId(partnerPo.getCommId())
                            .setCorpName(partnerPo.getName() + "互联企业")
                            .setContactName(partnerPo.getName())
                            .setPhone(userVo.getPhone())
                            .setType(CorpType.HLHT)
                            .setEmail(userVo.getEmail())
//                                        .setProvince(blocUser.getProvince())
                            .setCity(userVo.getCity() != null ? userVo.getCity().toString() : null)
//                                        .setDistrict(blocUser.getDistrict())
//                                        .setAddress(blocUser.getAddress())
//                                        .setOrganizationImage(blocUser.getOrganizationImage())
//                                        .setBusinessImage(blocUser.getBusinessImage())
                            .setAccount(partnerPo.getName() + "互联企业")
//                                        .setPassword(new Sha256Hash(blocUser.getPassword(), blocUser.getPhone()).toBase64())
                            .setPassword(UUIDUtils.getRandom(false, 6));
                        ObjectResponse<Long> res = authCenterFeignClient.addCorp(corp);
                        FeignResponseValidate.check(res);

                        // STEP 2、根据d_open.t_partner.uid, 新建授信账户。
                        ObjectResponse<CorpOrgPo> orgPoResponse = authCenterFeignClient.getOrgInfoByLevel(
                            res.getData(), 1);
                        FeignResponseValidate.check(orgPoResponse);
                        RBlocUser rBlocUser = new RBlocUser();
                        rBlocUser.setName(userVo.getUsername())
                            .setCorpOrgId(orgPoResponse.getData().getId())
                            .setLimitMoney(null)
                            .setLimitCycle(LimitCycle.UNLIMITED)
                            .setBlocUserId(res.getData())
                            .setPhone(userVo.getPhone())
                            .setCommId(userVo.getCommId())
                            .setStatus(1);
                        this.insertRBlocUser(rBlocUser);
                        //新增成功  修改t_user中的 defaultPayType,balanceId
                        User user = new User();
                        user.setDefaultPayType(2);
                        user.setBalanceId(rBlocUser.getId());
                        user.setId(rBlocUser.getUserId());
                        userMapper.updateByUidSelective(user);
                        publishCusInfoService.publishCusInfo(user.getId());

                        // STEP 3、组装返回数据
                        PartnerAccountPo accountPo = new PartnerAccountPo();
                        accountPo.setPid(partnerPo.getId())
                            .setCorpTopCommId(corp.getTopCommId())
                            .setCorpId(res.getData())
                            .setCorpName(corp.getCorpName())
                            .setCorpUserId(rBlocUser.getId());
                        d.accountPoMap.add(accountPo);
                    });
                log.info("accountPoMap: {}", JsonUtils.toJsonString(d.accountPoMap));
                return d.accountPoMap;
            })
            .map(RestUtils::buildListResponse)
            .block(Duration.ofSeconds(50L));
    }

    @Data
    private static class HlhlData {

        private List<PartnerPo> partnerPoList = new ArrayList<>();

        private Map<Long, UserVo> userVoMap = new HashMap<>();

        //        private Map<Long, PartnerAccountPo> accountPoMap = new HashMap<>();
        private List<PartnerAccountPo> accountPoMap = new ArrayList<>();
    }

}
