package com.cdz360.biz.cus.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.cus.service.DebugRedisIssueService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Tag(name = "系统相关的接口", description = "系统")
@RestController
@RequestMapping("/api/sys")
public class SystemRest {

    @Autowired
    private DebugRedisIssueService debugRedisIssueService;

    @GetMapping("/debugRedis")
    public BaseResponse debugRedis() {
        debugRedisIssueService.debugRedisIssue();
        return RestUtils.success();
    }
}
