package com.cdz360.biz.cus.repository;

import com.cdz360.biz.cus.domain.UserGroup;
import com.cdz360.biz.cus.domain.vo.UserGroupVo;
import com.github.pagehelper.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserGroupMapper {


    /**
     * 添加分组
     *
     * @param record
     * @return
     */
    int insertSelective(UserGroup record);

    /**
     * 更新分组
     *
     * @param record
     * @return
     */
    int updateSelectiveById(UserGroup record);

    /**
     * 查询客户分组列表
     *
     * @param groupName
     * @param commIdList
     * @return
     */
    Page<UserGroupVo> queryUserGroupList(@Param("groupName") String groupName, @Param("commIdList") List<Long> commIdList);

    /**
     * 根据 客户分组Id 查询 客户分组详情
     *
     * @param commIdList  当前商户及子商户列表
     * @param userGroupId 客户分组Id
     * @return
     */
    UserGroup queryUserGroupByIdAndcommIdList(@Param("GroupId") Long userGroupId, @Param("commIdList") List<Long> commIdList);

    /**
     * 查询客户分组信息
     *
     * @param userGroupId
     * @return
     */
    UserGroup selectByGroupId(@Param("GroupId") Long userGroupId);

    /**
     * 当前商户是否存在 此分组名称
     *
     * @param groupName 分组名称
     * @param commId    商户id
     * @return
     */
    UserGroup selectByGroupNameAndCommId(@Param("groupName") String groupName, @Param("commId") Long commId);
}