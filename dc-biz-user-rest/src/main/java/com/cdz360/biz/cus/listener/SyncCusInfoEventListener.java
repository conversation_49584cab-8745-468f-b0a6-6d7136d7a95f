package com.cdz360.biz.cus.listener;

import com.cdz360.base.model.corp.dto.CorpSyncDto;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.cus.service.UserCommRefService;
import com.cdz360.biz.ds.cus.rw.corp.ds.CorpRwDs;
import com.cdz360.biz.model.common.constant.Constant;
import com.cdz360.data.sync.event.DcBaseQEvent;
import com.cdz360.data.sync.event.MqEventSubType;
import com.cdz360.data.sync.event.SyncCorpInfoEvent;
import com.chargerlinkcar.framework.common.domain.UserCommRef;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RabbitListener(queues = Constant.MQ_QUEUE_CAR_USER_SYNC_INFO)
public class SyncCusInfoEventListener {

//    @Autowired
//    private UserSyncRwDs userSyncRwDs;
//
//    @Autowired
//    private CorpOrgRwDs corpOrgRwDs;
//
//    @Autowired
//    private CorpUserRwDs corpUserRwDs;
    @Autowired
    private UserCommRefService userCommRefService;

    @Autowired
    CorpRwDs corpRwDs;

    @RabbitHandler
    public void syncInfoListener(String msg) {
        log.info(">> msg = {}", msg);
        try {
            try {
                JsonNode json = JsonUtils.fromJson(msg);
                String subMqType = json.get(DcBaseQEvent.FIELD_MQ_SUB_TYPE).asText();
                if (MqEventSubType.MQ_CUS_INFO.name().equals(subMqType)) {
//                    this.syncCusInfo(msg);
                } else if (MqEventSubType.MQ_CORP.name().equals(subMqType)) {
                    this.syncCorpInfo(msg);
                } else if (MqEventSubType.MQ_CORP_ORG.name().equals(subMqType)) {
//                    this.syncCorpOrgInfo(msg);

                } else if (MqEventSubType.MQ_CORP_USER.name().equals(subMqType)) {
//                    this.syncCorpUserInfo(msg);
                } else {
                    log.info("收到未识别的消息. msg = {}", msg);
                }

            } catch (Exception e) {
                log.warn("收到未识别的消息. msg = {}", msg, e);
            }


        } catch (Exception e) {
            log.warn("收到未识别的消息. msg = {}", msg);
        }
        log.info("<<");
    }

//    private void syncCusInfo(String msg) {
//        SyncCusInfoEvent event = JsonUtils.fromJson(msg, SyncCusInfoEvent.class);
//        CusSyncDto dto = event.getData();
//        CusRepPo cus = new CusRepPo();
//        cus.setCommId(dto.getTopCommId())
//                .setUsername(dto.getUsername()).setName(dto.getName())
//                .setSex(dto.getSex()).setStatus(dto.getStatus())
//                .setPhone(dto.getPhone()).setEmail(dto.getEmail())
//                .setCityCode(dto.getCityCode())
//                .setBalanceId(dto.getBalanceId()).setDefaultPayType(dto.getDefaultPayType())
//                .setEnable(dto.getEnable()).setId(dto.getId());
//        this.userSyncRwDs.insertOrUpdate(cus);
//    }

//    private void syncCorpOrgInfo(String msg) {
//        SyncCorpOrgInfoEvent event = JsonUtils.fromJson(msg, SyncCorpOrgInfoEvent.class);
//        CorpOrgSyncDto dto = event.getData();
//        this.corpOrgRwDs.insertOrUpdate(dto);
//    }
//
//    private void syncCorpUserInfo(String msg) {
//        SyncCorpUserInfoEvent event = JsonUtils.fromJson(msg, SyncCorpUserInfoEvent.class);
//        CorpUserSyncDto dto = event.getData();
//        this.corpUserRwDs.insertOrUpdate(dto);
//    }

    private void syncCorpInfo (String msg) {
        log.info("同步企业信息 msg={}",msg);
        SyncCorpInfoEvent event = JsonUtils.fromJson(msg, SyncCorpInfoEvent.class);
        CorpSyncDto dto = event.getData();
        this.corpRwDs.insertOrUpdate(dto);
        //创建企业与B+phone所属关系
        UserCommRef userCommRef = new UserCommRef();
        userCommRef.setUid(dto.getUid())
                .setCommId(dto.getCommId())
                .setCreateTime(new Date());
        this.userCommRefService.addUserCommRef(userCommRef);
    }
}
