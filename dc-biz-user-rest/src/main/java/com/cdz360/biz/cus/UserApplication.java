package com.cdz360.biz.cus;

import com.netflix.discovery.EurekaClient;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import reactivefeign.spring.config.EnableReactiveFeignClients;
import reactor.core.publisher.Hooks;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableReactiveFeignClients(basePackages = {"com.cdz360.biz.utils.feign.*"})
@EnableFeignClients(basePackages = {
    "com.cdz360.biz.cus.client",
    "com.chargerlinkcar.framework.common.feign"
})
@Slf4j
@ComponentScan(basePackages = {
    "com.chargerlinkcar.core",
    "com.chargerlinkcar.framework.common",
    "com.cdz360"
})
@MapperScan(basePackages = {"com.cdz360.biz.cus.repository",
    "com.cdz360.biz.ds.cus.**.mapper"})
@EnableAsync
@EnableScheduling
public class UserApplication {

    @Autowired
    private EurekaClient discoveryClient;

    public static void main(String[] args) {
        log.info("starting dc-biz-user!!!");
        Hooks.enableAutomaticContextPropagation();  // 输出调用链traceId
//        SpringApplication.run(UserApplication.class, args);
        new SpringApplicationBuilder(UserApplication.class).web(WebApplicationType.REACTIVE)
            .run(args);
    }


    @PreDestroy
    public void destroy() {
        log.info("going to shutdown");

        //DiscoveryManager.getInstance().shutdownComponent();
        discoveryClient.shutdown();
        log.info("eureka de-registered.....");
    }
}
