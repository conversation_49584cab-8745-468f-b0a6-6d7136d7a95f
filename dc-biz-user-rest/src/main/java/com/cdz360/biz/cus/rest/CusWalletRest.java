package com.cdz360.biz.cus.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.cus.service.IWalletService;
import com.cdz360.biz.model.cus.wallet.param.ListRefundOrderParam;
import com.cdz360.biz.model.cus.wallet.po.RefundOrderPo;
import com.cdz360.biz.model.trading.cus.param.CusPayBillListParam;
import com.cdz360.biz.model.trading.cus.param.CusRefundAllParam;
import com.cdz360.biz.model.trading.cus.param.CusRefundOrderListParam;
import com.cdz360.biz.model.trading.cus.vo.CusPayBillVo;
import com.cdz360.biz.model.trading.cus.vo.CusRefundOrderVo;
import com.chargerlinkcar.framework.common.domain.PointPo;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Comparator;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * CusWalletRest
 *  TODO
 * @since 2019/9/23 10:48
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/wallet")
@Tag(description = "用户钱包", name = "用户钱包")
public class CusWalletRest extends BaseController {

    @Autowired
    private IWalletService walletService;

    /**
     * 创建退款单
     *
     * @param request
     * @param refundOrderPo
     * @return
     */
    @PostMapping(value = "/addRefundOrder")
    public ObjectResponse<RefundOrderPo> addRefundOrder(ServerHttpRequest request, @RequestBody RefundOrderPo refundOrderPo) {

        if (refundOrderPo.getCusId() <= 0L) {
            throw new DcArgumentException("用户信息参数错误");
        }

        if (refundOrderPo.getTopCommId() <= 0L) {
            throw new DcArgumentException("集团商户ID参数错误");
        }

        if (refundOrderPo.getBankCardId() <= 0L || StringUtils.isBlank(refundOrderPo.getBankCardNo())) {
            throw new DcArgumentException("银行卡信息参数错误");
        }

        if (refundOrderPo.getStatus() == null) {
            throw new DcArgumentException("退款单状态错误");
        }

        if (refundOrderPo.getAmount() == null) {
            throw new DcArgumentException("提现金额参数错误");
        }

        RefundOrderPo res = walletService.addRefundOrder(refundOrderPo);

        return RestUtils.buildObjectResponse(res);
    }

    /**
     * 获取可退款金额
     *
     * @param request
     * @param payAccountType
     * @param accountId
     * @param userId
     * @return
     */
    @GetMapping(value = "/getRefundable")
    public ObjectResponse<PointPo> getRefundable(ServerHttpRequest request, @RequestParam("payAccountType") PayAccountType payAccountType,
                                                 @RequestParam("accountId") long accountId, @RequestParam("userId") long userId) {

        if (payAccountType == null) {
            throw new DcArgumentException("参数错误");
        }

        if (accountId <= 0 || userId <= 0) {
            throw new DcArgumentException("参数错误");
        }

        PointPo pointPo = walletService.getRefundable(payAccountType, accountId, userId);
        return RestUtils.buildObjectResponse(pointPo);
    }

    /**
     * 获取提现信息列表
     *
     * @param request
     * @param param
     * @return
     */
    @PostMapping(value = "/listRefundOrder")
    public ListResponse<RefundOrderPo> listRefundOrder(ServerHttpRequest request, @RequestBody ListRefundOrderParam param) {
        log.info("获取提现信息列表。 param: {}", param);
        if (param.getTopCommId() <= 0 || param.getCusId() <= 0) {
            throw new DcArgumentException("参数错误");
        }

        ListResponse<RefundOrderPo> list = walletService.listRefundOrder(param);
        log.info("获取提现信息列表。request: {}, result: {}", request, list.getData().size());
        return list;
    }


    @PostMapping(value = "/getPayBillList")
    @Operation(summary = "获取用户可申请退款的充值记录")
    public ListResponse<CusPayBillVo> getCusPayBillList(
            ServerHttpRequest request, @RequestBody CusPayBillListParam param) {
        log.info("获取用户可申请退款的充值记录: {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        List<CusPayBillVo> result = walletService.getCusPayBillList(param, null);
        result.sort(Comparator.comparing(CusPayBillVo::getCreateTime).reversed());
        log.info("用户可用充值记录大小: size = {}", result.size());

        return RestUtils.buildListResponse(result);
    }

    @Operation(summary = "用户退款")
    @PostMapping(value = "/refundAll")
    public BaseResponse refundAll(ServerHttpRequest request, @RequestBody CusRefundAllParam param) {
        log.info("用户退款: {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        int i = walletService.refundAll(param);
        return RestUtils.success();
    }

    @Operation(summary = "获取用户的退款记录")
    @PostMapping(value = "/getRefundOrderRecord")
    public ListResponse<CusRefundOrderVo> getRefundOrderRecord(ServerHttpRequest request,
                                                               @RequestBody CusRefundOrderListParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}" + param);
        List<CusRefundOrderVo> result = walletService.getRefundOrderRecord(param);
        log.info("<< size = {}", result.size());
        return RestUtils.buildListResponse(result);
    }
}
