package com.cdz360.biz.cus.service.impl;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.cus.service.IBankCardService;
import com.cdz360.biz.ds.cus.ro.wallet.ds.BankCardRoDs;
import com.cdz360.biz.ds.cus.rw.wallet.ds.BankCardRwDs;
import com.cdz360.biz.model.cus.wallet.vo.BankCardVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * IBankCardServiceImpl
 *  TODO
 *
 * <AUTHOR>
 * @since 2019/9/19 11:18
 */
@Slf4j
@Service
public class BankCardServiceImpl implements IBankCardService {

    @Autowired
    private BankCardRoDs bankCardRoDs;

    @Autowired
    private BankCardRwDs bankCardRwDs;

    @Override
    public ObjectResponse<BankCardVo> findById(Long id) {
        log.info("获取银行卡。id: {}", id);
        BankCardVo bankCardVo = bankCardRwDs.getBankCard(id, false);
        log.info("获取银行卡结果。bankCardVo: {}", bankCardVo);
        return new ObjectResponse<>(bankCardVo);
    }

    /**
     * 获取银行卡信息
     *
     * @param cusId
     * @param enable
     * @param cardNo
     * @return
     */
    @Override
    public ListResponse<BankCardVo> listBankCard(Long cusId, String cardNo, Boolean enable) {

        log.info("获取银行卡列表 cusId: {},enable: {},cardNo: {}", cusId, enable, cardNo);

        List<BankCardVo> list = bankCardRoDs.listBankCard(cusId, cardNo, enable);

        log.info("获取银行卡列表结果 res.size：{}", list.size());
        return new ListResponse<>(list);
    }

    /**
     * 添加银行卡
     *
     * @param bankCardVo
     * @return
     */
    @Override
    public BaseResponse addBankCard(BankCardVo bankCardVo) {
        //        param.setCreateTime(new Date());
        log.info("添加银行卡。param: {}", JsonUtils.toJsonString(bankCardVo));

        List<BankCardVo> list = bankCardRoDs.listBankCard(bankCardVo.getCusId(),
            bankCardVo.getCardNo(), true);

        if (list != null && list.size() > 0) {
            throw new DcServiceException("不能绑定相同银行卡");
        }
        //插入数据库
        try {
            BankCardVo res = bankCardRwDs.addBankCard(bankCardVo);
            log.info("添加银行卡结果。res: {}", res);
            return new ObjectResponse<>(res);
        } catch (Exception e) {
            throw new DcServiceException("银行卡添加失败");
        }
    }

    /**
     * 修改银行卡信息
     *
     * @param bankCardVo
     * @return
     */
    @Override
    public BaseResponse updateBankCard(BankCardVo bankCardVo) {
        //        param.setUpdateTime(new Date());

        log.info("修改银行卡。param: {}", JsonUtils.toJsonString(bankCardVo));

        List<BankCardVo> list = bankCardRoDs.listBankCard(bankCardVo.getCusId(),
            bankCardVo.getCardNo(), true);

        if (list != null && list.size() > 0 && list.stream()
            .anyMatch(vo -> !vo.getId().equals(bankCardVo.getId()))) {
            throw new DcServiceException("不能绑定相同银行卡");
        }

        //更新数据库
        try {
            if (bankCardRwDs.updateBankCard(bankCardVo)) {
                BankCardVo res = bankCardRwDs.getBankCard(bankCardVo.getId(), false);
                log.info("修改银行卡结果。res: {}", res);
                return new ObjectResponse<>(res);
            } else {
                throw new DcServiceException("银行卡信息修改失败");
            }
        } catch (Exception e) {
            throw new DcServiceException("银行卡添加失败");
        }
    }

    /**
     * 删除银行卡信息（逻辑删除）
     *
     * @param userId
     * @param cardNo
     * @return
     */
    @Override
    public BaseResponse deleteBankCard(Long userId, String cardNo) {
        log.info("【删除银行卡】,userId:{},cardNo:{}", userId, cardNo);

        BankCardVo bankCardVo = new BankCardVo();
        bankCardVo.setCusId(userId);
        bankCardVo.setCardNo(cardNo);
        if (bankCardRwDs.deleteBankCard(bankCardVo)) {
            return new BaseResponse();
        } else {
            throw new DcServiceException("银行卡信息删除失败");
        }
    }

}
