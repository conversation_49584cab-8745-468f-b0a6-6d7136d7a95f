package com.cdz360.biz.cus.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version  1.0
 * Merchant
 *  商户用户表
 * @since 2018/12/6
 */
@Data
public class Merchant implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 账户
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 姓名
     */
    private String merchant;

    /**
     * 电话号码
     */
    private String phone;

    /**
     * 电子邮件
     */
    private String email;

    /**
     * 状态(1：启用  2：冻结）
     */
    private Integer status;

    /**
     * 父ID
     */
    private Long pid;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 登录时间
     */
    private Date loginTime;

    /**
     * 服务有效时间
     */
    private Date serviceTime;

    /**
     * 子账号数
     */
    private Integer accountNumber;

    /**
     * 密码是否修改(0.没有,1.修改)
     */
    private Integer isUpdate;

    /**
     * 头像)
     */
    private String logo;

    /**
     * 地址
     **/
    private String address;

    /**
     * 昵称
     **/
    private String nickName;

    /**
     * 性别
     **/
    private Integer sex;

    /**
     * 省
     **/
    private Integer provinceId;

    /**
     * 城市
     **/
    private Integer cityId;

    /**
     * 区域
     **/
    private Integer areaId;

    /**
     * 联系人
     */
    private String contacts;

    /**
     * 联系人手机号
     */
    private String contactsPhone;

    /**
     * 物业子账号权限
     */
    private String extendPermission;
}
