package com.cdz360.biz.cus.repository;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * AppDeviceInfo
 *  TODO
 * @since 2019/11/21 9:43
 * <AUTHOR>
 */
@Mapper
public interface AppDeviceInfoMapper {
    @Insert("INSERT into d_card_manager.`t_app_device_info`(`appType`,`uid`,`deviceToken`,`createTime`,`updateTime`) VALUES(#{appType},#{uid},#{deviceToken},now(),now()) ON DUPLICATE KEY UPDATE deviceToken = #{deviceToken}, updateTime = now() ")
    int insertRecord(@Param("appType") Integer appType, @Param("uid") Long uid, @Param("deviceToken") String deviceToken);
}
