package com.cdz360.biz.cus.service;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.cus.vo.CusInfoVo;
import com.cdz360.data.cache.RedisCusRwService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Slf4j
@Service
public class DebugRedisIssueService {
    @Autowired
    private RedisCusRwService redisCusRwService;


    public void debugRedisIssue() {
        log.info(">>");
        new Thread(() -> {
            log.info("thread start");
            long cusId = 1;
            boolean stop = false;
            CusInfoVo cusInfo = new CusInfoVo();
            cusInfo.setCusId(cusId).setName(UUID.randomUUID().toString())
                    .setPhone("98765432101").setName(UUID.randomUUID().toString())
                    .setCommId(1234L);
            redisCusRwService.updateCusInfo(AppClientType.WX_LITE, cusInfo);
            while (!stop) {
                String name = UUID.randomUUID().toString().replace("-", "");
                CusInfoVo cus = this.redisCusRwService.getCustomerInfoCache(AppClientType.WX_LITE, cusId);
                cus.setName(name);
                redisCusRwService.updateCusInfo(AppClientType.WX_LITE, cus);
                CusInfoVo result = this.redisCusRwService.getCustomerInfoCache(AppClientType.WX_LITE, cusId);
                try {
                    Thread.sleep(500);
                } catch (Exception e) {
                    throw new RuntimeException(e.getMessage(), e);
                }
            }
            log.info("thread end");
        }).start();
        log.info("<<");
    }
}
