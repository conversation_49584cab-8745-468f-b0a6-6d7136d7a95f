package com.cdz360.biz.cus.service.impl;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.biz.cus.repository.CorpOrgMapper;
import com.cdz360.biz.cus.service.CorpBizService;
import com.cdz360.biz.cus.service.ICorpOrgService;
import com.cdz360.biz.cus.service.PublishCorpOrgInfoService;
import com.cdz360.biz.ds.cus.ro.mechant.ds.CommercialRoDs;
import com.cdz360.biz.model.cus.corp.po.CorpOrgLoginVo;
import com.cdz360.biz.model.cus.corp.po.CorpOrgPo;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.corp.vo.CorpOrgVO;
import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.event.Level;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * CorpOrgServiceImpl
 *
 * @since 2019/12/30 10:57
 * <AUTHOR>
 */
@Service
@Slf4j
public class CorpOrgServiceImpl implements ICorpOrgService {
    @Autowired
    private CorpOrgMapper corpOrgMapper;
    @Autowired
    private CorpBizService corpBizService;

    @Autowired
    private CommercialRoDs commercialRoDs;

    @Autowired
    private PublishCorpOrgInfoService publishCorpOrgInfoService;

    @Override
    public int addOrUpdateCorpOrg(CorpOrgPo corpOrgPo) {
        if (corpOrgPo.getId() != null) {
            int ret = corpOrgMapper.updateCorpOrg(corpOrgPo);
            if (ret > 0) {
                publishCorpOrgInfoService.PublishCorpOrgInfo(corpOrgPo.getId());
            }
            return ret;
        }
        int count = corpOrgMapper.countOrgByName(corpOrgPo.getOrgName(), corpOrgPo.getAccount());
        if (count > 0) {
            throw new DcServiceException("存在同名(同账号)组织");
        }
        //把自己的id设置回l1Id或者l2Id,3级商户不用再次update
        int i = corpOrgMapper.addCorpOrg(corpOrgPo);
        if (i > 0) {
            publishCorpOrgInfoService.PublishCorpOrgInfo(corpOrgPo.getId());
        }
        if (corpOrgPo.getOrgLevel() == 1) {
            corpOrgPo.setL1Id(corpOrgPo.getId());
            if (corpOrgMapper.updateCorpOrg(corpOrgPo) > 0) {
                publishCorpOrgInfoService.PublishCorpOrgInfo(corpOrgPo.getId());
            }
        } else if (corpOrgPo.getOrgLevel() == 2) {
            corpOrgPo.setL2Id(corpOrgPo.getId());
            if (corpOrgMapper.updateCorpOrg(corpOrgPo) > 0) {
                publishCorpOrgInfoService.PublishCorpOrgInfo(corpOrgPo.getId());
            }
        }
        return i;
    }

    @Override
    public ListResponse<CorpOrgVO> getCorpOrgById(Long corpId, Long id, OldPageParam page) {
        Page<CorpOrgVO> pageInfo = PageHelper.startPage(page.getPageNum(), page.getPageSize(), true, false, null);
        Page<CorpOrgVO> corpOrgVOS = corpOrgMapper.getCorpOrgById(corpId, id);
        return new ListResponse<>(corpOrgVOS.stream().collect(Collectors.toList()), pageInfo.getTotal());
    }

    @Override
    @Transactional
    public ObjectResponse<CorpOrgLoginVo> getOrgByAccount(String account) {
        CorpOrgPo corpOrgPo = corpOrgMapper.getCorpOrgByAccount(account);
        if (corpOrgPo == null) {
            log.warn("账号不存在. account = {}", account);
            throw new DcServiceException("账号不存在", Level.INFO);
        }
        CorpOrgLoginVo corpOrgLoginVo = new CorpOrgLoginVo();
        BeanUtils.copyProperties(corpOrgPo, corpOrgLoginVo);
        CorpPo corpPo = corpBizService.getCorp(corpOrgLoginVo.getCorpId());
        corpOrgLoginVo.setCorpPo(corpPo);
        List<Long> orgids = null;
        if (corpOrgPo.getOrgLevel() == 1) {
            orgids = corpOrgMapper.getOrgByL1Id(corpOrgPo.getCorpId(), corpOrgPo.getId(), null).stream().map(CorpOrgPo::getId).collect(Collectors.toList());

        } else if (corpOrgPo.getOrgLevel() == 2) {
            orgids = corpOrgMapper.getOrgByL2Id(corpOrgPo.getCorpId(), corpOrgPo.getId(), null).stream().map(CorpOrgPo::getId).collect(Collectors.toList());
        } else {
            orgids = new ArrayList<>();
            orgids.add(corpOrgPo.getId());
        }
        corpOrgLoginVo.setOrgIds(orgids);

        CommercialSimpleVo comm = this.commercialRoDs.getCommerial(corpPo.getCommId());
        if (comm != null) {
            corpOrgLoginVo.setCommIdChain(comm.getIdChain());
        }
        return new ObjectResponse<>(corpOrgLoginVo);
    }

    /**
     * 企业平台车辆报表-组织下拉数据
     *
     * @param orgId    必传
     * @param orgLevel 必传
     * @param corpId   必传
     * @param orgName  选传
     * @return
     */
    public ListResponse<CorpOrgPo> getUsableCorpOrg(Long orgId, Integer orgLevel, Long corpId, String orgName) {
        log.info("orgId: {}, orgLevel: {}, corpId: {}, orgName:{}", orgId, orgLevel, corpId, orgName);
        IotAssert.isTrue(orgId != null && orgId > 0
                        && orgLevel != null && orgLevel > 0
                        && corpId != null && corpId > 0,
                "参数错误");
        List<CorpOrgPo> corpOrgPoList = null;
        if (orgLevel == 1) {
            corpOrgPoList = corpOrgMapper.getOrgByL1Id(corpId, orgId, orgName);

        } else if (orgLevel == 2) {
            corpOrgPoList = corpOrgMapper.getOrgByL2Id(corpId, orgId, orgName);
        } else {
            corpOrgPoList = new ArrayList<>();
            corpOrgPoList.add(corpOrgMapper.getCorpOrgByOrgId(orgId));
        }
        return new ListResponse<>(corpOrgPoList);
    }

    @Override
    public ListResponse<CorpOrgVO> getCorpOrgByLevel(Long corpId, Integer level) {
        List<CorpOrgVO> corpOrgVOS = corpOrgMapper.getCorpOrgByLevel(corpId, level);
        return new ListResponse<>(corpOrgVOS);
    }

    @Override
    public ObjectResponse<CorpOrgPo> getCorpOrgByIdAndLevel(Long corpId, Integer orgLevel) {
        return new ObjectResponse<>(corpOrgMapper.getCorpOrgByIdAndLevel(corpId, orgLevel));
    }

    @Override
    public Page<CorpOrgVO> getCorpOrgTree(Long corpId, Integer index, Integer size) {
        int total = corpOrgMapper.countL2OrgByCorpId(corpId);
        Page<CorpOrgVO> corpOrgVOPage = new Page<>(index, size);
        corpOrgVOPage.setTotal(total);
        int start = (index - 1) * size;
        CorpOrgPo l1Org = corpOrgMapper.getCorpOrgByIdAndLevel(corpId, 1);
        List<CorpOrgPo> corpOrgL2List = corpOrgMapper.getCorpL2OrgListByCorpId(corpId, start, size);
        List<CorpOrgPo> corpOrgList = corpOrgMapper.getCorpOrgListPage(corpId, corpOrgL2List.stream().map(CorpOrgPo::getId).collect(Collectors.toList()));
        corpOrgL2List.stream().forEach(e -> {
            CorpOrgVO a = new CorpOrgVO();
            a.setId(e.getId());
            a.setPName(l1Org.getOrgName()).setParentId(l1Org.getId());
            a.setOrgName(e.getOrgName());
            a.setOrgLevel(e.getOrgLevel());
            a.setChildren(new ArrayList<>());
            listToTree(corpOrgList, a);
            corpOrgVOPage.add(a);
        });
        return corpOrgVOPage;
    }

    @Override
    public Page<CorpOrgVO> getCorpOrgList(Long corpId, Integer index, Integer size) {
        Page<CorpOrgVO> pageInfo = PageHelper.startPage(index, size, true, false, null);
        Page<CorpOrgVO> corpOrgVOPage = corpOrgMapper.getCorpOrgList(corpId);
        corpOrgVOPage.setTotal(pageInfo.getTotal());
        return corpOrgVOPage;
    }

    private void listToTree(List<CorpOrgPo> list, CorpOrgVO treeNode) {
        for (CorpOrgPo c : list) {
            if (c.getOrgLevel() == 3 && c.getL2Id() == treeNode.getId()) {
                CorpOrgVO e = new CorpOrgVO();
                e.setId(c.getId());
                e.setOrgLevel(c.getOrgLevel());
                e.setOrgName(c.getOrgName());
                e.setPName(treeNode.getOrgName()).setParentId(treeNode.getId());
                treeNode.getChildren().add(e);
            }
        }
    }
}
