package com.cdz360.biz.cus.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.cus.service.MoveCorpService;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * MoveCorpRest
 *
 * @since 11/3/2020 8:43 AM
 * <AUTHOR>
 */
@Tag(name = "移动企业到新的商户", description = "移动企业到新商户")
@Slf4j
@RestController
public class MoveCorpRest {

    @Autowired
    private MoveCorpService moveCorpService;

    @GetMapping("/api/user/moveCorp/move")
    public Mono<ObjectResponse<Boolean>> getMeterReading(
            ServerHttpRequest request,
            @RequestParam(value = "corpId") Long corpId,
            @RequestParam(value = "commId") Long commId) {
        log.info("移动商户到新企业 {}: corpId = {}, commId = {}",
                LoggerHelper2.formatEnterLog(request, false), corpId, commId);

        return moveCorpService.move(corpId, commId);
    }
}