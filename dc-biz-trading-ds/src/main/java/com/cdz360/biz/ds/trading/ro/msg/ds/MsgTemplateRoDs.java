package com.cdz360.biz.ds.trading.ro.msg.ds;

import com.cdz360.biz.ds.trading.ro.msg.mapper.MsgTemplateRoMapper;
import com.cdz360.biz.ds.trading.ro.order.mapper.ChargerOrderCarRoMapper;
import com.cdz360.biz.model.trading.order.po.ChargerOrderCarPo;
import com.chargerlinkcar.framework.common.domain.MsgTemplatePO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MsgTemplateRoDs {

    @Autowired
    private MsgTemplateRoMapper msgTemplateRoMapper;
    public String getMsgTemplateByTopCommId(Long topCommId, String key) {
        return msgTemplateRoMapper.getMsgTemplateByTopCommId(topCommId,key);
    }

}
