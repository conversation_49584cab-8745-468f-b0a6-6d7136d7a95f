<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.trading.ro.geo.mapper.DistrictRoMapper">


    <select id="getDistrictName" resultType="java.lang.String">
        select name from t_geo_district where code=#{code}
    </select>


    <select id="getDistrict" resultType="com.cdz360.biz.model.geo.po.DistrictPo">
        select
            d.*,
            p.name as provinceName,
            c.name as cityName
        from
            t_geo_district d
        left join t_geo_province p on
            d.`provinceCode` = p.code
        left join t_geo_city c on
            d.`cityCode` = c.code
        where
            d.code = #{code}
    </select>

    <select id="getDistrictByList" resultType="com.cdz360.biz.model.geo.po.DistrictPo">
        select
            d.*,
            p.name as provinceName,
            c.name as cityName
        from
            t_geo_district d
        left join t_geo_province p on
            d.`provinceCode` = p.code
        left join t_geo_city c on
            d.`cityCode` = c.code
        where
            d.code in
        <foreach collection="codeList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>