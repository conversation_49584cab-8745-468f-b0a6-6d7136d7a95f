<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.trading.ro.site.mapper.SiteDailyBiRoMapper">

    <resultMap id="RESULT_SITE_DAILY_BI_PO" type="com.cdz360.biz.model.trading.site.po.SiteDailyBiPo">

        <result column="siteId" jdbcType="VARCHAR" property="siteId"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="kwMax" jdbcType="BIGINT" property="kwMax"/>
        <result column="kwLine" property="kwLine"
                typeHandler="com.cdz360.biz.ds.trading.MybatisJsonTypeHandler"/>
    </resultMap>


    <select id="getKwLineList" resultMap="RESULT_SITE_DAILY_BI_PO">
        select bi.*
        from t_site_daily_bi bi
        left join t_site site on bi.siteId = site.id
        left join t_r_commercial comm on site.operate_id = comm.id
        where comm.idChain like CONCAT(#{commIdChain}, '%')
        and `date` = #{date,jdbcType=DATE}
        order by bi.siteId
        limit #{start}, #{size}
    </select>

    <select id="getSiteDailyBi" resultMap="RESULT_SITE_DAILY_BI_PO">
        select bi.*
        from t_site_daily_bi bi
        where bi.siteId = #{siteId}
        and bi.`date` = #{date,jdbcType=DATE}
    </select>

    <select id="getSiteDailyBiList" resultMap="RESULT_SITE_DAILY_BI_PO">
        select bi.*
        from t_site_daily_bi bi
        where
        bi.siteId in
        <foreach collection="siteIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and bi.`date` = #{date,jdbcType=DATE}
    </select>

    <select id="getSiteDailyBiRangeDate" resultMap="RESULT_SITE_DAILY_BI_PO">
        select bi.*
        from t_site_daily_bi bi
        where bi.siteId = #{siteId}
        <![CDATA[ and bi.`date` >= #{fromDate, jdbcType=DATE} ]]>
        <![CDATA[ and bi.`date` < #{toDate, jdbcType=DATE} ]]>
    </select>
</mapper>