<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.trading.rw.sync.mapper.CustomerAttractSyncRwMapper">

  <select id="countByCustomerId" resultType="java.lang.Long">
    select
      count(*)
    from
      t_r_customer_attract
    where
      customerId = #{customerId}
  </select>

  <insert id="insertIgnore" parameterType="com.cdz360.biz.model.common.po.CustomerAttractPo">
    INSERT ignore INTO t_r_customer_attract
      (customerId, `type`, sysUid, sysUserName, createTime)
    VALUES
      (#{customerId}, #{type.code}, #{sysUid}, #{sysUserName}, now())
  </insert>

  <insert id="batchInsertIgnore">
    INSERT ignore INTO t_r_customer_attract
      (customerId, `type`, sysUid, sysUserName, createTime)
    VALUES
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.customerId}, #{item.type.code}, #{item.sysUid}, #{item.sysUserName}, now())
    </foreach>
  </insert>

</mapper>
