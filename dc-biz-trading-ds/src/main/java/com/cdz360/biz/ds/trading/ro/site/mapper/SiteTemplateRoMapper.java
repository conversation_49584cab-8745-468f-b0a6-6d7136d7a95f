package com.cdz360.biz.ds.trading.ro.site.mapper;

import com.cdz360.biz.model.trading.site.po.SiteTemplatePo;
import com.cdz360.biz.model.trading.site.vo.SiteTemplateVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SiteTemplateRoMapper {
    List<SiteTemplatePo> getSiteTemplateBySiteId(@Param("siteId") String siteId);

    List<SiteTemplateVo> getSiteTemplateInfoBySiteId(@Param("siteId") String siteId);

    List<SiteTemplateVo> getSiteTemplateInfoBySiteIdList(@Param("siteIdList") List<String> siteIdList);
}
