package com.cdz360.biz.ds.trading.rw.invoice.mapper;

import com.cdz360.biz.model.trading.invoice.po.InvoicedRecordContentPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.math.BigDecimal;
import java.lang.Double;
import java.util.Date;

@Mapper
public interface InvoicedRecordContentRwMapper {
	InvoicedRecordContentPo getById(@Param("id") Long id, @Param("lock") boolean lock);

	int insertInvoicedRecordContent(InvoicedRecordContentPo invoicedRecordContentPo);

	int updateInvoicedRecordContent(InvoicedRecordContentPo invoicedRecordContentPo);


	int disableContent(@Param("invoiceId") Long invoiceId);

	int batchInsert(@Param(value = "poList") List<InvoicedRecordContentPo> list);
}
