package com.cdz360.biz.ds.trading.rw.ads.ds;

import com.cdz360.biz.ds.trading.rw.ads.mapper.AdsSiteGroupRwMapper;
import com.cdz360.biz.model.ads.po.AdsSiteGroupPo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AdsSiteGroupRwDs {

    @Autowired
    private AdsSiteGroupRwMapper adsSiteGroupRwMapper;

    public AdsSiteGroupPo getByAdsId(Long id, boolean lock) {
        return this.adsSiteGroupRwMapper.getById(id, lock);
    }

    public int insertAdsSiteGroup(AdsSiteGroupPo adsSiteGroupPo) {
        return this.adsSiteGroupRwMapper.insertAdsSiteGroup(adsSiteGroupPo);
    }

    public int batchInsertAdsSiteGroup(List<AdsSiteGroupPo> adsSiteGroupPoList) {
        return this.adsSiteGroupRwMapper.batchInsertAdsSiteGroup(adsSiteGroupPoList);
    }

    public int updateAdsSiteGroup(AdsSiteGroupPo adsSiteGroupPo) {
        return this.adsSiteGroupRwMapper.updateAdsSiteGroup(adsSiteGroupPo);
    }

    public int updateAdsSiteGroupEnableByAdsId(Long adsId, Boolean enable) {
        return this.adsSiteGroupRwMapper.updateAdsSiteGroupEnableByAdsId(adsId, enable);
    }
}

