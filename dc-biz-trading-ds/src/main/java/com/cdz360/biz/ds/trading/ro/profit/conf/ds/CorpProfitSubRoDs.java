package com.cdz360.biz.ds.trading.ro.profit.conf.ds;

import com.cdz360.biz.ds.trading.ro.profit.conf.mapper.CorpProfitSubRoMapper;
import com.cdz360.biz.model.trading.profit.conf.po.CorpProfitSubPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@Service
public class CorpProfitSubRoDs {

	@Autowired
	private CorpProfitSubRoMapper corpProfitSubRoMapper;

	public CorpProfitSubPo getById(Long id) {
		return this.corpProfitSubRoMapper.getById(id);
	}
}
