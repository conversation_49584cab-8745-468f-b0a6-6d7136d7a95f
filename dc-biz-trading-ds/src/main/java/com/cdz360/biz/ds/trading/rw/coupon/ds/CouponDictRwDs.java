package com.cdz360.biz.ds.trading.rw.coupon.ds;

import com.cdz360.biz.model.trading.coupon.po.CouponDictPo;
import com.cdz360.biz.ds.trading.rw.coupon.mapper.CouponDictRwMapper;
import com.cdz360.biz.model.trading.coupon.type.CouponDictStatusType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class CouponDictRwDs {

	@Autowired
	private CouponDictRwMapper couponDictRwMapper;

	public CouponDictPo getById(Long id, boolean lock) {
		return this.couponDictRwMapper.getById(id, lock);
	}

	public boolean insertCouponDict(CouponDictPo CouponDictPo) {
		return this.couponDictRwMapper.insertCouponDict(CouponDictPo) > 0;
	}

	public boolean updateCouponDict(CouponDictPo CouponDictPo) {
		return this.couponDictRwMapper.updateCouponDict(CouponDictPo) > 0;
	}

	public boolean disableDict(Long id) {
		CouponDictPo couponDictPo = new CouponDictPo();
		couponDictPo.setId(id).setStatus(CouponDictStatusType.DISABLE);
		return couponDictRwMapper.updateCouponDict(couponDictPo) == 1;
	}

	public boolean disableDictById(List<Long> couponDictIdList) {
		return couponDictRwMapper.disableDictById(couponDictIdList)>0;
	}


    public int refreshStatus2Expire() {
		return couponDictRwMapper.refreshStatus2Expire();
    }
}
