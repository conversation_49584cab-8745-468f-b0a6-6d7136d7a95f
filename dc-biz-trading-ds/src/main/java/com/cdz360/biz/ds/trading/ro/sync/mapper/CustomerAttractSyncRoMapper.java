package com.cdz360.biz.ds.trading.ro.sync.mapper;

import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.biz.model.common.constant.CustomerAttractType;
import com.cdz360.biz.model.trading.sync.vo.CustomerAttractBiDto;
import com.cdz360.biz.model.trading.sync.vo.CustomerAttractBiVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@Mapper
public interface CustomerAttractSyncRoMapper {

    List<CustomerAttractBiDto> getNumDto(@NonNull @Param("sysUid") long sysUid,
        @Nullable @Param("createTimeFilter") TimeFilter createTimeFilter);

    CustomerAttractBiDto getKwhDto(@NonNull @Param("sysUid") long sysUid,
        @NonNull @Param("type") CustomerAttractType type,
        @Nullable @Param("createTimeFilter") TimeFilter createTimeFilter);

    List<CustomerAttractBiVo> getUserAttractBiList(@NonNull @Param("type") CustomerAttractType type,
        @NonNull @Param("sysUid") long sysUid,
        @Nullable @Param("strKeyword") String strKeyword,
        @Nullable @Param("start") Long start,
        @Nullable @Param("size") Integer size);

    List<CustomerAttractBiVo> getCorpAttractBiList(@NonNull @Param("type") CustomerAttractType type,
        @NonNull @Param("sysUid") long sysUid,
        @Nullable @Param("strKeyword") String strKeyword,
        @Nullable @Param("start") Long start,
        @Nullable @Param("size") Integer size);

}
