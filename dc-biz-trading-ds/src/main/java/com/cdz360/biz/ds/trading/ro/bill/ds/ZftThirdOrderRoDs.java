package com.cdz360.biz.ds.trading.ro.bill.ds;


import com.cdz360.biz.ds.trading.ro.bill.mapper.ZftThirdOrderRoMapper;
import com.cdz360.biz.model.trading.bill.dto.ZftThirdOrderDto;
import com.cdz360.biz.model.trading.bill.param.ListZftThirdOrderParam;
import com.cdz360.biz.model.trading.bill.vo.TradeOrderBi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j

@Service

public class ZftThirdOrderRoDs {



	@Autowired

	private ZftThirdOrderRoMapper zftThirdOrderRoMapper;


    public List<ZftThirdOrderDto> findAllZftThirdOrder(ListZftThirdOrderParam param) {
        return this.zftThirdOrderRoMapper.findAllZftThirdOrder(param);
    }

    public List<TradeOrderBi> tradeOrderBi(ListZftThirdOrderParam param) {
        return this.zftThirdOrderRoMapper.tradeOrderBi(param);
    }

    public Long count(ListZftThirdOrderParam param) {
        return this.zftThirdOrderRoMapper.count(param);
    }
}

