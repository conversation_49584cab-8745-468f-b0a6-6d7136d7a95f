package com.cdz360.biz.ds.trading.rw.soc.mapper;

import com.cdz360.biz.model.trading.soc.po.SiteSocCfgPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SiteSocCfgRwMapper {
	SiteSocCfgPo getById(@Param("id") Long id, @Param("lock") boolean lock);

	int insertSiteSocCfg(SiteSocCfgPo siteSocCfgPo);

	int updateSiteSocCfg(SiteSocCfgPo siteSocCfgPo);

	int insertOrUpdate(SiteSocCfgPo siteSocCfgPo);


}
