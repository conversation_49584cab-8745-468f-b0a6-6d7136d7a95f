<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.trading.ro.soc.mapper.SocStrategyRoMapper">

	<resultMap id="RESULT_SOCSTRATEGY_PO" type="com.cdz360.biz.model.trading.soc.po.SocStrategyPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="siteId" jdbcType="VARCHAR" property="siteId" />
		<result column="corpId" jdbcType="BIGINT" property="corpId" />
		<result column="name" jdbcType="VARCHAR" property="name" />
		<result column="allow" jdbcType="BOOLEAN" property="allow" />
		<result column="soc" jdbcType="INTEGER" property="soc" />
		<result column="createTime" jdbcType="DATE" property="createTime" />
		<result column="updateTime" jdbcType="DATE" property="updateTime" />
	</resultMap>

	<resultMap id="RESULT_SOCSTRATEGY_VO"
			   extends="RESULT_SOCSTRATEGY_PO"
			   type="com.cdz360.biz.model.trading.soc.vo.SocStrategyVo">
		<collection property="userIdList" ofType="java.lang.Long" javaType="list">
			<result column="uid" property="value"/>
		</collection>

	</resultMap>

	<select id="getById"
			resultMap="RESULT_SOCSTRATEGY_PO">	
		select * from t_soc_strategy where id = #{id}
	</select>

	<select id="getStrategyBySiteId"
			resultMap="RESULT_SOCSTRATEGY_VO">
		select
			ss.id,
			ss.siteId,
			ss.allow,
			ss.soc,
			ss.createTime,
			ss.updateTime,
			uss.userId AS uid
		from t_soc_strategy ss
		inner join t_user_soc_strategy uss on
			ss.id=uss.strategyId
		<where>
			ss.siteId = #{siteId}
			<if test="userId != null">
				and uss.userId = #{userId}
			</if>
		</where>
	</select>

	<select id="queryStrategy"
			resultMap="RESULT_SOCSTRATEGY_PO">
		select
			ss.id,
			ss.siteId,
			ss.corpId,
			ss.name,
			ss.allow,
			ss.soc,
			ss.createTime,
			ss.updateTime
		from t_soc_strategy ss
		<where>
			1 = 1
			and ss.corpId = #{corpId}
			<if test="strategyId != null">
				and ss.id = #{strategyId}
			</if>
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( strategyName )">
				and name like concat('%', #{strategyName}, '%')
			</if>
		</where>
        order by updateTime desc
	</select>

</mapper>
