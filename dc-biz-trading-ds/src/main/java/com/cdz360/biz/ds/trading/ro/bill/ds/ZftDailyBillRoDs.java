package com.cdz360.biz.ds.trading.ro.bill.ds;


import com.cdz360.biz.ds.trading.ro.bill.mapper.ZftDailyBillRoMapper;
import com.cdz360.biz.model.trading.bill.param.ListZftDailyBillParam;
import com.cdz360.biz.model.trading.bill.po.ZftDailyBillPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j

@Service

public class ZftDailyBillRoDs {



	@Autowired

	private ZftDailyBillRoMapper zftDailyBillRoMapper;



	public ZftDailyBillPo getById(Long id) {

		return this.zftDailyBillRoMapper.getById(id);

	}

	public List<ZftDailyBillPo> findAllZftDailyBill(ListZftDailyBillParam param) {
		return zftDailyBillRoMapper.findAllZftDailyBill(param);
	}

	public Long count(ListZftDailyBillParam param) {
		return zftDailyBillRoMapper.count(param);
	}
}

