<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.trading.rw.prerun.mapper.PrerunRwMapper">

	<resultMap id="RESULT_TEST_RUN_PO" type="com.cdz360.biz.model.trading.prerun.po.PrerunPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="prerunNo" jdbcType="VARCHAR" property="prerunNo" />
		<result column="category" jdbcType="INTEGER" property="category" />
		<result column="siteId" jdbcType="VARCHAR" property="siteId" />
		<result column="status" jdbcType="JAVA_OBJECT" property="status" />
		<result column="enable" jdbcType="BOOLEAN" property="enable" />
		<result column="prerunnerUid" jdbcType="BIGINT" property="prerunnerUid" />
		<result column="prerunnerName" jdbcType="VARCHAR" property="prerunnerName" />
		<result column="prerunTime" jdbcType="DATE" property="prerunTime" />
		<result column="deliverUid" jdbcType="BIGINT" property="deliverUid" />
		<result column="deliverName" jdbcType="VARCHAR" property="deliverName" />
		<result column="deliverPhone" jdbcType="VARCHAR" property="deliverPhone" />
		<result column="infoSupplyUid" jdbcType="BIGINT" property="infoSupplyUid" />
		<result column="infoSupplyName" jdbcType="VARCHAR" property="infoSupplyName" />
		<result column="infoSupplyPhone" jdbcType="VARCHAR" property="infoSupplyPhone" />
		<result column="clientName" jdbcType="VARCHAR" property="clientName" />
		<result column="clientPhone" jdbcType="VARCHAR" property="clientPhone" />
		<result column="masterName" jdbcType="VARCHAR" property="masterName" />
		<result column="masterPhone" jdbcType="VARCHAR" property="masterPhone" />
		<result column="salesUid" jdbcType="BIGINT" property="salesUid" />
		<result column="salesName" jdbcType="VARCHAR" property="salesName" />
		<result column="salesPhone" jdbcType="VARCHAR" property="salesPhone" />
		<result column="maintainUid" jdbcType="BIGINT" property="maintainUid" />
		<result column="maintainName" jdbcType="VARCHAR" property="maintainName" />
		<result column="maintainPhone" jdbcType="VARCHAR" property="maintainPhone" />
		<result column="platformConn" jdbcType="BOOLEAN" property="platformConn" />
		<result column="chargeTypes" property="chargeTypes" typeHandler="com.cdz360.biz.ds.trading.ListChargeTypeHandler" />
		<result column="clientRank" jdbcType="INTEGER" property="clientRank" />
		<result column="clientComment" jdbcType="VARCHAR" property="clientComment" />
		<result column="clientSignPic" jdbcType="VARCHAR" property="clientSignPic" />
		<result column="scenePic" property="scenePic" typeHandler="com.cdz360.biz.ds.trading.ListTypeHandler" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_TEST_RUN_PO">	
		select * from t_prerun where id = #{id}
		<if test="lock == true">
			for update
		</if>
	</select>

	<insert id="insertPrerun" useGeneratedKeys="true" keyProperty="id"
		keyColumn="id" parameterType="com.cdz360.biz.model.trading.prerun.po.PrerunPo">
		insert into t_prerun (`prerunNo`,
			`category`,
			`siteId`,
			`status`,
			`enable`,
			`prerunnerUid`,
			`prerunnerName`,
			`prerunTime`,
			`deliverUid`,
			`deliverName`,
			`deliverPhone`,
			`infoSupplyUid`,
			`infoSupplyName`,
			`infoSupplyPhone`,
			`clientName`,
			`clientPhone`,
			`masterName`,
			`masterPhone`,
			`salesUid`,
			`salesName`,
			`salesPhone`,
			`maintainUid`,
			`maintainName`,
			`maintainPhone`,
			`platformConn`,
			`chargeTypes`,
			`clientRank`,
			`clientComment`,
			`clientSignPic`,
			`scenePic`,
			`createTime`,
			`updateTime`)
		values (#{prerunNo},
			#{category},
			#{siteId},
			#{status},
			#{enable},
			#{prerunnerUid},
			#{prerunnerName},
			#{prerunTime},
			#{deliverUid},
			#{deliverName},
			#{deliverPhone},
			#{infoSupplyUid},
			#{infoSupplyName},
			#{infoSupplyPhone},
			#{clientName},
			#{clientPhone},
			#{masterName},
			#{masterPhone},
			#{salesUid},
			#{salesName},
			#{salesPhone},
			#{maintainUid},
			#{maintainName},
			#{maintainPhone},
			#{platformConn},
			#{chargeTypes, typeHandler=com.cdz360.biz.ds.trading.ListChargeTypeHandler},
			#{clientRank},
			#{clientComment},
			#{clientSignPic},
			#{scenePic, typeHandler=com.cdz360.biz.ds.trading.ListTypeHandler},
			now(),
			now())
	</insert>

	<update id="updatePrerun" parameterType="com.cdz360.biz.model.trading.prerun.po.PrerunPo">
		update t_prerun set
		<if test="prerunNo != null">
			prerunNo = #{prerunNo},
		</if>
		<if test="category != null">
			category = #{category},
		</if>
		<if test="siteId != null">
			siteId = #{siteId},
		</if>
		<if test="status != null">
			status = #{status},
		</if>
		<if test="enable != null">
			enable = #{enable},
		</if>
		<if test="prerunnerUid != null">
			prerunnerUid = #{prerunnerUid},
		</if>
		<if test="prerunnerName != null">
			prerunnerName = #{prerunnerName},
		</if>
		<if test="prerunTime != null">
			prerunTime = #{prerunTime},
		</if>
		<if test="deliverUid != null">
			deliverUid = #{deliverUid},
		</if>
		<if test="deliverName != null">
			deliverName = #{deliverName},
		</if>
		<if test="deliverPhone != null">
			deliverPhone = #{deliverPhone},
		</if>
		<if test="infoSupplyUid != null">
			infoSupplyUid = #{infoSupplyUid},
		</if>
		<if test="infoSupplyName != null">
			infoSupplyName = #{infoSupplyName},
		</if>
		<if test="infoSupplyPhone != null">
			infoSupplyPhone = #{infoSupplyPhone},
		</if>
		<if test="clientName != null">
			clientName = #{clientName},
		</if>
		<if test="clientPhone != null">
			clientPhone = #{clientPhone},
		</if>
		<if test="masterName != null">
			masterName = #{masterName},
		</if>
		<if test="masterPhone != null">
			masterPhone = #{masterPhone},
		</if>
		<if test="salesUid != null">
			salesUid = #{salesUid},
		</if>
		<if test="salesName != null">
			salesName = #{salesName},
		</if>
		<if test="salesPhone != null">
			salesPhone = #{salesPhone},
		</if>
		<if test="maintainUid != null">
			maintainUid = #{maintainUid},
		</if>
		<if test="maintainName != null">
			maintainName = #{maintainName},
		</if>
		<if test="maintainPhone != null">
			maintainPhone = #{maintainPhone},
		</if>
		<if test="platformConn != null">
			platformConn = #{platformConn},
		</if>
		<if test="chargeTypes != null">
			chargeTypes = #{chargeTypes, typeHandler=com.cdz360.biz.ds.trading.ListChargeTypeHandler},
		</if>
		<if test="clientRank != null">
			clientRank = #{clientRank},
		</if>
		<if test="clientComment != null">
			clientComment = #{clientComment},
		</if>
		<if test="clientSignPic != null">
			clientSignPic = #{clientSignPic},
		</if>
		<if test="scenePic != null">
			scenePic = #{scenePic, typeHandler=com.cdz360.biz.ds.trading.ListTypeHandler},
		</if>
		updateTime = now()
		where id = #{id}
	</update>

</mapper>
