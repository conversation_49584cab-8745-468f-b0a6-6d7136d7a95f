package com.cdz360.biz.ds.trading.rw.site.mapper;

import com.cdz360.biz.model.trading.site.po.SiteInspectionRecordPo;
import com.cdz360.biz.model.trading.site.type.SiteInspectionStatus;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SiteInspectionRecordRwMapper {

    int changeStatus(@Param("idList") List<Long> idList,
        @Param("status") SiteInspectionStatus status,
        @Param("qcRemark") String qcRemark,
        @Param("qcUid") Long qcUid,
        @Param("qcTime") Date qcTime,
        @Param("score") Long score);

    int create(SiteInspectionRecordPo req);

    int editById(SiteInspectionRecordPo req);

}
