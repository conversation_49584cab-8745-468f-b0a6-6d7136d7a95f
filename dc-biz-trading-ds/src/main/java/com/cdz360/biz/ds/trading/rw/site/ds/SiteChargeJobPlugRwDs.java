package com.cdz360.biz.ds.trading.rw.site.ds;

import com.cdz360.biz.ds.trading.rw.site.mapper.SiteChargeJobPlugRwMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SiteChargeJobPlugRwDs {

    @Autowired
    private SiteChargeJobPlugRwMapper siteChargeJobPlugRwMapper;

    public int deleteByEvseNo(String evseNo) {
        return siteChargeJobPlugRwMapper.deleteByEvseNo(evseNo);
    }

    public int deleteByJobIdAndPlugNo(long jobId, String evseNo, int plugIdx) {
        return siteChargeJobPlugRwMapper.deleteByJobIdAndEvsePlug(jobId, evseNo, plugIdx);
    }

    public int bindByJobIdAndPlugNo(long jobId, String evseNo, int plugIdx) {
        return siteChargeJobPlugRwMapper.bindByJobIdAndPlugNo(jobId, evseNo, plugIdx);
    }

}
