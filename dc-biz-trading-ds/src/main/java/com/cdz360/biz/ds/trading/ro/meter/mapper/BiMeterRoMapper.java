package com.cdz360.biz.ds.trading.ro.meter.mapper;

import com.cdz360.biz.model.iot.param.SiteBiParam;
import com.cdz360.biz.model.trading.meter.po.BiMeterPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BiMeterRoMapper {

	BiMeterPo getById(@Param("id") Long id);

	List<BiMeterPo> meterReadingBi(SiteBiParam siteBiTopParam);
}
