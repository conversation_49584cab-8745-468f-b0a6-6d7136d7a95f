<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.trading.ro.iot.mapper.BsChargerRoMapper">

    <resultMap id="BASE" type="com.cdz360.biz.model.trading.iot.po.BsChargerPo">
        <result column="bc_id" jdbcType="BIGINT" property="bcId" />
        <result column="evseNo" jdbcType="VARCHAR" property="evseNo"/>
        <result column="plugNo" jdbcType="VARCHAR" property="plugNo"/>
        <result column="connector_id" jdbcType="INTEGER" property="connectorId"/>
        <result column="business_id" jdbcType="VARCHAR" property="businessId"/>
        <result column="station_code" jdbcType="VARCHAR" property="stationCode"/>
        <result column="business_id" jdbcType="INTEGER" property="currentType"/>
        <result column="charger_name" jdbcType="VARCHAR" property="chargerName"/>
        <result column="last_modify_time" jdbcType="TIMESTAMP" property="lastModifyTime"/>
    </resultMap>

    <select id="getHlhtChargerInfo" resultMap="BASE">
        select
            c.*
        from
            t_site s
        left join bs_charger c on
            s.id = c.station_code
        where
            s.`bizType` = 3
            and s.`partnerCode` is not null
            and s.open_site_id is not null
            and s.id = #{siteId}
    </select>
    <select id="getPlugList" resultType="com.cdz360.base.model.iot.vo.PlugVo">
        select plugNo from bs_charger where station_code=#{siteId}
    </select>
    <select id="getPlugListByEvseNo" resultMap="BASE">
        select * from bs_charger where evseNo=#{evseNo}
    </select>
</mapper>