<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.biz.ds.trading.rw.bill.mapper.ZftDailyBillRwMapper">



	<resultMap id="RESULT_ZFT_DAILY_BILL_PO" type="com.cdz360.biz.model.trading.bill.po.ZftDailyBillPo">

		<id column="id" jdbcType="BIGINT" property="id" />

		<result column="name" jdbcType="VARCHAR" property="name" />

		<result column="status" jdbcType="VARCHAR" property="status" />
		<result column="mchId" jdbcType="VARCHAR" property="mchId" />

		<result column="zftId" jdbcType="BIGINT" property="zftId" />

		<result column="zftName" jdbcType="VARCHAR" property="zftName" />

		<result column="zftCommId" jdbcType="BIGINT" property="zftCommId" />

		<result column="billDate" jdbcType="TIMESTAMP" property="billDate" />

		<result column="channel" typeHandler="com.cdz360.ds.type.EnumTypeHandler" property="channel" />

		<result column="downloadUrl" jdbcType="VARCHAR" property="downloadUrl" />

		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />

		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />

	</resultMap>



	<select id="getById"

			resultMap="RESULT_ZFT_DAILY_BILL_PO">
		select * from t_zft_daily_bill where id = #{id}

		<if test="lock == true">

			for update

		</if>

	</select>



	<insert id="insertZftDailyBill" useGeneratedKeys="true" keyProperty="id"

		keyColumn="id" parameterType="com.cdz360.biz.model.trading.bill.po.ZftDailyBillPo">

		insert into t_zft_daily_bill (`name`,

			`status`,
    <if test="mchId != null">
      `mchId`,
    </if>
    <if test="zftId != null">
			`zftId`,
    </if>
    <if test="zftName != null">
			`zftName`,
    </if>
    <if test="zftCommId != null">
			`zftCommId`,
    </if>
			`billDate`,

			`channel`,

			`downloadUrl`,

			`createTime`,

			`updateTime`)

		values (#{name},

			#{status},
    <if test="mchId != null">
			#{mchId},
    </if>

    <if test="zftId != null">
			#{zftId},
    </if>

    <if test="zftName != null">
			#{zftName},
    </if>

    <if test="zftCommId != null">
			#{zftCommId},
    </if>

			#{billDate},

			#{channel.code},

			#{downloadUrl},

			now(),

			now())

	</insert>



	<update id="updateZftDailyBill" parameterType="com.cdz360.biz.model.trading.bill.po.ZftDailyBillPo">

		update t_zft_daily_bill set

		<if test="name != null">

			name = #{name},

		</if>

		<if test="status != null">

			status = #{status},

		</if>

		<if test="mchId != null">

			mchId = #{mchId},

		</if>

		<if test="zftId != null">

			zftId = #{zftId},

		</if>

		<if test="zftName != null">

			zftName = #{zftName},

		</if>

		<if test="zftCommId != null">

			zftCommId = #{zftCommId},

		</if>

		<if test="billDate != null">

			billDate = #{billDate},

		</if>

		<if test="channel != null">

			channel = #{channel.code},

		</if>

		<if test="downloadUrl != null">

			downloadUrl = #{downloadUrl},

		</if>

		updateTime = now()

		where id = #{id}

	</update>



</mapper>

