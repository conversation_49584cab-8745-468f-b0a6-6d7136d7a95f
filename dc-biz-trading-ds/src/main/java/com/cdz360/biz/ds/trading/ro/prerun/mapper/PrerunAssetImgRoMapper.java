package com.cdz360.biz.ds.trading.ro.prerun.mapper;

import com.cdz360.biz.model.trading.prerun.po.PrerunAssetImgPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.math.BigDecimal;
import java.lang.Double;
import java.util.Date;

@Mapper
public interface PrerunAssetImgRoMapper {

	PrerunAssetImgPo getById(@Param("id") Long id);

	List<PrerunAssetImgPo> getByPrerunId(@Param("prerunId") Long prerunId);
}
