package com.cdz360.biz.ds.trading.rw.site.mapper;

import com.cdz360.biz.model.cus.vo.SiteCommentTotal;
import com.cdz360.biz.model.trading.site.po.SitePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SiteRwMapper {

    void addSite(SitePo site);

    void addSiteList(@Param("sitePoList") List<SitePo> sitePoList);

    /**
     * 更新场站信息，仅更新有变更部分的内容
     *
     * @param site 场站信息
     * @return
     */
    int updateSite(SitePo site);

    int updateStatus(@Param("status") Integer status,
                     @Param("siteIdList") List<String> siteIdList);

    /**
     * 更新t_site表的功率统计
     *
     * @param siteId
     * @return
     */
    int updateSitePower(@Param("siteId") String siteId);

//    /**
//     * 修改场站的商户名称(用于数据同步)
//     *
//     * @param commId   商户ID（operate_id)
//     * @param commName 要变更的商户名称
//     * @return
//     */
//    int updateSiteCommName(@Param("commId") Long commId, @Param("commName") String commName);

    Long moveCorpSiteConfStart(Long corpId, String idChain);

    int updateSiteCommentLevelAvg(
            @Param(value = "updateList") List<SiteCommentTotal> updateList);

    int updateNoCardSetting(@Param(value = "corpId") Long corpId);
    int updateCommNoCardList(@Param(value = "commId") Long commId,
                            @Param(value = "userId") Long userId);

    SitePo getSiteById(@Param(value = "siteId") String siteId, @Param(value = "lock") boolean lock);

    List<String> getSiteIdsByMobileTempSalId(@Param("mobileTempSalId") Long mobileTempSalId);
}
