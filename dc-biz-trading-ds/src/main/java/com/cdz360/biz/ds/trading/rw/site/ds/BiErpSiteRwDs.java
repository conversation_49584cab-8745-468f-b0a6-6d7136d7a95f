package com.cdz360.biz.ds.trading.rw.site.ds;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.biz.ds.trading.rw.site.mapper.BiErpSiteRwMapper;
import com.cdz360.biz.model.trading.bi.param.ListBiErpSiteParam;
import com.cdz360.biz.model.trading.bi.po.BiErpSitePo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BiErpSiteRwDs {
    @Autowired
    private BiErpSiteRwMapper biErpSiteRwMapper;

    /**
     * 新增记录
     *
     * @param po
     * @return
     */
    public int add(BiErpSitePo po) {
        return this.biErpSiteRwMapper.insert(po);
    }

    /**
     * 更新数据记录
     *
     * @param po
     * @return
     */
    public int updateById(BiErpSitePo po) {
        if (null == po.getId()) {
            throw new DcArgumentException("请提供需要更新的记录的ID值");
        }

        return this.biErpSiteRwMapper.updateById(po);
    }

    /**
     * 获取纪录列表
     *
     * @param param
     * @return
     */
    public List<BiErpSitePo> listErpSite(ListBiErpSiteParam param) {
        return this.biErpSiteRwMapper.listErpSite(param);
    }
}
