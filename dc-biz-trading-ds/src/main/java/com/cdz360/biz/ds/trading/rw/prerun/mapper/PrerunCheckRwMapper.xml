<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.trading.rw.prerun.mapper.PrerunCheckRwMapper">

	<resultMap id="RESULT_TEST_RUN_CHECK_PO" type="com.cdz360.biz.model.trading.prerun.po.PrerunCheckPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="prerunId" jdbcType="BIGINT" property="prerunId" />
		<result column="opUid" jdbcType="BIGINT" property="opUid" />
		<result column="opName" jdbcType="VARCHAR" property="opName" />
		<result column="fromStatus" jdbcType="JAVA_OBJECT" property="fromStatus" />
		<result column="toStatus" jdbcType="JAVA_OBJECT" property="toStatus" />
		<result column="comment" jdbcType="VARCHAR" property="comment" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_TEST_RUN_CHECK_PO">	
		select * from t_prerun_check where id = #{id}
		<if test="lock == true">
			for update
		</if>
	</select>

	<insert id="insertPrerunCheck" useGeneratedKeys="true" keyProperty="id"
		keyColumn="id" parameterType="com.cdz360.biz.model.trading.prerun.po.PrerunCheckPo">
		insert into t_prerun_check (`prerunId`,
			`opUid`,
			`opName`,
			`fromStatus`,
			`toStatus`,
			`comment`,
			`createTime`)
		values (#{prerunId},
			#{opUid},
			#{opName},
			#{fromStatus},
			#{toStatus},
			#{comment},
			now())
	</insert>

	<update id="updatePrerunCheck" parameterType="com.cdz360.biz.model.trading.prerun.po.PrerunCheckPo">
		update t_prerun_check set
		<if test="prerunId != null">
			prerunId = #{prerunId},
		</if>
		<if test="opUid != null">
			opUid = #{opUid},
		</if>
		<if test="opName != null">
			opName = #{opName},
		</if>
		<if test="fromStatus != null">
			fromStatus = #{fromStatus},
		</if>
		<if test="toStatus != null">
			toStatus = #{toStatus},
		</if>
		<if test="comment != null">
			comment = #{comment},
		</if>
		where id = #{id}
	</update>

</mapper>
