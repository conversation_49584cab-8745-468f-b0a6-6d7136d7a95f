<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.trading.ro.site.mapper.SiteInspectionCfgRoMapper">

    <select id="getCfg" resultType="com.cdz360.biz.model.trading.site.po.SiteInspectionCfgPo">
        select
            *
        from
            t_site_inspection_cfg
        where
            siteId = #{siteId}
        limit 1
    </select>

    <select id="getNeedInspectionSiteCfg" resultType="com.cdz360.biz.model.trading.site.po.SiteInspectionCfgPo">
        select
            cfg.*
        from
            t_site_inspection_cfg cfg
        inner join t_site s on
            cfg.siteId = s.id
        inner join t_r_commercial comm on
            s.operate_id = comm.id
        where
            comm.idChain like CONCAT(#{commIdChain},'%')
            and cfg.`type` = "FIXED"
        order by
            cfg.createTime desc
        limit #{start}, #{size}
    </select>

    <select id="getNeedInspectionSiteCfgCount" resultType="java.lang.Long">
        select
            count(cfg.id)
        from
            t_site_inspection_cfg cfg
        inner join t_site s on
            cfg.siteId = s.id
        inner join t_r_commercial comm on
            s.operate_id = comm.id
        where
            comm.idChain like CONCAT(#{commIdChain},'%')
            and cfg.`type` = "FIXED"
    </select>

    <select id="getNeedRemindSite" resultType="com.cdz360.biz.model.trading.site.po.SiteInspectionCfgPo">
        select
            *
        from
            t_site_inspection_cfg cfg
        where
            cfg.`type` = "FIXED"
            and `cycle` is not null
    </select>

</mapper>