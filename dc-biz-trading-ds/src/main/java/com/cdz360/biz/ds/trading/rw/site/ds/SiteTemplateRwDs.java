package com.cdz360.biz.ds.trading.rw.site.ds;

import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.biz.ds.trading.rw.site.mapper.SiteTemplateRwMapper;
import com.cdz360.biz.model.trading.site.po.SiteTemplatePo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SiteTemplateRwDs {
    @Autowired
    private SiteTemplateRwMapper siteTemplateRwMapper;

    public void insertTemplate(SiteTemplatePo  siteTemplatePo) {
        siteTemplateRwMapper.insertTemplate(siteTemplatePo);
    }
    public void deleteBySiteId(String  siteId) {
        siteTemplateRwMapper.deleteBySiteId(siteId);
    }

    public void deleteBySiteIdAndType(String  siteId, SupplyType templateType) {
        siteTemplateRwMapper.deleteBySiteIdAndType(siteId, templateType);
    }
}
