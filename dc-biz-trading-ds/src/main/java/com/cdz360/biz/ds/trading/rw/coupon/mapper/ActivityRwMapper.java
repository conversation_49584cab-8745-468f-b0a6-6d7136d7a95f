package com.cdz360.biz.ds.trading.rw.coupon.mapper;

import com.cdz360.biz.model.trading.coupon.po.ActivityPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ActivityRwMapper {
	ActivityPo getById(@Param("id") Long id, @Param("lock") boolean lock);

	List<ActivityPo> getByIdList(@Param("idList") List<Long> idList, @Param("lock") boolean lock);

	int insertActivity(ActivityPo activityPo);

	int updateActivity(ActivityPo activityPo);

	int updateActivityById(@Param("activityIdList") List<Long> activityIdList);

    int refreshStatus2Processing();

	int refreshStatus2Finished();
}
