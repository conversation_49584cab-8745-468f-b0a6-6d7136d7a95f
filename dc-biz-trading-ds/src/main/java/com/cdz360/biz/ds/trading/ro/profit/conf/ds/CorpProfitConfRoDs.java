package com.cdz360.biz.ds.trading.ro.profit.conf.ds;

import com.cdz360.biz.ds.trading.ro.profit.conf.mapper.CorpProfitConfRoMapper;
import com.cdz360.biz.model.trading.profit.conf.po.CorpProfitConfPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@Deprecated
public class CorpProfitConfRoDs {

	@Autowired
	private CorpProfitConfRoMapper corpProfitConfRoMapper;

	public CorpProfitConfPo getById(Long id) {
		return this.corpProfitConfRoMapper.getById(id);
	}

	public List<CorpProfitConfPo> getListById(Long corpId) {
		return this.corpProfitConfRoMapper.getListById(corpId);
	}

	public List<CorpProfitConfPo> getAllGradientList() {
		return this.corpProfitConfRoMapper.getAllGradientList();
	}
}
