<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.trading.ro.site.mapper.InvoiceRecordRoMapper">

	<resultMap id="RESULT_INVOICED_RECORD_PO" type="com.cdz360.biz.model.trading.site.po.InvoicedRecordPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="invoiced_type" jdbcType="VARCHAR" property="invoicedType" />
		<result column="invoice_type" jdbcType="VARCHAR" property="invoiceType" />
		<result column="user_id" jdbcType="BIGINT" property="userId" />
		<result column="applyNo" jdbcType="VARCHAR" property="applyNo" />
		<result column="invoiced_status" jdbcType="VARCHAR" property="invoicedStatus" />
		<result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode" />
		<result column="invoice_number" jdbcType="VARCHAR" property="invoiceNumber" />
		<result column="invoice_date" jdbcType="TIMESTAMP" property="invoiceDate" />
		<result column="issuedTime" jdbcType="TIMESTAMP" property="issuedTime" />
		<result column="invoice_amount" jdbcType="INTEGER" property="invoiceAmount" />
		<result column="elec_actual_fee" jdbcType="DECIMAL" property="elecActualFee" />
		<result column="serv_actual_fee" jdbcType="DECIMAL" property="servActualFee" />
		<result column="park_actual_fee" jdbcType="DECIMAL" property="parkActualFee" />
		<result column="invoice_name" jdbcType="VARCHAR" property="invoiceName" />
		<result column="invoice_tin" jdbcType="VARCHAR" property="invoiceTin" />
		<result column="invoice_address" jdbcType="VARCHAR" property="invoiceAddress" />
		<result column="invoice_tel" jdbcType="VARCHAR" property="invoiceTel" />
		<result column="invoice_bank" jdbcType="VARCHAR" property="invoiceBank" />
		<result column="invoice_account" jdbcType="VARCHAR" property="invoiceAccount" />
		<result column="invoice_desc" jdbcType="VARCHAR" property="invoiceDesc" />
		<result column="created_date" jdbcType="TIMESTAMP" property="createdDate" />
		<result column="reviewed_date" jdbcType="TIMESTAMP" property="reviewedDate" />
		<result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
		<result column="email" jdbcType="VARCHAR" property="email" />
		<result column="tracking_number" jdbcType="VARCHAR" property="trackingNumber" />
		<result column="province" jdbcType="VARCHAR" property="province" />
		<result column="city" jdbcType="VARCHAR" property="city" />
		<result column="area" jdbcType="VARCHAR" property="area" />
		<result column="auditor_date" jdbcType="TIMESTAMP" property="auditorDate" />
		<result column="auditor_name" jdbcType="VARCHAR" property="auditorName" />
		<result column="approver_date" jdbcType="TIMESTAMP" property="approverDate" />
		<result column="approver_name" jdbcType="VARCHAR" property="approverName" />
		<result column="tracking_order_no" jdbcType="VARCHAR" property="trackingOrderNo" />
		<result column="receiver_name" jdbcType="VARCHAR" property="receiverName" />
		<result column="receiver_mobile_phone" jdbcType="VARCHAR" property="receiverMobilePhone" />
		<result column="receiver_province" jdbcType="VARCHAR" property="receiverProvince" />
		<result column="receiver_city" jdbcType="VARCHAR" property="receiverCity" />
		<result column="receiver_area" jdbcType="VARCHAR" property="receiverArea" />
		<result column="receiver_address" jdbcType="VARCHAR" property="receiverAddress" />
		<result column="source" jdbcType="VARCHAR" property="source" />
		<result column="station_id" jdbcType="VARCHAR" property="stationId" />
		<result column="commercial_id" jdbcType="BIGINT" property="commercialId" />
		<result column="hlht_apply_no" jdbcType="VARCHAR" property="hlhtApplyNo" />
		<result column="platform_source" jdbcType="INTEGER" property="platformSource" />
		<result column="reject_reason" jdbcType="VARCHAR" property="rejectReason" />
		<result column="hlht_user_no" jdbcType="VARCHAR" property="hlhtUserNo" />
		<result column="procInstId" jdbcType="VARCHAR" property="procInstId" />
		<result column="tempSalId" jdbcType="BIGINT" property="tempSalId" />
		<result column="productTempId" jdbcType="BIGINT" property="productTempId" />
	</resultMap>

	<resultMap id="RESULT_INVOICED_RECORD_DTO"
		extends="RESULT_INVOICED_RECORD_PO"
		type="com.cdz360.biz.model.trading.invoice.dto.InvoicedRecordDto">
		<result column="channel" jdbcType="JAVA_OBJECT" property="channel" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_INVOICED_RECORD_DTO">
		select
			rec.*,
			sal.channel as channel
		from invoiced_record rec
		left join invoiced_template_sal sal
		on sal.id=rec.tempSalId
		<where>
			rec.id = #{id}
		</where>
	</select>

	<select id="existById" resultType="java.lang.Long">
		select count(id) from invoiced_record
		where id = #{id}
	</select>


	<select id="queryInvoiceRecordId" resultType="java.lang.Long">
		select id from invoiced_record
		where
		<choose>
			<when test="isSureInvoiceNumber == null or isSureInvoiceNumber== true">
				invoice_number = #{invoiceNumber}
			</when>
			<otherwise>
				invoice_number like concat('%',#{invoiceNumber},'%')
			</otherwise>
		</choose>
		limit 1
	</select>

	<select id="queryIdByApplyNo" resultType="java.lang.Long">
		select id from invoiced_record
		where applyNo = #{applyNo}
		and invoiced_enabled = 1
		limit 1
	</select>

	<select id="getInvoiceVo4PrepaidProcess" resultType="com.cdz360.biz.model.oa.vo.OaInvoicedVo">
		select
			ir.invoice_name as name,
			ir.invoice_tin as tin,
			ir.invoice_bank as bank,
			ir.invoice_account as bankAcoount,
			ir.invoice_address as address,
			ir.invoice_tel as tel,
			ir.email as email,
			ir.receiver_name as receiverName,
			ir.receiver_mobile_phone as receiverMobilePhone,
			ir.receiver_province as receiverProvince,
			ir.receiver_city as receiverCity,
			ir.receiver_area as receiverArea,
			ir.receiver_address as receiverAddress,
			its.sale_name as saleName,
			its.channel,
			tistr.name as productTempName
		from
			invoiced_record ir
		left join invoiced_template_sal its on
			ir.tempSalId = its.id
		left join t_invoiced_sal_temp_ref tistr on
			ir.productTempId = tistr.id
		where
			ir.procInstId = #{procInstId}
		limit 1
	</select>

</mapper>
