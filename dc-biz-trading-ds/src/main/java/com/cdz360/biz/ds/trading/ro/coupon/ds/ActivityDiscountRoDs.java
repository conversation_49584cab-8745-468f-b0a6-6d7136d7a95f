package com.cdz360.biz.ds.trading.ro.coupon.ds;

import com.cdz360.biz.ds.trading.ro.coupon.mapper.ActivityDiscountRoMapper;
import com.cdz360.biz.model.trading.coupon.po.ChargePo;
import java.math.BigDecimal;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ActivityDiscountRoDs {

    @Autowired
    private ActivityDiscountRoMapper activityDiscountRoMapper;

    public List<ChargePo> getById(Long id, Boolean enable) {
        return activityDiscountRoMapper.getById(id, enable);
    }

    public ChargePo getDiscountInfo(Long activityId, BigDecimal amount) {
        return activityDiscountRoMapper.getDiscountInfo(activityId, amount);
    }

}
