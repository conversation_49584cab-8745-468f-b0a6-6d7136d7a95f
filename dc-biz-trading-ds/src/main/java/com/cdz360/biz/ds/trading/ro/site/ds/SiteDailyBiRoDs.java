package com.cdz360.biz.ds.trading.ro.site.ds;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ds.trading.ro.site.mapper.SiteDailyBiRoMapper;
import com.cdz360.biz.model.trading.site.po.SiteDailyBiPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class SiteDailyBiRoDs {

    @Autowired
    private SiteDailyBiRoMapper siteDailyBiRoMapper;


    @Transactional(readOnly = true)
    public List<Integer> getCommDailyKwLine(String commIdChain, Date date) {
        long start = 0L;
        int size = 100;
        int retSize = size;
        List<Integer> ret = new ArrayList<>();
//        FieldVector v = null;
        while (retSize >= size) {
            List<SiteDailyBiPo> biList = siteDailyBiRoMapper.getKwLineList(commIdChain, date, start, size);
//            log.info("bi[0] = {}", JsonUtils.toJsonString(biList.get(0)));
            ret = biList.get(0).getKwLine();


            retSize = biList.size();
            start = start + size;
        }
        log.info("ret = {}", JsonUtils.toJsonString(ret));
        return ret;
    }

    public SiteDailyBiPo getSiteDailyBi(String siteId, Date date) {
        return siteDailyBiRoMapper.getSiteDailyBi(siteId, date);
    }

    public List<SiteDailyBiPo> getSiteDailyBiList(List<String> siteIds, Date date) {
        if (CollectionUtils.isEmpty(siteIds)) {
            return new ArrayList<>();
        }
        return siteDailyBiRoMapper.getSiteDailyBiList(siteIds, date);
    }

    public List<SiteDailyBiPo> getSiteDailyBiRangeDate(String siteId, Date fromDate, Date toDate) {
        return siteDailyBiRoMapper.getSiteDailyBiRangeDate(siteId, fromDate, toDate);
    }
}
