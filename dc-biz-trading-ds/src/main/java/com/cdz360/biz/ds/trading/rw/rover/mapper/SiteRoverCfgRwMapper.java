package com.cdz360.biz.ds.trading.rw.rover.mapper;

import com.cdz360.biz.model.trading.rover.po.SiteRoverCfgPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SiteRoverCfgRwMapper {
	SiteRoverCfgPo getById(@Param("id") Long id, @Param("lock") boolean lock);

	int insertSiteRoverCfg(SiteRoverCfgPo siteRoverCfgPo);

	int updateSiteRoverCfg(SiteRoverCfgPo siteRoverCfgPo);

	int updateSiteRoverGroup(@Param("siteId") String siteId);

	int batchInsertSiteRoverGroup(@Param("siteId") String siteId,@Param("gidList") List<String> gidList);


}
