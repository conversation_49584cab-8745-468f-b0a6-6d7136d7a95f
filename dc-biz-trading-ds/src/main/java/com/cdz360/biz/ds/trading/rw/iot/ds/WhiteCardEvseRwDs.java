package com.cdz360.biz.ds.trading.rw.iot.ds;

import com.cdz360.biz.ds.trading.rw.iot.mapper.WhiteCardEvseRwMapper;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class WhiteCardEvseRwDs {

    @Autowired
    private WhiteCardEvseRwMapper mapper;

    public boolean disable(@NonNull List<String> whiteCardNoList) {
        return mapper.disable(whiteCardNoList) > 0;
    }

}
