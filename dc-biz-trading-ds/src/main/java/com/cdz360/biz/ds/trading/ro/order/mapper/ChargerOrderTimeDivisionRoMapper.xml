<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.trading.ro.order.mapper.ChargerOrderTimeDivisionRoMapper">

    <resultMap id="BaseResultMap" type="com.cdz360.biz.model.trading.order.po.ChargerOrderTimeDivision">
        <result column="time_division_order_id" jdbcType="VARCHAR" property="timeDivisionOrderId"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="start_time" jdbcType="VARCHAR" property="startTime"/>
        <result column="stop_time" jdbcType="VARCHAR" property="stopTime"/>
        <result column="duration" jdbcType="INTEGER" property="duration"/>
        <result column="start_meter" jdbcType="INTEGER" property="startMeter"/>
        <result column="end_meter" jdbcType="BIGINT" property="endMeter"/>
        <result column="electric" jdbcType="DECIMAL" property="electric"/>
        <result column="start_soc" jdbcType="INTEGER" property="startSoc" />
        <result column="stop_soc" jdbcType="INTEGER" property="stopSoc" />
        <result column="electric_price" jdbcType="DECIMAL" property="electricPrice"/>
        <result column="service_price" jdbcType="DECIMAL" property="servicePrice"/>
        <result column="order_price" jdbcType="DECIMAL" property="orderPrice"/>
        <result column="tag" jdbcType="INTEGER" property="tag" />
        <result column="template_id" jdbcType="VARCHAR" property="templateId" />
        <result column="template_start_time" jdbcType="VARCHAR" property="templateStartTime" />
        <result column="template_stop_time" jdbcType="VARCHAR" property="templateStopTime" />
        <result column="template_type" jdbcType="VARCHAR" property="templateType" />
        <result column="electric_unit" jdbcType="DECIMAL" property="electricUnit"/>
        <result column="service_unit" jdbcType="DECIMAL" property="serviceUnit"/>
    </resultMap>

    <resultMap id="BaseResultMapEx"
               type="com.cdz360.biz.model.trading.order.po.ChargerOrderTimeDivisionEx"
               extends="BaseResultMap">
        <result column="box_code" jdbcType="VARCHAR" property="evseNo"/>
        <result column="connector_id" jdbcType="INTEGER" property="plugId"/>
    </resultMap>

    <sql id="Base_Column_List">
        division.time_division_order_id,
        division.order_id,
        division.orderNo,
        division.start_time,
        division.stop_time,
        division.duration,
        division.start_meter,
        division.end_meter,
        division.electric,
        division.start_soc,
        division.stop_soc,
        division.electric_price,
        division.service_price,
        division.order_price,
        division.tag,
        division.template_id,
        division.template_start_time,
        division.template_stop_time,
        division.template_type,
        division.electric_unit,
        division.service_unit
    </sql>

    <sql id="Base_Column_List_EX">
        ,od.box_code
        ,od.connector_id
    </sql>

<!--    <select id="queryAll" parameterType="java.util.Map" resultMap="BaseResultMap">-->
<!--        SELECT-->
<!--        <include refid="Base_Column_List"/>-->
<!--        FROM t_charger_order_time_division division-->
<!--    </select>-->

    <select id="getDivisionBySiteAndTimeRange" resultMap="BaseResultMapEx">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Column_List_EX"/>

        FROM t_charger_order_time_division division
        LEFT JOIN t_charger_order od ON od.order_no = division.orderNo
        WHERE
            od.order_no in (
                select o.order_no
                from t_charger_order o
                where
                o.station_id = #{siteId}
                <![CDATA[
                and ( (o.create_time >= #{startTime} and o.create_time < #{endTime})
                    or (o.stop_time >= #{startTime} and o.stop_time < #{endTime})
                    or (o.create_time < #{startTime} and o.stop_time > #{endTime})
                )
                ]]>
            )
        <![CDATA[
            AND division.start_time >= #{startTime}
            AND division.stop_time <= #{endTime}
        ]]>
    </select>

    <select id="getDivisionBySiteAndTimeRangeSpanDay" resultMap="BaseResultMapEx">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Column_List_EX"/>

        FROM t_charger_order_time_division division
        LEFT JOIN t_charger_order od ON od.order_no = division.orderNo
        WHERE
        od.station_id = #{siteId}
        <![CDATA[
            AND
            (
                (
                division.start_time < #{startTime}
                AND division.stop_time > #{startTime}
                )
            OR
                (
                division.start_time < #{endTime}
                AND division.stop_time > #{endTime}
                )
            )
        ]]>
    </select>

    <select id="selectListByOrderNoList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_charger_order_time_division division
        where
        <if test="orderNoList != null and orderNoList.size()>0  ">
            orderNo IN
            <foreach item="item" index="index" collection="orderNoList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>