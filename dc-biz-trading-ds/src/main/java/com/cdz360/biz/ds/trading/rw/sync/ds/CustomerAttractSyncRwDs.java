package com.cdz360.biz.ds.trading.rw.sync.ds;

import com.cdz360.biz.ds.trading.rw.sync.mapper.CustomerAttractSyncRwMapper;
import com.cdz360.biz.model.common.po.CustomerAttractPo;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CustomerAttractSyncRwDs {

    @Autowired
    private CustomerAttractSyncRwMapper mapper;

    public boolean alreadyExist(Long customerId) {
        return mapper.countByCustomerId(customerId) > 0;
    }

    public boolean insertIgnore(CustomerAttractPo po) {
        return mapper.insertIgnore(po) > 0;
    }

    public boolean batchInsertIgnore(List<CustomerAttractPo> list) {
        return mapper.batchInsertIgnore(list) > 0;
    }

}
