<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.trading.rw.sync.mapper.CorpUserSyncRwMapper">

    <insert id="insertOrUpdate" useGeneratedKeys="true" keyProperty="id"
            keyColumn="id" parameterType="com.cdz360.base.model.corp.dto.CorpUserSyncDto">
        insert into t_r_corp_user(
        id,
        corpId,
        uid,
        corpOrgId,
        name,
        phone,
        commId,
        createTime,
        updateTime,
        enable)

        values(
        #{id},
        #{corpId},
        #{uid},
        #{corpOrgId},
        #{name},
        #{phone},
        #{commId},
        #{createTime},
        now(),
        #{enable,jdbcType=BOOLEAN})

        on DUPLICATE key UPDATE

        id=#{id},
        corpId=#{corpId},
        uid=#{uid},
        corpOrgId=#{corpOrgId},
        name=#{name},
        phone=#{phone},
        commId=#{commId},
        createTime=#{createTime},
        updateTime=now(),
        enable=#{enable,jdbcType=BOOLEAN}
    </insert>

</mapper>