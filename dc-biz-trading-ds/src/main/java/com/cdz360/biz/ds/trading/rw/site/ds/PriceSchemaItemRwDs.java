package com.cdz360.biz.ds.trading.rw.site.ds;

import com.cdz360.biz.ds.trading.rw.site.mapper.PriceSchemaItemRwMapper;
import com.cdz360.biz.model.site.po.PriceItemPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class PriceSchemaItemRwDs {

    @Autowired
    private PriceSchemaItemRwMapper priceSchemaItemRwMapper;

    @Transactional
    public void batchAddPriceSchemaItems(List<PriceItemPo> itmes) {
        itmes.stream().forEach(i -> {
            priceSchemaItemRwMapper.insertPriceSchemaItem(i);
        });
    }
}
