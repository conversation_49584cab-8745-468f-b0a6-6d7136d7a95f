package com.cdz360.biz.ds.trading.rw.invoice.ds;

import com.cdz360.biz.ds.trading.rw.invoice.mapper.CorpInvoiceRecordRwMapper;
import com.cdz360.biz.model.trading.invoice.po.CorpInvoiceRecordPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CorpInvoiceRecordRwDs {

    @Autowired
    private CorpInvoiceRecordRwMapper corpInvoiceRecordRwMapper;

    public int insertOrUpdate(CorpInvoiceRecordPo po) {
        return corpInvoiceRecordRwMapper.insertOrUpdate(po);
    }

    public int updateByCondition(CorpInvoiceRecordPo po) {
        return corpInvoiceRecordRwMapper.updateByCondition(po);
    }

    public int deleteByApplyNo(String applyNo) {
        return corpInvoiceRecordRwMapper.deleteByApplyNo(applyNo);
    }
    public Integer updateCorpInvoiceRecordReturnFlag(CorpInvoiceRecordPo po) {
        return corpInvoiceRecordRwMapper.updateCorpInvoiceRecordReturnFlag(po);
    }

    public Integer updateCorpInvoiceRecordModelId(Long oldId, Long newId) {
        return corpInvoiceRecordRwMapper.updateCorpInvoiceRecordModelId(oldId, newId);
    }

    public Integer updateCorpInvoiceRecordProTempId(Long oldId, Long newId) {
        return corpInvoiceRecordRwMapper.updateCorpInvoiceRecordProTempId(oldId, newId);
    }

    public boolean updateCorpInvoiceRecordTempSalId(Long oldId, Long newId) {
        return corpInvoiceRecordRwMapper.updateCorpInvoiceRecordTempSalId(oldId, newId) > 0;
    }

    public Boolean updateFailRemark(String applyNo, String failRemark) {
        return corpInvoiceRecordRwMapper.updateFailRemark(applyNo, failRemark) > 0;
    }
}
