package com.cdz360.biz.ds.trading.ro.site.ds;

import com.cdz360.biz.ds.trading.ro.site.mapper.SiteTotalBiRoMapper;
import com.cdz360.biz.model.trading.order.vo.ChargeOrderBiVo;
import java.math.BigDecimal;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SiteTotalBiRoDs {

    @Autowired
    private SiteTotalBiRoMapper siteTotalBiRoMapper;

    public ChargeOrderBiVo orderBiOfCommChain(String commIdChain, String siteId,
        List<Integer> bizTypeList) {
        val result = siteTotalBiRoMapper.orderBiOfCommChain(
            commIdChain, siteId, bizTypeList);
        if (null != result.getFee()) { // 临时调整
            result.setServFee(result.getFee().multiply(new BigDecimal("0.35")));
        }
        return result;
    }
}
