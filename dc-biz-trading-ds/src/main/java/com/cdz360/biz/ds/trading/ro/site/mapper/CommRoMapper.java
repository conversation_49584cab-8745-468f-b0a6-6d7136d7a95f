package com.cdz360.biz.ds.trading.ro.site.mapper;

import com.cdz360.biz.model.merchant.param.ListCommercialParam;
import com.cdz360.biz.model.trading.comm.po.TRCommercialPo;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.po.CommPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface CommRoMapper {

    CommPo getCommById(@Param("commId") Long commId);

    List<CommPo> getCommList(ListSiteParam param);
    List<CommPo> getCommList2(ListCommercialParam param);

    Long countCorp(ListSiteParam param);

    /**
     * 查询集团商户ID列表
     */
    List<Long> getTopCommIdList();

//    TRCommercialPo getCommercialById(@Param("id") long id);

//    List<TRCommercialPo> listCommercial(ListCommercialParam param);

    Long getCount(ListCommercialParam param);
}
