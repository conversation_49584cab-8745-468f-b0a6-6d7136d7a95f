package com.cdz360.biz.ds.trading.rw.coupon.ds;

import com.cdz360.biz.ds.trading.rw.coupon.mapper.CouponScoreSettingRwMapper;
import com.cdz360.biz.model.trading.coupon.po.CouponScoreSettingPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class CouponScoreSettingRwDs {

    @Autowired
    private CouponScoreSettingRwMapper couponScoreSettingRwMapper;

    public CouponScoreSettingPo getById(Long id, boolean lock) {
        return this.couponScoreSettingRwMapper.getById(id, lock);
    }

    public boolean insertCouponScoreSetting(CouponScoreSettingPo couponScoreSettingPo) {
        return this.couponScoreSettingRwMapper.insertCouponScoreSetting(couponScoreSettingPo) > 0;
    }

    public boolean updateCouponScoreSetting(CouponScoreSettingPo couponScoreSettingPo) {
        return this.couponScoreSettingRwMapper.updateCouponScoreSetting(couponScoreSettingPo) > 0;
    }

    public boolean updateCouponScoreSettingByCouponId(Long couponDictId, List<Long> scoreSettingIdList) {
        return this.couponScoreSettingRwMapper.updateCouponScoreSettingByCouponId(couponDictId, scoreSettingIdList) > 0;
    }

    public int batchInsert(List<CouponScoreSettingPo> list) {
        return this.couponScoreSettingRwMapper.batchInsert(list);
    }

}
