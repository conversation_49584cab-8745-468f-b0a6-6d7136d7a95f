<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.trading.ro.geo.mapper.CityRoMapper">


    <select id="getCityName" resultType="java.lang.String">
        select name from t_geo_city where code=#{code}
    </select>
    <select id="listCity" resultType="com.cdz360.biz.model.geo.po.CityPo">
        select * from t_geo_city
    </select>

    <select id="getCitySiteBi"
            resultType="com.cdz360.biz.model.trading.site.vo.CitySiteBiVo">
        select
        city.code as cityCode,
        city.name as cityName,
        count(distinct s.id) as siteNum
        from t_site s
        left join t_r_commercial comm on s.operate_id = comm.id
        left join t_geo_city city on s.city = city.code
        where city.code = #{cityCode}
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
            and comm.idChain like CONCAT(#{commIdChain}, '%')
        </if>
    </select>

    <select id="getCityByCode" resultType="com.cdz360.biz.model.geo.po.CityPo">
        select * from t_geo_city where code=#{code}
    </select>
</mapper>