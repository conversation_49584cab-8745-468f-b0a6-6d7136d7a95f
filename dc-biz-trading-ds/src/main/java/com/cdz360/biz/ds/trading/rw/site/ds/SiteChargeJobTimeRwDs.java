package com.cdz360.biz.ds.trading.rw.site.ds;

import com.cdz360.biz.ds.trading.rw.site.mapper.SiteChargeJobTimeRwMapper;
import com.cdz360.biz.model.trading.site.po.SiteChargeJobTimePo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SiteChargeJobTimeRwDs {

    @Autowired
    private SiteChargeJobTimeRwMapper siteChargeJobTimeRwMapper;

    public int insert(List<SiteChargeJobTimePo> po) {
        return siteChargeJobTimeRwMapper.insert(po);
    }

    public int deleteByJobId(Long jobId) {
        return siteChargeJobTimeRwMapper.deleteByJobId(jobId);
    }
}
