package com.cdz360.biz.ds.trading.ro.site.mapper;

import com.cdz360.biz.model.trading.site.param.ChargeJobParam;
import com.cdz360.biz.model.trading.site.po.SiteChargeJobPo;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobPlugVo;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SiteChargeJobRoMapper {

    List<SiteChargeJobVo> jobList(ChargeJobParam param);
    long jobListCount(ChargeJobParam param);

    List<SiteChargeJobVo> findByCondition(SiteChargeJobVo param);

    List<SiteChargeJobPlugVo> getJobInFoByPlugNos(@Param("plugNoList") List<String> plugNoList);
}
