<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.trading.ro.order.mapper.PayBillRoMapper">

  <resultMap id="BaseResultMap" type="com.cdz360.biz.model.trading.order.po.PayBillPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="user_id" jdbcType="BIGINT" property="userId"/>
    <result column="comm_id" jdbcType="BIGINT" property="commId"/>
    <result column="topCommId" jdbcType="BIGINT" property="topCommId"/>
    <result column="commercialName" jdbcType="VARCHAR" property="commercialName"/>
    <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
    <result column="amount" jdbcType="DECIMAL" property="amount"/>
    <result column="freeAmount" jdbcType="DECIMAL" property="freeAmount"/>
    <result column="outFlowAmount" jdbcType="DECIMAL" property="outFlowAmount"/>
    <result column="outFlowFreeAmount" jdbcType="DECIMAL" property="outFlowFreeAmount"/>
    <result column="flowType" typeHandler="com.cdz360.ds.type.EnumTypeHandler" property="flowType"/>
    <result column="sourceType" typeHandler="com.cdz360.ds.type.EnumTypeHandler"
      property="sourceType"/>
    <result column="payChannel" typeHandler="com.cdz360.ds.type.EnumTypeHandler"
      property="payChannel"/>
    <result column="accountType" typeHandler="com.cdz360.ds.type.EnumTypeHandler"
      property="accountType"/>
    <result column="accountCode" jdbcType="BIGINT" property="accountCode"/>
    <result column="flowInAccountType" typeHandler="com.cdz360.ds.type.EnumTypeHandler"
      property="flowInAccountType"/>
    <result column="refBillNo" jdbcType="VARCHAR" property="refBillNo"/>
    <result column="taxType" typeHandler="com.cdz360.ds.type.EnumTypeHandler" property="taxType"/>
    <result column="taxStatus" typeHandler="com.cdz360.ds.type.EnumTypeHandler"
      property="taxStatus"/>
    <result column="invoiceType" typeHandler="com.cdz360.ds.type.EnumTypeHandler"
      property="invoiceMode"/>
    <result column="taxNo" jdbcType="VARCHAR" property="taxNo"/>
    <result column="expressStatus" typeHandler="com.cdz360.ds.type.EnumTypeHandler"
      property="expressStatus"/>
    <result column="expressCompany" jdbcType="VARCHAR" property="expressCompany"/>
    <result column="expressNo" jdbcType="VARCHAR" property="expressNo"/>
    <result column="amountBefore" jdbcType="DECIMAL" property="amountBefore"/>
    <result column="amountAfter" jdbcType="DECIMAL" property="amountAfter"/>
    <result column="subject" jdbcType="VARCHAR" property="subject"/>
    <result column="client_ip" jdbcType="VARCHAR" property="clientIp"/>
    <result column="body" jdbcType="VARCHAR" property="body"/>
    <result column="open_id" jdbcType="VARCHAR" property="openId"/>
    <result column="pay_type" jdbcType="TINYINT" property="payType"/>
    <result column="status" jdbcType="TINYINT" property="status"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="trade_no" jdbcType="VARCHAR" property="tradeNo"/>
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime"/>
    <result column="notify_type" jdbcType="TINYINT" property="notifyType"/>
    <result column="trade_type" jdbcType="VARCHAR" property="tradeType"/>
    <result column="bank_type" jdbcType="VARCHAR" property="bankType"/>
    <result column="out_refund_no" jdbcType="VARCHAR" property="outRefundNo"/>
    <result column="refund_recv_accout" jdbcType="VARCHAR" property="refundRecvAccout"/>
    <result column="refund_request_source" jdbcType="VARCHAR" property="refundRequestSource"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="refund_old_order_id" jdbcType="VARCHAR" property="refundOldOrderId"/>
    <result column="charge_order_no" jdbcType="VARCHAR" property="chargeOrderNo"/>
    <result column="outAccountName" jdbcType="VARCHAR" property="outAccountName"/>
    <result column="outBankName" jdbcType="VARCHAR" property="outBankName"/>
    <result column="outAccountNo" jdbcType="VARCHAR" property="outAccountNo"/>
    <result column="inAccountName" jdbcType="VARCHAR" property="inAccountName"/>
    <result column="inBankName" jdbcType="VARCHAR" property="inBankName"/>
    <result column="inAccountNo" jdbcType="VARCHAR" property="inAccountNo"/>
    <result column="flowSeqNo" jdbcType="VARCHAR" property="flowSeqNo"/>
    <result column="opUserType" typeHandler="com.cdz360.ds.type.EnumTypeHandler"
      property="opUserType"/>
    <result column="opUid" jdbcType="BIGINT" property="opUid"/>
    <result column="opName" jdbcType="VARCHAR" property="opName"/>
    <result column="channel" jdbcType="VARCHAR" property="channel"/>

    <result column="payAccountName" jdbcType="VARCHAR" property="payAccountName"/>
    <result column="cusName" jdbcType="VARCHAR" property="cusName"/>
    <result column="cusPhone" jdbcType="VARCHAR" property="cusPhone"/>
    <result column="userType" typeHandler="com.cdz360.ds.type.EnumTypeHandler" property="userType"/>
    <result column="invoicedAmount" property="invoicedAmount"/>

    <result column="zftName" jdbcType="VARCHAR" property="zftName"/>
    <result column="wxSubMchId" jdbcType="VARCHAR" property="wxSubMchId"/>
    <result column="alipaySubMchId" jdbcType="VARCHAR" property="alipaySubMchId"/>
  </resultMap>

  <resultMap id="BiResultMap" type="com.cdz360.biz.model.trading.order.vo.PayBillBi">
    <result column="num" jdbcType="BIGINT" property="num"/>
    <result column="completedNum" jdbcType="BIGINT" property="completedNum"/>
    <result column="flowType" typeHandler="com.cdz360.ds.type.EnumTypeHandler" property="flowType"/>
    <result column="total" jdbcType="DECIMAL" property="total"/>
    <result column="arrivalTotal" jdbcType="DECIMAL" property="arrivalTotal"/>
    <result column="costTotal" jdbcType="DECIMAL" property="costTotal"/>
    <result column="freeTotal" jdbcType="DECIMAL" property="freeTotal"/>
  </resultMap>

  <resultMap id="InvoiceBiMap" type="com.cdz360.biz.model.trading.order.dto.PayBillInvoiceBi">
    <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
    <result column="amount" jdbcType="DECIMAL" property="amount"/>
    <result column="freeAmount" jdbcType="DECIMAL" property="freeAmount"/>
    <result column="sourceType" typeHandler="com.cdz360.ds.type.EnumTypeHandler"
      property="sourceType"/>
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime"/>
    <result column="canInvoiceAmount" jdbcType="DECIMAL" property="canInvoiceAmount"/>
    <result column="totalAmount" jdbcType="DECIMAL" property="totalAmount"/>
  </resultMap>

  <resultMap id="ZftOrderVo_MAP" type="com.cdz360.biz.model.trading.order.vo.ZftBillVo">
    <result column="orderId" jdbcType="VARCHAR" property="orderId"/>
    <result column="chargeOrderNo" jdbcType="VARCHAR" property="chargeOrderNo"/>
    <result column="tradeNo" jdbcType="VARCHAR" property="tradeNo"/>
    <result column="tradeAmount" jdbcType="DECIMAL" property="tradeAmount"/>
    <result column="tradeType" typeHandler="com.cdz360.ds.type.EnumTypeHandler"
      property="tradeType"/>
    <result column="checkResult" jdbcType="VARCHAR" property="checkResult"/>
    <result column="payTime" jdbcType="TIMESTAMP" property="payTime"/>
    <result column="zftName" jdbcType="VARCHAR" property="zftName"/>
    <result column="commName" jdbcType="VARCHAR" property="commName"/>
    <result column="phone" jdbcType="VARCHAR" property="phone"/>
    <result column="dailyBillName" jdbcType="VARCHAR" property="dailyBillName"/>
    <result column="accountType" jdbcType="VARCHAR" property="accountType"/>
    <result column="payWay" jdbcType="VARCHAR" property="payWay"/>
    <result column="financialType" jdbcType="VARCHAR" property="financialType"/>
  </resultMap>

  <sql id="BaseColumnList">
    b.id, b.user_id, b.comm_id, b.topCommId, b.order_id, b.amount, b.freeAmount, b.flowType,
    b.refBillNo,
    b.accountType, b.accountCode, b.payChannel, b.sourceType, b.flowInAccountType, b.amountBefore,
    b.amountAfter, b.subject, b.client_ip, b.body, b.open_id, b.taxType, b.taxStatus,b.invoiceType,
    b.taxNo,
    b.expressStatus, b.expressCompany, b.expressNo, b.pay_type, b.status, b.create_time, b.trade_no,
    b.pay_time, b.notify_type, b.trade_type, b.bank_type, b.out_refund_no, refund_recv_accout,
    b.refund_request_source, b.remark, b.update_time, b.refund_old_order_id, b.charge_order_no,
    b.flowSeqNo, b.outAccountName, b.outBankName, b.outAccountNo, b.inAccountName, b.inBankName,
    b.inAccountNo, b.opUserType, b.opUid, b.opName, b.userType,
    b.invoicedAmount,b.zftName,b.wxSubMchId,b.alipaySubMchId,
    b.outFlowAmount, b.outFlowFreeAmount,
    b.channel
  </sql>

  <sql id="SelectSql">
    <!-- 默认不查看支付失败的记录 -->
    and b.`status` != 2
    <if test="userTypeList != null and userTypeList.size()>0  ">
      and b.userType IN
      <foreach item="item" index="index" collection="userTypeList"
        open="(" separator="," close=")">
        #{item.code}
      </foreach>
    </if>
    <if test="sourceTypeList != null and sourceTypeList.size()>0  ">
      and b.sourceType IN
      <foreach item="item" index="index" collection="sourceTypeList"
        open="(" separator="," close=")">
        #{item.code}
      </foreach>
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( statusList )">
      and b.status IN
      <foreach item="item" index="index" collection="statusList"
        open="(" separator="," close=")">
        #{item.code}
      </foreach>
    </if>
    <if test="flowTypeList != null and flowTypeList.size()>0  ">
      and b.flowType IN
      <foreach item="item" index="index" collection="flowTypeList"
        open="(" separator="," close=")">
        #{item.code}
      </foreach>
    </if>
    <if test="accountTypeList != null and accountTypeList.size()>0  ">
      and b.accountType IN
      <foreach item="item" index="index" collection="accountTypeList"
        open="(" separator="," close=")">
        #{item.code}
      </foreach>
    </if>
    <if test="payChannelList != null and payChannelList.size()>0  ">
      and b.payChannel IN
      <foreach item="item" index="index" collection="payChannelList"
        open="(" separator="," close=")">
        #{item.code}
      </foreach>
    </if>
    <if test="flowInAccountTypeList != null and flowInAccountTypeList.size()>0  ">
      and b.flowInAccountType IN
      <foreach item="item" index="index" collection="flowInAccountTypeList"
        open="(" separator="," close=")">
        #{item.code}
      </foreach>
    </if>
    <if test="taxTypeList != null and taxTypeList.size()>0  ">
      and b.taxType IN
      <foreach item="item" index="index" collection="taxTypeList"
        open="(" separator="," close=")">
        #{item.code}
      </foreach>
    </if>
    <if test="taxStatus != null and taxStatus.size()>0  ">
      and
      <foreach item="item" index="index" collection="taxStatus"
        open="(" separator="or" close=")">
        <choose>
          <!--                    未开票-->
          <when test="item.code == 0">
            (b.amount > 0 and b.invoicedAmount = 0 and b.amount > b.outFlowAmount and b.flowType =
            1)
          </when>
          <!--                    已开票-->
          <when test="item.code == 1">
            (b.amount > 0 and b.amount != b.outFlowAmount and b.amount = (b.outFlowAmount +
            b.invoicedAmount))
          </when>
          <!--                    部分开票-->
          <when test="item.code == 3">
            (b.amount > 0 and b.amount > (b.outFlowAmount + b.invoicedAmount) and b.invoicedAmount >
            0)
          </when>
          <!--                    不可开票-->
          <when test="item.code == 9999">
            ((b.flowType = 2) or (b.amount = b.outFlowAmount) or (b.amount = 0 and b.freeAmount >
            0))
          </when>
        </choose>
      </foreach>
    </if>
    <if test="expressStatusList != null and expressStatusList.size()>0  ">
      and b.expressStatus IN
      <foreach item="item" index="index" collection="expressStatusList"
        open="(" separator="," close=")">
        #{item.code}
      </foreach>
    </if>
    <if test="commIdList != null and commIdList.size()>0  ">
      and b.comm_id IN
      <foreach item="item" index="index" collection="commIdList"
        open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and c.idChain like CONCAT(#{commIdChain}, '%')
    </if>
    <if test="payAccountList != null and payAccountList.size()>0  ">
      and
      <foreach item="item" index="index" collection="payAccountList"
        open="(" separator="or" close=")">
        (b.accountType=#{item.accountType.code} and b.accountCode=#{item.accountCode})
      </foreach>
    </if>
    <if test="userId != null">
      and b.user_id=#{userId}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(orderId)">
      <choose>
        <when test="orderId.length() gte 20">
          and b.order_id = #{orderId}
        </when>
        <otherwise>
          and b.order_id like CONCAT('%',#{orderId},'%')
        </otherwise>
      </choose>
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(tradeNo)">
      and b.trade_no like
      CONCAT('%',#{tradeNo},'%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(zftName)">
      and b.zftName like
      CONCAT('%',#{zftName},'%')
    </if>
    <if test="orderIdList != null and orderIdList.size()>0  ">
      and b.order_id IN
      <foreach item="item" index="index" collection="orderIdList"
        open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="exclusiveOrderIdList != null and exclusiveOrderIdList.size()>0  ">
      and b.order_id NOT IN
      <foreach item="item" index="index" collection="exclusiveOrderIdList"
        open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <!-- 仅仅为了查询可减少的充值记录-->
    <if test="status != null">
      and b.status!=#{status.code} and b.status=1 and b.flowType=1
    </if>
    <if test="fromTime != null">
      <![CDATA[ and b.create_time >= #{fromTime} ]]>
    </if>
    <if test="toTime != null">
      <![CDATA[ and b.create_time <= #{toTime} ]]>
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(keyword)">
      and (
      b.order_id like CONCAT('%',#{keyword},'%')
      or
      DATE_FORMAT(b.pay_time, '%Y-%m-%d %h:%M:%s') like CONCAT('%',#{keyword},'%')
      )
    </if>
  </sql>

  <sql id="zftSql2">
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and comm.idChain like CONCAT(#{commIdChain}, '%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( orderId )">
      and bill.order_id like CONCAT('%',#{orderId}, '%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( tradeNo )">
      and bill.trade_no like CONCAT('%',#{tradeNo}, '%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( chargeOrderNo )">
      and bill.charge_order_no like CONCAT('%',#{chargeOrderNo}, '%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( zftName )">
      and bill.zftName like CONCAT('%',#{zftName}, '%')
    </if>
    <if test="commId != null">
      and bill.comm_id = #{commId}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( phone )">
      and u.phone like CONCAT('%',#{phone}, '%')
    </if>
    <!--时间范围-->
    <if test="payTimeFilter != null">
      <![CDATA[ and bill.pay_time >= #{payTimeFilter.startTime} ]]>
      <![CDATA[ and bill.pay_time < #{payTimeFilter.endTime} ]]>
    </if>
    <!--交易渠道-->
    <choose>
      <when test="payWay != null and payWay == 'WX'">
        and bill.flowInAccountType = 2
      </when>
      <when test="payWay != null and payWay == 'ALIPAY'">
        and bill.flowInAccountType = 1
      </when>
      <otherwise>
        and bill.flowInAccountType IN ( 1, 2 )
      </otherwise>
    </choose>
    <!--财务类型-->
    <choose>
      <when test="financialType != null and financialType == 'PREPAY'">
        and bill.accountType = 4
      </when>
      <when test="financialType != null and financialType == 'PERSON'">
        and bill.accountType in (1,3,5)
      </when>
      <when test="financialType != null and financialType == 'FUNCTION'">
        and bill.accountType = 999 AND bill.sourceType = 0
      </when>
      <when test="financialType != null and financialType == 'CREDIT'">
        and bill.accountType in (7,8)
      </when>
      <otherwise>
        and bill.accountType in (1,3,4,5,7,8,999)
      </otherwise>
    </choose>
    <if test="null != flowType">
      and bill.flowType = #{flowType.code}
    </if>
    <if test="null != checkResult">
      and bill.checkResult = #{checkResult}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( dailyBillName )">
      and dailyBill.name like CONCAT('%',#{dailyBillName}, '%')
    </if>
  </sql>

  <sql id="zftSql">
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and c.idChain like CONCAT(#{commIdChain}, '%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( orderId )">
      and a.order_id like CONCAT('%',#{orderId}, '%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( tradeNo )">
      and a.trade_no like CONCAT('%',#{tradeNo}, '%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( chargeOrderNo )">
      and a.charge_order_no like CONCAT('%',#{chargeOrderNo}, '%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( zftName )">
      and a.zftName like CONCAT('%',#{zftName}, '%')
    </if>
    <if test="commId != null">
      and a.comm_id = #{commId}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( phone )">
      and u.phone like CONCAT('%',#{phone}, '%')
    </if>
    <!--时间范围-->
    <if test="payTimeFilter != null">
      <![CDATA[ and a.pay_time >= #{payTimeFilter.startTime} ]]>
      <![CDATA[ and a.pay_time < #{payTimeFilter.endTime} ]]>
    </if>
    <!--交易渠道-->
    <choose>
      <when test="payWay != null and payWay == 'WX'">
        and a.flowInAccountType = 2
      </when>
      <when test="payWay != null and payWay == 'ALIPAY'">
        and a.flowInAccountType = 1
      </when>
      <otherwise>
        and a.flowInAccountType IN ( 1, 2 )
      </otherwise>
    </choose>
    <!--财务类型-->
    <choose>
      <when test="financialType != null and financialType == 'PREPAY'">
        and a.accountType = 4
      </when>
      <when test="financialType != null and financialType == 'PERSON'">
        and a.accountType in (1,3,5)
      </when>
      <when test="financialType != null and financialType == 'CREDIT'">
        and a.accountType in (7,8)
      </when>
      <otherwise>
        and a.accountType in (1,3,4,5,7,8,999)
      </otherwise>
    </choose>
    <if test="null != flowType">
      and a.flowType = #{flowType.code}
    </if>
    <if test="null != checkResult">
      and a.checkResult = #{checkResult}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( dailyBillName )">
      and dailyBill.name like CONCAT('%',#{dailyBillName}, '%')
    </if>
  </sql>

  <!--查询充值列表-->
  <select id="payBillList" parameterType="com.cdz360.biz.model.trading.order.param.PayBillParam"
    resultMap="BaseResultMap">
    select
    <include refid="BaseColumnList"/>
    ,
    (case b.accountType
    when 5 then corp.corpName
    else u.username
    end) cusName,

    u.phone as cusPhone, c.comm_name as commercialName,

    (case b.accountType
    when 1 then '个人账户'
    when 3 then concat('商户会员-', accComm.comm_name)
    when 5 then concat('企业账户-', corp.corpName) end) payAccountName

    from t_pay_bill b
    left join t_r_user u on u.id = b.user_id
    left join t_r_commercial c on c.id = b.comm_id
    left join t_r_commercial accComm on accComm.id = b.accountCode and b.accountType = 3
    left join t_r_corp corp on corp.uid = b.user_id and b.accountType = 5

    <if test="null != inCorpInvoice and inCorpInvoice">
      left join t_invoice_record_order_ref ref on ref.orderNo = b.order_id and ref.invoiceWay =
      'PRE_PAY'
    </if>

    <!-- 仅仅查询现金账户(个人账户和企业账户)和商户会员 -->
    where
    <!--(accountType=1 or accountType=3 or accountType=5)-->
    1 = 1
    <choose>
      <when test="accountTypeList != null and accountTypeList.size()>0  ">
        and b.accountType IN
        <foreach item="item" index="index" collection="accountTypeList"
          open="(" separator="," close=")">
          #{item.code}
        </foreach>
      </when>
      <otherwise>
        and b.accountType in (1,3,5)
      </otherwise>
    </choose>
    <include refid="SelectSql"/>
    <if
      test="@com.cdz360.base.utils.StringUtils@isNotBlank(cusName) and @com.cdz360.base.utils.StringUtils@isNotBlank(cusPhone)">
      and (u.username like
      CONCAT('%',#{cusName},'%') or
      u.phone like
      CONCAT('%',#{cusPhone},'%')
      )
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(cusName) and cusPhone == null">
      and u.username like
      CONCAT('%',#{cusName},'%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(cusPhone) and cusName == null">
      and u.phone like
      CONCAT('%',#{cusPhone},'%')
    </if>
    <if test="showFail != null and showFail == true">
      and b.status <![CDATA[ <> ]]> 2
    </if>

    <if test="payTimeFilter != null">
      <![CDATA[ and b.pay_time >= #{payTimeFilter.startTime} ]]>
      <![CDATA[ and b.pay_time < #{payTimeFilter.endTime} ]]>
    </if>

    <if test="null != inCorpInvoice and inCorpInvoice">
      and b.status = 1
      <choose>
        <when test="@com.cdz360.base.utils.StringUtils@isNotBlank(applyNo)">
          and ref.applyNo = #{applyNo}
        </when>
        <otherwise>
          and b.invoicedAmount = 0 and ref.orderNo is null
        </otherwise>
      </choose>
    </if>

    <if test="accountType != null">
      and b.accountType = #{accountType.code}

      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(accCorpName)">
        and corp.corpName like
        CONCAT('%',#{accCorpName},'%')
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(accCommName)">
        and accComm.comm_name like
        CONCAT('%',#{accCommName},'%')
      </if>
    </if>
    <if test="activityId!=null">
      and b.activityId = #{activityId}
    </if>

    order by create_time desc, order_id desc
    <if test="null != start and null != size">
        limit #{start},#{size}
    </if>
  </select>


  <!--查询充值列表总数-->
  <select id="payBillCount" parameterType="com.cdz360.biz.model.trading.order.param.PayBillParam"
    resultType="java.lang.Long">
    select
    count(b.id)
    from t_pay_bill b
    left join t_r_user u on u.id = b.user_id
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
        left join t_r_commercial c on c.id = b.comm_id
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(accCommName)">
        left join t_r_commercial accComm on accComm.id = b.accountCode and b.accountType = 3
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(accCorpName)">
        left join t_r_corp corp on corp.uid = b.user_id and b.accountType = 5
    </if>

    <if test="null != inCorpInvoice and inCorpInvoice">
      left join t_invoice_record_order_ref ref on ref.orderNo = b.order_id and ref.invoiceWay =
      'PRE_PAY'
    </if>

    <!-- 仅仅查询现金账户(个人账户和企业账户)和商户会员 -->
    where
    <!--(accountType=1 or accountType=3 or accountType=5)-->
    1 = 1
    <choose>
      <when test="accountTypeList != null and accountTypeList.size()>0  ">
        and b.accountType IN
        <foreach item="item" index="index" collection="accountTypeList"
          open="(" separator="," close=")">
          #{item.code}
        </foreach>
      </when>
      <otherwise>
        and b.accountType in (1,3,5)
      </otherwise>
    </choose>
    <include refid="SelectSql"/>
    <if
      test="@com.cdz360.base.utils.StringUtils@isNotBlank(cusName) and @com.cdz360.base.utils.StringUtils@isNotBlank(cusPhone)">
      and (u.username like
      CONCAT('%',#{cusName},'%') or
      u.phone like
      CONCAT('%',#{cusPhone},'%')
      )
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(cusName) and cusPhone == null">
      and u.username like
      CONCAT('%',#{cusName},'%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(cusPhone) and cusName == null">
      and u.phone like
      CONCAT('%',#{cusPhone},'%')
    </if>
    <if test="showFail != null and showFail == true">
      and b.status <![CDATA[ <> ]]> 2
    </if>

    <if test="payTimeFilter != null">
      <![CDATA[ and b.pay_time >= #{payTimeFilter.startTime} ]]>
      <![CDATA[ and b.pay_time < #{payTimeFilter.endTime} ]]>
    </if>

    <if test="null != inCorpInvoice and inCorpInvoice">
      and b.status = 1
      <choose>
        <when test="@com.cdz360.base.utils.StringUtils@isNotBlank(applyNo)">
          and ref.applyNo = #{applyNo}
        </when>
        <otherwise>
          and b.invoicedAmount = 0 and ref.orderNo is null
        </otherwise>
      </choose>
    </if>

    <if test="accountType != null">
      and b.accountType = #{accountType.code}

      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(accCorpName)">
        and corp.corpName like
        CONCAT('%',#{accCorpName},'%')
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(accCommName)">
        and accComm.comm_name like
        CONCAT('%',#{accCommName},'%')
      </if>
    </if>
    <if test="activityId!=null">
      and b.activityId = #{activityId}
    </if>
  </select>

  <!--通过充值记录Id获取充值记录-->
  <select id="findById" resultMap="BaseResultMap">
    select
    <include refid="BaseColumnList"/>
    , u.username as cusName, u.phone as cusPhone
    from t_pay_bill b
    left join t_r_user u on u.id = b.user_id
    where b.id=#{id}
  </select>

  <!--通过充值记录订单号获取充值记录-->
  <select id="findByOrderId" resultMap="BaseResultMap">
    select
    <include refid="BaseColumnList"/>
    , u.username as cusName, u.phone as cusPhone
    from t_pay_bill b
    left join t_r_user u on u.id = b.user_id
    where b.order_id=#{orderId}
    <if test="lock == true">
      for update
    </if>
  </select>

  <!--数据统计-->
  <select id="payBillBi" parameterType="com.cdz360.biz.model.trading.order.po.PayBillPo"
    resultMap="BiResultMap">
    SELECT
    count( * ) AS num,
    b.flowType AS flowType,
    COALESCE(sum( b.amount + b.freeAmount ), 0) AS total,
    COALESCE(sum( b.amount ), 0) AS costTotal,
    COALESCE(sum( b.freeAmount ), 0) AS freeTotal,
    count( CASE WHEN b.`status`=1 OR b.`status`=3 THEN 1 ELSE null END ) AS completedNum,
    sum( CASE WHEN b.`status`=1 OR b.`status`=3 THEN (b.amount + b.freeAmount) ELSE 0 END ) AS
    arrivalTotal
    FROM t_pay_bill b
    left join t_r_user u on u.id = b.user_id
    left join t_r_commercial c on c.id = b.comm_id
    left join t_r_commercial accComm on accComm.id = b.accountCode and b.accountType = 3
    left join t_r_corp corp on corp.uid = b.user_id and b.accountType = 5
    where (accountType=1 or accountType=3 or accountType=5)
    <!-- 充值成功才被统计 -->
    and b.status in (1,6)
    <include refid="SelectSql"/>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(cusName)">
      and u.username like
      CONCAT('%',#{cusName},'%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(cusPhone)">
      and u.phone like
      CONCAT('%',#{cusPhone},'%')
    </if>
    <if test="accountType != null">
      and b.accountType = #{accountType.code}

      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(accCorpName)">
        and corp.corpName like
        CONCAT('%',#{accCorpName},'%')
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(accCommName)">
        and accComm.comm_name like
        CONCAT('%',#{accCommName},'%')
      </if>
    </if>
    GROUP BY flowType
  </select>

  <!--getPayBillList-->
  <select id="getPayBillList" resultMap="BaseResultMap">
    select
    <include refid="BaseColumnList"/>
    from t_pay_bill b
    <where>
      <if test="orderIdList != null and orderIdList.size() > 0">
        b.order_id in
        <foreach collection="orderIdList" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

  <select id="getPayBillOfRefBillNo" resultMap="BaseResultMap">
    select
    <include refid="BaseColumnList"/>
    from t_pay_bill b
    where b.refBillNo = #{orderId} and b.`status`!=2 and flowType = 2
  </select>

  <select id="refundAnalyze" resultType="com.cdz360.biz.model.trading.order.vo.RefundReasonCountVo">
    select
    bill.`cusNote`,
    count(*) as cusNoteCount
    from
    d_charger.t_pay_bill bill
    inner join d_charger.t_r_user u on
    bill.user_id = u.id
    where
    bill.`flowType` = #{flowType}
    and bill.notify_type = #{notifyType}
    and bill.`cusNote` is not null
    <if test="startDate != null and stopDate != null">
      <![CDATA[ and bill.create_time >= #{startDate} ]]>
      <![CDATA[ and bill.create_time  < #{stopDate} ]]>
    </if>
    group by
    bill.`cusNote`
  </select>

  <select id="refundList" resultType="com.cdz360.biz.model.wallet.vo.RefundReasonVo">
    select
    bill.order_id as refundNo,
    bill.user_id as cusId,
    bill.amount as refundAmount,
    u.username as cusName,
    u.phone as cusPhone,
    bill.create_time as createTime,
    case
    bill.status
    when 1 then "退款成功"
    when 2 then "退款失败"
    else "退款中"
    end as refundStatus,
    bill.`cusNote` as refundReason
    from
    d_charger.t_pay_bill bill
    inner join d_charger.t_r_user u on
    bill.user_id = u.id
    where
    bill.`flowType` = #{flowType}
    and bill.notify_type = #{notifyType}
    and bill.`cusNote` is not null
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( cusName )">
      and u.username like CONCAT("%" , #{cusName}, "%")
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( cusPhone )">
      and u.phone like CONCAT("%" , #{cusPhone}, "%")
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( cusNote )">
      and bill.`cusNote` like CONCAT("%" , #{cusNote}, "%")
    </if>
    order by
    bill.create_time desc
    limit #{start}, #{size}
  </select>

  <select id="refundListCount" resultType="java.lang.Long">
    select
    count(bill.order_id)
    from
    d_charger.t_pay_bill bill
    inner join d_charger.t_r_user u on
    bill.user_id = u.id
    where
    bill.`flowType` = #{flowType}
    and bill.notify_type = #{notifyType}
    and bill.`cusNote` is not null
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( cusName )">
      and u.username like CONCAT("%" , #{cusName}, "%")
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( cusPhone )">
      and u.phone like CONCAT("%" , #{cusPhone}, "%")
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( cusNote )">
      and bill.`cusNote` like CONCAT("%" , #{cusNote}, "%")
    </if>
    order by
    bill.create_time desc
  </select>

  <select id="invoiceBi4Master"
    parameterType="com.cdz360.biz.model.trading.order.param.PayBillParam"
    resultMap="InvoiceBiMap">
    /*FORCE_MASTER*/
    select
    b.order_id,
    b.amount,
    b.freeAmount,
    b.sourceType,
    b.pay_time,
    (b.amount + b.freeAmount) totalAmount,
    (b.amount - tmp.subAmount) canInvoiceAmount
    from t_pay_bill b
    left join t_r_user u on u.id = b.user_id
    left join t_r_commercial c on c.id = b.comm_id

    <if test="null != inCorpInvoice and inCorpInvoice">
      left join t_invoice_record_order_ref ref on ref.orderNo = b.order_id and ref.invoiceWay =
      'PRE_PAY'
    </if>

    left join (
    select b.order_id orderId, IFNULL(sum(refbill.amount), 0) subAmount
    from d_charger.t_pay_bill b
    left join t_r_user u on u.id = b.user_id
    left join t_r_commercial c on c.id = b.comm_id
    left join t_pay_bill refbill on b.order_id = refbill.refBillNo
    <if test="null != inCorpInvoice and inCorpInvoice">
      left join t_invoice_record_order_ref ref on ref.orderNo = b.order_id and ref.invoiceWay =
      'PRE_PAY'
    </if>

    where 1=1
    <include refid="SelectSql"/>

    <if test="payTimeFilter != null">
      <![CDATA[ and b.pay_time >= #{payTimeFilter.startTime} ]]>
      <![CDATA[ and b.pay_time < #{payTimeFilter.endTime} ]]>
    </if>

    <if test="null != inCorpInvoice and inCorpInvoice">
      and b.status in (1, 6)
      <choose>
        <when test="@com.cdz360.base.utils.StringUtils@isNotBlank(applyNo)">
          and ref.applyNo = #{applyNo}
        </when>
        <otherwise>
          and b.invoicedAmount = 0 and ref.orderNo is null
        </otherwise>
      </choose>
    </if>

    group by b.order_id
    ) tmp on tmp.orderId = b.order_id
    where 1=1
    <include refid="SelectSql"/>

    <if test="payTimeFilter != null">
      <![CDATA[ and b.pay_time >= #{payTimeFilter.startTime} ]]>
      <![CDATA[ and b.pay_time < #{payTimeFilter.endTime} ]]>
    </if>

    <if test="null != inCorpInvoice and inCorpInvoice">
      and b.status in (1, 6)
      <choose>
        <when test="@com.cdz360.base.utils.StringUtils@isNotBlank(applyNo)">
          and ref.applyNo = #{applyNo}
        </when>
        <otherwise>
          and b.invoicedAmount = 0 and ref.orderNo is null
        </otherwise>
      </choose>
    </if>

    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <when test="size != null">
        limit #{size}
      </when>
    </choose>
  </select>

  <select id="invoiceBi"
    parameterType="com.cdz360.biz.model.trading.order.param.PayBillParam"
    resultMap="InvoiceBiMap">
    select
    b.order_id,
    b.amount,
    b.freeAmount,
    b.sourceType,
    b.pay_time,
    (b.amount + b.freeAmount) totalAmount,
    (b.amount - tmp.subAmount) canInvoiceAmount
    from t_pay_bill b
    left join t_r_user u on u.id = b.user_id
    left join t_r_commercial c on c.id = b.comm_id

    <if test="null != inCorpInvoice and inCorpInvoice">
      left join t_invoice_record_order_ref ref on ref.orderNo = b.order_id and ref.invoiceWay =
      'PRE_PAY'
    </if>

    left join (
    select b.order_id orderId, IFNULL(sum(refbill.amount), 0) subAmount
    from d_charger.t_pay_bill b
    left join t_r_user u on u.id = b.user_id
    left join t_r_commercial c on c.id = b.comm_id
    left join t_pay_bill refbill on b.order_id = refbill.refBillNo
    <if test="null != inCorpInvoice and inCorpInvoice">
      left join t_invoice_record_order_ref ref on ref.orderNo = b.order_id and ref.invoiceWay =
      'PRE_PAY'
    </if>

    where 1=1
    <include refid="SelectSql"/>

    <if test="payTimeFilter != null">
      <![CDATA[ and b.pay_time >= #{payTimeFilter.startTime} ]]>
      <![CDATA[ and b.pay_time < #{payTimeFilter.endTime} ]]>
    </if>

    <if test="null != inCorpInvoice and inCorpInvoice">
      and b.status in (1, 6)
      <choose>
        <when test="@com.cdz360.base.utils.StringUtils@isNotBlank(applyNo)">
          and ref.applyNo = #{applyNo}
        </when>
        <otherwise>
          and b.invoicedAmount = 0 and ref.orderNo is null
        </otherwise>
      </choose>
    </if>

    group by b.order_id
    ) tmp on tmp.orderId = b.order_id
    where 1=1
    <include refid="SelectSql"/>

    <if test="payTimeFilter != null">
      <![CDATA[ and b.pay_time >= #{payTimeFilter.startTime} ]]>
      <![CDATA[ and b.pay_time < #{payTimeFilter.endTime} ]]>
    </if>

    <if test="null != inCorpInvoice and inCorpInvoice">
      and b.status in (1, 6)
      <choose>
        <when test="@com.cdz360.base.utils.StringUtils@isNotBlank(applyNo)">
          and ref.applyNo = #{applyNo}
        </when>
        <otherwise>
          and b.invoicedAmount = 0 and ref.orderNo is null
        </otherwise>
      </choose>
    </if>

    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( sorts )">
      <foreach item="sort" collection="sorts"
        open="order by" separator="," close=" ">
        ${sort.columnsString} ${sort.order}
      </foreach>
    </if>

    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <when test="size != null">
        limit #{size}
      </when>
    </choose>
  </select>
  <select id="countInvoiceBi" resultType="java.lang.Long">
    select count(b.order_id)
    from t_pay_bill b
    left join t_r_user u on u.id = b.user_id
    left join t_r_commercial c on c.id = b.comm_id

    <if test="null != inCorpInvoice and inCorpInvoice">
      left join t_invoice_record_order_ref ref on ref.orderNo = b.order_id and ref.invoiceWay =
      'PRE_PAY'
    </if>

    where 1=1
    <include refid="SelectSql"/>

    <if test="payTimeFilter != null">
      <![CDATA[ and b.pay_time >= #{payTimeFilter.startTime} ]]>
      <![CDATA[ and b.pay_time < #{payTimeFilter.endTime} ]]>
    </if>

    <if test="null != inCorpInvoice and inCorpInvoice">
      and b.status in (1, 6)
      <choose>
        <when test="@com.cdz360.base.utils.StringUtils@isNotBlank(applyNo)">
          and ref.applyNo = #{applyNo}
        </when>
        <otherwise>
          and b.invoicedAmount = 0 and ref.orderNo is null
        </otherwise>
      </choose>
    </if>
  </select>

  <select id="findInvoicePayBill"
    resultType="com.cdz360.biz.model.trading.order.dto.TaxOrderDto">
    select tpb.order_id orderNo, ir.id invoiceId, ir.invoiced_status invoicedStatus, ir.invoice_type
    invoiceType, ir.invoice_code invoiceCode, ir.invoice_number invoiceNumber
    from t_pay_bill tpb
    left join t_invoice_record_order_ref ror on tpb.order_id = ror.orderNo and ror.invoiceWay =
    'PRE_PAY'
    left join invoiced_record ir on ir.applyNo = ror.applyNo
    where ror.applyNo is not null and tpb.order_id IN
    <foreach item="item" index="index" collection="orderIdList"
      open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
  <select id="userBillAccountName"
    resultType="com.cdz360.biz.model.trading.order.vo.UserBillAccountNameVo">
    SELECT
    user_id as userId,
    group_concat( DISTINCT inAccountName ) inAccountName,
    group_concat( DISTINCT outAccountName ) outAccountName
    FROM
    t_pay_bill AS bill

    <choose>
      <when test="null != userType and userType == 'group'">
        LEFT JOIN t_r_corp corp on bill.user_id = corp.uid where bill. sourceType = 20 and corp.id =
        #{userId}
      </when>
      <otherwise>
        where sourceType = 20 AND user_id = #{userId}
      </otherwise>
    </choose>

  </select>
  <select id="zftBillList2"
    parameterType="com.cdz360.biz.model.trading.order.param.ZftBillParam"
    resultMap="ZftOrderVo_MAP">
    select
    bill.order_id orderId, bill.charge_order_no chargeOrderNo, bill.trade_no tradeNo, bill.amount
    tradeAmount,
    bill.flowType tradeType, bill.checkResult, bill.pay_time payTime, bill.zftName, comm.comm_name
    commName, u.phone,
    dailyBill.name dailyBillName,
    CASE WHEN bill.accountType = 5 THEN "企业账户"
    WHEN bill.accountType = 4 THEN "第三方支付"
    WHEN bill.accountType =3 THEN concat("商户会员","-", accComm.short_name)
    WHEN bill.accountType = 1 THEN "个人账户"
    WHEN bill.accountType = 7 THEN "微信支付分"
    WHEN bill.accountType = 8 THEN "支付宝芝麻信用"
    WHEN bill.accountType = 999 AND bill.sourceType = 0 THEN "功能订阅"
    ELSE "未知"
    END AS accountType,
    CASE WHEN bill.flowInAccountType = 1 THEN "支付宝"
    ELSE "微信"
    END AS payWay,
    CASE WHEN bill.accountType = 4 THEN "即充即退充电"
    WHEN bill.accountType in (1, 3, 5) THEN "在线充值"
    WHEN bill.accountType in (7, 8) THEN "免充值先充电"
    WHEN bill.accountType = 999 AND bill.sourceType = 0 THEN "功能订阅"
    ELSE "未知"
    END AS financialType
    from t_pay_bill bill
    <if test="payTimeFilter != null">
      force index(t_pay_bill_pay_time)
    </if>
    left join t_r_commercial comm on comm.id = bill.comm_id
    left join t_r_user u on u.id = bill.user_id
    LEFT JOIN t_r_commercial accComm on bill.accountCode = accComm.id
    left join t_zft_daily_bill dailyBill on dailyBill.id = bill.dailyBillId
    where bill.status in (1,6)
    and bill.sourceType IN (0, 2, 4, 6, 8, 24 )
    and bill.zftName != ''
    and bill.zftName is not null
    <include refid="zftSql2"/>
    order by bill.id desc
    limit #{start}, #{size}
  </select>
  <select id="zftBillList" resultType="com.cdz360.biz.model.trading.order.vo.ZftBillVo">
    SELECT
    a.id,
    a.order_id AS orderId,
    a.charge_order_no as chargeOrderNo,
    any_value ( a.zftName ) AS zftName,
    any_value ( a.trade_no ) AS tradeNo,
    any_value ( a.pay_time ) AS payTime,
    any_value ( c.comm_name ) AS commercialName,
    any_value ( u.phone ) phone,
    sum(ifnull( a.amount, 0 )) AS chargeAmount,
    sum(ifnull( b.amount, 0 )) AS refundAmount,
    (sum(ifnull( a.amount, 0 ))-sum(ifnull( b.amount, 0 ))) AS realAmount,
    CASE
    WHEN any_value ( a.accountType ) = 5 THEN
    "企业账户"
    WHEN any_value ( a.accountType ) = 4 THEN
    "第三方支付"
    WHEN any_value(a.accountType) =3 THEN
    concat("商户会员","-",any_value(trc.short_name))
    WHEN any_value ( a.accountType ) = 1
    AND a.flowType = 1 THEN
    "个人账户" ELSE "未知"
    END AS accountType,
    CASE
    WHEN any_value ( a.flowInAccountType ) = 1 THEN
    "支付宝" ELSE "微信"
    END AS payWay,
    CASE
    WHEN any_value ( a.accountType ) = 4 THEN
    "即充即退充电"
    WHEN any_value ( a.accountType ) = 1
    OR any_value ( a.accountType ) = 3
    OR any_value ( a.accountType ) = 5 THEN
    "在线充值" ELSE "未知"
    END AS financialType
    FROM
    t_pay_bill a
    LEFT JOIN t_pay_bill b ON b.refBillNo = a.order_id
    LEFT JOIN t_r_user u ON u.id = a.user_id
    LEFT JOIN t_r_commercial c ON c.id = a.comm_id
    LEFT JOIN t_r_commercial trc on a.accountCode = trc.id
    WHERE
    a.STATUS in (1,6)
    AND a.sourceType IN ( 2, 4, 6, 8, 24 )
    AND a.flowType = 1
    AND a.zftName != ''
    <include refid="zftSql"/>
    GROUP BY
    a.id
    order by a.id desc

    limit #{start},#{size}
  </select>
  <!--直付通记录统计-->
  <select id="zftBillBi" resultType="com.cdz360.biz.model.trading.order.vo.ZftBillBi">
    SELECT
    a.flowInAccountType ,
    a.flowType ,
    ifnull(CASE WHEN a.flowType = 1 THEN count( a.order_id ) END, 0) as chargeOrderNum,
    ifnull(CASE WHEN a.flowType = 2 THEN count( a.order_id ) END, 0) AS refundOrderNum,
    ifnull(CASE WHEN a.flowType = 1 THEN sum(ifnull(a.amount,0)) end, 0) as chargeAmount,
    ifnull(CASE WHEN a.flowType = 2 THEN sum(ifnull( a.amount,0) ) end, 0) as refundAmount
    <!--            (sum(ifnull(a.amount,0))-sum(ifnull(b.amount,0))) as realAmount-->
    FROM
    t_pay_bill a
    <!--            LEFT JOIN t_pay_bill b ON a.order_id = b.refBillNo-->
    LEFT JOIN t_r_user u ON u.id = a.user_id
    LEFT JOIN t_r_commercial c ON c.id = a.comm_id
    left join t_zft_daily_bill dailyBill on dailyBill.id = a.dailyBillId
    WHERE
    <!--            a.flowType = 1-->
    <!--            AND -->
    a.STATUS in (1,6)
    AND a.sourceType IN ( 0, 2, 4, 6, 8, 24 )
    and a.zftName != ''
    and a.zftName is not null
    <include refid="zftSql"/>
    GROUP BY
    a.flowInAccountType,
    a.flowType
  </select>
  <select id="zftBillCount2" resultType="java.lang.Long">
    select
    count(bill.order_id)
    from t_pay_bill bill
    <if test="payTimeFilter != null">
      force index(t_pay_bill_pay_time)
    </if>
    left join t_r_commercial comm on comm.id = bill.comm_id
    left join t_r_user u on u.id = bill.user_id
    LEFT JOIN t_r_commercial accComm on bill.accountCode = accComm.id
    left join t_zft_daily_bill dailyBill on dailyBill.id = bill.dailyBillId
    where bill.status in (1,6)
    and bill.sourceType IN ( 2, 4, 6, 8, 24 )
    and bill.zftName != ''
    and bill.zftName is not null
    <include refid="zftSql2"/>
    order by bill.id desc
  </select>
  <select id="zftBillCount" resultType="java.lang.Long">
    SELECT
    count(distinct a.order_id)
    FROM
    t_pay_bill a
    LEFT JOIN t_r_user u ON u.id = a.user_id
    LEFT JOIN t_r_commercial c ON c.id = a.comm_id
    WHERE
    a.STATUS in (1,6)
    AND a.sourceType IN ( 2, 4, 6, 8 )
    AND a.flowType = 1
    AND a.zftName != ''
    <include refid="zftSql"/>
  </select>
  <select id="findByOutRefundNo" resultMap="BaseResultMap">
    select
    <include refid="BaseColumnList"/>
    , u.username as cusName, u.phone as cusPhone
    from t_pay_bill b
    left join t_r_user u on u.id = b.user_id
    where b.out_refund_no = #{outRefundNo}
    limit 1
  </select>


  <select id="queryPayInfo" resultType="com.cdz360.biz.model.trading.order.vo.PayInfo">
    SELECT
    bill.charge_order_no AS orderNo,
    bill.order_id AS payOrderNo,
    refund.thirdTradeNo AS refundOrderNo
    FROM
    t_pay_bill bill
    LEFT JOIN t_charger_order_refund refund ON bill.charge_order_no = refund.orderNo
    WHERE
    bill.flowType = 1
    <!--AND bill.STATUS = 1-->
    <if test="orderNoList!=null and orderNoList.size() > 0">
      AND bill.charge_order_no in
      <foreach item="item" index="index" collection="orderNoList"
        open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>

  </select>
  <select id="invoiceOrderBiForCorp"
    resultType="com.chargerlinkcar.framework.common.domain.vo.OrderBiVo">
    select
    count(b.order_id) as orderAmount,
    sum(b.amount + b.freeAmount) totalAmount,
    sum(b.amount - tmp.subAmount) invoiceAmount
    from t_pay_bill b
    left join t_r_user u on u.id = b.user_id
    left join t_r_commercial c on c.id = b.comm_id

    <if test="null != inCorpInvoice and inCorpInvoice">
      left join t_invoice_record_order_ref ref on ref.orderNo = b.order_id and ref.invoiceWay =
      'PRE_PAY'
    </if>

    left join (
    select b.order_id orderId, IFNULL(sum(refbill.amount), 0) subAmount
    from d_charger.t_pay_bill b
    left join t_r_user u on u.id = b.user_id
    left join t_r_commercial c on c.id = b.comm_id
    left join t_pay_bill refbill on b.order_id = refbill.refBillNo
    <if test="null != inCorpInvoice and inCorpInvoice">
      left join t_invoice_record_order_ref ref on ref.orderNo = b.order_id and ref.invoiceWay =
      'PRE_PAY'
    </if>

    where 1=1
    <include refid="SelectSql"/>

    <if test="payTimeFilter != null">
      <![CDATA[ and b.pay_time >= #{payTimeFilter.startTime} ]]>
      <![CDATA[ and b.pay_time < #{payTimeFilter.endTime} ]]>
    </if>

    <if test="null != inCorpInvoice and inCorpInvoice">
      and b.status in (1, 6)
      <choose>
        <when test="@com.cdz360.base.utils.StringUtils@isNotBlank(applyNo)">
          and ref.applyNo = #{applyNo}
        </when>
        <otherwise>
          and b.invoicedAmount = 0 and ref.orderNo is null
        </otherwise>
      </choose>
    </if>

    group by b.order_id
    ) tmp on tmp.orderId = b.order_id
    where 1=1
    <include refid="SelectSql"/>

    <if test="payTimeFilter != null">
      <![CDATA[ and b.pay_time >= #{payTimeFilter.startTime} ]]>
      <![CDATA[ and b.pay_time < #{payTimeFilter.endTime} ]]>
    </if>

    <if test="null != inCorpInvoice and inCorpInvoice">
      and b.status in (1, 6)
      <choose>
        <when test="@com.cdz360.base.utils.StringUtils@isNotBlank(applyNo)">
          and ref.applyNo = #{applyNo}
        </when>
        <otherwise>
          and b.invoicedAmount = 0 and ref.orderNo is null
        </otherwise>
      </choose>
    </if>
  </select>

  <select id="selectByChargeOrderNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="BaseColumnList"/>
    from t_pay_bill b
    where b.charge_order_no = #{orderNo,jdbcType=VARCHAR}
    <if test="flowType != null">
      and b.flowType = #{flowType.code}
    </if>
    <!-- 这里订单详情显示有问题，暂时这样调整 -->
    limit 1
  </select>

    <select id="selectByChargeOrderNoList" resultType="java.lang.String">
        select
            order_id
        from
            t_pay_bill
        where
            charge_order_no in
        <foreach collection="orderNoList" item="orderNo"
          open="(" separator="," close=")">
            #{orderNo}
        </foreach>
        <if test="flowType != null">
            and b.flowType = #{flowType.code}
        </if>
    </select>

    <select id="invoicingDataValidityCheck" resultType="java.lang.Long">
        select
            count(order_id)
        from
            t_pay_bill
        where
            order_id in
        <foreach item="item" index="index" collection="czOrderIds"
          open="(" separator="," close=")">
            #{item}
        </foreach>
            and (amount - outFlowAmount) >= invoicedAmount
    </select>

</mapper>