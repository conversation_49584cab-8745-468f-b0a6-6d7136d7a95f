package com.cdz360.biz.ds.trading.ro.coupon.ds;

import com.cdz360.biz.model.trading.coupon.po.ActivityCouponPo;
import com.cdz360.biz.ds.trading.ro.coupon.mapper.ActivityCouponRoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class ActivityCouponRoDs {

	@Autowired
	private ActivityCouponRoMapper activityCouponRoMapper;

	public ActivityCouponPo getById(Long id) {
		return this.activityCouponRoMapper.getById(id);
	}

	public List<ActivityCouponPo> getListByActivityId(Long activityId) {
		return this.activityCouponRoMapper.getListByActivityId(activityId);
	}
}
