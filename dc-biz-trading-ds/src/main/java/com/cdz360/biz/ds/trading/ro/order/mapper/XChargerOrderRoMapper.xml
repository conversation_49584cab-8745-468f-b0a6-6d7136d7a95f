<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.trading.ro.order.mapper.XChargerOrderRoMapper">

  <sql id="QUERY_FIELDS">
    o.*,
    IFNULL(o.frozenAmount, 0) frozenAmount,
    pay.*,
    (pay.elecCostFee+pay.servCostFee) as principal_amount,
    (pay.elecFreeFee+pay.servFreeFee) as free_gold_amount,
    ocar.*,
    site.name as station_name,
    site.siteNo
  </sql>

  <sql id="QUERY_FIELDS_EX">
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(corpOrgIds)">
      ,tco.orgName AS corpOrgName
    </if>
    ,box.box_name
    ,plug.charger_name
    ,cou.id as couponId,
    cou.amount as couponAmount,
    oo.parkingFee,
    ztohb.checkResult,
    ztohb.zftTotalMoney
  </sql>

  <sql id="ORDER_CONDITION">
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( orderNo )">
      AND (o.order_no = #{orderNo})
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( orderNoLike )">
      AND (o.order_no LIKE CONCAT('%', #{orderNoLike}, '%'))
    </if>
  </sql>

  <sql id="ORDER_PAY_CONDITION">

  </sql>

  <sql id="ORDER_CAR_CONDITION">

  </sql>

  <sql id="WHERE_SQL">
    <where>
      <include refid="ORDER_CONDITION"/>
      <include refid="ORDER_PAY_CONDITION"/>
      <include refid="ORDER_CAR_CONDITION"/>
    </where>
  </sql>

  <sql id="SORTS_CONDITION">
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( sorts )">
      <foreach item="sort" collection="sorts"
        open="order by" separator="," close=" ">
        ${sort.columnsString} ${sort.order}
      </foreach>
    </if>
  </sql>

  <sql id="LIMIT_CHOOSE">
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <when test="size != null">
        limit #{size}
      </when>
    </choose>
  </sql>

  <select id="queryChargeOrderList"
    parameterType="com.cdz360.biz.model.trading.order.param.ListChargerOrderParamX"
    resultType="com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo">
    select
    <include refid="QUERY_FIELDS"/>
    ,
    <include refid="QUERY_FIELDS_EX"/>
    from t_charger_order o
    <include refid="WHERE_SQL"/>
    <include refid="SORTS_CONDITION"/>
    <include refid="LIMIT_CHOOSE"/>
  </select>
  <select id="countChargeOrder" resultType="java.lang.Long">
    select
    count(o.order_no)
    from t_charger_order o
    <include refid="WHERE_SQL"/>
  </select>
</mapper>
