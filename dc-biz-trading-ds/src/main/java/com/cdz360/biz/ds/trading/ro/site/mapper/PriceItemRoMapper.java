package com.cdz360.biz.ds.trading.ro.site.mapper;

import com.cdz360.biz.model.site.po.PriceItemPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PriceItemRoMapper {

    // not need 'for update'
    List<PriceItemPo> getPriceItemListByTempId(@Param(value = "tempId") Long tempId);

    List<PriceItemPo> getPriceItemList(@Param("idList") List<Long> idList);
}
