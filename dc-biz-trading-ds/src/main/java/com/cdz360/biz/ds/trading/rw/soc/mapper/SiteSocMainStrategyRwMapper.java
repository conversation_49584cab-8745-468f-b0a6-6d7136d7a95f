package com.cdz360.biz.ds.trading.rw.soc.mapper;

import com.cdz360.biz.model.trading.soc.po.SiteSocMainStrategyPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SiteSocMainStrategyRwMapper {
	SiteSocMainStrategyPo getById(@Param("id") Long id, @Param("lock") boolean lock);

	int insertSiteSocMainStrategy(SiteSocMainStrategyPo siteSocMainStrategyPo);

	int updateSiteSocMainStrategy(SiteSocMainStrategyPo siteSocMainStrategyPo);

	int deleteBySiteId(@Param("siteId") String siteId);
}
