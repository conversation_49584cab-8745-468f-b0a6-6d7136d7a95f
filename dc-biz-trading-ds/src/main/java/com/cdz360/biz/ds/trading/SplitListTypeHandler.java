package com.cdz360.biz.ds.trading;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

@Slf4j
@SuppressWarnings("ALL")
public class SplitListTypeHandler<E extends List<String>> extends BaseTypeHandler<E> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, E parameter, JdbcType jdbcType)
        throws SQLException {
        ps.setString(i, String.join(",", parameter));
    }

    @Override
    public E getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Object ordinal = rs.getObject(columnName);
        if (ordinal == null) {
            return null;
        } else if (ordinal instanceof String) {
            return (E) Arrays.asList(rs.getString(columnName).split(","));
        } else {
            log.error("不识别的类型. {}/{}", ordinal, ordinal.getClass());
            return null;
        }
    }

    @Override
    public E getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Object ordinal = rs.getObject(columnIndex);
        if (ordinal == null) {
            return null;
        } else if (ordinal instanceof String) {
            return (E) Arrays.asList(rs.getString(columnIndex).split(","));
        } else {
            log.error("不识别的类型. {}/{}", ordinal, ordinal.getClass());
            return null;
        }
    }

    @Override
    public E getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Object ordinal = cs.getObject(columnIndex);
        if (ordinal == null) {
            return null;
        } else if (ordinal instanceof String) {
            return (E) Arrays.asList(cs.getString(columnIndex).split(","));
        } else {
            log.error("不识别的类型. {}/{}", ordinal, ordinal.getClass());
            return null;
        }
    }
}
