package com.cdz360.biz.ds.trading.ro.prerun.ds;

import com.cdz360.biz.ds.trading.ro.prerun.mapper.PrerunAssetImgRoMapper;
import com.cdz360.biz.model.trading.prerun.po.PrerunAssetImgPo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PrerunAssetImgRoDs {

	@Autowired
	private PrerunAssetImgRoMapper prerunAssetImgRoMapper;

	public PrerunAssetImgPo getById(Long id) {
		return this.prerunAssetImgRoMapper.getById(id);
	}

	public List<PrerunAssetImgPo> getByPrerunId(Long prerunId) {
		return this.prerunAssetImgRoMapper.getByPrerunId(prerunId);
	}
}
