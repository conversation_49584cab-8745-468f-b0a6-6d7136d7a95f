package com.cdz360.biz.ds.trading.rw.bill.mapper;


import com.cdz360.biz.model.trading.bill.po.ZftThirdOrderHlhtBiPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper

public interface ZftThirdOrderHlhtBiRwMapper {

	ZftThirdOrderHlhtBiPo getByOpenOrderId(@Param("openOrderId") String openOrderId, @Param("lock") boolean lock);



	int insertZftThirdOrderHlhtBiBill(ZftThirdOrderHlhtBiPo zftThirdOrderHlhtBiPo);



	int updateZftThirdOrderHlhtBiBill(ZftThirdOrderHlhtBiPo zftThirdOrderHlhtBiPo);





}

