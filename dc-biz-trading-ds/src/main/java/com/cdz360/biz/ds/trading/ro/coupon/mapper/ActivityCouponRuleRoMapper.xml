<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.trading.ro.coupon.mapper.ActivityCouponRuleRoMapper">

	<resultMap id="RESULT_ACTIVITYCOUPONRULE_PO" type="com.cdz360.biz.model.trading.coupon.po.ActivityCouponRulePo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="timeFrom" jdbcType="TIMESTAMP" property="timeFrom" />
		<result column="timeTo" jdbcType="TIMESTAMP" property="timeTo" />
		<result column="activityId" jdbcType="BIGINT" property="activityId" />
		<result column="repeatActive" jdbcType="BOOLEAN" property="repeatActive"/>
		<result column="couponsPerTime" jdbcType="BIGINT" property="couponsPerTime" />
		<result column="maxAmountPerTime" jdbcType="BIGINT" property="maxAmountPerTime" />
		<result column="totalAmount" jdbcType="BIGINT" property="totalAmount" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
		<result column="enable" jdbcType="BOOLEAN" property="enable"/>
	</resultMap>

	<select id="getListByActivityId"
			resultMap="RESULT_ACTIVITYCOUPONRULE_PO">
		select * from t_activity_coupon_rule where activityId = #{activityId} and enable = 1
	</select>

	<select id="getListByActivityIdList"
			resultMap="RESULT_ACTIVITYCOUPONRULE_PO">
		select * from t_activity_coupon_rule where activityId in
			<foreach collection="activityIdList" item="item" index="index"
				open="(" separator="," close=")">
				#{item}
			</foreach>
		and enable = 1
	</select>

</mapper>
