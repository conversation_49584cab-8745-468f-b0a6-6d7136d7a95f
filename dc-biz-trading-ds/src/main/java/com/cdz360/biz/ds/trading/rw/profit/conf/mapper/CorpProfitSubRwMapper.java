package com.cdz360.biz.ds.trading.rw.profit.conf.mapper;

import com.cdz360.biz.model.trading.profit.conf.po.CorpProfitSubPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Mapper
public interface CorpProfitSubRwMapper {
	CorpProfitSubPo getById(@Param("id") Long id, @Param("lock") boolean lock);

	int insertCorpProfitSub(CorpProfitSubPo corpProfitSubPo);

	int batchInsertCorpProfitSub(@Param("corpProfitSubPoList") List<CorpProfitSubPo> corpProfitSubPoList);

	int updateCorpProfitSub(CorpProfitSubPo corpProfitSubPo);


    int disableByBaseId(@Param("baseId") Long baseId);
}
