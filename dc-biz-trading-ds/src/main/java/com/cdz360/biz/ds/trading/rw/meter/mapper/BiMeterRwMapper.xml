<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.trading.rw.meter.mapper.BiMeterRwMapper">

	<resultMap id="RESULT_BIMETER_PO" type="com.cdz360.biz.model.trading.meter.po.BiMeterPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="date" jdbcType="TIMESTAMP" property="date" />
		<result column="siteId" jdbcType="VARCHAR" property="siteId" />
		<result column="meterId" jdbcType="BIGINT" property="meterId" />
		<result column="electricity" jdbcType="DECIMAL" property="electricity" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_BIMETER_PO">	
		select * from t_bi_meter where id = #{id}
		<if test="lock == true">
			for update
		</if>
	</select>

	<insert id="insertBiMeter" useGeneratedKeys="true" keyProperty="id"
		keyColumn="id" parameterType="com.cdz360.biz.model.trading.meter.po.BiMeterPo">
		insert into t_bi_meter (`date`,
			`siteId`,
			`meterId`,
			`electricity`,
			`createTime`,
			`updateTime`)
		values (#{date},
			#{siteId},
			#{meterId},
			#{electricity},
			now(),
			now())
	</insert>

	<update id="updateBiMeter" parameterType="com.cdz360.biz.model.trading.meter.po.BiMeterPo">
		update t_bi_meter set
		<if test="date != null">
			date = #{date},
		</if>
		<if test="siteId != null">
			siteId = #{siteId},
		</if>
		<if test="meterId != null">
			meterId = #{meterId},
		</if>
		<if test="electricity != null">
			electricity = #{electricity},
		</if>
		updateTime = now()
		where id = #{id}
	</update>

	<insert id="batchInsert" parameterType="java.util.List">
		insert into t_bi_meter
		(
		`date`,
		`siteId`,
		`meterId`,
		`electricity`,
		`createTime`,
		`updateTime`
		)
		values
		<foreach collection="list" item="item" index="index" separator=",">
			(
			#{item.date},
			#{item.siteId},
			#{item.meterId},
			#{item.electricity},
			now(),
			now()
			)
		</foreach>
	</insert>


</mapper>
