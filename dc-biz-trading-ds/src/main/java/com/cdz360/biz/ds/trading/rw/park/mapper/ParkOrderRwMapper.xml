<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.trading.rw.park.mapper.ParkOrderRwMapper">

	<resultMap id="RESULT_PARKORDER_PO" type="com.cdz360.biz.model.trading.park.po.ParkOrderPo">
		<id column="id" jdbcType="INTEGER" property="id" />
		<result column="status" jdbcType="JAVA_OBJECT" property="status" />
		<result column="carNo" jdbcType="VARCHAR" property="carNo" />
		<result column="parkId" jdbcType="INTEGER" property="parkId" />
		<result column="inTime" jdbcType="TIMESTAMP" property="inTime" />
		<result column="outTime" jdbcType="TIMESTAMP" property="outTime" />
		<result column="parkOrderId" jdbcType="VARCHAR" property="parkOrderId" />
		<result column="chargeOrderNo" jdbcType="VARCHAR" property="chargeOrderNo" />
		<result column="duration" jdbcType="INTEGER" property="duration" />
		<result column="couponDuration" jdbcType="INTEGER" property="couponDuration" />
		<result column="carType" jdbcType="VARCHAR" property="carType" />
		<result column="payType" jdbcType="VARCHAR" property="payType" />
		<result column="totalAmount" jdbcType="DECIMAL" property="totalAmount" />
		<result column="reduceAmount" jdbcType="DECIMAL" property="reduceAmount" />
		<result column="pidAddr" jdbcType="LONGVARCHAR" property="pidAddr" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_PARKORDER_PO">	
		select * from t_park_order where id = #{id}
		<if test="lock == true">
			for update
		</if>
	</select>

	<insert id="insertParkOrder" useGeneratedKeys="true" keyProperty="id"
		keyColumn="id" parameterType="com.cdz360.biz.model.trading.park.po.ParkOrderPo">
		insert into t_park_order (`status`,
			`carNo`,
			`parkId`,
			`inTime`,
			`outTime`,
			`parkOrderId`,
			`chargeOrderNo`,
			`duration`,
			`couponDuration`,
			`carType`,
			`payType`,
			`totalAmount`,
			`reduceAmount`,
			`pidAddr`,
			`createTime`,
			`updateTime`)
		values (#{status},
			#{carNo},
			#{parkId},
			#{inTime},
			#{outTime},
			#{parkOrderId},
			#{chargeOrderNo},
			#{duration},
			#{couponDuration},
			#{carType},
			#{payType},
			#{totalAmount},
			#{reduceAmount},
			#{pidAddr},
			now(),
			now())
	</insert>

	<update id="updateParkOrder" parameterType="com.cdz360.biz.model.trading.park.po.ParkOrderPo">
		update t_park_order set
		<if test="status != null">
			status = #{status},
		</if>
		<if test="carNo != null">
			carNo = #{carNo},
		</if>
		<if test="parkId != null">
			parkId = #{parkId},
		</if>
		<if test="inTime != null">
			inTime = #{inTime},
		</if>
		<if test="outTime != null">
			outTime = #{outTime},
		</if>
		<if test="parkOrderId != null">
			parkOrderId = #{parkOrderId},
		</if>
		<if test="chargeOrderNo != null">
			chargeOrderNo = #{chargeOrderNo},
		</if>
		<if test="duration != null">
			duration = #{duration},
		</if>
		<if test="couponDuration != null">
			couponDuration = #{couponDuration},
		</if>
		<if test="carType != null">
			carType = #{carType},
		</if>
		<if test="payType != null">
			payType = #{payType},
		</if>
		<if test="totalAmount != null">
			totalAmount = #{totalAmount},
		</if>
		<if test="reduceAmount != null">
			reduceAmount = #{reduceAmount},
		</if>
		<if test="pidAddr != null">
			pidAddr = #{pidAddr},
		</if>
		updateTime = now()
		where id = #{id}
	</update>

</mapper>
