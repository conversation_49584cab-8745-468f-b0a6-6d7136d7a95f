<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.trading.ro.soc.mapper.UserSocStrategyRoMapper">

	<resultMap id="RESULT_USERSOCSTRATEGY_PO" type="com.cdz360.biz.model.trading.soc.po.UserSocStrategyPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="userId" jdbcType="BIGINT" property="userId" />
		<result column="strategyId" jdbcType="BIGINT" property="strategyId" />
		<result column="vinId" jdbcType="VARCHAR" property="vinId" />
		<result column="createTime" jdbcType="DATE" property="createTime" />
	</resultMap>

	<resultMap id="RESULT_MOVE_CORP_USERSOCSTRATEGY_PO"
			   extends="RESULT_USERSOCSTRATEGY_PO"
			   type="com.cdz360.biz.model.trading.soc.vo.MoveCorpUserSocStrategyVo">
		<result column="idChain" jdbcType="VARCHAR" property="idChain" />
		<result column="siteName" jdbcType="VARCHAR" property="siteName" />
		<result column="siteId" jdbcType="VARCHAR" property="siteId" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_USERSOCSTRATEGY_PO">	
		select * from t_user_soc_strategy where id = #{id}
	</select>

	<select id="getByCorpId"
			resultMap="RESULT_MOVE_CORP_USERSOCSTRATEGY_PO">
		select
			uss.*,
			trc.idChain,
			site.id as siteId,
			site.name as siteName
		from t_user_soc_strategy uss
			left join t_r_corp_user tru on tru.id = uss.userId
			left join t_soc_strategy tss on tss.id=uss.strategyId
			left join t_site site on site.id=tss.siteId
			left join t_r_commercial trc ON site.operate_id = trc.id
		where
			tru.corpId = #{corpId}
	</select>

	<select id="getByStrategyId"
			resultMap="RESULT_USERSOCSTRATEGY_PO">
		select * from t_user_soc_strategy where strategyId = #{strategyId}
	</select>

</mapper>
