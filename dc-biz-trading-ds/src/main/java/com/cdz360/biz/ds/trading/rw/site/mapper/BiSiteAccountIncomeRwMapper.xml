<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.trading.rw.site.mapper.BiSiteAccountIncomeRwMapper">

	<resultMap id="RESULT_BI_SITE_ACCOUNT_INCOME_PO" type="com.cdz360.biz.model.trading.site.po.BiSiteAccountIncomePo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="siteId" jdbcType="VARCHAR" property="siteId" />
		<result column="month" jdbcType="DATE" property="month" />
		<result column="accountType" jdbcType="INTEGER" property="accountType"
				typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
		<result column="settlementType" jdbcType="INTEGER" property="settlementType"
				typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
		<result column="accountId" jdbcType="INTEGER" property="accountId" />
		<result column="accountName" jdbcType="VARCHAR" property="accountName" />
		<result column="billNo" jdbcType="VARCHAR" property="billNo" />
		<result column="elec" jdbcType="DECIMAL" property="elec" />
		<result column="elecOriginFee" jdbcType="DECIMAL" property="elecOriginFee" />
		<result column="elecCostFee" jdbcType="DECIMAL" property="elecCostFee" />
		<result column="servCostFee" jdbcType="DECIMAL" property="servCostFee" />
		<result column="orderElecProfit" jdbcType="DECIMAL" property="orderElecProfit" />
		<result column="orderServProfit" jdbcType="DECIMAL" property="orderServProfit" />
		<result column="totalIncome" jdbcType="DECIMAL" property="totalIncome" />
		<result column="servProfit" jdbcType="DECIMAL" property="servProfit" />
		<result column="enable" jdbcType="BOOLEAN" property="enable" />
		<result column="createTime" jdbcType="DATE" property="createTime" />
		<result column="updateTime" jdbcType="DATE" property="updateTime" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_BI_SITE_ACCOUNT_INCOME_PO">	
		select * from t_bi_site_account_income where id = #{id}
		<if test="lock == true">
			for update
		</if>
	</select>

	<insert id="insertBiSiteAccountIncome" useGeneratedKeys="true" keyProperty="id"
		keyColumn="id" parameterType="com.cdz360.biz.model.trading.site.po.BiSiteAccountIncomePo">
		insert into t_bi_site_account_income (`siteId`,
			`month`,
			`accountType`,
			`settlementType`,
		accountId,
			`accountName`,
		`billNo`,
			`elec`,
			`elecOriginFee`,
			`elecCostFee`,
			`servCostFee`,
			`orderElecProfit`,
			`orderServProfit`,
			`totalIncome`,
			`servProfit`,
			`enable`,
			`createTime`,
			`updateTime`)
		values (#{siteId},
			#{month},
			#{accountType.code},
			#{settlementType.code},
		#{accountId},
			#{accountName},
		#{billNo},
			#{elec},
			#{elecOriginFee},
			#{elecCostFee},
			#{servCostFee},
			#{orderElecProfit},
			#{orderServProfit},
			#{totalIncome},
			#{servProfit},
			#{enable},
			now(),
			now())
	</insert>

	<update id="updateBiSiteAccountIncome" parameterType="com.cdz360.biz.model.trading.site.po.BiSiteAccountIncomePo">
		update t_bi_site_account_income set
		<if test="settlementType != null">
			settlementType = #{settlementType.code},
		</if>
		<if test="accountName != null">
			accountName = #{accountName},
		</if>
		<if test="elec != null">
			elec = #{elec},
		</if>
		<if test="elecOriginFee != null">
			elecOriginFee = #{elecOriginFee},
		</if>
		<if test="elecCostFee != null">
			elecCostFee = #{elecCostFee},
		</if>
		<if test="servCostFee != null">
			servCostFee = #{servCostFee},
		</if>
		<if test="orderElecProfit != null">
			orderElecProfit = #{orderElecProfit},
		</if>
		<if test="orderServProfit != null">
			orderServProfit = #{orderServProfit},
		</if>
		<if test="totalIncome != null">
			totalIncome = #{totalIncome},
		</if>
		<if test="servProfit != null">
			servProfit = #{servProfit},
		</if>
		<if test="enable != null">
			enable = #{enable},
		</if>
		updateTime = now()
		where id = #{id}
	</update>


	<select id="getBySiteMonth"
			resultMap="RESULT_BI_SITE_ACCOUNT_INCOME_PO">
		select * from t_bi_site_account_income
		where siteId = #{siteId}
		and YEAR(month) = #{year}
		and MONTH(month) = #{month}
		and accountType = #{accountType.code}
		and accountId = #{accountId}
		and billNo = #{billNo}
		<if test="lock == true">
			for update
		</if>
	</select>
</mapper>
