package com.cdz360.biz.ds.trading.rw.site.ds;

import com.cdz360.biz.ds.trading.rw.site.mapper.BiSiteMonthRwMapper;
import com.cdz360.biz.model.trading.site.po.BiSiteMonthPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SiteDailyDataRwDs {

    @Autowired
    private BiSiteMonthRwMapper biSiteMonthRwMapper;

//    public List<BiSiteMonthPo> getLastSiteMonthData(String siteId, LocalDate date) {
//        return biSiteMonthRwMapper.getLastSiteMonthData(siteId, date);
//    }

    public boolean insertSiteDailyData(BiSiteMonthPo po) {
        return biSiteMonthRwMapper.insert(po) > 0;
    }
}
