<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.trading.rw.soc.mapper.UserSocStrategyRwMapper">

	<resultMap id="RESULT_USERSOCSTRATEGY_PO" type="com.cdz360.biz.model.trading.soc.po.UserSocStrategyPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="userId" jdbcType="BIGINT" property="userId" />
		<result column="strategyId" jdbcType="BIGINT" property="strategyId" />
		<result column="createTime" jdbcType="DATE" property="createTime" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_USERSOCSTRATEGY_PO">	
		select * from t_user_soc_strategy where id = #{id}
		<if test="lock == true">
			for update
		</if>
	</select>

	<insert id="insertUserSocStrategy" useGeneratedKeys="true" keyProperty="id"
		keyColumn="id" parameterType="com.cdz360.biz.model.trading.soc.po.UserSocStrategyPo">
		insert into t_user_soc_strategy (`userId`,
			`strategyId`,
			`createTime`)
		values (#{userId},
			#{strategyId},
			now())
	</insert>

	<update id="updateUserSocStrategy" parameterType="com.cdz360.biz.model.trading.soc.po.UserSocStrategyPo">
		update t_user_soc_strategy set
		<if test="userId != null">
			userId = #{userId},
		</if>
		<if test="strategyId != null">
			strategyId = #{strategyId},
		</if>
		where id = #{id}
	</update>

	<delete id="deleteByUserIdList">
		delete from t_user_soc_strategy
		where userId in
		<foreach collection="userIdList" item="ref" open="(" close=")" separator=",">
			#{ref}
		</foreach>
	</delete>

	<delete id="deleteByIdList">
		delete from t_user_soc_strategy
		where id in
		<foreach collection="idList" item="ref" open="(" close=")" separator=",">
			#{ref}
		</foreach>
	</delete>

	<insert id="batchInsert" parameterType="com.cdz360.biz.model.trading.soc.po.UserSocStrategyPo">
		INSERT into t_user_soc_strategy (`userId`,
		`strategyId`,
		`vinId`,
		`createTime`)
		VALUES
		<foreach collection="poList" item="item" index="index" separator=",">
			(
			#{item.userId},
			#{item.strategyId},
			#{item.vinId},
			now()
			)
		</foreach>
	</insert>

	<delete id="deleteBySiteId">
		delete from t_user_soc_strategy
		where strategyid in
		(select id from t_soc_strategy where siteId=#{siteId})
	</delete>

	<delete id="moveCorpSoc">
		delete
			uss
		from
			t_user_soc_strategy uss
			left join t_r_corp_user tru on tru.id = uss.userId
			left join t_soc_strategy tss on tss.id=uss.strategyId
			left join t_site site on site.id=tss.siteId
			left join t_r_commercial trc ON site.operate_id = trc.id
		where
		tru.corpId = #{corpId}
		and trc.idChain not like concat(#{idChain}, '%')
	</delete>

	<delete id="deleteByStrategyId">
		delete from t_user_soc_strategy
		<where>
			strategyId = #{strategyId}
		</where>
	</delete>


</mapper>
