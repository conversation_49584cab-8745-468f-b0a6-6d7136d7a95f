package com.cdz360.biz.ds.trading.ro.coupon.mapper;

import com.cdz360.biz.model.trading.coupon.po.ActivityCouponRulePo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ActivityCouponRuleRoMapper {

	List<ActivityCouponRulePo> getListByActivityId(@Param("activityId") Long activityId);

	List<ActivityCouponRulePo> getListByActivityIdList(@Param("activityIdList") List<Long> activityIdList);
}
