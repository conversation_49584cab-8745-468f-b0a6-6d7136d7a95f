package com.cdz360.biz.ds.trading.rw.soc.mapper;

import com.cdz360.biz.model.trading.soc.po.SiteSocStrategyPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SiteSocStrategyRwMapper {
	SiteSocStrategyPo getById(@Param("id") Long id, @Param("lock") boolean lock);

	int insertSiteSocStrategy(SiteSocStrategyPo siteSocStrategyPo);

	int updateSiteSocStrategy(SiteSocStrategyPo siteSocStrategyPo);


	int deleteBySiteId(@Param("siteId") String siteId);

	int deleteById(@Param("socUserIdList") List<Long> socUserIdList);

	int batchInsert(@Param(value = "poList") List<SiteSocStrategyPo> poList);
}
