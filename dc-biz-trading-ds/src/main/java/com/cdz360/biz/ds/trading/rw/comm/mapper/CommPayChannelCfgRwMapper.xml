<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.trading.rw.comm.mapper.CommPayChannelCfgRwMapper">

  <insert id="insertOrIgnore"
    parameterType="com.cdz360.biz.model.trading.comm.po.CommPayChannelCfgPo">
    insert ignore into t_comm_pay_channel_cfg
    (commId, clientType, payChannel, payCfgUniqueNo, updateTime)
    values
    (#{commId}, #{clientType.code}, #{payChannel.code}, #{payCfgUniqueNo}, now())
  </insert>

  <delete id="deleteCfg">
    delete from t_comm_pay_channel_cfg
    where commId = #{commId}
    and clientType = #{clientType.code}
    <if test="null != payChannel">
      and payChannel = #{payChannel.code}
    </if>
  </delete>
</mapper>