<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.trading.rw.coupon.mapper.CouponSiteRwMapper">

	<resultMap id="RESULT_COUPONSITE_PO" type="com.cdz360.biz.model.trading.coupon.po.CouponSitePo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="couponDictId" jdbcType="BIGINT" property="couponDictId" />
		<result column="siteId" jdbcType="VARCHAR" property="siteId" />
		<result column="createTime" jdbcType="DATE" property="createTime" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_COUPONSITE_PO">	
		select * from t_coupon_site where id = #{id}
		<if test="lock == true">
			for update
		</if>
	</select>

	<insert id="insertCouponSite" useGeneratedKeys="true" keyProperty="id"
		keyColumn="id" parameterType="com.cdz360.biz.model.trading.coupon.po.CouponSitePo">
		insert into t_coupon_site (`couponDictId`,
			`siteId`,
			`createTime`)
		values (#{couponDictId},
			#{siteId},
			now())
	</insert>

	<update id="updateCouponSite" parameterType="com.cdz360.biz.model.trading.coupon.po.CouponSitePo">
		update t_coupon_site set
		<if test="couponDictId != null">
			couponDictId = #{couponDictId},
		</if>
		<if test="siteId != null">
			siteId = #{siteId},
		</if>
		where id = #{id}
	</update>
	<update id="updateCouponSiteBySiteId">
		update t_coupon_site set
		enable = 0
		where
		siteId = #{siteId}
		and couponDictId IN
		<foreach collection="couponDictIdList" item="item" open="(" close=")" separator=",">
			#{item}
		</foreach>
	</update>

	<insert id="batchInsert" parameterType="com.cdz360.biz.model.trading.coupon.po.CouponSitePo">
		insert into t_coupon_site (`couponDictId`,
		`siteId`,
		`createTime`)
		values
		<foreach collection="list" item="item" index="index" separator=",">
			(#{item.couponDictId},
			#{item.siteId},
			now())
		</foreach>
	</insert>
	<insert id="batchInsertGid" parameterType="com.cdz360.biz.model.trading.coupon.po.CouponGidPo">
		insert into t_coupon_gid (`couponDictId`,
		`gid`,
		`createTime`)
		values
		<foreach collection="list" item="item" index="index" separator=",">
			(#{item.couponDictId},
			#{item.gid},
			now())
		</foreach>
	</insert>

</mapper>
