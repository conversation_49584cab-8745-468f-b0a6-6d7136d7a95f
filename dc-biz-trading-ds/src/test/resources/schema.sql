
DROP TABLE IF EXISTS t_geo_province;
create table t_geo_province(
    code char(6) not null,
    `name` varchar(8) not null,
    `fullname` varchar(16) not null,
    `character` char(1) comment '首字母',
    `pinyin` text comment '拼音',
    lng decimal(10,6),
    lat decimal(10,6),
    municipality boolean not null default false comment '直辖市',
    `enable` boolean not null DEFAULT true,
    `createTime` datetime NOT NULL ,
    `updateTime` datetime NOT NULL,
    unique key t_geo_province_code(code) using BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS t_geo_city;
CREATE TABLE `t_geo_city` (
  `code` char(6) NOT NULL COMMENT '行政编码',
  `name` varchar(8) NOT NULL COMMENT '城市名称',
  `fullname` varchar(16) NOT NULL COMMENT '城市全称',
  `provinceCode` char(6) NOT NULL COMMENT '省编码',
  `character` char(1) DEFAULT NULL COMMENT '首字母',
  `pinyin` text COMMENT '拼音',
  `hot` boolean not null default false comment '热门城市',
  `lng` decimal(10,6) DEFAULT NULL COMMENT '经度',
  `lat` decimal(10,6) DEFAULT NULL COMMENT '纬度',
  `enable` tinyint(1) NOT NULL DEFAULT '1',
  `createTime` datetime NOT NULL,
  `updateTime` datetime NOT NULL,
  UNIQUE KEY `t_geo_city_code` (`code`) ,
  KEY `t_geo_city_provinceCode` (`provinceCode`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS t_geo_district;
create table t_geo_district(
    code char(6) not null,
    `name` varchar(16) not null,
    `fullname` varchar(32) not null,
    provinceCode char(6) not null,
    cityCode char(6) not null,
    `character` char(1) comment '首字母',
    `pinyin` text comment '拼音',
    lng decimal(10,6),
    lat decimal(10,6),
    `enable` boolean not null DEFAULT true,
    `createTime` datetime NOT NULL ,
    `updateTime` datetime NOT NULL,
    unique key t_geo_district_code(code) using BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS t_charger_order_refund;
create table t_charger_order_refund (
id BIGINT(20) NOT NULL auto_increment primary key,
cusId bigint(20) not null comment '客户ID. t_user.id',
orderNo varchar(64) not null comment '订单号. t_charger_order.order_no',
seqNo varchar(32) not null comment '请求单号',
amount decimal(12,2) not null comment '退款金额',
status int(4) not null comment '状态. 1, 已提交; 2, 已退款; 3, 失败',
thirdTradeNo varchar(64) comment '三方(微信/支付宝)交易流水号',
enable boolean not null comment '是否有效',
createTime datetime not null comment '创建时间',
updateTime datetime not null comment '最后更新时间',
index t_charger_order_refund_orderNo(`orderNo`),
unique index t_charger_order_refund_seqNo(`seqNo`)
)ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;



DROP TABLE IF EXISTS t_site;
CREATE TABLE t_site (
  `id` varchar(32) NOT NULL COMMENT '站点编号',
  `siteNo` varchar(50) DEFAULT '' COMMENT '场站编号',
  `frozenAmount` decimal(10,2) DEFAULT '20.00' COMMENT '实时冻结金额',
  `id_no` varchar(64) NOT NULL,
  `name` varchar(128) DEFAULT NULL COMMENT '站点名称',
  `supplyType` enum('AC','DC','BOTH','UNKNOWN') DEFAULT NULL COMMENT '场站充电桩支持电流形式: AC/DC/BOTH/UNKNOWN',
  `type` int(1) DEFAULT NULL COMMENT '站点类型**0-未知 1-公共 2-公交 3-物流 4-混合**',
  `bizType` int(4) DEFAULT NULL COMMENT '运营属性: 0,未知; 1,自营; 2,非自营',
  `gcType` int(2) DEFAULT '5' COMMENT '0，未知； 1，投建运营； 2，以租代售； 3，纯租赁； 4，EPC+O; 5, 销售的代收代付； 6，代运营； 7，委托运营',
  `bizName` varchar(255) DEFAULT NULL COMMENT '运营方名称',
  `longitude` double DEFAULT NULL COMMENT '经度',
  `latitude` double DEFAULT NULL COMMENT '纬度',
  `geohash` varchar(32) DEFAULT NULL COMMENT '保存经纬度对应的geohash,用于计算附近的站点',
  `address` varchar(512) DEFAULT NULL COMMENT '站点地址',
  `province` int(6) DEFAULT NULL COMMENT '省',
  `city` int(6) DEFAULT NULL COMMENT '市',
  `area` int(6) DEFAULT NULL COMMENT '区',
  `phone` varchar(256) DEFAULT NULL COMMENT '服务号码',
  `status` int(1) unsigned DEFAULT '1' COMMENT '站点状态**0-已删除 1-待上线 2-已上线**',
  `topCommId` bigint(20) DEFAULT NULL COMMENT '集团商户ID',
  `operate_id` bigint(20) DEFAULT NULL COMMENT '运营商ID',
  `operate_name` varchar(64) DEFAULT NULL COMMENT '运营商名称',
  `merchant_id` bigint(20) DEFAULT NULL COMMENT '物业ID或代理商账号ID',
  `contacts` varchar(64) DEFAULT NULL COMMENT '联系人',
  `contacts_phone` varchar(64) DEFAULT NULL COMMENT '联系人号码',
  `remark` varchar(512) DEFAULT NULL,
  `service_workday_time` varchar(256) DEFAULT NULL COMMENT '工作日服务时间',
  `service_holiday_time` varchar(256) DEFAULT NULL COMMENT '节假日服务时间',
  `park` tinyint(2) DEFAULT '1' COMMENT '停车是否收费 **0-未知 1-收费 2-免费**',
  `park_fee` varchar(256) DEFAULT NULL COMMENT '停车费',
  `parkTimeoutFee` tinyint(1) DEFAULT '0' COMMENT '停充超时收费. true: 收费; false: 不收费（不启用）',
  `appoint` tinyint(2) DEFAULT NULL COMMENT '是否需要预约 **0-未知 1-需要预约 2-不需要预约**',
  `scope` tinyint(2) DEFAULT NULL COMMENT '使用范围**0-未知 1-对外开放 2-内部使用 3-特定人群**',
  `forbiddenClient` varchar(64) DEFAULT NULL COMMENT '禁用的客户端编号,使用","分隔',
  `defaultPayType` int(10) DEFAULT '10' COMMENT '0, 禁用; 1, 个人基本账户; 2, 集团授权账户; 3, 商户专属账户; 999, 启动时选择',
  `payAccountId` bigint(20) DEFAULT '0' COMMENT 'defaultPayType=1时t_user.id; defaultPayType=2时为t_r_bloc_user.id; defaultPayType=3时为t_comm_cus_ref.id; ',
  `online_date` timestamp NULL DEFAULT NULL COMMENT '上线时间',
  `update_time` timestamp NULL DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `images` text COMMENT '图片',
  `brand_ids` varchar(256) DEFAULT NULL,
  `network_status` int(1) DEFAULT '2' COMMENT '【弃用】联网状态(0:全部, 1: 已联网, 2:未联网)',
  `specilic` int(1) DEFAULT '0' COMMENT '【弃用】特定用户(1: 只针对特定用户开放, 0: 对所有用户开放)',
  `qrcode` int(1) DEFAULT '1' COMMENT '【弃用】支持扫码(1: 支持.  2: 不支持)',
  `throug` varchar(32) DEFAULT NULL COMMENT '【弃用】服务号码转',
  `fee_description` text COMMENT '收费说明',
  `invoiceDesc` varchar(256) DEFAULT NULL COMMENT '发票描述',
  `fee_min` bigint(20) DEFAULT '0' COMMENT '费用范围(最低)(分)',
  `fee_max` bigint(20) DEFAULT '0' COMMENT '费用范围(最高)',
  `app_pay` varchar(32) DEFAULT NULL COMMENT '【弃用】app支付(1001:充电网APP,1002:奥能APP)',
  `wx_pay` varchar(32) DEFAULT NULL COMMENT '【弃用】微信支付(2001:充电网微信公众号  2002：奥能微信公众号  2003： e充网公众账号 )',
  `card_pay` varchar(32) DEFAULT NULL COMMENT '【弃用】刷卡支付(3001:上海地铁公交卡 3002： 普天充电卡 3003：充电网充电卡  3004：招行闪付卡)',
  `cash_pay` varchar(32) DEFAULT '1' COMMENT '【弃用】现金支付（1:支持  0：不支持）',
  `notice` text COMMENT '【弃用】站点公共',
  `max_power` decimal(18,2) DEFAULT NULL COMMENT '【弃用】最大功率（KW）',
  `join_union` tinyint(4) DEFAULT '0' COMMENT '【弃用】是否加入互联互通',
  `open_site_id` varchar(20) DEFAULT NULL COMMENT '【弃用】互联互通的站点ID',
  `scale_imgs` text COMMENT '【弃用】',
  `pay_description` varchar(255) DEFAULT NULL COMMENT '【弃用】支付方式描述',
  `construction` int(4) NOT NULL DEFAULT '-1' COMMENT '【弃用】建设场所',
  `open_operator_id` varchar(20) DEFAULT NULL COMMENT '【弃用】',
  `collect_data` tinyint(2) DEFAULT '0' COMMENT '【弃用】',
  `is_share_site` tinyint(1) DEFAULT '0' COMMENT '【弃用】是否为app分享站点 ，默认0-否;1-是',
  `electricity_costs` int(11) DEFAULT NULL COMMENT '【弃用】成本电单价,单位:分',
  `electricity_costs_ratio` int(11) DEFAULT NULL COMMENT '【弃用】物业承担电价比例40% ，此处存40',
  `invoiced_valid` int(2) NOT NULL DEFAULT '1' COMMENT '鼎充专用 开票标识（1：可以 0：不可以） 默认这个站点可以开票',
  `template_id` bigint(32) DEFAULT NULL COMMENT '【新增】站点默认关联计费模板Id',
  `template_name` varchar(64)  DEFAULT NULL COMMENT '【新增】站点默认计费模板名称',
  `acEvseNum` int(6) DEFAULT '0' COMMENT '交流桩数量',
  `dcEvseNum` int(6) DEFAULT '0' COMMENT '直流桩数量',
  `acPlugNum` int(6) DEFAULT '0' COMMENT '交流枪头数量',
  `dcPlugNum` int(6) DEFAULT '0' COMMENT '直流枪头数量',
  `acPower` int(9) DEFAULT '0' COMMENT '交流总功率,单位kw',
  `dcPower` int(9) DEFAULT '0' COMMENT '直流总功率,单位kw',
  PRIMARY KEY (`id`),
  index `geohash` (`geohash`),
  index `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS t_r_user;
CREATE TABLE `t_r_user` (
  `id` bigint(20) NOT NULL COMMENT '用户ID',
  `commId` bigint(20) DEFAULT NULL COMMENT '商户Id',
  `username` varchar(128) DEFAULT NULL,
  `name` varchar(64)  DEFAULT NULL,
  `sex` tinyint(2) DEFAULT '1' COMMENT '1.男,2.女',
  `phone` varchar(20)  DEFAULT NULL COMMENT '手机号',
  `email` varchar(64)  DEFAULT NULL COMMENT '邮箱',
  `cityCode` varchar(8) DEFAULT NULL COMMENT '城市编码',
  `status` int(5) DEFAULT '10001' COMMENT '用户状态（10000-删除，10001-正常,10002-加入黑名单）',
  `balanceId` bigint(10) DEFAULT NULL COMMENT '默认扣款账户ID. 现金账户/商户会员时为商户ID; 授信账户时为授信账户ID',
  `defaultPayType` int(4) DEFAULT '1' COMMENT '1, 基本账户(t_balance); 2, 集团授权账户(t_r_bloc_user); 3, 权益(商户)账户;',
  `enable` boolean NOT NULL DEFAULT true,
  `createTime` datetime NOT NULL,
  `updateTime` datetime NOT NULL,
  PRIMARY KEY (`id`),
  index `t_r_user_phone` (`commId`, `phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



DROP TABLE IF EXISTS t_template;
CREATE TABLE `t_template` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '计费模板ID',
  `code` varchar(32)  DEFAULT NULL COMMENT '主模板编号**唯一标识**',
  `version` int(6) DEFAULT NULL COMMENT '版本号',
  `name` varchar(64) DEFAULT NULL COMMENT '模板名称',
  `commercial_id` bigint(32) DEFAULT NULL COMMENT '代理商ID',
  `calculate_unit` int(6) DEFAULT NULL COMMENT '充电计费单位**1-按充电时长计费 2-按充电度数计费**',
  `calculate_type` int(6) DEFAULT NULL COMMENT '充电计费方式**10-所有功率统一计费 11-按不同功率段分别计费 20-所有时段统一计费 21-按不同时段分别计费**',
  `charging_type` int(6) DEFAULT NULL COMMENT '【预留】充电收费方式**0-固定收费 1-实时收费**',
  `remark_charge` varchar(512) DEFAULT NULL,
  `remark_service` varchar(512) DEFAULT NULL,
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `free_charge_flag` int(2) DEFAULT NULL COMMENT '免费充电标识**0-收费 1-免费**',
  `delete_flag` int(2) NOT NULL COMMENT '删除标识**0-正常 1-已删除**',
  `enable` boolean NOT NULL DEFAULT true COMMENT 'true(启用), false(禁用): 禁用后，场站的计费模板下发不可选',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS t_sub_template;
CREATE TABLE `t_sub_template` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '子模板ID',
  `template_id` bigint(32) NOT NULL COMMENT '主模板ID',
  `code` varchar(32)  DEFAULT NULL COMMENT '主模板编号**唯一标识**',
  `num` int(4) DEFAULT NULL COMMENT '时段编号, 0~255. 在单个价格模板内确保唯一',
  `version` int(6) DEFAULT NULL COMMENT '版本号',
  `start_power` int(6) DEFAULT NULL COMMENT '【预留】开始功率**单位：瓦**',
  `stop_power` int(6) DEFAULT NULL COMMENT '【预留】结束功率**单位：瓦**',
  `start_time` int(6) DEFAULT NULL COMMENT '开始时间**单位：分钟**',
  `stop_time` int(6) DEFAULT NULL COMMENT '结束时间**单位：分钟**',
  `price` decimal(10,4) DEFAULT NULL COMMENT '电费单价,单位"元"',
  `scale` int(6) DEFAULT NULL COMMENT '充电费单位-分母**分钟或度数**',
  `service_price` decimal(10,4) DEFAULT NULL COMMENT '服务费价格, 单位：元',
  `service_scale` int(6) DEFAULT NULL COMMENT '服务费单位-分母**分钟或度数**',
  `remark` varchar(64) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `tariff_tag` int(2) DEFAULT NULL COMMENT '尖峰平谷标记存储字段 1:尖,2:峰,3:平,4:谷',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ;

DROP TABLE IF EXISTS t_r_corp_user;
CREATE table t_r_corp_user (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `corpId` bigint(20) DEFAULT NULL COMMENT '企业Id',
  `uid` bigint(20) DEFAULT NULL COMMENT '客户Id',
  `corpOrgId` bigint(20) DEFAULT NULL COMMENT '集团组织id',
  `name` varchar(50) DEFAULT NULL COMMENT '姓名',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `topCommId` bigint(20) DEFAULT NULL COMMENT '所属集团商户id',
  `createTime` datetime DEFAULT NULL,
  `updateTime` datetime DEFAULT NULL,
  `enable` boolean DEFAULT true COMMENT 'true有效, false禁用',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS t_evse_cfg_schedule;
CREATE TABLE t_evse_cfg_schedule (
  `id` bigint(10) NOT NULL AUTO_INCREMENT,
  `evseNo` varchar(32) NOT null COMMENT '桩号',
  `scheduleTime` datetime DEFAULT NULL COMMENT '定时下发时间',
  `priceSchemeId` bigint(10) COMMENT '使用的计费模板Id',
  `opUid` bigint(20) COMMENT '操作人id',
  `enable` boolean DEFAULT true COMMENT '（true有效false无效）',
  `createTime` datetime DEFAULT NULL COMMENT '创建时间',
  `updateTime` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `t_evse_cfg_evseNo` (`evseNo`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `t_bi_site_order`;
CREATE TABLE `t_bi_site_order` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `siteId` varchar(32) NOT NULL,
  `time` datetime NOT NULL,
  `dependOn` enum('CREATE_TIME','STOP_TIME','CHARGE_START_TIME','CHARGE_END_TIME','PAY_TIME') DEFAULT NULL COMMENT '根据订单时间节点做的统计',
  `orderCount` bigint(20) unsigned NULL COMMENT '订单量',
  `power` bigint(20) NOT NULL DEFAULT '0' COMMENT '实时功率',
  `elecFee` decimal(14,2) DEFAULT NULL COMMENT '电费总金额',
  `servFee` decimal(14,2) DEFAULT NULL COMMENT '服务费',
  `fee` decimal(15,2) DEFAULT NULL COMMENT '总金额',
  `noAccountFee` decimal(10,2) DEFAULT NULL COMMENT '无结算账户金额(单位: 元)',
  `postSettlementFee` decimal(10,2) DEFAULT NULL COMMENT '后付费金额(单位: 元)',
  `preSettlementFee` decimal(10,2) DEFAULT NULL COMMENT '预付费金额(单位: 元)',
  `freeFee` decimal(10,2) DEFAULT NULL COMMENT '赠送金额(单位: 元)',
  `costFee` decimal(10,2) DEFAULT NULL COMMENT '实际金额(单位: 元)',
  `electricity` decimal(17,4) DEFAULT NULL COMMENT '总电量',
  `elecTag1` decimal(16,4) DEFAULT NULL COMMENT '尖',
  `elecTag2` decimal(16,4) DEFAULT NULL COMMENT '峰',
  `elecTag3` decimal(16,4) DEFAULT NULL COMMENT '平',
  `elecTag4` decimal(16,4) DEFAULT NULL COMMENT '谷',
  `createTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updateTime` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `siteId_time_depend_on_index` (`siteId`,`time`,`dependOn`)
) ENGINE=InnoDB AUTO_INCREMENT=2498558 DEFAULT CHARSET=utf8mb4;
DROP TABLE IF EXISTS `t_bi_plug`;
CREATE TABLE `t_bi_plug` (
`id`  bigint(20) NOT NULL AUTO_INCREMENT ,
`siteId`  varchar(32) NOT NULL ,
`evseNo`  varchar(32) NOT NULL ,
`plugId`  int(2) NOT NULL ,
`errorCount`  int(7) NULL COMMENT '离线次数，当plugId为1时使用' ,
`offlineCount`  int(7) NULL COMMENT '离线次数，当plugId为1时使用' ,
`date`  datetime NULL COMMENT '统计日期当天的第一秒' ,
`duration`  int(7) NULL COMMENT '充电时长' ,
`orderCount`  int(7) NULL COMMENT '订单量' ,
`elecFee`  decimal(7,2) NULL COMMENT '电费总金额' ,
`servFee`  decimal(7,2) NULL COMMENT '服务费' ,
`fee`  decimal(8,4) NULL COMMENT '总金额' ,
`electricity`  decimal(9,4) NULL COMMENT '总电量' ,
`elecTag1`  decimal(9,4) NULL COMMENT '尖' ,
`elecTag2`  decimal(9,4) NULL COMMENT '峰' ,
`elecTag3`  decimal(9,4) NULL COMMENT '平' ,
`elecTag4`  decimal(9,4) NULL COMMENT '谷' ,
`createTime`  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ,
`updateTime`  datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP  ,
PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


DROP TABLE IF EXISTS `t_r_order_user_comm_ref`;
CREATE table `t_r_order_user_comm_ref` (
  `orderNo` varchar(64) not NULL COMMENT '充电订单号',
  `enable` boolean DEFAULT true COMMENT 'true有效, false禁用',
  `createTime` datetime DEFAULT NULL,
  `updateTime` datetime DEFAULT NULL,
  PRIMARY KEY (`orderNo`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;


DROP TABLE IF EXISTS `t_site_limit_soc_time`;
CREATE TABLE `t_site_limit_soc_time` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `siteId` varchar(32) NOT NULL COMMENT '场站ID',
  `startTime` int(6) not null comment '开始时间,闭区间,最小0,单位"分钟"',
  `endTime` int(6) not null comment '结束时间,开区间,最大1440,单位"分钟"',
  `createTime` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  unique KEY `t_site_limit_soc_time_siteId` (`siteId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `t_site_limit_soc`;
CREATE TABLE `t_site_limit_soc` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `siteId` varchar(32) NOT NULL COMMENT '场站ID',
  `corpId` bigint(20) NOT NULL COMMENT '企业ID',
  `soc` int(4) not null comment '限制的最大SOC, 0表示禁止充电',
  `createTime` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  unique KEY `t_site_limit_soc_siteId_corpId` (`siteId`, `corpId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `t_site_defult_setting`;
CREATE TABLE `t_site_defult_setting` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `siteId` varchar(32) DEFAULT NULL COMMENT '场站编号',
  `chargeId` bigint(20) DEFAULT NULL COMMENT '计费模板id',
  `adminPassword` varchar(20) DEFAULT NULL COMMENT '管理员密码',
  `level2Password` varchar(20) DEFAULT NULL COMMENT '二级管理员密码',
  `dayVolume` int(10) DEFAULT NULL COMMENT '白天音量',
  `nightVolume` int(10) DEFAULT NULL COMMENT '夜晚音量',
  `url` varchar(100) DEFAULT NULL COMMENT '二维码url',
  `isQueryChargeRecord` tinyint(4) DEFAULT NULL COMMENT '是否支持充电记录查询 （1是0否）',
  `isTimedCharge` tinyint(4) DEFAULT NULL COMMENT '是否支持定时充电 （1是0否）',
  `isNoCardCharge` tinyint(4) DEFAULT NULL COMMENT '是否支持无卡充电 （1是0否）',
  `isScanCharge` tinyint(4) DEFAULT NULL COMMENT '是否支持扫码充电 （1是0否）',
  `isVinCharge` tinyint(4) DEFAULT NULL COMMENT '是否支持Vin码充电 （1是0否）',
  `isCardCharge` tinyint(4) DEFAULT NULL COMMENT '是否支持刷卡充电 （1是0否）',
  `isQuotaEleCharge` tinyint(4) DEFAULT NULL COMMENT '是否支持定额电量充电 （1是0否）',
  `isQuotaMoneyCharge` tinyint(4) DEFAULT NULL COMMENT '是否支持固定金额充电 （1是0否）',
  `isQuotaTimeCharge` tinyint(4) DEFAULT NULL COMMENT '是否支持固定时长充电 （1是0否）',
  `internationalAgreement` varchar(50) DEFAULT NULL COMMENT '国际协议',
  `isAutoStopCharge` tinyint(4) DEFAULT NULL COMMENT '自动停充 （1是0否）',
  `avgOrTurnCharge` tinyint(4) DEFAULT NULL COMMENT '均/轮充设置 0均充 1轮充',
  `isCombineCharge` tinyint(4) DEFAULT NULL COMMENT '合充开关 （1开0关）',
  `limitSoc` int(2) DEFAULT NULL COMMENT '是否启用SOC限制. true: 启用soc限制',
  `overtimeParkingNum` int(4) DEFAULT NULL COMMENT '停充超时充电允许次数',
  `overtimeParkingTime` int(4) DEFAULT NULL COMMENT '停充超时单次允许超时时间',
  `createTime` datetime DEFAULT NULL COMMENT '创建时间',
  `updateTime` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updateByUserid` bigint(20) DEFAULT NULL COMMENT '操作人id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `t_site_defult_setting_siteId` (`siteId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- d_charger.t_charger_order definition
DROP TABLE IF EXISTS `t_charger_order`;
CREATE TABLE `t_charger_order` (
  `order_no` varchar(64) NOT NULL COMMENT '订单号（桩端生成）',
  `customer_id` bigint(20) DEFAULT '0' COMMENT '用户ID',
  `payAccountId` bigint(20) DEFAULT NULL COMMENT '现金账户/权益账户支付时为商户ID;授信账户支付时为授信账户ID',
  `defaultPayType` int(10) DEFAULT NULL COMMENT '1个人账户(t_balance)2集团授权账户(t_r_bloc_user)',
  `frozenAmount` decimal(10,2) DEFAULT '0.00' COMMENT '订单冻结金额, 单位"元"',
  `formId` varchar(128) DEFAULT '' COMMENT '微信小程序formId',
  `status` int(11) DEFAULT '0' COMMENT '状态,-500：硬件故障；-40：上报超时；-35：断连超时；-30关电超时；-29：结束充电失败；-20：链接超时；-10：打开电闸超时；-7：开启充电失败，PIN可能错误；-5:系统繁忙；0：订单未激活；100：电闸打开；200：充电中；250：关电闸，充电停止；300：充电完成；800：待支付；1000：用户已支付；2000：钱已到账',
  `order_status` varchar(10) DEFAULT NULL COMMENT '订单状态(枚举 字符)',
  `topCommId` bigint(20) DEFAULT NULL COMMENT '集团商户ID',
  `commercial_id` int(20) DEFAULT '0' COMMENT '顶级商户ID',
  `station_id` varchar(35) DEFAULT NULL COMMENT '站点ID',
  `open_order_id` varchar(30) DEFAULT NULL COMMENT '合作方(互联互通)订单号',
  `commercial_name` varchar(128) DEFAULT '0' COMMENT '设备运营商名称',
  `customer_commercial_id` bigint(20) DEFAULT '0' COMMENT '客户运营商ID',
  `customer_commercial_name` varchar(128) DEFAULT '0' COMMENT '客户运营商名称',
  `card_no` varchar(16) DEFAULT NULL COMMENT '充电卡号/身份唯一识别号',
  `card_chip_no` varchar(30) DEFAULT NULL COMMENT '卡片芯片号 物理卡号',
  `card_name` varchar(100) DEFAULT NULL COMMENT '卡名称',
  `channel_id` int(11) DEFAULT NULL COMMENT '订单来源类型,0:微信渠道;1:APP在线发起充电;2:APP蓝牙发起充电:3:刷卡充电:4:桩上报离线数据5定时充电6手动开启7曹操专车开启8批量开启9互联互通',
  `order_type` int(5) DEFAULT '0' COMMENT '充电启动方式. \n0, 未知; \n1, 紧急充电卡; \n2, 桩端无卡充电; \n17, 在线卡; \n18, VIN码; \n33, 管理后台; \n34, 批量启动; \n35, 互联互通; \n36, 定时任务; \n50, 微信小程序;\n51, iOS APP;\n52, 安卓APP;\n53, 支付宝小程序',
  `hlhtType` int(4) DEFAULT '0' COMMENT '0,非互联互通订单; 1,正向互联互通; 2,反向互联互通',
  `order_price` decimal(10,2) DEFAULT '0.00' COMMENT '订单金额, 单位"元"',
  `service_price` decimal(10,2) DEFAULT '0.00' COMMENT '服务费, 单位"元"',
  `servActualFee` decimal(10,2) DEFAULT '0.00' COMMENT '服务费实收金额',
  `elec_price` decimal(10,2) DEFAULT '0.00' COMMENT '电费, 单位"元"',
  `elecActualFee` decimal(10,2) DEFAULT '0.00' COMMENT '电费实收金额',
  `manual` tinyint(1) DEFAULT '0' COMMENT 'true: 有手动修改过; false: 没有手动修改, 默认false',
  `manual_price` decimal(10,2) DEFAULT '-1.00' COMMENT '人工调整后金额, 单位"元"',
  `actual_price` decimal(10,2) DEFAULT '0.00' COMMENT '客户实付金额, 单位"元"',
  `discount` decimal(10,2) DEFAULT NULL COMMENT '电损金额. 单位"元"',
  `clearing_mode` tinyint(2) DEFAULT '0' COMMENT '结算方式，0:未知，1：后付费，2：预付费，3：先付费（固定收费：不退还费用），4：先付费（实时计费：退还费用）',
  `order_electricity` decimal(12,4) DEFAULT NULL COMMENT '订单电量, 单位"kwh", 4位小数',
  `price_scheme_id` bigint(20) DEFAULT NULL COMMENT '计费模板ID',
  `remark` text COMMENT '备注',
  `create_time` datetime DEFAULT NULL COMMENT '订单创建时间',
  `stop_time` datetime DEFAULT NULL COMMENT '订单结束时间',
  `update_time` datetime DEFAULT NULL COMMENT '订单状态修改时间',
  `trade_no` varchar(128) DEFAULT NULL COMMENT '硬件流水号',
  `box_type` int(11) DEFAULT NULL COMMENT '厂商盒子类型',
  `plugNo` varchar(32) DEFAULT '' COMMENT '枪头编号',
  `connect_id` varchar(50) DEFAULT NULL COMMENT '充电接口(枪)编号',
  `charge_start_time` int(11) DEFAULT NULL COMMENT '充电开始时间',
  `charge_end_time` int(11) DEFAULT NULL COMMENT '充电结束时间',
  `start_electricity` decimal(12,4) DEFAULT NULL COMMENT '充电开始电量,单位"kwh"',
  `end_electricity` decimal(12,4) DEFAULT NULL COMMENT '充电结束电量,单位"kwh"',
  `car_no` varchar(100) DEFAULT NULL COMMENT '车牌号',
  `platform_id` varchar(64) DEFAULT NULL COMMENT 'platformId区分app或者微信或者其他平台发起的充电的回传标示',
  `mobile_phone` varchar(16) DEFAULT '0' COMMENT '手机号码',
  `station_name` varchar(128) DEFAULT '0' COMMENT '站点名称',
  `pay_modes` tinyint(2) DEFAULT '0' COMMENT '支付方式',
  `settlementType` int(4) NOT NULL DEFAULT '0' COMMENT '0,未知; 1,账户余额扣减; 2,担保消费结算; 3,授信后结算; 4,外部平台结算',
  `renewal` tinyint(1) DEFAULT NULL COMMENT '是否支持实时续费. true, 支持; false, 不支持',
  `pay_status` tinyint(2) DEFAULT '1' COMMENT '支付状态',
  `corpPayStatus` tinyint(2) DEFAULT '0' COMMENT '0不适用; 1待支付; 2已支付',
  `billNo` varchar(16) DEFAULT '' COMMENT '结算单号',
  `pay_time` datetime DEFAULT NULL COMMENT '结算时间',
  `customer_name` varchar(200) DEFAULT '0' COMMENT '客户名称',
  `open_operator_id` varchar(50) DEFAULT NULL COMMENT '合作方(互联互通)编号 (d_open.t_partner.code)',
  `device_commercial_id` bigint(11) DEFAULT NULL COMMENT '设备运营商ID',
  `timeout_status` tinyint(2) DEFAULT '0' COMMENT 'core推送发起充电超时，通知第三方结束订单标志 0:正常 其他异常',
  `principal_amount` decimal(10,2) DEFAULT '0.00' COMMENT '本金(实际收入), 单位"元"',
  `free_gold_amount` decimal(10,2) DEFAULT '0.00' COMMENT '赠送金, 单位"元"',
  `box_code` varchar(100) DEFAULT NULL COMMENT '盒子编码',
  `duration` varchar(100) DEFAULT NULL COMMENT '充电时长',
  `version` varchar(100) DEFAULT NULL COMMENT '客户端版本号',
  `rt_power` varchar(50) DEFAULT NULL COMMENT '实时功率',
  `template_power_is_changed` varchar(11) DEFAULT NULL COMMENT '客户实际消费功率与客户选择的功率对比 0：相等 1：不等',
  `exception_reason` varchar(200) DEFAULT NULL COMMENT '订单异常结束原因',
  `qr_code` varchar(100) DEFAULT NULL COMMENT '插座二维码',
  `connector_id` varchar(300) DEFAULT NULL COMMENT '插座序列号',
  `end_unexpected` int(1) DEFAULT '0' COMMENT '充电是否提前结束标示',
  `vin` varchar(100) DEFAULT NULL COMMENT '车辆Vin码',
  `car_depart` varchar(50) DEFAULT NULL COMMENT '车队名称',
  `car_num` varchar(100) DEFAULT NULL COMMENT '车辆自编号',
  `lineNum` varchar(100) DEFAULT NULL COMMENT '车辆线路名称',
  `start_soc` varchar(100) DEFAULT NULL,
  `stop_soc` varchar(100) DEFAULT NULL,
  `limitSoc` int(4) DEFAULT NULL COMMENT '限制的最大SOC',
  `stop_reason` varchar(200) DEFAULT NULL COMMENT '结束原因',
  `stopCode` int(6) DEFAULT NULL COMMENT '停充原因. 0表示正常停充;其他都表示异常',
  `current_soc` varchar(10) DEFAULT NULL,
  `discount_money` bigint(20) DEFAULT NULL COMMENT '单次充电优惠金额--等级优惠金额(单位分)',
  `coupon_money` bigint(20) DEFAULT NULL COMMENT '优惠券金额（单位分）',
  `activity_id` bigint(20) DEFAULT NULL COMMENT '优惠券id',
  `invoiced_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '鼎充开票Id（0：表示 没有关联开票申请Id,大于0表示已经关联开票ID） ',
  `invoiceAmount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '可开票金额, 单位"元"',
  `invoicedAmount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '已开票金额: 单位: 元',
  `charger_name` varchar(255) DEFAULT NULL COMMENT '枪头名称',
  `abnormal` int(4) DEFAULT NULL COMMENT 'null, 正常; 0, 未知异常; 1, 心跳超时; 2, 订单更新超时; 3, 订单电量越限; 4, 订单金额越限; 5, 充电中超时; 6, 启动中超时; 7, 充电中停用',
  `overtimeParking` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否停充超时',
  PRIMARY KEY (`order_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 充电订单车辆信息
DROP TABLE IF EXISTS `t_charger_order_car`;
CREATE TABLE `t_charger_order_car` (
  `orderNo` varchar(32) NOT NULL COMMENT '订单号',
  `vin` varchar(34) DEFAULT '' COMMENT '车辆Vin码',
  `carNo` varchar(16) DEFAULT '' COMMENT '车牌号',
  `lineNum` varchar(50) DEFAULT '' COMMENT '车辆线路名称',
  `carDepart` varchar(50) DEFAULT '' COMMENT '车队名称',
  `carNum` varchar(50) DEFAULT '' COMMENT '车辆自编号',
  `bmsProtocol` varchar(64) DEFAULT '' COMMENT 'BMS通信协议版本号',
  `bmsSwVer` varchar(64) DEFAULT '' COMMENT 'BMS软件版本号',
  `bmsVoltage` int(4) DEFAULT NULL COMMENT 'BMS辅助电压. 12V, 24V',
  `maxVoltage` decimal(5,1) DEFAULT NULL COMMENT '整车最高允许充电总电压. 单位: V',
  `maxCurrent` decimal(5,1) DEFAULT NULL COMMENT '整车最高允许充电总电流. 单位: A',
  `startVoltage` decimal(5,1) DEFAULT NULL COMMENT '整车当前电池电压. 单位: V',
  `maxTemp` int(4) DEFAULT NULL COMMENT '最高允许温度. 单位: ℃',
  `batteryType` int(4) DEFAULT NULL COMMENT '电池类型. 1铅酸电池, 2镍氢电池, 3磷酸铁锂电池, 4锰酸锂电池, 5钴酸锂电池, 6三元材料电池, 7聚合物锂离子电池, 8钛酸锂电池',
  `batteryCapacity` decimal(5,1) DEFAULT NULL COMMENT '整车电池容量. 单位: Ah',
  `batteryPower` decimal(5,1) DEFAULT NULL COMMENT '整车电池标称总能量. 单位: kWh',
  `batteryVoltage` decimal(5,1) DEFAULT NULL COMMENT '整车额定总电压. 单位: V',
  `batteryVendor` varchar(8) DEFAULT '' COMMENT '电池厂商名称',
  `batterySeqNo` int(12) DEFAULT NULL COMMENT '电池组序号',
  `batteryProduceDate` date DEFAULT NULL COMMENT '电池生产日期',
  `batteryOwner` int(4) DEFAULT NULL COMMENT '电池组产权标识. 0租赁, 1车自有, 255不支持',
  `batteryUnitMaxVoltage` decimal(5,1) DEFAULT NULL COMMENT '电池单体最高允许充电电压. 单位: V',
  `insulationPositive` int(6) DEFAULT NULL COMMENT 'DC+绝缘检测值. 单位1Ω/V',
  `insulationNegative` int(6) DEFAULT NULL COMMENT 'DC-绝缘检测值. 单位1Ω/V',
  `createTime` datetime DEFAULT NULL COMMENT '创建时间',
  `updateTime` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY `t_charger_order_car_orderNo` (`orderNo`),
  KEY `t_charger_order_car_orderNo_vin` (`vin`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- d_charger.t_r_commercial definition
DROP TABLE IF EXISTS `t_r_commercial`;
CREATE TABLE `t_r_commercial` (
  `id` bigint(20) NOT NULL COMMENT '编号',
  `pid` bigint(20) DEFAULT NULL COMMENT '所属商户(0.表示集团商户,-1.表示商户但是没有所属集团商户)',
  `comm_level` int(3) DEFAULT NULL COMMENT '商户级别(1.集团商户,2.商户,3.子商户)',
  `merchants` varchar(7) DEFAULT NULL COMMENT '商户号',
  `comm_type` int(1) DEFAULT NULL COMMENT '商户类型(1:个人、2:企业)',
  `topCommId` int(20) NOT NULL DEFAULT '0' COMMENT '集团商户ID',
  `comm_name` varchar(255) DEFAULT NULL COMMENT '商户名称',
  `short_name` varchar(255) DEFAULT NULL COMMENT '简称',
  `status` tinyint(11) DEFAULT '1',
  `phone` varchar(64) DEFAULT NULL COMMENT '联系电话',
  `idChain` text COMMENT '商户ID链',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `t_activity_coupon`;
CREATE TABLE `t_activity_coupon` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `activityId` bigint(20) NOT NULL COMMENT '活动id',
  `couponDictId` bigint(20) NOT NULL COMMENT '券模板id',
  `createTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


DROP TABLE IF EXISTS `t_coupon`;
CREATE TABLE `t_coupon` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `activityId` bigint(20) NOT NULL,
  `couponDictId` bigint(20) NOT NULL,
  `userId` bigint(20) NOT NULL,
  `topCommId` bigint(20) DEFAULT NULL,
  `orderNo` varchar(64) DEFAULT NULL,
  `type` enum('SERV_FEE_FIX','TOTAL_FEE') DEFAULT NULL COMMENT '服务费满减',
  `personalEnable` tinyint(1) DEFAULT '0' COMMENT '个人账户可用',
  `prepayEnable` tinyint(1) DEFAULT '0' COMMENT '即充即退可用',
  `commEnable` tinyint(1) DEFAULT '0' COMMENT '商户会员可用',
  `status` enum('ENABLE','DISABLE','EXPIRE','USED') NOT NULL,
  `validType` enum('FIX','RELATE') DEFAULT NULL COMMENT '有效期类型, 固定期限 | 自领取时间',
  `validTimeFrom` datetime DEFAULT NULL COMMENT '有效期-开始',
  `validTimeTo` datetime DEFAULT NULL COMMENT '有效期-截至',
  `validRelateDay` int(8) DEFAULT NULL COMMENT '过期日, 自领取时间',
  `conditionAmount` decimal(10,2) DEFAULT NULL COMMENT '使用条件金额',
  `amount` decimal(10,2) DEFAULT NULL COMMENT '减金额',
  `createTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updateTime` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS t_r_corp;
CREATE TABLE `t_r_corp` (
  `id` bigint(20) NOT NULL,
  `topCommId` bigint(20) NOT NULL COMMENT '集团商户ID',
  `commId` bigint(20) DEFAULT NULL COMMENT '商户Id',
  `uid` bigint(20) NOT NULL COMMENT 't_user.id',
  `corpName` varchar(255) DEFAULT NULL COMMENT '集团客户名称',
  `contactName` varchar(50) DEFAULT NULL COMMENT '联系人名称',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `settlementType` int(4) NOT NULL DEFAULT '1' COMMENT '1,账户余额扣减; 3,后付费',
  `invoiceWay` enum('UNKNOWN','PRE_PAY','POST_CHARGER','POST_SETTLEMENT') DEFAULT 'UNKNOWN' COMMENT '(存在值并不代表开启)企业客户开票方式: 充值预开票(PRE_PAY)、充电后开票(POST_CHARGER)、后付费账单开票(POST_SETTLEMENT)',
  `email` varchar(50) DEFAULT NULL COMMENT '电子邮箱',
  `province` varchar(8) DEFAULT NULL COMMENT '省',
  `city` varchar(8) DEFAULT NULL COMMENT '市',
  `district` varchar(8) DEFAULT NULL COMMENT '区',
  `address` varchar(50) DEFAULT NULL COMMENT '地址',
  `account` varchar(50) DEFAULT NULL COMMENT '账户',
  `createTime` datetime DEFAULT NULL COMMENT '创建时间',
  `updateTime` datetime DEFAULT NULL COMMENT '修改时间',
  `enable` tinyint(1) DEFAULT '1' COMMENT 'true有效, false禁用',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS t_invoiced_sal_temp_ref;
CREATE TABLE t_invoiced_sal_temp_ref (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name` varchar(100) NOT NULL DEFAULT '' COMMENT '商品行模板名称',
    saleTin varchar(30) NOT NULL DEFAULT '' COMMENT '纳税人识别号',
    `target` enum('UNKNOWN', 'MOBILE', 'PLATFORM') DEFAULT 'UNKNOWN' COMMENT '使用对象: MOBILE -- 移动端; PLATFORM -- 平台端',
    createTime datetime DEFAULT NULL COMMENT '创建时间',
    updateTime datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `enable` boolean  DEFAULT true comment '是否有效. true/false',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS invoiced_template_sal;
CREATE TABLE `invoiced_template_sal` (
  `sale_name` varchar(30) NOT NULL COMMENT '销售方-名称',
  `sale_tin` varchar(30) NOT NULL COMMENT '销售方-纳税人识别号',
  `sale_address` varchar(100) DEFAULT NULL COMMENT '销售方-地址',
  `sale_tel` varchar(30) DEFAULT NULL COMMENT '销售方-电话',
  `sale_bank` varchar(50) DEFAULT NULL COMMENT '销售方-开户行',
  `sale_account` varchar(30) DEFAULT NULL COMMENT '销售方-账号',
  `creator_name` varchar(30) DEFAULT NULL COMMENT '创建人',
  `create_date` datetime DEFAULT NULL COMMENT '创建日期 DateTime',
  `accepter` varchar(12) DEFAULT NULL COMMENT '收款人',
  `checker` varchar(12) DEFAULT NULL COMMENT '复核人',
  `invoicer` varchar(12) DEFAULT NULL COMMENT '开票人',
  `topCommId` bigint(20) NOT NULL COMMENT '集团商户ID',
  `commercial_id` bigint(20) DEFAULT NULL,
  `sender_company` varchar(30) DEFAULT NULL COMMENT '寄件人-公司',
  `sender_province_name` varchar(12) DEFAULT NULL COMMENT '寄件人-省',
  `sender_city_name` varchar(12) DEFAULT NULL COMMENT '寄件人-市',
  `sender_exparea_name` varchar(30) DEFAULT NULL COMMENT '寄件人-区',
  `sender_name` varchar(30) DEFAULT NULL COMMENT '寄件人-姓名',
  `sender_address` varchar(255) DEFAULT NULL COMMENT '寄件人-地址',
  `sender_tel` varchar(30) DEFAULT NULL COMMENT '寄件人-电话',
  `e_business_id` varchar(30) DEFAULT NULL COMMENT '电商id',
  `app_key` varchar(64) DEFAULT NULL COMMENT '加密私钥',
  `req_url` varchar(255) DEFAULT NULL COMMENT '请求地址',
  `print_url` varchar(255) DEFAULT NULL COMMENT '打印物流单地址',
  `shipper_code` varchar(12) DEFAULT NULL COMMENT '快递编号',
  `month_code` varchar(30) DEFAULT NULL COMMENT '月结账号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS t_invoice_record_order_ref;
CREATE TABLE t_invoice_record_order_ref (
    invoiceWay enum('UNKNOWN', 'PRE_PAY', 'POST_CHARGER', 'POST_SETTLEMENT') DEFAULT 'UNKNOWN' COMMENT '开票方式: 充值预开票(PRE_PAY)、充电后开票(POST_CHARGER)、后付费账单开票(POST_SETTLEMENT)',
    applyNo varchar(16) NOT NULL COMMENT '企业申请开票记录单号(t_corp_invoice_record.applyNo)',
    orderNo varchar(20) NOT NULL COMMENT '订单号: 开票方式决定属于哪一类订单',
    elecFee decimal(12,2) not null default 0 comment '电费开票金额',
    servFee decimal(12,2) not null default 0 comment '服务费开票金额',
    PRIMARY KEY (`invoiceWay`,`orderNo`,`applyNo`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS t_corp_invoice_record;
CREATE TABLE t_corp_invoice_record (
    `applyNo` varchar(16) NOT NULL COMMENT '申请单号',
    corpId bigint(20) NOT NULL COMMENT 't_corp的id',
    `status` enum('NOT_SUBMITTED', 'SUBMITTED', 'REVIEWED', 'AUDIT_FAILED', 'INVOICING_FAIL', 'COMPLETED', 'RED_DASHED', 'INVALID') DEFAULT 'NOT_SUBMITTED' COMMENT '流程状态: 草稿(NOT_SUBMITTED); 审核中(SUBMITTED); 开票中(REVIEWED); 审核未通过(AUDIT_FAILED); 开票未通过(INVOICING_FAIL);已开具(COMPLETED); 已作废(INVALID)[含红冲状态]',
    saleTin varchar(30) NOT NULL DEFAULT '' COMMENT '开票主体纳税人识别号',
    productTempId bigint(20) NOT NULL COMMENT '商品行模板ID(t_invoiced_sal_temp.id)',
    invoiceName varchar(100) DEFAULT '' COMMENT '开票抬头',
    invoiceType varchar(20) DEFAULT '' COMMENT '开票种类: PER_COMMON(个人普票); ENTER_COMMON(企业普票); ENTER_PROFESSION(企业专票)',
    invoiceWay enum('UNKNOWN', 'PRE_PAY', 'POST_CHARGER', 'POST_SETTLEMENT') DEFAULT 'UNKNOWN' COMMENT '企业客户开票方式: 充值预开票(PRE_PAY)、充电后开票(POST_CHARGER)、后付费账单开票(POST_SETTLEMENT)',
    actualElecFee decimal(12,2) not null default 0 comment '应开电费',
    fixElecFee decimal(12,2) not null default 0 comment '修正电费',
    actualServFee decimal(12,2) not null default 0 comment '应开服务费',
    fixServFee decimal(12,2) not null default 0 comment '修正服务费',
    totalFee decimal(14,2) not null default 0 comment '应开总额',
    fixTotalFee decimal(14,2) not null default 0 comment '修正总额',
    createOpType int(4) not null default 0 comment '创建操作人类型. 0,未知; 1,商户/平台用户; 3,企业客户; 4,系统',
    createOpId bigint(20) not null default 0 comment '创建操作人ID',
    createOpName varchar(32) not null default '' comment '创建操作人名字',
    updateOpType int(4) not null default 0 comment '操作人类型. 0,未知; 1,商户/平台用户; 3,企业客户; 4,系统',
    updateOpId bigint(20) not null default 0 comment '操作人ID',
    updateOpName varchar(32) not null default '' comment '操作人名字',
    applyRemark varchar(255) not null default '' comment '申请备注',
    images text comment '图片',
    auditRemark varchar(255) not null default '' comment '审核建议',
    createTime datetime DEFAULT NULL COMMENT '创建时间',
    updateTime datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    unique key t_corp_invoice_record_applyNo(`applyNo`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS t_overtime_parkfee_order;
CREATE TABLE t_overtime_parkfee_order (
    `orderNo` varchar(64) NOT NULL COMMENT '充电订单号',
    `hlhtOrderNo` varchar(64) NOT NULL COMMENT '互联互通订单号',
    `uid` bigint(20) NOT NULL COMMENT 't_user.id',
    `siteId` varchar(32) NOT NULL COMMENT '站点编号',
    `freeTime` int(4) NOT NULL COMMENT '限免时长, 单位: 分钟',
    `stopTime` datetime NOT NULL COMMENT '平台收到充电完成的时间',
    `parkingPrice` decimal(6, 2) NOT NULL DEFAULT 0 COMMENT '超时收费单价, 单位: 元/分钟',
    `parkingFee` decimal(8, 2) NOT NULL DEFAULT 0 COMMENT '超时费, 单位: 元',
    `calFromTime` datetime NOT NULL COMMENT '计费开始时长',
    `calToTime` datetime NOT NULL COMMENT '计费结束时长',
    `createTime` datetime NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`orderNo`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS t_zft_daily_bill;
CREATE TABLE `t_zft_daily_bill` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '发票记录ID',
  `name` varchar(200) NOT NULL default '' COMMENT '账单文件名称',
  `status` enum('LOADING', 'LOAD_FAIL', 'LOAD_SUCCESS', 'CHECKING', 'CHECK_FAIL', 'CHECK_SUCCESS') NOT NULL COMMENT '账单状态: LOADING(下载中), LOAD_FAIL(下载失败), LOAD_SUCCESS(下载成功), CHECKING(对账中), CHECK_FAIL(对账失败), CHECK_SUCCESS(完成对账)',
  `zftId` bigint(20) DEFAULT 0 COMMENT '直付商家ID(t_zft.id)',
  `zftName` varchar(200) NOT NULL DEFAULT '' COMMENT '直付商家名称',
  `zftCommId` bigint(20) NULL COMMENT '直付商家所属商户ID',
  `billDate` datetime NOT NULL COMMENT '账期',
  `channel` int(4) NOT NULL COMMENT '渠道: 1(支付宝); 2(微信)',
  `downloadUrl` varchar(200) default '' COMMENT '账单下载地址',
  `createTime` datetime DEFAULT NULL COMMENT '创建时间',
  `updateTime` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS t_zft_third_order;
CREATE TABLE `t_zft_third_order` (
  `platformNo` varchar(64) DEFAULT '' COMMENT '平台支付流水号',
  `channelNo` varchar(64) DEFAULT '' COMMENT '渠道流水号',
  `checkResult` enum('FULL_MATCH', 'AMOUNT_NOT_MATCH', 'NO_NOT_MATCH', 'OTHERS') NOT NULL COMMENT '支付平台对账结果: FULL_MATCH(完全匹配); AMOUNT_NOT_MATCH(金额不匹配); NO_NOT_MATCH(未找到对应订单); OTHERS(其他)',
  `tradeType` enum('IN_FLOW', 'OUT_FLOW') NOT NULL COMMENT '交易类型: IN_FLOW(收入), OUT_FLOW(支出)',
  `tradeAmount` decimal(12,2) NOT NULL COMMENT '交易金额（单位: 元）',
  `payFee` decimal(12,2) DEFAULT '0' COMMENT '手续费（单位: 元）',
  `tradeTime` datetime NOT NULL COMMENT '交易时间',
  `dailyBillId` bigint(20) NOT NULL default 0 COMMENT '对账单ID(t_zft_daily_bill.id)',
  UNIQUE KEY `t_zft_third_order_platformNo_channelNo` (`platformNo`, `channelNo`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS t_bi_site_order_account;
CREATE TABLE `t_bi_site_order_account` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `biSiteOrderId` bigint(20) NOT NULL default 0 COMMENT '场站充电信息统计小时表ID(t_bi_site_order.id)',
  `accountType` enum('UNKNOWN', 'PREPAY', 'PERSONAL', 'COMMERCIAL', 'NORMAL_CORP', 'HLHT_CORP') COMMENT '账户类型: UNKNOWN(无结算账户)；PREPAY(即充即退)；PERSONAL(个人账户)；COMMERCIAL(商户会员)；NORMAL_CORP(普通企业客户)；HLHT_CORP(互联企业客户)',
  `orderCnt` bigint(20) unsigned DEFAULT NULL COMMENT '充电订单量(单位: 个)',
  `orderFee` decimal(12,2) DEFAULT NULL COMMENT '充电订单总金额(单位: 元)',
  `noAccountFee` decimal(10,2) DEFAULT NULL COMMENT '无结算账户金额(单位: 元)',
  `postSettlementFee` decimal(10,2) DEFAULT NULL COMMENT '后付费金额(单位: 元)',
  `preSettlementFee` decimal(10,2) DEFAULT NULL COMMENT '预付费金额(单位: 元)',
  `freeFee` decimal(10,2) DEFAULT NULL COMMENT '赠送金额(单位: 元)',
  `costFee` decimal(10,2) DEFAULT NULL COMMENT '实际金额(单位: 元)',
  `createTime` datetime DEFAULT NULL COMMENT '创建时间',
  `updateTime` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  unique key t_bi_site_order_account_biSiteOrderId_accountType(`biSiteOrderId`, `accountType`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS t_bi_site_order_hlht;
CREATE TABLE `t_bi_site_order_hlht` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `biAccountId` bigint(20) NOT NULL default 0 COMMENT '场站各账户充电消费统计ID(t_bi_site_order_account.id)',
  `corpId` BIGINT (20) NOT NULL COMMENT '企业客户ID(t_bloc_user.id)',
  `orderCnt` bigint(20) unsigned DEFAULT NULL COMMENT '充电订单量(单位: 个)',
  `orderFee` decimal(12,2) DEFAULT NULL COMMENT '充电订单总金额(单位: 元)',
  `postSettlementFee` decimal(10,2) DEFAULT NULL COMMENT '后付费金额(单位: 元)',
  `preSettlementFee` decimal(10,2) DEFAULT NULL COMMENT '预付费金额(单位: 元)',
  `freeFee` decimal(10,2) DEFAULT NULL COMMENT '赠送金额(单位: 元)',
  `costFee` decimal(10,2) DEFAULT NULL COMMENT '实际金额(单位: 元)',
  `createTime` datetime DEFAULT NULL COMMENT '创建时间',
  `updateTime` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  unique key t_bi_site_order_hlht_biAccountId_corpId(`biAccountId`, `corpId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS t_yw_order;
CREATE TABLE `t_yw_order` (
  `ywOrderNo` varchar(32) NOT NULL COMMENT '运维工单编号',
  `siteId` varchar(32) NOT NULL COMMENT '场站编号',
  `orderStatus` enum('INIT','RECEIVED','PROCESSING','TRANSFERRING','WAIT_CHECK','NO_PASS','SOLVED') DEFAULT 'INIT' COMMENT '工单状态(INIT: 待接收; RECEIVED: 已接收; PROCESSING: 处理中; TRANSFERRING: 转派中; WAIT_CHECK : 待质检; NO_PASS: 不合格; SOLVED: 已完成;)',
  `evseNoList` text DEFAULT NULL COMMENT '故障的充电桩编号(可多个)',
  `overExpireDate` tinyint(1) DEFAULT NULL COMMENT '是否过质保期(桩存在未过质保期则为否(false))',
  `orderNo` varchar(32) DEFAULT '' COMMENT '关联充电订单号',
  `faultImages` text DEFAULT NULL COMMENT '故障图片',
  `faultDesc` varchar(400) DEFAULT '' COMMENT '故障描述',
  `faultLevel` int(2) unsigned NOT NULL DEFAULT '0' COMMENT '故障等级: 10(一般); 20(紧急); 30(重大)',
  `sourceType` int(4) NOT NULL DEFAULT '0' COMMENT '创建运维工单客户端. 0,未知; 20,充电管理平台; 21,运营支撑平台; 22,企业客户平台; 23,桩管家小程序',
  `goods` text DEFAULT NULL COMMENT '配件(运维物品): 例: [{"name": "物品名称", "num": 1}]',
  `carInfo` text DEFAULT NULL COMMENT '涉及车辆信息: 例: [{"brand": "车辆品牌", "model": "车辆类型", "needVoltage": "需求电压", "needCurrent": "需求电流", "otherEvse": true}]',
  `maintType` int(4) NOT NULL DEFAULT '0' COMMENT '运维人类型. 0,未知; 1,商户/平台用户; 3,企业客户; 4,系统',
  `maintUid` bigint(20) NOT NULL DEFAULT '0' COMMENT '运维人ID',
  `maintName` varchar(32) NOT NULL DEFAULT '' COMMENT '运维人名字',
  `maintTime` datetime DEFAULT NULL COMMENT '运维人处理时间',
  `qcType` int(4) NOT NULL DEFAULT '0' COMMENT '质检人类型. 0,未知; 1,商户/平台用户; 3,企业客户; 4,系统',
  `qcUid` bigint(20) DEFAULT '0' COMMENT '质检人ID',
  `qcName` varchar(32) DEFAULT '' COMMENT '质检人名字',
  `qcTime` datetime DEFAULT NULL COMMENT '质检人处理时间',
  `remote` tinyint(1) DEFAULT NULL COMMENT '是否远程解决',
  `faultReason` varchar(400) DEFAULT '' COMMENT '可能原因',
  `checkStep` varchar(400) DEFAULT '' COMMENT '检查步骤',
  `dealProcess` varchar(400) DEFAULT '' COMMENT '处理措施及结果',
  `images` text DEFAULT NULL COMMENT '维修图片',
  `recNotice` tinyint(1) DEFAULT '0' COMMENT '创建工单的用户是否已经接收处理结果反馈',
  `isPerfect` tinyint(1) DEFAULT '0' COMMENT '客户是否满意,0-默认项，1-满意,2-不满意',
  `advice` varchar(300) DEFAULT '' COMMENT '客户建议',
  `signImage` varchar(100) DEFAULT '' COMMENT '客户签名',
  `createOpType` int(4) NOT NULL DEFAULT '0' COMMENT '创建操作人类型. 0,未知; 1,商户/平台用户; 3,企业客户; 4,系统',
  `createOpUid` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建操作人ID',
  `createOpName` varchar(32) NOT NULL DEFAULT '' COMMENT '创建操作人名字',
  `updateOpType` int(4) NOT NULL DEFAULT '0' COMMENT '操作人类型. 0,未知; 1,商户/平台用户; 3,企业客户; 4,系统',
  `updateOpUid` bigint(20) NOT NULL DEFAULT '0' COMMENT '操作人ID',
  `updateOpName` varchar(32) NOT NULL DEFAULT '' COMMENT '操作人名字',
  `createTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updateTime` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `ywType` int(4) NOT NULL COMMENT '运维工单类型 1：光； 2：储； 3：充；',
  PRIMARY KEY (`ywOrderNo`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS t_yw_order_log;
CREATE TABLE `t_yw_order_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ywOrderNo` varchar(32) NOT NULL COMMENT '运维工单编号',
  `orderStatus` enum('INIT','RECEIVED','PROCESSING','TRANSFERRING', 'WAIT_CHECK','NO_PASS','SOLVED') NULL DEFAULT 'INIT' COMMENT '工单状态(INIT: 待接收; RECEIVED: 已接收; PROCESSING: 处理中; TRANSFERRING: 转派中; WAIT_CHECK : 待质检; NO_PASS: 不合格; SOLVED: 已完成;)',
  `opType` int(4) not null default 0 comment '操作类型. 0,未知; ',
  `opUid` bigint(20) not null default 0 comment '操作人ID',
  `opName` varchar(32) not null default '' comment '操作人名字',
  `opTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP comment '操作人操作时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS t_download_job;
CREATE TABLE `t_download_job` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `uid` bigint(20) NOT NULL COMMENT '申请人ID(sys_user.id)',
  `status` enum('WAIT','PROCESSING','PROCESSING_FAIL','COMPLETED','CANCEL') DEFAULT 'WAIT' COMMENT '进行状态: 待处理(WAIT)，下载中(PROCESSING)，下载中异常(PROCESSING_FAIL)，下载完成(COMPLETED)，已失效(CANCEL)',
  `functionMap` varchar(60) NULL COMMENT '请求功能映射标识符',
  `reqParam` text NULL COMMENT '请求参数',
  `filePosition` text NULL COMMENT '文件位置信息',
  `fileType` enum('UNKNOWN','EXCEL','PDF') DEFAULT 'UNKNOWN' COMMENT '文件类型(用于下载文件寻址):UNKNOWN(未知);EXCEL(excel):PDF(pdf文件)',
  `startTime` datetime NULL COMMENT '开始下载时间',
  `completeTime` datetime NULL COMMENT '完成时间',
  `createTime` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `updateTime` datetime NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) DEFAULT CHARSET=utf8mb4;