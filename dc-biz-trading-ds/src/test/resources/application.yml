app:
  name: TradingDsTest

server:
  address: 0.0.0.0
  port: 8080
  use-forward-headers: true
  compression.enabled: true


management:
  context-path: /admin
  security:
    enabled: false



logging:
  level:
    com.cdz360: 'DEBUG'
    org.springframework: 'INFO'
    org.springframework.transaction: DEBUG
    org.springframework.jdbc: DEBUG
    com.zaxxer.hikari: DEBUG

  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] - %logger{36}.%M\\(%line\\) - %msg%n"



spring:
  h2:
    console:
      enabled: true
  datasource:
    continue-on-error: false
    driver-class-name: org.h2.Driver
    name: cus_db
    username: sa
    password:
    url: jdbc:h2:mem:testdbsa;mode=MySQL
    initialPoolSize: 1
    minPoolSize: 1
    maxPoolSize: 20
    acquireIncrement: 1
    maxIdleTime: 10
    checkoutTimeout: 30000