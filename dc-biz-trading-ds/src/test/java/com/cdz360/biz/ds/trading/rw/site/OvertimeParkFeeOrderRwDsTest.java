package com.cdz360.biz.ds.trading.rw.site;

import com.cdz360.biz.ds.trading.TradingDsTestBase;
import com.cdz360.biz.ds.trading.ro.site.ds.OvertimeParkFeeOrderRoDs;
import com.cdz360.biz.ds.trading.rw.site.ds.OvertimeParkFeeOrderRwDs;
import com.cdz360.biz.ds.trading.rw.site.mapper.OvertimeParkFeeOrderRwMapper;
import com.cdz360.biz.model.trading.site.po.OvertimeParkFeeOrderPo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

class OvertimeParkFeeOrderRwDsTest extends TradingDsTestBase {

    @Autowired
    private OvertimeParkFeeOrderRwDs overtimeParkFeeOrderRwDs;

    @Autowired
    private OvertimeParkFeeOrderRwMapper overtimeParkFeeOrderRwMapper;

    private static final String ORDER_NO = "ORDER_NO123456789"; // data.sql exist

    @Test
    void getByOrderNo() {
        OvertimeParkFeeOrderPo po = overtimeParkFeeOrderRwDs.getByOrderNo(ORDER_NO, true);
        assertNotNull(po);
        po = overtimeParkFeeOrderRwDs.getByOrderNo("", true);
        assertNull(po);

//        =========================================

        po = overtimeParkFeeOrderRwMapper.getByOrderNo(ORDER_NO, true);
        assertNotNull(po);
        po = overtimeParkFeeOrderRwMapper.getByOrderNo("", true);
        assertNull(po);
    }

    @Test
    void insertOvertimeParkFeeOrder() {
        String tmpOrderNo = "TEMP_ORDER_NO123456789";

        OvertimeParkFeeOrderPo po;
        po = overtimeParkFeeOrderRwDs.getByOrderNo( tmpOrderNo + "1", true);
        assertNull(po);

        po = new OvertimeParkFeeOrderPo();
        po.setOrderNo(tmpOrderNo + "1")
                .setHlhtOrderNo("HLHT123456789")
                .setUid(123L)
                .setSiteId("123456789")
                .setFreeTime(10)
                .setParkingFee(BigDecimal.TEN)
                .setStopTime(new Date())
                .setCalFromTime(new Date())
                .setCalToTime(new Date())
                .setParkingPrice(BigDecimal.TEN);
        boolean b = overtimeParkFeeOrderRwDs.insertOvertimeParkFeeOrder(po);
        assertTrue(b, "新增记录");

        po = overtimeParkFeeOrderRwDs.getByOrderNo( tmpOrderNo + "1", true);
        assertNotNull(po);

//        ==============================================================

        po = overtimeParkFeeOrderRwMapper.getByOrderNo(tmpOrderNo, true);
        assertNull(po);

        po = new OvertimeParkFeeOrderPo();
        po.setOrderNo(tmpOrderNo)
                .setHlhtOrderNo("HLHT123456789")
                .setUid(123L)
                .setSiteId("123456789")
                .setFreeTime(10)
                .setParkingFee(BigDecimal.TEN)
                .setStopTime(new Date())
                .setCalFromTime(new Date())
                .setCalToTime(new Date())
                .setParkingPrice(BigDecimal.TEN);
        int i = overtimeParkFeeOrderRwMapper.insertOvertimeParkFeeOrder(po);
        assertTrue(i > 0, "新增记录");

        po = overtimeParkFeeOrderRwMapper.getByOrderNo(tmpOrderNo, true);
        assertNotNull(po);
    }

}