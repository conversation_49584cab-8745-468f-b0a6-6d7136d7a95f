package com.chargerlinkcar.core.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * AmqpSenderServiceImpl
 *
 * @since 2019/9/2 13:40
 * <AUTHOR>
 */
@Slf4j
@Service
public class AmqpSenderServiceImpl implements AmqpMessageSenderService {
    @Resource
    private Sender sender;

    @Override
    public void send(String message, String routingKey) {
        sender.send(message, routingKey);
    }
}
