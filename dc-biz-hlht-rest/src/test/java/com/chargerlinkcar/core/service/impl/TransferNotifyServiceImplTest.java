package com.chargerlinkcar.core.service.impl;

import com.chargerlinkcar.core.HlhtTestBase;
import com.chargerlinkcar.core.domain.TransferNotify;
import com.chargerlinkcar.core.service.TransferNotifyService;
import com.chargerlinkcar.framework.common.utils.UUIDUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Random;

/**
 * @ProjectName chargerlink-car
 * <AUTHOR>
 * @CreateDate 2019/9/5 13:42
 */
@Slf4j
public class TransferNotifyServiceImplTest extends HlhtTestBase {
    TransferNotify insertObj = null;
    TransferNotify insertSelectiveObj = null;
    TransferNotify insertForSelect = null;
    TransferNotify deletObj = null;
    TransferNotify updateObj = null;
    TransferNotify updateSelectiveObj = null;
    @Autowired
    private TransferNotifyService transferNotifyService;

    @BeforeEach
    public void setUp() {
        insertObj = this.getTransferNotify();
        insertSelectiveObj = this.getTransferNotify();
        insertForSelect = this.getTransferNotify();
        deletObj =  this.getTransferNotify();
        updateObj = this.getTransferNotify();
        updateSelectiveObj =  this.getTransferNotify();
    }

    @Test
    public void testDeleteByPrimaryKey() {
        transferNotifyService.insertSelective(deletObj);
        int count = transferNotifyService.deleteByPrimaryKey(deletObj.getId());
        Assertions.assertTrue(count > 0);
    }

    @Test
    public void testInsert() {
        int count = transferNotifyService.insert(insertObj);
        TransferNotify transferNotify = transferNotifyService.selectByPrimaryKey(insertObj.getId());
        log.info("count = {},transferNotify = {}", count, transferNotify);
        Assertions.assertTrue(count > 0 && transferNotify != null);
    }

    @Test
    public void testInsertSelective() {
        TransferNotify transferNotify = new TransferNotify();
        BeanUtils.copyProperties(insertObj,transferNotify);
        transferNotify.setIncomeBankCardName("");
        transferNotify.setMsgId(null);
        transferNotify.setIncomeBankName(null);
        int count = transferNotifyService.insertSelective(transferNotify);

        transferNotify = transferNotifyService.selectByPrimaryKey(transferNotify.getId());
        log.info("count = {},transferNotify = {}", count, transferNotify);
        Assertions.assertTrue(count > 0 && transferNotify != null);
    }

    @Test
    public void testSelectByPrimaryKey() {
        transferNotifyService.insertSelective(insertForSelect);
        TransferNotify transferNotify = transferNotifyService.selectByPrimaryKey(insertForSelect.getId());
        Assertions.assertNotNull(transferNotify);
    }

    @Test
    public void testUpdateByPrimaryKeySelective() {
        transferNotifyService.insertSelective(updateSelectiveObj);
        int count = transferNotifyService.updateByPrimaryKeySelective(updateSelectiveObj);
        Assertions.assertTrue(count > 0);
    }

    @Test
    public void testUpdateByPrimaryKey() {
        transferNotifyService.insertSelective(updateObj);
        int count = transferNotifyService.updateByPrimaryKey(updateObj);
        Assertions.assertTrue(count > 0);
    }

    public TransferNotify getTransferNotify() {
        String randomSuffix = UUIDUtils.getRandom(true, 5);
        return new TransferNotify()
                .setMsgId(Long.parseLong(randomSuffix))
                .setTopCommId(33421L)
                .setRemitBankCardNo(UUIDUtils.getRandom(false, 5))
                .setRemitBankName("RBN".concat(randomSuffix))
                .setRemitBankCardName("RBCN".concat(randomSuffix))
                .setIncomeBankCardNo(UUIDUtils.getRandom(false, 5))
                .setIncomeBankName("IBN".concat(randomSuffix))
                .setIncomeBankCardName("IBCN".concat(randomSuffix))
                .setSettleStartTime(new Date())
                .setSettleEndTime(new Date())
                .setSettleTradeNum(Long.valueOf(randomSuffix))
                .setCommId(33421L)
                .setRemitAmount(new BigDecimal(2019.8888))
                .setRemitFee(new BigDecimal(10.8888))
                .setRealAmount(new BigDecimal(2029.8888))
                .setFlowType(new Random().nextInt(5))
                .setTradeNo(UUIDUtils.getRandom(false, 5))
                .setBankTradeNo(UUIDUtils.getRandom(false, 5));
    };
}