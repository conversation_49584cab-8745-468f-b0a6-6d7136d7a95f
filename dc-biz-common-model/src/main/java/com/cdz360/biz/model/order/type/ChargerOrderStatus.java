package com.cdz360.biz.model.order.type;

/**
 * 订单状态
 *
 * <AUTHOR>
 * @since 2019/7/31 16:29
 */
//public enum ChargerOrderStatus {
//    // 0：订单未激活；
//    INIT,
//    // 100：电闸打开；
//    STARTING,
//    // 200：充电中；
//    START,
//    // 250：关电闸，充电停止；
//    STOPPING,
//    // 300：充电完成；
//    // 800：订单结束；
//    STOP,
//    CANCEL,
//    // -500：硬件故障；-40：上报超时；-35：断连超时；-30关电超时；-29：结束充电失败；
//    // -20：链接超时；-10：打开电闸超时；-7：开启充电失败，PIN可能错误；-5:系统繁忙；
//    ERROR;
//
//    private ChargerOrderStatus() {
//    }
//}
