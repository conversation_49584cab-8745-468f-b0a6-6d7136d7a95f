package com.cdz360.biz.model.sys.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "投建场站组信息")
@Data
@Accessors(chain = true)
public class TjSiteGroupVo {

    @Schema(description = "场站组唯一ID")
    @JsonInclude(Include.NON_EMPTY)
    private String gid;

    @Schema(description = "场站组名称")
    @JsonInclude(Include.NON_EMPTY)
    private String name;
}
