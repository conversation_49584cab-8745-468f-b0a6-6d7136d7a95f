package com.cdz360.biz.model.bi.dashboard;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * SubCommStatisticBiVo
 *
 * @since 6/22/2020 3:40 PM
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper=false)
@ToString
@Accessors(chain = true)
@Schema(description = "首页-日营收数据对比")
public class SubCommStatisticBiVo extends CommStatisticBiVo {

    @Schema(description = "商户名称")
    private String commName = "";

    @Schema(description = "商户id")
    private Long commId = 0L;

    @Schema(description = "站点数")
    private Integer siteCount = 0;

    @Schema(description = "充电桩数")
    private Integer evseCount = 0;

    @Schema(description = "枪头数")
    private Integer plugCount = 0;

    @Schema(description = "当前总功率")
    private BigDecimal power = BigDecimal.ZERO;

    @Schema(description = "单KW 服务费收入")
    private BigDecimal servFeePerPower = BigDecimal.ZERO;

    @Schema(description = "统计总功率")
    private BigDecimal sumPower = BigDecimal.ZERO;

}