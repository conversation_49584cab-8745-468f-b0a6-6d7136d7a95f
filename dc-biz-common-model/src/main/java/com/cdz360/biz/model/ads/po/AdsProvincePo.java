package com.cdz360.biz.model.ads.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * t_ads_province,广告的投放区域和省份的关系表
 */
@Data
@Accessors(chain = true)
public class AdsProvincePo {
    @Schema(description = "ID")
    private Long id;

    @Schema(description = "广告ID")
    private Long adsId;

    @Schema(description = "省份ID")
    private String provinceCode;

    @Schema(description = "是否可用，0不可用，1可用")
    private Boolean enable;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
