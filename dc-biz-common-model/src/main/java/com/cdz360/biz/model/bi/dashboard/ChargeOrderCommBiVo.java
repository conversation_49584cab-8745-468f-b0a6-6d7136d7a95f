package com.cdz360.biz.model.bi.dashboard;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;


/**
 * ChargeOrderCommBiVo
 *
 * @since 6/22/2020 1:22 PM
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
@ToString
@Accessors(chain = true)
@Schema(description = "首页-充电订单统计")
public class ChargeOrderCommBiVo {

    @Schema(description = "充电订单数")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer orderCount = 0;

    @Schema(description = "总电量", required = true)
    private BigDecimal electricity = BigDecimal.ZERO;

    @Schema(description = "总金额", required = true)
    private BigDecimal fee = BigDecimal.ZERO;

    @Schema(description = "总服务费", required = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal servFee = BigDecimal.ZERO;

    @Schema(description = "总电费", required = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal elecFee = BigDecimal.ZERO;

    @Schema(description = "电量-尖", required = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal elecTag1 = BigDecimal.ZERO;

    @Schema(description = "电量-峰", required = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal elecTag2 = BigDecimal.ZERO;

    @Schema(description = "电量-平", required = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal elecTag3 = BigDecimal.ZERO;

    @Schema(description = "电量-谷", required = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal elecTag4 = BigDecimal.ZERO;
}