package com.cdz360.biz.model.evse.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ListPackageParam extends BaseListParam {

    @Schema(description = "模糊查询")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String keyWord;

    @Schema(description = "品牌")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String brand;

    @Schema(description = "版本")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String version;

    @Schema(description = "状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long status;

}
