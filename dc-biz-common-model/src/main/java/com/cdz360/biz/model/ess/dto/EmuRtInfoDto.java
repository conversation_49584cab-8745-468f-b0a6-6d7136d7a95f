package com.cdz360.biz.model.ess.dto;

import com.cdz360.base.model.es.vo.BmsRtInfo;
import com.cdz360.base.model.es.vo.DehRtInfo;
import com.cdz360.base.model.es.vo.FfsRtInfo;
import com.cdz360.base.model.es.vo.LiquidRtInfo;
import com.cdz360.base.model.es.vo.PcsRtInfo;
import com.cdz360.base.model.es.vo.UpsRtInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EmuRtInfoDto extends com.cdz360.base.model.es.vo.EmuRtInfo{

   /**
    * PCS 信息列表
    */
   @Schema(title = "PCS实时信息")
   @JsonInclude(Include.NON_NULL)
   private List<PcsRtInfo> pcsList;

   /**
    * BMS 信息列表
    */
   @Schema(title = "BMS实时信息")
   @JsonInclude(Include.NON_NULL)
   private List<BmsRtInfo> bmsList;

   @Schema(title = "液冷实时信息")
   @JsonInclude(Include.NON_NULL)
   private List<LiquidRtInfo> liquidList;

   @Schema(title = "消防系统实时信息")
   @JsonInclude(Include.NON_NULL)
   private List<FfsRtInfo> ffsList;

   @Schema(title = "UPS实时信息")
   @JsonInclude(Include.NON_NULL)
   private List<UpsRtInfo> upsList;

   @Schema(title = "除湿器实时信息")
   @JsonInclude(Include.NON_NULL)
   private List<DehRtInfo> dehList;

}
