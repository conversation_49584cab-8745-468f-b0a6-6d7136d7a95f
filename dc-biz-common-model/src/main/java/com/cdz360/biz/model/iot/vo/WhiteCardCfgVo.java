package com.cdz360.biz.model.iot.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * WhiteCardCfgVo
 *
 * @since 2019/8/13 18:48
 * <AUTHOR>
 */
@Data
public class WhiteCardCfgVo {
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cardNo;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cardChipNo;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String remark;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cardName;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String blocUserName;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String blocUserPhone;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String username;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commName;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String phone;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String passWord;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cardStatus;
}
