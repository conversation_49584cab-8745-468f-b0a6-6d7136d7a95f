package com.cdz360.biz.model.exception;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.exception.DcException;
import org.slf4j.event.Level;

public class DcNoRollbackForException extends DcException {
    private static final int STATUS = DcConstants.KEY_RES_CODE_SERVICE_ERROR;


    public DcNoRollbackForException(int status, String msg) {
        super(status, msg);
    }

    public DcNoRollbackForException(String msg) {
        super(STATUS, msg);
    }

    public DcNoRollbackForException(String msg, Throwable e) {
        super(STATUS, msg, e);
    }


    public DcNoRollbackForException(String msg, Level logLevel) {
        super(STATUS, msg, logLevel);
    }


    public DcNoRollbackForException(int status, String msg, Level logLevel) {
        super(status, msg, logLevel);
    }
}
