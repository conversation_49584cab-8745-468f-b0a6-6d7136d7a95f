package com.cdz360.biz.model.site.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(title = "储能站基础信息", description="储能站基础信息.")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EssSiteSmallDto extends SiteSmallDto {

    @Schema(title = "储能装机功率", description = "储能装机功率")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal essPower;


    @Schema(title = "储能装机容量", description = "储能装机容量")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal essCapacity;


    /**
     * 上线时间
     **/
    @Schema(title = "场站上线时间", description = "场站上线时间")
    @JsonInclude(Include.NON_NULL)
    private Date onlineDate;

    @Schema(title = "场站图片", description = "场站图片")
    @JsonInclude(Include.NON_NULL)
    private List<String> images;
}
