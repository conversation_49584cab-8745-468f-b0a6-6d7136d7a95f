package com.cdz360.biz.model.ess.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "查询光储ESS 挂载设备信息列表参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListEssEquipParam extends BaseListParam {

    @Schema(description = "挂载设备信息名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String essEquipName;

    @Schema(description = "商户Id链")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commIdChain;

    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "场站ID列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> siteIdList;

    @Schema(description = "ESS设备编号 平台分配唯一")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String essDno;

    @Schema(description = "设备编号列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> dnoList;

    @Schema(description = "设备类型")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<EssEquipType> equipTypes;

    @Schema(description = "电池簇ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long batteryClusterEquipId;

    @Schema(description = "电池簇号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String batteryClusterNo;

    @Schema(description = "电池组SN")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String batteryPackSN;

    @Schema(description = "BMS设备号")
    @JsonInclude(Include.NON_NULL)
    private List<String> bmsDnos;

    @Schema(title = "状态", description = "设备状态.0未知;1在线;2离线;3启动中;4告警;5故障;99,下线")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<EquipStatus> statusList;
}
