package com.cdz360.biz.model.bi.site;

import com.cdz360.biz.model.common.vo.BiDaily;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * SiteUtilization
 * 
 * @since 3/19/2020 4:46 PM
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
@ToString
@Accessors(chain = true)
@Schema(description = "场站充电时长利用率")
public class SiteUtilization implements BiDaily {
    private Long id;
    @Schema(description = "场站ID")
    private String siteId;
    @Schema(description = "采样时间点")
    private Date date;
    @Schema(description = "充电时长", required = true)
    private Long duration = 0L;
    @Schema(description = "利用率，精确2位小数的百分数:98.76", required = true)
    private BigDecimal useRate = BigDecimal.ZERO;
    @Schema(description = "单个枪头平均使用时长（秒）", required = true)
    private Long plugUseTime = 0L;
    @Schema(description = "功率利用率，精确2位小数的百分数:98.76", required = true)
    private BigDecimal powerUseRate = BigDecimal.ZERO;
//    @Schema(description = "功率和", required = true)
//    private BigDecimal powerSum = BigDecimal.ZERO;
    @Schema(description = "日单kW平均时长（秒）", required = true)
    private Long durationPerPower = 0L;
    @Schema(description = "选取的日功率的记录id", required = true)
    private Long dailyPowerId = 0L;
    @Schema(description = "场站优先功率。按idchain查询时，为所有场站各自t_bi_site_order最大id的功率和", required = true)
    private Long dailyPower = 0L;
    @Schema(description = "充电量", required = true)
    private BigDecimal electricitySum = BigDecimal.ZERO;

//    private BigDecimal factor = BigDecimal.ZERO;


    @Override
    @JsonIgnore
    public LocalDate getFullDate() {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }
}