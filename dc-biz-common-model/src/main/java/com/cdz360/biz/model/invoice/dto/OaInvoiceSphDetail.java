package com.cdz360.biz.model.invoice.dto;

import com.cdz360.biz.model.invoice.type.ProductType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "开票商品行信息")
@Data
@Accessors(chain = true)
public class OaInvoiceSphDetail {

    @Schema(description = "分类编码 对应 Spbmbbh 的商品和服务税收分类编码")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String productCode;

    @Schema(description = "商品或服务名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String productName;

    @Schema(description = "商品类别: ACTUAL_FEE -- 实际金额消费;" +
        "SERV_ACTUAL_FEE -- 实际金额服务费; ELEC_ACTUAL_FEE -- 实际金额电费;PARK_OUT_FEE -- 超时停车费")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ProductType productType;

    @Schema(description = "商品规格型号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String spec;

    @Schema(description = "商品单位")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String unit;

    @Schema(description = "商品税率 16,6表示16%，6%")
    private Integer taxRate;

    // ============== 开票金额 ==============
    @Schema(description = "商品数量")
    private int count;

    @Schema(description = "应开金额,单位: 元", example = "12.34")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal amount;

    @Schema(description = "实开金额,单位: 元", example = "12.34")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal fixAmount;
}
