package com.cdz360.biz.model.invoice.dto;

import com.cdz360.biz.model.invoice.type.InvoiceType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "购方开票信息")
@Data
@Accessors(chain = true)
public class OaInvoiceBuyerInfo {

    @Schema(description = "开票种类")
    @JsonInclude(Include.NON_NULL)
    private InvoiceType type;

    @Schema(description = "购买方纳税人税号", requiredMode = RequiredMode.REQUIRED)
    private String tin;

    @Schema(description = "购买方名称(发票抬头)", requiredMode = RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "购买方地址", requiredMode = RequiredMode.NOT_REQUIRED)
    private String address;

    @Schema(description = "购买方邮箱")
    private String email;

    @Schema(description = "购买方电话")
    private String phone;

    @Schema(description = "购买方开户行")
    private String bankName;

    @Schema(description = "购买方银行账号")
    private String bankAccount;

    //  ============== 收件人相关信息 ===============
    @Schema(description = "收件人相关信息")
    private OaReceiver receiver;
    //  ============== 收件人相关信息 ===============
}
