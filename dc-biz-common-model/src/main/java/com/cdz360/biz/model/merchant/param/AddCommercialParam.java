package com.cdz360.biz.model.merchant.param;

import com.cdz360.base.utils.JsonUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Schema(description = "修改商户信息参数")
@EqualsAndHashCode(callSuper = true)
public class AddCommercialParam extends UpdateCommercialParam {

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}
