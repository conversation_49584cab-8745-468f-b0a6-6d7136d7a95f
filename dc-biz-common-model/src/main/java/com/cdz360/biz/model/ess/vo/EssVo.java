package com.cdz360.biz.model.ess.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "光出ESS信息")
@Data
@Accessors(chain = true)
@JsonInclude(Include.NON_NULL)
public class EssVo {

    @Schema(description = "主键ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long id;

    @Schema(description = "微网控制器编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String gwno;

    @Schema(description = "ESS编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String dno;

    @Schema(description = "设备sn")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String sn;

    @Schema(description = "设备名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String name;

    @Schema(description = "通讯状态")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long status;

    @Schema(description = "告警状态")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer alertStatus;

    @Schema(description = "场站名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;

    @Schema(description = "控制器名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String gwName;

    @Schema(description = "品牌名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String vendor;

    @Schema(title = "时区", example = "GMT+08:00/UTC+08:00")
    private String timeZone;

    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<EssEquipVo> essEquipVoList;
}
