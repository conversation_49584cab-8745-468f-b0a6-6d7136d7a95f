package com.cdz360.biz.model.discount.param;

import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.biz.model.discount.type.ProtocolType;
import com.cdz360.biz.model.discount.vo.CustomFee;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "场站服务费基础计费查询参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class DiscountServiceParam extends BaseObject {
    @Schema(description = "场站ID列表", required = true)
    @NotNull(message = "场站ID列表(siteIdList)不能为空")
    private List<String> siteIdList;

    @Schema(description = "计费模板ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long priceCode;

    @Schema(description = "协议价类型: 11(固定服务费); 12(折扣比例);" +
            " 13(固定总价); 14(自定义尖峰平谷); 15(自定义服务费尖峰平谷); 0(未知)")
    @NotNull(message = "type 不能为 null")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ProtocolType type;

    @Schema(description = "协议价 type不一样，意思也不一样")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal discount;

    @Schema(description = "自定义尖峰平谷 协议价类型为14,15时存在")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private CustomFee discountCustomFee;
}
