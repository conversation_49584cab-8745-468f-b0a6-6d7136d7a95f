package com.cdz360.biz.model.site.dto;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OaSiteMonthDataDto {

    @Schema(description = "场站ID")
    @JsonInclude(Include.NON_NULL)
    private String siteId;

    @Schema(description = "充电量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal elec;

    @Schema(description = "电费收入,单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal elecFee;

    @Schema(description = "服务费收入,单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal servFee;

    @Schema(description = "自动扣款: true/false")
    @JsonInclude(Include.NON_NULL)
    private Boolean autoDebit;

    @Deprecated(since = "20220930")
    @Schema(description = "月份(记录为每月1号)")
    @JsonInclude(Include.NON_NULL)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private LocalDate date;

    @Schema(description = "账期日期开始时间(0点)")
    @JsonInclude(Include.NON_NULL)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private LocalDate fromDate;


    @Schema(description = "账期日期结束时间(24点)")
    @JsonInclude(Include.NON_NULL)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private LocalDate toDate;

    @Schema(description = "流程实例ID")
    @JsonInclude(Include.NON_EMPTY)
    private String procInstId;

    @Schema(description = "充电订单汇总数据")
    @JsonInclude(Include.NON_EMPTY)
    private String orderData;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
