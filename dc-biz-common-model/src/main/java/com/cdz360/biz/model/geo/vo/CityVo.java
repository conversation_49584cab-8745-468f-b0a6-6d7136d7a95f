package com.cdz360.biz.model.geo.vo;

import com.cdz360.biz.model.geo.po.CityPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CityVo extends CityPo {

    @Schema(description = "省份名称", example = "广东")
    private String provinceName;

}
