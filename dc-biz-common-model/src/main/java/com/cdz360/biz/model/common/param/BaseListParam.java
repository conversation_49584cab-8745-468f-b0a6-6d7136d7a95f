package com.cdz360.biz.model.common.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 *
 * @since 2019/8/7
 **/
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BaseListParam
        extends com.cdz360.base.model.base.param.BaseListParam {
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String type;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "集团商户ID(一般后端赋值)", example = "123")
    private Long topCommId;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "商户ID(一般后端赋值)", example = "123")
    private Long commId;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "商户ID链", example = "34474,34475")
    private String commIdChain;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "企业ID(一般后端赋值)", example = "123")
    private Long corpId;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "当前操作人ID(一般后端赋值)", example = "123")
    private Long opId;



//    public BaseListParam() {
//    }
//
//    public String getType() {
//        return this.type;
//    }
//
//    public BaseListParam setType(String type) {
//        this.type = type;
//        return this;
//    }
//
//    public String getSk() {
//        return this.sk;
//    }
//
//    public BaseListParam setSk(String sk) {
//        this.sk = sk;
//        return this;
//    }
//
//    public Boolean getEnable() {
//        return this.enable;
//    }
//
//    public BaseListParam setEnable(Boolean enable) {
//        this.enable = enable;
//        return this;
//    }
//
////    public boolean isLock() {
////        return this.lock;
////    }
////
////    public BaseListParam setLock(boolean lock) {
////        this.lock = lock;
////        return this;
////    }
//
//    public Long getStart() {
//        return this.start;
//    }
//
//    public BaseListParam setStart(Long start) {
//        this.start = start;
//        return this;
//    }
//
//    public Integer getSize() {
//        return this.size;
//    }
//
//    public BaseListParam setSize(Integer size) {
//        this.size = size;
//        return this;
//    }
//
//    public List<SortParam> getSorts() {
//        return this.sorts;
//    }
//
//    public BaseListParam setSorts(List<SortParam> sorts) {
//        this.sorts = sorts;
//        return this;
//    }
}
