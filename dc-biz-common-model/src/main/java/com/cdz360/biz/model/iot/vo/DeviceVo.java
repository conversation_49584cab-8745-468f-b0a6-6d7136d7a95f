package com.cdz360.biz.model.iot.vo;

import com.cdz360.biz.model.iot.po.EvseModuleDetailPo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class DeviceVo {

    @Schema(description = "器件名称")
    private String deviceName;

    @Schema(description = "型号")
    private String moduleType;

    @Schema(description = "器件详情")
    private List<EvseModuleDetailPo> detailList;

    private String siteId;
    private String siteName;
}
