package com.cdz360.biz.model.invoice.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "收件人信息")
@Data
@Accessors(chain = true)
public class OaReceiver {

    @Schema(description = "收件人名称")
    private String name;

    @Schema(description = "收件人联系电话")
    private String phone;

    @Schema(description = "收件人省")
    private String provinceName;

    @Schema(description = "收件人省(编号)")
    private String provinceCode;

    @Schema(description = "收件人市")
    private String cityName;

    @Schema(description = "收件人市(编号)")
    private String cityCode;

    @Schema(description = "收件人区")
    private String areaName;

    @Schema(description = "收件人区(编号)")
    private String areaCode;

    @Schema(description = "收件人详细地址")
    private String address;
}
