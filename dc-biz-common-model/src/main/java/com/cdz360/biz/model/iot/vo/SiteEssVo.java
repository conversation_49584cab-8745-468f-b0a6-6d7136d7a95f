package com.cdz360.biz.model.iot.vo;

import com.cdz360.base.model.es.vo.EssDeviceBiVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Schema(description = "场站数量")
@Data
@EqualsAndHashCode(callSuper = true)
public class SiteEssVo extends EssDeviceBiVo {

    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

}
