package com.cdz360.biz.model.iot.vo;

import com.cdz360.base.model.es.type.EquipCfgStatus;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.biz.model.ess.type.EquipAlertStatus;
import com.cdz360.biz.model.iot.type.GtiVendor;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "逆变器信息")
@Data
@Accessors(chain = true)
public class GtiVo {

    private Long id;

    @Schema(description = "微网控制器编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String gwno;

    @Schema(description = "逆变器唯一编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String dno;

    @Schema(description = "设备名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String name;

    @Schema(description = "场站名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;

    @Schema(description = "控制器名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String gwName;

    @Schema(description = "串口通信(485/modbus) ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer sid;

    @Schema(description = "品牌名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private GtiVendor vendor;

    @Schema(description = "组串个数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer groupNum;

    @Schema(title = "状态", description = "设备状态.0未知;1在线;2离线;3启动中;4告警;5故障;99,下线")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private EquipStatus status;

    @Schema(description = "告警状态: 0,未知;1,正常;2,异常")
    private EquipAlertStatus alertStatus;

    @Schema(description = "当前配置模板ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long cfgId;

    @Schema(description = "配置信息状态: 0,未知;1,已下发;2,下发中;3,下发失败")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private EquipCfgStatus cfgStatus;

    @Schema(description = "上次成功下发配置模板ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long cfgSuccessId;

    @Schema(description = "上次成功下发配置模板名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cfgSuccessName;

    @Schema(description = "最后一次下发成功时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(/*shape = JsonFormat.Shape.NUMBER, */pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date cfgSuccessTime;

    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "逆变器redis中保存的信息")
    private RedisPvRtData pvData;

    // 挂载ESS,则下面存在下面信息
    @Schema(description = "储能ESS编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String essDno;

    @Schema(description = "储能ESS编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String essName;

    @Schema(description = "储能ESS设备ID(t_ess_equip.id)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long essEquipId;

    @Schema(description = "本身从属储能ESS设备equipId")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long ownEquipId;

}
