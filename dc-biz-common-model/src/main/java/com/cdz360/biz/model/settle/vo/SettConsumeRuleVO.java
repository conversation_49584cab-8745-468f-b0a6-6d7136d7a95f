package com.cdz360.biz.model.settle.vo;

import com.cdz360.biz.model.settle.po.SettConsumeRule;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "结算消费清分规则")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SettConsumeRuleVO extends SettConsumeRule {

    @Schema(description = "消费对象 前端显示使用")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String accountName;
}
