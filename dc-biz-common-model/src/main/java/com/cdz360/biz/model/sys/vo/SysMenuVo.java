package com.cdz360.biz.model.sys.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "系统菜单信息")
@Data
@Accessors(chain = true)
public class SysMenuVo {

    public static final int MENU_TYPE = 1;
    public static final int PAGE_TYPE = 2;
    public static final int BUTTON_TYPE = 3;
    public static final int MODULE_TYPE = 0;

    private Long id;

    private Long pid;
    private String pids;
    private String perm;
    private String name;
    private String icon;
    private String url;

    private Long subsysId;
    /**
     * 1 菜单  2. 页面 3 按钮
     */
    private Integer type;
    private String tips;
    private Boolean hidden;
    private Integer status;

    private Boolean opened;

    private List<SysMenuVo> children;

    /**
     * 所属应用名称
     */

    private String subsysName;
    /**
     * 标记角色是否选中此菜单
     */

    private Boolean isChecked;
    private String extra;
    /**
     *
     */

    private String fullKey;
}
