package com.cdz360.biz.oa.bpmn.executor;

import com.cdz360.biz.oa.service.OaInstanceService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * OA流程放弃申请监听器
 */
@Slf4j
@Component(value = "oaEndCancelExecutor")
public class OaEndCancelExecutor implements JavaDelegate {

    @Autowired
    private OaInstanceService oaInstanceService;

    @Override
    public void execute(DelegateExecution execution) {
        log.info("提交人放弃申请: id = {}, activityId = {}",
            execution.getId(),
            execution.getCurrentActivityId());
        oaInstanceService.oaCancel(execution);
        log.info("<<< 提交人放弃申请完成");
    }

}
