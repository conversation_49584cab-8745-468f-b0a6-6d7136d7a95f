package com.cdz360.biz.oa.utils;

import com.cdz360.biz.model.oa.constant.OaConstants;
import com.cdz360.biz.oa.dto.ActHiVarinstDto;
import com.cdz360.biz.oa.dto.ProcessInstanceDto;
import com.cdz360.biz.oa.dto.ProcessInstanceExDto;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

/**
 * ConcernVarStrategyOtherFeePayApply
 *
 * @since 5/23/2023 2:07 PM
 * <AUTHOR>
 */
@Slf4j
@Service
public class ConcernVarStrategyOtherFeePayApply extends ConcernVarStrategyAbstract {

    @Autowired
    private OaProcessInstanceConcernInfoService util;

    @PostConstruct
    public void init() {
        util.addSettlementStrategy(OaConstants.PD_KEY_OTHER_FEE_PAY_APPLY, this);
    }

    @Override
    public ProcessInstanceExDto fillData(ProcessInstanceDto in, List<ActHiVarinstDto> vars) {
        final List<ActHiVarinstDto> varsConcern = vars.stream()
            .filter(e -> in.getId().equals(e.getProcInstId()))
            .collect(Collectors.toList());

        ProcessInstanceExDto out = new ProcessInstanceExDto();
        BeanUtils.copyProperties(in, out);

        final Pair<Date, Date> datePair = this.extractTimeRange("feeSettDate", varsConcern);
        if(datePair != null) {
            out.setBillStartTime(datePair.getFirst());
            out.setBillEndTime(datePair.getSecond());
        }
        out.setAmount(this.extractAmount("payAmount", varsConcern));

        log.info("{}", varsConcern);

        return out;
    }
}