package com.cdz360.biz.oa.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.oa.dto.OaProcessInstanceDto;
import com.cdz360.biz.oa.service.OaInstanceService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/oa/instance")
public class OaInstanceRest {

    @Autowired
    private OaInstanceService oaInstanceService;

    @ResponseBody
    @GetMapping(value = "/submitter/getDraftInstances")
    public ListResponse<OaProcessInstanceDto> getDraftInstances(@RequestParam Long uid) {
        log.info("获取未提交的审理单列表. uid: {}", uid);
        List<OaProcessInstanceDto> insts = this.oaInstanceService.getDraftInstances(uid);
        return RestUtils.buildListResponse(insts);
    }

    @Operation(summary = "申请人获取待审核的列表")
    @ResponseBody
    @GetMapping(value = "/submitter/getOwnerReviewInstances")
    public ListResponse<OaProcessInstanceDto> getOwnerReviewInstances(@RequestParam Long uid) {
        log.info("申请人获取待审核的审批单列表. uid = {}", uid);
        List<OaProcessInstanceDto> tasks = this.oaInstanceService.getOwnerReviewInstances(uid);
        return RestUtils.buildListResponse(tasks);
    }

}
