<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.oa.repository.ro.mapper.ActHiVarinstRoMapper">

  <select id="fastSearchProcInstIdBy" resultType="java.lang.String">
    select
      PROC_INST_ID_
    from act_hi_varinst
    <where>
    NAME_ = 'fastSearch'
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteId )">
        and SUBSTRING(JSON_SEARCH(CAST(TEXT_ AS json), 'one', #{siteId}), 1, 13) = '"$.siteIdList'
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteIdListStr )">
        and TEXT_ like CONCAT("%", #{siteIdListStr} ,"%")
      </if>
      <if test="corpId != null">
        and JSON_CONTAINS(json_extract(CAST(TEXT_ AS json), '$.corpIdList'), '${corpId}', '$') = 1
      </if>
    </where>
    order by CREATE_TIME_ desc
  </select>

  <select id="findConcernVar" resultType="com.cdz360.biz.oa.dto.ActHiVarinstDto">
    select

    var.ID_ as id,
    var.PROC_INST_ID_ as procInstId,
    var.NAME_ as name,
    var.VAR_TYPE_ as varType,
    var.DOUBLE_ as doubleVar,
    var.LONG_ as longVar,
    var.TEXT_ as text,
    var.BYTEARRAY_ID_ as byteArrayId,
    proc.PROC_DEF_ID_ as procDefinitionId,
    arr.BYTES_ AS longString,
    def.KEY_ as procInstKey

    from act_hi_varinst var
    left join act_hi_procinst proc on var.PROC_INST_ID_ = proc.ID_
    left join act_ge_bytearray arr on arr.ID_ = var.BYTEARRAY_ID_
    left join act_re_procdef def on def.ID_ = proc.PROC_DEF_ID_
    <where>
    var.PROC_INST_ID_ in
      <foreach collection="procInstIdList" item="procInstId" open="(" separator="," close=")">
        #{procInstId}
      </foreach>
    and var.NAME_ in
      <foreach collection="concernVarList" item="concernVar" open="(" separator="," close=")">
        #{concernVar}
      </foreach>
    </where>
    order by proc.START_TIME_ desc
  </select>
</mapper>