package com.cdz360.biz.model.trading.site.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "场站定时任务时间配置")
public class SiteChargeJobTimePo {
    private Long id;
    @Schema(description = "任务ID")
    private Long jobId;
    @Schema(description = "有效期开始时间")
    private Date startDate;
    @Schema(description = "有效期结束时间")
    private Date endDate;
    @Schema(description = "任务执行时间", example = "[{'operation': 'START', 'time': '12:10'}, {'operation': 'STOP', 'time': '14:10'}]")
    private List<JobTacticTime> time;

    private Date createTime;
}
