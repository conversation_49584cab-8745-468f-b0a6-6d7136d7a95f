package com.cdz360.biz.model.trading.iot.vo;

import com.cdz360.biz.model.trading.iot.po.GtiCfgPo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * GtiCfgVo
 *
 * @since 8/31/2021 4:34 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class GtiCfgVo extends GtiCfgPo {

    @Schema(description = "关联的逆变器ID")
    private List<Long> inverterIdList;

}