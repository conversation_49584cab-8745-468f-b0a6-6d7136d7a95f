package com.cdz360.biz.model.trading.site.vo;

import com.cdz360.biz.model.trading.site.po.SiteChargeJobPo;
import com.cdz360.biz.model.trading.site.po.SiteChargeJobTimePo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SiteChargeJobVo extends SiteChargeJobPo {

    @Schema(description = "此次关联的枪头列表")
    private List<String> plugNoList;

    @Schema(description = "任务时间配置")
    private List<SiteChargeJobTimePo> jobTimePoList;

    @Schema(description = "站点名称")
    private String siteName;

    @Schema(description = "扣款手机号")
    private String debitAccountPhone;
    @Schema(description = "扣款账户名称")
    private String debitAccountName;
    @Schema(description = "扣款账户所属商户ID")
    private Long debitAccountCommId;
    @Schema(description = "扣款账户-企业客户ID")
    private Long debitAccountCorpId;
    @Schema(description = "扣款账户-企业授信客户ID")
    private Long debitAccountRCorpUserId;

    @Schema(description = "任务名称模糊匹配")
    private String fuzzyJobName;

    @Schema(description = "扣款商户idChain")
    private String idChain;

    @Schema(description = "场站对应idChain")
    private String idChainSite;

    /**
     * 任务状态列表-查询参数
     */
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private List<Integer> statusList;

//    @Schema(name = "停充SOC", description = "充电订单SOC限制")
//    @JsonInclude(Include.NON_NULL)
//    private Integer stopSoc;

}
