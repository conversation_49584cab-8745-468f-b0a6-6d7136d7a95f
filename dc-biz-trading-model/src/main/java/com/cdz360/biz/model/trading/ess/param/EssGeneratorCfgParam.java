package com.cdz360.biz.model.trading.ess.param;


import com.cdz360.biz.model.trading.ess.type.GeneratorPowerOutMode;
import com.cdz360.biz.model.trading.ess.type.GeneratorStopMode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;


@Data

@Accessors(chain = true)

@Schema(description = "储能ESS柴油机配置")

public class EssGeneratorCfgParam {

    @Schema(description = "是否启用柴油机控制")
    private Boolean ctrlEnable;

    @Schema(description = "启停模式: SOC(SOC); TIME(时间); MANUAL(手动)")
    private GeneratorStopMode stopMode;

    @Schema(description = "开启soc")
    private Integer startSoc;

    @Schema(description = "停止soc")
    private Integer stopSoc;

    @Schema(description = "开启时间, 单位: 分钟")
    private Integer startTime;

    @Schema(description = "停止时间, 单位: 分钟")
    private Integer stopTime;

    @Schema(description = "输出功率模式: CHARGE_POWER(电能输出), GENERATOR(柴油机输出)")
    private GeneratorPowerOutMode powerOutMode;

    @Schema(description = "电池充电功率, 单位: 0.1kW")
    private BigDecimal chargePower;

    @Schema(description = "柴油机额定功率, 单位: kW")
    private BigDecimal ratedPower;
}

