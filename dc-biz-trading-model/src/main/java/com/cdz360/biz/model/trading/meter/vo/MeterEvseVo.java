package com.cdz360.biz.model.trading.meter.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * MeterEvseVo
 * 
 * @since 9/21/2020 1:35 PM
 * <AUTHOR>
 */

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MeterEvseVo extends MeterVo {

    private List<DeviceMeterVo> deviceMeterPoList;
}