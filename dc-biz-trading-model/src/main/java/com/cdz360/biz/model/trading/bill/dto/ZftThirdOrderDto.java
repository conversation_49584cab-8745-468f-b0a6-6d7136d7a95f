package com.cdz360.biz.model.trading.bill.dto;

import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.biz.model.trading.bill.type.DailyBillCheckResult;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "支付平台订单对账信息传输对象")
@Data
@Accessors(chain = true)
public class ZftThirdOrderDto implements Serializable {

    @Schema(description = "平台支付流水号")
    @Size(max = 64, message = "platformNo 长度不能超过 64")
    private String platformNo;

    @Schema(description = "渠道流水号")
    @Size(max = 64, message = "channelNo 长度不能超过 64")
    private String channelNo;

    @Schema(description = "支付平台对账结果: FULL_MATCH(完全匹配); AMOUNT_NOT_MATCH(金额不匹配); NO_NOT_MATCH(未找到对应订单); OTHERS(其他)")
    @NotNull(message = "checkResult 不能为 null")
    private DailyBillCheckResult checkResult;

    @Schema(description = "交易类型: IN_FLOW(收入), OUT_FLOW(支出)")
    @NotNull(message = "tradeType 不能为 null")
    private DepositFlowType tradeType;

    @Schema(description = "交易金额（单位: 元）")
    @NotNull(message = "tradeAmount 不能为 null")
    private BigDecimal tradeAmount;

    @Schema(description = "交易时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "tradeTime 不能为 null")
    private Date tradeTime;


    @Schema(description = "对账单ID(t_zft_daily_bill.id)")
    @NotNull(message = "dailyBillId 不能为 null")
    private Long dailyBillId;


//    =========== 关联表查询数据(t_zft_daily_bill) 👇 ===============
    @Schema(description = "支付平台账单名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String dailyBillName;

    @Schema(description = "所属直付商家")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String zftName;

    @Schema(description = "支付商家所属商户")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String zftCommName;

    @Schema(description = "交易渠道")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private PayChannel channel;

    @Schema(description = "互联互通订单号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String openOrderId;
//    =========== 关联表查询数据(t_zft_daily_bill) 👆 ===============

}
