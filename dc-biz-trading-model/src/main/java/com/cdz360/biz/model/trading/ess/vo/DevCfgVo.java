package com.cdz360.biz.model.trading.ess.vo;


import com.cdz360.biz.model.trading.ess.type.CfgType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "光储配置模板信息")
@Data
@Accessors(chain = true)
public class DevCfgVo {

    @Schema(description = "主键ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long id;

    @Schema(description = "模板名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String name;

    @Schema(description = "模板类型")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private CfgType type;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Date updateTime;

    @Schema(description = "更新人")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String opName;

    @Schema(description = "关联网元个数")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long amount;


}
