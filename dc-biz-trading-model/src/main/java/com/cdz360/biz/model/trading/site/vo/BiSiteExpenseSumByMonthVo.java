package com.cdz360.biz.model.trading.site.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "运营场站支出项按月汇总")
@Data
@Accessors(chain = true)
public class BiSiteExpenseSumByMonthVo {

    @Schema(description = "场站ID")
    @JsonInclude(Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "年月")
    @JsonInclude(Include.NON_NULL)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private LocalDate month;

    @Schema(description = "总支出金额,单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal fee;

    @Schema(description = "电费支出金额,单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal elecFee;

    @Schema(description = "租金支出金额,单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal rentFee;

    @Schema(description = "分成支出金额,单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal divideFee;

    @Schema(description = "劳务支出金额,单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal laborFee;

    @Schema(description = "引流支出金额,单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal attractFee;

    @Schema(description = "运维支出金额,单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal opsFee;

    @Schema(description = "折旧支出金额,单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal depreciationFee;

    @Schema(description = "其他支出金额,单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal otherFee;

    @Schema(description = "数量. 电费时为电量")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal number;
}
