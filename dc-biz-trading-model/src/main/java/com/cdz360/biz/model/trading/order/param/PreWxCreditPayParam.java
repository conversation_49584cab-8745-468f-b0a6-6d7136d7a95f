package com.cdz360.biz.model.trading.order.param;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.type.PayChannel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 微信信用充支付前置操作
 */
@Data
public class PreWxCreditPayParam {

    private Long commId;

    private String openid;

    @Schema(description = "支付渠道")
    private PayChannel payChannel;


    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private Long topCommId;

    /**
     * ip
     */
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String remoteIp;

    /**
     * 客户id
     */
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private long cusId;

    /**
     * 客户端类型
     */
    @Schema(hidden = true)
    private AppClientType appClientType;

    private AppClientType clientType;


    @Schema(title = "枪头二维码", description = "扫码的枪头二维码,用于拼接APP跳转RUI")
    private String qrCode;


    @Schema(title = "枪头编号", description = "扫码的枪头编号,用于拼接APP跳转RUI")
    private String plugNo;

}
