package com.cdz360.biz.model.trading.yw.param;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.model.FileItem;
import com.cdz360.biz.model.trading.yw.dto.Goods;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "运维工单解决参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SolvedYwOrderParam extends BaseOpParam {

    @Schema(description = "运维工单编号", required = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String ywOrderNo;

//    @Schema(description = "配件(运维物品): 例: [{\"name\": \"物品名称\", \"num\": 1}]")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private GoodsInfo goods;

    @Schema(description = "配件(运维物品): 例: [{\"name\": \"物品名称\", \"num\": 1}]")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Goods> goods;

    @Schema(description = "是否远程解决", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean remote;


    @Schema(description = "可能原因")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String faultReason;


    @Schema(description = "检查步骤")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String checkStep;



    @Schema(description = "处理措施及结果", required = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String dealProcess;



    @Schema(description = "维修图片")
    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private List<String> images;
    private List<FileItem> images;

    @Schema(description = "客户是否满意 1-满意，2-不满意", required = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long isPerfect;

    @Schema(description = "客户建议")
    private String advice;

    @Schema(description = "客户签名")
    private String signImage;


    public static void checkParam(SolvedYwOrderParam param) {
        if (StringUtils.isBlank(param.getYwOrderNo())) {
            throw new DcArgumentException("运维工单不能为空");
        }

//        if (null == param.getRemote()) {
//            throw new DcArgumentException("请选择是否支持远程解决");
//        }
//
//        if (StringUtils.isBlank(param.getDealProcess())) {
//            throw new DcArgumentException("请填写处理措施及结果");
//        }

        if (param.getGoods() == null) {
            if (param.getRemote() == null) {
                throw new DcArgumentException("请选择是否远程解决");
            }

            if (StringUtils.isBlank(param.getDealProcess())) {
                throw new DcArgumentException("请填写处理措施及结果");
            }
        }
    }
}
