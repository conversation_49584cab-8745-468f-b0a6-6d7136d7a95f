package com.cdz360.biz.model.trading.yw.type;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "运维工单状态")
@Getter
public enum YwOrderStatus {

    INIT("待接收"),

    RECEIVED("已接收"),

    PROCESSING("处理中"),

    TRANSFERRING("转派中"),

    WAIT_CHECK("待质检"),

    NO_PASS("不合格"),

    SOLVED("已完成"),

    DELETED("已删除"),

    SUSPEND("已挂起"),
    ;

    private String desc;

    YwOrderStatus(String desc) {
        this.desc = desc;
    }
}
