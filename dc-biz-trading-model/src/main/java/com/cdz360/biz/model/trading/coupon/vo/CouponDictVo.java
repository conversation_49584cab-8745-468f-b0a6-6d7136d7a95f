package com.cdz360.biz.model.trading.coupon.vo;

import com.cdz360.biz.model.trading.coupon.po.ActivityPo;
import com.cdz360.biz.model.trading.coupon.po.CouponDictPo;
import com.cdz360.biz.model.trading.coupon.po.CouponScoreSettingPo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * CouponDictVo
 *
 * @since 7/29/2020 1:12 PM
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class CouponDictVo extends CouponDictPo {
    private List<SitePo> siteList;
    private List<ActivityPo> activityList;
    private String commName;
    private String idChain;
    private Long siteAmount;
    private List<CouponScoreSettingPo> scoreSettingList;
}