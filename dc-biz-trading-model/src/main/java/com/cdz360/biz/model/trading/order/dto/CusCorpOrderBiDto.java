package com.cdz360.biz.model.trading.order.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * CusCorpOrderBiDto
 *
 * @since 3/10/2022 2:00 PM
 * <AUTHOR>
 */
@Data
@Schema(description = "客户/企业充电统计")
@Accessors(chain = true)
@ToString(callSuper = true)
public class CusCorpOrderBiDto {
    private List<CusOrderBiDto> userBi;
    private List<CusOrderBiDto> corpBi;
}