package com.cdz360.biz.model.trading.invoice.dto;

import com.cdz360.biz.model.trading.invoice.po.InvoiceRecordOrderRefPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(description = "企业开票记录和订单关联信息")
public class InvoiceRecordOrderRefDto extends InvoiceRecordOrderRefPo {
}
