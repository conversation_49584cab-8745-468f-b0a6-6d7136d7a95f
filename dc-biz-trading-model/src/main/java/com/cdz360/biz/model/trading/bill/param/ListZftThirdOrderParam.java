package com.cdz360.biz.model.trading.bill.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.biz.model.trading.bill.type.DailyBillCheckResult;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "支付平台账单订单查询参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListZftThirdOrderParam extends BaseListParam {

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "商户ID链", example = "34474,34475", hidden = true)
    private String commIdChain;

    @Schema(description = "平台内部充值订单号 t_pay_bill.order_id")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orderId;

    @Schema(description = "账单名 t_zft_daily_bill.name, 支持模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String dailyBillName;

    @Schema(description = "直付商家名称 t_zft_daily_bill.zftName, 支持模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String zftName;

    @Schema(description = "直付商所属商户 t_zft_daily_bill.zftCommId")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long zftCommId;

    @Schema(description = "支付渠道", example = "1",
            format = "java.lang.Integer 1(支付宝);2(微信)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private PayChannel channel;

    @Schema(description = "收支类型: IN_FLOW(收入), OUT_FLOW(支出)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private DepositFlowType tradeType;

    @Schema(description = "支付平台对账结果 FULL_MATCH(完全匹配); AMOUNT_NOT_MATCH(金额不匹配); NO_NOT_MATCH(未找到对应订单); OTHERS(其他)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private DailyBillCheckResult checkResult;

    @Schema(description = "交易时间-开始时间", example = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date fromDate;

    @Schema(description = "交易时间-结束时间", example = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date toDate;

    @Schema(description = "企业用户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long corpId;

    @Schema(description = "下载导出文件名 文件导出使用")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String exFileName;
}
