package com.cdz360.biz.model.trading.order.vo;

import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 直付通记录统计
 *
 * <AUTHOR>
 * @since 2019/11/6 15:51
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode
@ToString
@Schema(description = "直付通记录统计")
public class ZftBillBi {
    @Schema(description = "充值类型: 1-支付宝 2-财付通", required = true)
    @NotEmpty(message = "flowInAccountType 不能为空")
    private  Long flowInAccountType;

    /**
     * {@link DepositFlowType}
     */
    @Schema(description = "交易类型: 1(收入), 2(支出) 供查询使用，前端不可知",
            hidden = true)
    private Integer flowType;

    @Schema(description = "充值订单数", required = true)
    private  Long chargeOrderNum = 0L;

    @Schema(description = "退款订单数", required = true)
    private  Long refundOrderNum = 0L;

    @Schema(description = "充值金额", required = true)
    private  BigDecimal chargeAmount = BigDecimal.ZERO;

    @Schema(description = "退款金额", required = true)
    private  BigDecimal refundAmount = BigDecimal.ZERO;

    @Schema(description = "实际到账金额", required = true)
    private  BigDecimal realAmount = BigDecimal.ZERO;


}
