package com.cdz360.biz.model.trading.site.po;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Schema(description = "储能常规检查")
public class EssReport {

    @Schema(description = "EMS 正常")
    private Base isEMSNormal;

    @Schema(description = "PCS 正常")
    private Base isPCSNormal;

    @Schema(description = "集控正常")
    private Base isCentralControlNormal;

    @Schema(description = "电池堆正常")
    private Base isBatteryStackNormal;

    @Schema(description = "电池簇正常")
    private Base isBatteryClusterNormal;

    @Schema(description = "关口电表正常")
    private Base isGridGatewayMeterNormal;

    @Schema(description = "并网点电表正常")
    private Base isEssGatewayMeterNormal;

    @Schema(description = "高压侧用电电表正常")
    private Base isHighVoltageSideMeterNormal;

    @Schema(description = "内部用电电表正常")
    private Base isEssInsideMeterNormal;

    @Schema(description = "空调正常")
    private Base isAirConditionNormal;

    @Schema(description = "消防正常")
    private Base isFireFightingNormal;

    @Data
    @Accessors(chain = true)
    public static class Base {
        private Boolean normal;

        private String remark;
    }
}

