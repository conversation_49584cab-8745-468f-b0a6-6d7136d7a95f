package com.cdz360.biz.model.trading.bi.warning;

import com.cdz360.biz.model.annotations.ExcelField;
import com.cdz360.biz.model.site.type.AlarmStatusEnum;
import com.cdz360.biz.model.trading.bi.dto.BiExportGroups;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * groups 字段归属组 {@link BiExportGroups}
 */
@Data
@Accessors(chain = true)
@Schema(description = "告警查询出参")
public class WarningBiI18nDto implements Serializable {

    @ExcelField(title = "告警编号", i18nTitle = "waring.warningNo", sort = 1, groups = 11, align = 2)
    @Schema(description = "告警编号")
    private Long warningId;

    @ExcelField(title = "发生时间", i18nTitle = "warning.startTime", sort = 2, groups = 11, align = 2, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "发生时间")
    private Date startTime;

    @ExcelField(title = "结束时间", i18nTitle = "common.endTime", sort = 3, groups = 11, align = 2, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "结束时间")
    private Date endTime;

    @ExcelField(title = "设备编号(名称)", i18nTitle = "warning.warningDeviceNo", sort = 4, groups = 11, align = 2)
    @Schema(description = "设备编号(名称)")
    private String boxOutFactoryCode;

    @Schema(description = "枪头序号")
    @ExcelField(title = "枪头序号", i18nTitle = "warning.plugIdx", sort = 5, groups = 11, align = 2)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer plugIdx;

    private String siteId;

    @ExcelField(title = "告警所属场站", i18nTitle = "warning.siteName", sort = 6, groups = 11, align = 2)
    @Schema(description = "告警所属场站")
    private String siteName;

    /**
     * 告警名-告警报表中‘告警备注’
     */
    @ExcelField(title = "告警名称", i18nTitle = "warning.waringName", sort = 7, groups = 11, align = 2)
    @Schema(description = "告警名称")
    private String warningName;

    /**
     * 告警处理说明-告警报表中‘补充说明’
     */
    @ExcelField(title = "补充说明", i18nTitle = "warning.remark", sort = 8, groups = 11, align = 2)
    @Schema(description = "补充说明")
    private String warningInstructions;

    @Schema(description = "告警状态", hidden = true)
    private AlarmStatusEnum status;

    @ExcelField(title = "告警状态", i18nTitle = "waring.status", sort = 9, groups = 11, align = 2, value = "status.label")
    @Schema(description = "告警状态（0：未结束，进行中,1：自动结束,2：手动结束）")
    private String warningStatus;

    @Schema(description = "桩名称", hidden = true)
    private String evseName;

    @Schema(description = "设备ID", hidden = true)
    private String deviceId;

    @Schema(description = "订单号", hidden = true)
    private String orderNo;

    @Schema(description = "枪头名称", hidden = true)
    private String plugName;
}
