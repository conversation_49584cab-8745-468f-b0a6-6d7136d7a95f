package com.cdz360.biz.model.trading.order.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Schema(description = "客户充电统计")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CusOrderBiDto extends OrderThinBiDto {

    @Schema(description = "客户ID")
    private Long uid;

    @Schema(description = "企业ID")
    private Long corpId;

    @Schema(description = "未缴费次数")
    private Integer unPayParkingCount;

    @Schema(description = "未缴费总超时费")
    private BigDecimal unPayParkingFee;
}
