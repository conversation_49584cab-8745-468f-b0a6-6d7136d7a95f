package com.cdz360.biz.model.trading.site.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

@Data
public class InspectionRecordBi {

    private Long opUid;

    @Schema(description = "巡检人")
    private String rummager;

    private Integer count;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @Schema(description = "最后一次巡检时间")
    private Date recentTime;

}
