package com.cdz360.biz.model.trading.ess.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "查询电池组参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListEssBatteryPackParam extends BaseListParam {

    @Schema(description = "场站ID列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> siteIdList;

    @Schema(description = "ESS平台分配的设备编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String essDno;

    @Schema(description = "电池堆设备名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String stackEquipName;

    @Schema(description = "电池堆设备ID ESS下唯一")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long stackEquipId;

    @Schema(description = "电池簇号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long clusterNo;

    @Schema(description = "电池簇设备ID ESS下唯一")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long clusterEquipId;

    @Schema(description = "电池组SN")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long lmuSn;
}
