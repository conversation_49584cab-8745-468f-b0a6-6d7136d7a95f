package com.cdz360.biz.model.trading.iot.vo;

import com.cdz360.base.model.iot.type.DtuType;
import com.cdz360.base.model.iot.type.NetType;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.biz.model.iot.type.EvseBizType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EvseInfoVo extends EvseVo {

    @Schema(description = "桩ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long id;

    @Schema(description = "设备型号ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long modelId;

    @Schema(description = "品牌")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String brand;

    @Schema(description = "铭牌编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String physicalNo;

    @Schema(description = "生产单号(出厂编号)")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Deprecated
    private String produceNo;

    @Schema(description = "生产日期(出厂日期)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date produceDate;

    @Schema(description = "质保到期日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date expireDate;

    @Schema(description = "场站名称")
    private String siteName;

    @Schema(description = "场站ID")
    private String siteId;

    @Schema(description = "网关名称")
    private String gwName;

    @Schema(description = "桩网络类型")
    private NetType net;

    @Schema(description = "DTU类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private DtuType dtuType;

    @Schema(description = "运营模式")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private EvseBizType bizType;

    @Schema(description = "场站直属商户名称")
    private String siteCommName;

    @Schema(description = "集团商户ID")
    private Long topCommId;

    @Schema(description = "集团商户名称")
    private String topCommName;

    @Schema(description = "SIM卡卡号, 20位数字")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String iccid;

    @Schema(description = "国际移动用户识别码(卡),15位数字")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String imsi;

    @Schema(description = "移动设备国际识别码(设备),15位数字")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String imei;

    @Schema(description = "模块类型")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String moduleType;

    @Schema(description = "模块数量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer moduleNum;

    @Schema(description = "槽位数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer slotNum;

    @Schema(description = "桩号+枪号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> plugNoList;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<PlugVo> plugPoList;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Integer> flags;

    @Schema(description = "桩本地vin鉴权最新下发时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date issuedTime;

    @Schema(description = "SIM卡卡号, 20位数字")
    private String simIccid;

    @Schema(description = "移动台国际ISDN号码")
    private String simMsisdn;

    @Data
    public static class PlugVo {
        private int plugId;

        private String name;
    }

}
