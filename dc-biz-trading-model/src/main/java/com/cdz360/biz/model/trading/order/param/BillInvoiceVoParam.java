package com.cdz360.biz.model.trading.order.param;

import java.util.List;
import java.util.Optional;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class BillInvoiceVoParam {

    private String interimCode;

    private List<String> orderNoList;

    private String procInstId;

    public BillInvoiceVoParam() {
    }

    public BillInvoiceVoParam(String interimCode, List<String> orderNoList, String procInstId) {
        this.interimCode = interimCode;
        this.orderNoList = orderNoList;
        this.procInstId = procInstId;
    }

    @Override
    public String toString() {
        return String.format("interimCode: %s, orderNoList.size: %s, procInstId: %s",
            interimCode,
            Optional.ofNullable(orderNoList).map(List::size).orElse(0), procInstId);
    }

}
