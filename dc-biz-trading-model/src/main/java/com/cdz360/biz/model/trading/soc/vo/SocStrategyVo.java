package com.cdz360.biz.model.trading.soc.vo;

import com.cdz360.biz.model.trading.soc.po.SocStrategyPo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * SocPriorityStrategyFlat
 *
 * @since 8/10/2020 7:09 PM
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(description = "场站soc限制-优先策略Vo")
public class SocStrategyVo extends SocStrategyPo {
    private List<Long> userIdList;
}