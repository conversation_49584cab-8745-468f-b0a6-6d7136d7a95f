package com.cdz360.biz.model.trading.contract.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.biz.model.trading.contract.type.ContractStatus;
import com.cdz360.biz.model.trading.contract.type.ContractType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合约列表
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ContractListParam extends BaseListParam {

    @Schema(description = "有效时间(指合约在当前时间是有效的)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonInclude(Include.NON_NULL)
    private Date validDate;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String contractNo;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String contractName;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String customerName;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private ContractType contractType;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private ContractStatus contractStatus;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String status;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> siteIdList;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> commIdList;


    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String idChain;
}