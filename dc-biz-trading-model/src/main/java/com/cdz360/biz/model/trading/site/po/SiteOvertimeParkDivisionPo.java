package com.cdz360.biz.model.trading.site.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.lang.Double;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel(value = "占位分时设定详情")
public class SiteOvertimeParkDivisionPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@ApiModelProperty(value = "t_site_overtime_park_setting.id")
	private Long settingId;

	@ApiModelProperty(value = "生效开始时间：分钟，右闭区间")
	private Integer startTime;

	@ApiModelProperty(value = "生效结束时间：分钟，左开区间")
	private Integer endTime;

	@ApiModelProperty(value = "收费单价")
	private BigDecimal fee;

	private Boolean enable;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;


}
