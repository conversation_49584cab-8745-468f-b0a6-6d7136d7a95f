package com.cdz360.biz.model.trading.coupon.param;

import com.cdz360.biz.model.trading.coupon.type.CouponDictType;
import com.cdz360.biz.model.trading.coupon.type.CouponValidType;
import com.cdz360.biz.model.trading.coupon.type.ExtraScoreSettingType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * CouponDictParam
 *
 * <AUTHOR>
 * @since 7/27/2020 2:55 PM
 */
@Data
public class CouponDictParam {
    @Schema(description = "模板名称")
    private String name;

    private CouponDictType type;

    @Schema(description = "使用条件金额")
    private BigDecimal conditionAmount;

    @Schema(description = "显示的使用条件金额")
    private BigDecimal showConditionAmount;

    @Schema(description = "减金额")
    private BigDecimal amount;

    @Schema(description = "有效期类型, 固定期限 | 自领取时间")
    private CouponValidType validType;

    @Schema(description = "有效期-开始")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date validTimeFrom;

    @Schema(description = "有效期-截至")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date validTimeTo;

    @Schema(description = "过期日, 自领取时间")
    private Integer validRelateDay;

    @Schema(description = "个人账户可用")
    private Boolean personalEnable;

    @Schema(description = "即充即退可用")
    private Boolean prepayEnable;

    @Schema(description = "商户会员")
    private Boolean commEnable;

    @Schema(description = "同时支持微信/支付宝的先充后付")
    private Boolean wxCreditEnable;

    @Schema(description = "所属商户")
    private Long commId;

    @Schema(description = "可用场站ID集合")
    private List<String> usableSiteIdList;

    @Schema(description = "可用场站组集合")
    private List<String> gidList;

    @Schema(description = "叠加积分体系的方式")
    private ExtraScoreSettingType extraScoreSettingType;

    @Schema(description = "叠加的部分积分体系列表")
    private List<ExtraScoreSettingParam> extraScoreSettingList;
}