package com.cdz360.biz.model.trading.order.wrapper;

import com.cdz360.base.model.base.type.ChargeOrderStatus;
import com.cdz360.base.model.charge.type.OrderAbnormalReason;
import com.cdz360.biz.model.trading.order.po.ChargerOrderPo;
import java.math.BigDecimal;
import java.util.Date;

public class ChargerOrderWrapper {

    public static void setPlugNo(String plugNo, ChargerOrderPo curObj, ChargerOrderPo obj4Update) {
        curObj.setPlugNo(plugNo);
        obj4Update.setPlugNo(plugNo);
    }

    public static void setConnectorId(Integer connectorId, ChargerOrderPo curObj,
        ChargerOrderPo obj4Update) {
        curObj.setConnectorId(connectorId);
        obj4Update.setConnectorId(connectorId);
    }

    public static void setChargeStartTime(Long chargeStartTime, ChargerOrderPo curObj,
        ChargerOrderPo obj4Update) {
        curObj.setChargeStartTime(chargeStartTime);
        obj4Update.setChargeStartTime(chargeStartTime);
    }


    public static void setStartElectricity(BigDecimal startElectricity, ChargerOrderPo curObj,
        ChargerOrderPo obj4Update) {
        curObj.setStartElectricity(startElectricity);
        obj4Update.setStartElectricity(startElectricity);
    }

    public static void setStartSoc(Integer startSoc, ChargerOrderPo curObj,
        ChargerOrderPo obj4Update) {
        curObj.setStartSoc(startSoc);
        obj4Update.setStartSoc(startSoc);
    }

    public static void setDuration(Integer duration, ChargerOrderPo curObj,
        ChargerOrderPo obj4Update) {
        curObj.setDuration(duration);
        obj4Update.setDuration(duration);
    }

//    public static void setOrderPrice(BigDecimal orderPrice, ChargerOrderPo curObj, ChargerOrderPo obj4Update) {
//        curObj.setOrderPrice(orderPrice);// 后续版本可移除
//        obj4Update.setOrderPrice(orderPrice);// 后续版本可移除
//    }

//    public static void setElecPrice(BigDecimal elecPrice, ChargerOrderPo curObj, ChargerOrderPo obj4Update) {
//        curObj.setElecPrice(elecPrice);// 后续版本可移除
//        obj4Update.setElecPrice(elecPrice);// 后续版本可移除
//    }

//    public static void setElecActualFee(BigDecimal elecActualFee, ChargerOrderPo curObj, ChargerOrderPo obj4Update) {
//        curObj.setElecActualFee(elecActualFee);// 后续版本可移除
//        obj4Update.setElecActualFee(elecActualFee);// 后续版本可移除
//    }
//
//    public static void setServicePrice(BigDecimal servicePrice, ChargerOrderPo curObj, ChargerOrderPo obj4Update) {
//        curObj.setServicePrice(servicePrice);   // 后续版本可移除
//        obj4Update.setServicePrice(servicePrice);// 后续版本可移除
//    }

//    public static void setServActualFee(BigDecimal servActualFee, ChargerOrderPo curObj, ChargerOrderPo obj4Update) {
//        curObj.setServActualFee(servActualFee); // 后续版本可移除
//        obj4Update.setServActualFee(servActualFee); // 后续版本可移除
//    }

    public static void setOrderElectricity(BigDecimal orderElectricity, ChargerOrderPo curObj,
        ChargerOrderPo obj4Update) {
        curObj.setOrderElectricity(orderElectricity);
        obj4Update.setOrderElectricity(orderElectricity);
    }

    public static void setStatus(Integer status, ChargerOrderPo curObj, ChargerOrderPo obj4Update) {
        curObj.setStatus(status);
        obj4Update.setStatus(status);
    }

    public static void setOrderStatus(ChargeOrderStatus orderStatus, ChargerOrderPo curObj,
        ChargerOrderPo obj4Update) {
        curObj.setOrderStatus(orderStatus);
        obj4Update.setOrderStatus(orderStatus);
    }

    public static void setCardChipNo(String cardChipNo, ChargerOrderPo curObj,
        ChargerOrderPo obj4Update) {
        curObj.setCardChipNo(cardChipNo);
        obj4Update.setCardChipNo(cardChipNo);
    }

    public static void setUpdateTime(Date updateTime, ChargerOrderPo curObj,
        ChargerOrderPo obj4Update) {
        curObj.setUpdateTime(updateTime);
        obj4Update.setUpdateTime(updateTime);
    }

    public static void setDefaultPayType(Integer defaultPayType, ChargerOrderPo curObj,
        ChargerOrderPo obj4Update) {
        curObj.setDefaultPayType(defaultPayType);
        obj4Update.setDefaultPayType(defaultPayType);
    }

    public static void setPayAccountId(Long payAccountId, ChargerOrderPo curObj,
        ChargerOrderPo obj4Update) {
        curObj.setPayAccountId(payAccountId);
        obj4Update.setPayAccountId(payAccountId);
    }

    public static void setPayStatus(Integer payStatus, ChargerOrderPo curObj,
        ChargerOrderPo obj4Update) {
        curObj.setPayStatus(payStatus);
        obj4Update.setPayStatus(payStatus);
    }

    public static void setStopTime(Date stopTime, ChargerOrderPo curObj,
        ChargerOrderPo obj4Update) {
        curObj.setStopTime(stopTime);
        obj4Update.setStopTime(stopTime);
    }

    public static void setStopSoc(Integer soc, ChargerOrderPo curObj, ChargerOrderPo obj4Update) {
        curObj.setStopSoc(soc);
        obj4Update.setStopSoc(soc);
    }

//    public static void setActualPrice(BigDecimal actualPrice, ChargerOrderPo curObj, ChargerOrderPo obj4Update) {
//        curObj.setActualPrice(actualPrice);// 后续版本可移除
//        obj4Update.setActualPrice(actualPrice);// 后续版本可移除
//    }

    public static void setRemark(String remark, ChargerOrderPo curObj, ChargerOrderPo obj4Update) {
        curObj.setRemark(remark);
        obj4Update.setRemark(remark);
    }

    public static void setAbnormal(OrderAbnormalReason abnormal, ChargerOrderPo curObj,
        ChargerOrderPo obj4Update) {
        curObj.setAbnormal(abnormal);
        obj4Update.setAbnormal(abnormal);
    }

    public static void setManual(Boolean manual, ChargerOrderPo curObj, ChargerOrderPo obj4Update) {
        curObj.setManual(manual);
        obj4Update.setManual(manual);
    }

    public static void setFrozenAmount(BigDecimal frozenAmount, ChargerOrderPo curObj,
        ChargerOrderPo obj4Update) {
        curObj.setFrozenAmount(frozenAmount);
        obj4Update.setFrozenAmount(frozenAmount);
    }


    public static void setChargeEndTime(Long chargeEndTime, ChargerOrderPo curObj,
        ChargerOrderPo obj4Update) {
        curObj.setChargeEndTime(chargeEndTime);
        obj4Update.setChargeEndTime(chargeEndTime);
    }

//    public static void xxxxxxxxxxx(BigDecimal orderPrice, ChargerOrderPo curObj, ChargerOrderPo obj4Update) {
//        curObj.setOrderPrice(orderPrice);
//        obj4Update.setOrderPrice(orderPrice);
//    }
}
