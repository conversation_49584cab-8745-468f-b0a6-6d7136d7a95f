package com.cdz360.biz.model.trading.profit.sett.po;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "计算公式配置项")
@Data
@Accessors(chain = true)
public class CalculateRuleBase {

    @Schema(description = "电费SpEL计算规则配置")
    private String elecFee;

    @Schema(description = "服务费SpEL计算规则配置")
    private String servFee;

    @Schema(description = "停车费SpEL计算规则配置")
    private String parkFee;
}
