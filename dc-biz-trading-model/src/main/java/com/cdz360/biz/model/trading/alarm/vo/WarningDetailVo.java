package com.cdz360.biz.model.trading.alarm.vo;

import com.cdz360.base.utils.JsonUtils;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * WarningDetailVo
 *
 * WarningDetailVo
 * <AUTHOR>
 *
 * @since 2019.4.16
 */
@Data
@Accessors(chain = true)
public class WarningDetailVo {
    private Long id;
    /**
     * 告警编码
     */
    private String warningCode;
    /**
     * 告警名
     */
    private String warningName;
    /**
     * 告警处理说明
     */
    private String warningInstructions;
    /**
     * 告警等级
     */
    private Integer warningLevel;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
