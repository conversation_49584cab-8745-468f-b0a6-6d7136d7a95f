package com.cdz360.biz.model.trading.coupon.param;

import com.cdz360.biz.model.trading.coupon.type.CouponDictStatusType;
import com.cdz360.biz.model.trading.coupon.type.CouponDictType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * CouponDictSearchParam
 *
 * @since 7/27/2020 3:17 PM
 * <AUTHOR>
 */
@Data
public class CouponDictSearchParam {

    @Schema(description = "券模板名")
    private String name;

    @Schema(description = "券类型")
    private CouponDictType type;

    @Schema(description = "所属商户")
    private Long commId;

    @Schema(description = "模板状态")
    private CouponDictStatusType status;

    @Schema(description = "偏移量，从0开始")
    private Integer start;
    @Schema(description = "每页最大显示条数")
    private Integer size;

    private String idChain;
}