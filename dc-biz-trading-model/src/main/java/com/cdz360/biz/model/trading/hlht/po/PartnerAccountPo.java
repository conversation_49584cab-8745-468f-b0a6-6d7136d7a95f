package com.cdz360.biz.model.trading.hlht.po;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class PartnerAccountPo {

    private Long id;

    private Long pid;

    private Long corpId;
    
    private Long corpTopCommId;

    private String corpName;

    private Long corpUserId;

    private Date createTime;

    private Date updateTime;

    private Boolean enable;

}
