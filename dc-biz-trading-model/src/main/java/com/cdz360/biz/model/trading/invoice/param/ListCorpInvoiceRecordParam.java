package com.cdz360.biz.model.trading.invoice.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.biz.model.invoice.type.InvoiceType;
import com.cdz360.biz.model.invoice.type.InvoicedStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Schema(description = "查询企业开票记录参数")
public class ListCorpInvoiceRecordParam extends BaseListParam {

    @Schema(description = "商户ID链", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commIdChain;

    @Schema(description = "登录用户的所属商户ID", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long commId;

    @Schema(description = "申请单号 支持模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String applyNo;

    @Schema(description = "申请单号列表 导出复选")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> applyNoList;

    @Schema(description = "企业开票方式")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private InvoicingMode invoiceWay;

    @Schema(description = "企业开票方式 支持复选")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<InvoicingMode> invoiceWayList;

    @Schema(description = "开票抬头 支持模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String invoiceName;

    @Schema(description = "开票种类: PER_COMMON(个人普票); ENTER_COMMON(企业普票); ENTER_PROFESSION(企业专票)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private InvoiceType invoiceType;

    @Schema(description = "开票种类: PER_COMMON(个人普票); ENTER_COMMON(企业普票); ENTER_PROFESSION(企业专票) 支持复选")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<InvoiceType> invoiceTypeList;

    @Schema(description = "流程状态: " +
        "草稿(NOT_SUBMITTED); 审核中(SUBMITTED); " +
        "开票中(REVIEWED); 审核未通过(AUDIT_FAILED); " +
        "开票未通过(INVOICING_FAIL);已开具(COMPLETED); " +
        "已作废(INVALID)[含红冲状态]")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<InvoicedStatus> statusList;

    @Schema(description = "创建时间查询")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter createTimeFilter;

    @Schema(description = "审核时间查询")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter reviewTimeFilter;

    @Schema(description = "开具时间查询")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter issuedTimeFilter;

    @Schema(description = "企业ID", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long corpId;

    @Schema(description = "企业名称 支持模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String corpName;

    @Schema(description = "子订单号(充值订单号/充电订单号/账单号) 支持模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orderNo;

    @Schema(description = "回款情况 0 未回款 1已回款")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer returnFlag;
}
