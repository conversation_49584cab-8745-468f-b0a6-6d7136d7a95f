package com.cdz360.biz.model.trading.site.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.ToString;

/**
 * MoveCorpUserSocStrategyList
 *
 * @since 10/28/2020 3:53 PM
 * <AUTHOR>
 */
@Schema(description = "无卡账户结算配置保留/删除列表")
@Data
@ToString(callSuper = true)
public class MoveCorpNoCardList {
    private List<MoveCorpNoCardVo> removeList = new ArrayList<>();
    private List<MoveCorpNoCardVo> remainList = new ArrayList<>();
}