package com.cdz360.biz.model.trading.ess.param;


import com.cdz360.biz.model.ess.po.RangeTime;
import com.cdz360.biz.model.trading.ess.type.DrySpotCtrlMode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;


@Data

@Accessors(chain = true)

@Schema(description = "储能ESS干接点配置")

public class EssDrySpotCfgParam {

    @Schema(description = "干接点1打开关闭")
    private Boolean enable;

    @Schema(description = "ON,OFF,AUTO")
    private DrySpotCtrlMode ctrlMode;

    @Schema(description = "时间范围, 单位: 分钟: [{start: 100, end: 600}]")
    private List<RangeTime> rangeTime;

    @Schema(description = "soc阈值")
    private Integer socThreshold;

    @Schema(description = "一周每天使能(bit0~6表示一周七天)")
    private Integer weekEnable;

}

