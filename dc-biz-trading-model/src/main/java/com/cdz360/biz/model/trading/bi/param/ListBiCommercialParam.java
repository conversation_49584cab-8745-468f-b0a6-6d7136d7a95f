package com.cdz360.biz.model.trading.bi.param;


import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.order.param.ListBiParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "商户汇总统计请求参数")
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ListBiCommercialParam extends ListBiParam {

    @Schema(description = "商户ID列表, 复选框使用")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Long> commIds;

    @Schema(description = "商户ID列表, 复选框使用")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Long> commIdList;

    @Schema(description = "商户Id链", required = true, hidden = true)
    private String commIdChain;

    @Schema(description = "筛选类型  1-直属站点", required = false)
    private Long type;


    @Schema(description = "仅用作excel导出使用", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ExcelPosition excelPosition;
}
