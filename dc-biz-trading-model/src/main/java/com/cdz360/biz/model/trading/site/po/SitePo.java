package com.cdz360.biz.model.trading.site.po;

import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.base.model.charge.type.SiteDynamicPowerType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.model.finance.type.BizType;
import com.cdz360.biz.model.site.type.SiteCategory;
import com.cdz360.biz.model.site.type.SitePayChannelType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonProperty.Access;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SitePo extends BaseObject implements Serializable {

    private static final long serialVersionUID = 6251248055347687785L;
    /**
     * 站点Id
     */
    @Schema(title = "场站ID")
    private String id;

    @Schema(title = "场站编号")
    private String siteNo;

    /**
     * 站点编号
     */
    private String idNo;

    @Schema(title = "集团商户ID")
    private Long topCommId;

    /**
     * 是否是采集数据,0否，1是
     */
    private int collectData;

    /**
     * 站点名称
     */
    @Schema(title = "站点名称")
    private String siteName;

    @Schema(description = "站点简称")
    private String siteShortName;

    /**
     * 站点类型(0:未知,1:公共,2:公交,3:物流,4:混合)
     **/
    private Integer type;

    /**
     * 站点类型描述(0:未知,1:公共,2:个人,3:运营)
     **/
    private String typeDesc;

    /**
     * 内部类型（0，未知； 1，投建运营； 2，以租代售； 3，纯租赁； 4，EPC+O; 5, 销售的代收代付； 6，代运营； 7，委托运营）
     */
    private Integer gcType;

    /**
     * 经度
     **/
    private BigDecimal longitude;

    /**
     * 纬度
     **/
    private BigDecimal latitude;

    @Schema(description = "关联城市ID(t_geo_cities.id)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long cityId;

    @Schema(description = "关联城市ID(t_geo_cities.country_id)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long countryId;

    /**
     * 站点地址
     **/
    private String address;

    /**
     * 省编号
     */
    @Schema(title = "省编号")
    private Integer province;


    /**
     * 市编号
     */
    @Schema(title = "市编号")
    private Integer city;


    /**
     * 区编号
     **/
    @Schema(title = "区编号")
    private Integer area;

    /**
     * 区名称
     **/
    @Schema(title = "区名称")
    private String areaName;


    /**
     * 服务号码
     **/
    private String phone;

    /**
     * 工作日服务时间
     **/
    private String serviceWorkdayTime;

    /**
     * 使用范围 0.未知 1:对外开放 2：内部使用
     **/
    private Integer scope;

    @Schema(title = "内部使用场站是否在客户端隐藏")
    private Boolean isHidden;

    @Schema(title = "禁用的客户端类型(AppClientType)编号,使用, 分隔")
    private String forbiddenClient;

    /**
     * 停车是否收费（0：未知 1：收费 2：免费）
     **/
    private Integer park;

    /**
     * 停车费
     **/
    private String parkFee;

    @Schema(title = "停充超时收费. true: 收费(启用); false: 不收费（不启用）", required = true)
    private Boolean parkTimeoutFee;

    /**
     * 站点图片
     */
    private String images;

    /**
     *
     */
    private String scaleImgs;

    /**
     * 联网状态(0:全部, 1: 已联网, 2:未联网)
     **/
    private Integer networkStatus;

    /**
     * 节假日服务时间
     **/
    private String serviceHolidayTime;

    /**
     * 其他服务
     **/
    private String serviceInfo;

    /**
     * 特定用户(1: 只针对特定用户开放, 2: 对所有用户开放)
     **/
    private Integer specilic;

    /**
     * 支持扫码(1: 支持. 2: 不支持)
     **/
    private Integer qrcode;

    /**
     * 服务号码转
     **/
    private String throug;

    /**
     * 站点状态(1.待上线；0已删除；2.已上线; 3.维护中;)
     **/
    @Schema(title = "站点状态: 0-已删除; 1-待上线; 2-已上线; 3-维护中")
    private Integer status;

    /**
     * 备注
     **/
    private String remark;

    /**
     * 运营商名称
     **/
    @Schema(title = "运营商名称")
    private String operateName;

    @Schema(description = "运营商企业代码")
    @JsonInclude(Include.NON_EMPTY)
    private String operateCorpCode;

    private String geohash;


    @Schema(title = "时区", description = "如: +8")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String timeZone;

    /**
     * 运营商ID
     **/
    @Schema(title = "运营商ID")
    private Long operateId;

    @Schema(description = "包含该场站的场站组")
    @JsonInclude(Include.NON_NULL)
    private List<String> gids;

    /**
     * 站点实时冻结金额
     */
    @Schema(title = "站点实时冻结金额")
    private BigDecimal frozenAmount;

    /**
     * 充电站的电流形式
     */
    @Schema(title = "充电站的电流形式")
    private SupplyType supplyType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 收费说明
     */
    private String feeDescription;

    /**
     * 收费范围（最低）
     */
    private Long feeMin;

    /**
     * 收费范围（最高）
     */
    private Long feeMax;

    /**
     * app支付(1001:充电网APP,1002:奥能APP)
     */
    private String appPay;

    /**
     * 微信支付(2001:充电网微信公众号  2002：奥能微信公众号  2003： e充网公众账号 )
     */
    private String wxPay;

    /**
     * 刷卡支付(3001:上海地铁公交卡 3002： 普天充电卡 3003：充电网充电卡  3004：招行闪付卡)
     */
    private String cardPay;

    /**
     * 现金支付（1:支持  0：不支持）
     */
    private Integer cashPay;

    /**
     * 最大功率
     **/
    private Double maxPower;
    /**
     * 是否需要预约(0.未知，1.需要预约，2.不需要预约)
     */
    private Integer appoint;

    /**
     * 公告
     */
    private String notice;

    /**
     * 是否加入互联互通
     */
    private boolean joinUnion;

    /**
     * 该站点互联互通的id
     **/
    private String openSiteId;

    /**
     * 联系人姓名
     **/
    private String contacts;

    /**
     * 联系人电话
     **/
    private String contactsPhone;

    /**
     * 支持的支付渠道列表,空表示全部支持. PT,平台余额; ALIPAY,支付宝; WXPAY,微信支付
     */
    private String payChannels;

    /**
     * 支付方式描述
     **/
    private String payDescription;

    /**
     * 是否为app分享站点 ，默认0-否;1-是
     **/
    private Integer isShareSite;

    /**
     * 子账号主键
     **/
    private Long merchantId;

    @Schema(title = "互联互通合作方编码, 仅用于互联互通接入的场站")
    private String partnerCode;


    /**
     * 上线时间
     **/
    @Schema(title = "场站上线时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date onlineDate;

    /**
     * 电桩总数
     */
    private Integer boxCount;

    /**
     * app使用的电桩总数
     */
    private Integer countOfBox;

    /**
     * 站点默认计费模板Id
     */
    private Long templateId;

    /**
     * 站点默认计费模板名称
     */
    private String templateName;

    /**
     * 是否由云端重新根据计费模板来计算价格
     */
    private Boolean cloudPrice;


    /**
     * 发票描述
     */
    private String invoiceDesc;

    @Schema(title = "移动端开票标识（1：可以 0：不可以）", example = "0")
    private Integer invoicedValid;

    @Schema(description = "移动端开票主体(移动端允许开票需要配置)")
    @JsonInclude(Include.NON_NULL)
    private Long mobileTempSalId;

    @Schema(title = "平台开票标识（1：可以 0：不可以）", example = "0")
    private Boolean platformInvoicedValid;

    /**
     * 0, 禁用; 1, 个人基本账户; 2, 集团授权账户; 3, 商户专属账户; 999, 启动时选择
     *  {@link com.cdz360.base.model.base.type.PayAccountType}
     */
    @Schema(title = "默认扣款类型")
    private Integer defaultPayType;

    @Schema(title = "扣款账户ID")
    private Long payAccountId;

    @Schema(description = "停充SOC 场站后台启动SOC限制")
    @JsonInclude(Include.NON_NULL)
    private Integer stopSoc;

    @Schema(title = "是否支持数币支付")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean ecny;

    /**
     * {@link BizType}
     */
    @Schema(title = "运营属性. 0,未知; 1,自营; 2,非自营; 3,互联互通; 4, 脱机自营; 5, 脱机非自营;")
    private Integer bizType;

    @Schema(title = "运营方名称")
    private String bizName;

    @Deprecated
    private String siteId;//临时用于2020-06版本升级(清洗场站桩枪数量)

    @Schema(title = "交流桩数量")
    private Integer acEvseNum;

    @Schema(title = "直流桩数量")
    private Integer dcEvseNum;

    @Schema(title = "交流枪头数量")
    private Integer acPlugNum;

    @Schema(title = "直流枪头数量")
    private Integer dcPlugNum;

    @Schema(title = "交流总功率,单位kw")
    private Integer acPower;

    @Schema(title = "直流总功率,单位kw")
    private Integer dcPower;

    @Schema(title = "场站功率分配模式")
    private SiteDynamicPowerType dyPow;

    @Schema(title = "光储充类型")
    private List<SiteCategory> category;

    @Schema(title = "光储充类型")
    private List<Integer> categoryIntList;

    @Schema(title = "光伏装机功率")
    private BigDecimal pvInstalledCapacity;

    @Schema(title = "光伏收益模板ID")
    private Long pvIncomeTemplateId;

    @Schema(title = "并网日期")
    private Date onGridDate;

    @Schema(title = "并网电压等级")
    private Integer onGridVoltageLevel;

    @Schema(title = "储能充电支出模板ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long essInPriceId;

    @Schema(title = "储能放电收入模板ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long essOutPriceId;

    @Schema(title = "储能装机总功率, 单位: kW")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal essPower;

    @Schema(title = "储能装机容量, 单位: kW·h")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal essCapacity;

    @Schema(title = "代收代付首次转账时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date firstTransferTime;

    @Schema(title = "场站支持的支付类型")
    @JsonProperty(access = Access.READ_ONLY)
    public List<SitePayChannelType> getPayChannelTypes() {
        final List<SitePayChannelType> defaultPayChannels = List.of(SitePayChannelType.PT,
            SitePayChannelType.WXPAY,
            SitePayChannelType.ALIPAY);
        if (StringUtils.isBlank(this.payChannels)) {
            return defaultPayChannels;
        }
        try {
            List<SitePayChannelType> accList = Arrays.stream(this.payChannels.split(","))
                .map(s -> SitePayChannelType.valueOf(s))
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(accList)) {
                // 配置为空时，表示默认使用平台余额、微信、支付宝这3个支付渠道
                return defaultPayChannels;
            } else {
                return accList;
            }
        } catch (Exception e) {
        }
        return List.of();
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
