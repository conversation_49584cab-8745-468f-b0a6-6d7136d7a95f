package com.cdz360.biz.auth.model.vo;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("sys_login_log")
public class SysLoginLog {
    @TableId(type = IdType.INPUT, value = "user_id")
    private Long userId;
    @TableField(value = "error_num")
    private Integer errorNum;
    @TableField(value = "last_login_time")
    private Date lastLoginTime;
    private String msg;
    @TableField(exist = false)
    private Date createTime;
    @TableField(exist = false)
    private Date updateTime;
    @TableField(exist = false)
    private Long createBy;
    @TableField(exist = false)
    private Long updateBy;
}
