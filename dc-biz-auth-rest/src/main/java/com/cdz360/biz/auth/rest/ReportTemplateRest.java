package com.cdz360.biz.auth.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.service.ReportTemplateService;
import com.cdz360.biz.model.sys.constant.ReportPage;
import com.cdz360.biz.model.sys.po.SysUserReportTemplatePo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping(value = "/api/",  produces = MediaType.APPLICATION_JSON_VALUE)
public class ReportTemplateRest {

    @Autowired
    private ReportTemplateService service;

    @GetMapping(value = "/data/report/getInfo")
    public ListResponse<SysUserReportTemplatePo> getInfo(@RequestParam(value = "sysUserId") Long sysUserId,
                                                         @RequestParam(value = "page", required = false) Integer page) {
        log.info("getInfo sysUserId: {}, page: {}", sysUserId, page);
        return RestUtils.buildListResponse(service.getInfo(sysUserId, page));
    }

    @PostMapping(value = "/data/report/add")
    public BaseResponse add(@RequestBody @Validated SysUserReportTemplatePo template) {
        log.info("add template: {}", template);
        return service.add(template);
    }

    @GetMapping(value = "/data/report/delete")
    public BaseResponse delete(@RequestParam(value = "templateId") Long templateId) {
        log.info("delete templateId: {}", templateId);
        return service.delete(templateId);
    }

}
