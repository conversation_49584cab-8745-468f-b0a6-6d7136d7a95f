package com.cdz360.biz.auth.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.config.CurrentUser;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.service.AccRelativeService;
import com.cdz360.biz.auth.sys.param.AccRelativeParam;
import com.cdz360.biz.auth.sys.param.AddAccRelativeParam;
import com.cdz360.biz.auth.sys.param.YwUserParam;
import com.cdz360.biz.auth.sys.vo.AccRelativeVo;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/accRelative")
public class AccRelativeRest {

    @Autowired
    private AccRelativeService accRelativeService;

    @PostMapping(value = "/list")
    public ListResponse<AccRelativeVo> getVoList(@RequestBody AccRelativeParam param) {
        log.info("getVoList param: {}", param);
        return accRelativeService.getVoList(param);
    }

    @GetMapping(value = "/syncOrderNum")
    public BaseResponse syncOrderNum() {
        accRelativeService.syncOrderNum();
        return RestUtils.success();
    }

    // 获取运维账号信息
    @Operation(summary = "获取运维账号信息")
    @GetMapping("/getYwAccount")
    public ObjectResponse<AccRelativeVo> getYwAccount(@CurrentUser SysUser user,
        @RequestParam("sysUid") Long sysUid) {
        log.debug("获取运维账号信息: sysUid = {}", sysUid);
        return RestUtils.buildObjectResponse(accRelativeService.getYwAccount(sysUid));
    }

    @GetMapping(value = "/findByAccount")
    public ObjectResponse<AccRelativeVo> findByAccount(
        @RequestParam(value = "account") String account,
        @RequestParam(value = "commIdChain") String commIdChain) {
        log.info("findByAccount account: {}, account: {}", account, commIdChain);
        return accRelativeService.findByAccount(account, commIdChain);
    }

    @Operation(summary = "运维账户新增操作")
    @PostMapping(value = "/add")
    public ListResponse<SysUserVo> addAccRelative(@RequestBody AddAccRelativeParam param) {
        log.debug("addAccRelative param: {}", JsonUtils.toJsonString(param));
        return accRelativeService.addAccRelative(param);
    }

    @Operation(summary = "检查运维账户下是否存在未完成的工单")
    @GetMapping(value = "/checkForOutstandingOrder")
    public BaseResponse checkForOutstandingOrder(@RequestParam("sysUid") Long sysUid) {
        log.debug("checkForOutstandingOrder sysUid: {}", sysUid);
        return accRelativeService.checkForOutstandingOrder(sysUid);
    }

    @Operation(summary = "运维账户编辑操作")
    @PostMapping(value = "/edit")
    public ListResponse<SysUserVo> editAccRelative(@RequestBody AddAccRelativeParam param) {
        log.debug("editAccRelative param: {}", JsonUtils.toJsonString(param));
        return accRelativeService.editAccRelative(param);
    }

    @Operation(summary = "运维账户删除操作")
    @GetMapping(value = "/delete")
    public ObjectResponse<SysUserVo> deleteBySysUid(
        @Parameter(name = "系统用户ID", required = true) @RequestParam("sysUid") Long sysUid) {
        log.debug("deleteBySysUid sysUid: {}", sysUid);
        return accRelativeService.deleteBySysUid(sysUid);
    }

    @Operation(summary = "获取运维人员列表", description = "通过场站组来获取")
    @PostMapping(value = "/findYwUser")
    public ListResponse<SysUser> findYwUser(@RequestBody YwUserParam param) {
        log.debug("获取运维人员列表: param = {}", param);
        return RestUtils.buildListResponse(accRelativeService.findYwUser(param));
    }

    @Operation(summary = "获取用户所属运维组其他人员列表", description = "通过场站组来获取")
    @GetMapping(value = "/getYwGroupOtherUser")
    public ListResponse<SysUser> getYwGroupOtherUser(
        @ApiParam(value = "用户ID", required = true) @RequestParam(value = "uid") Long uid,
        @ApiParam(value = "是否同组", required = true) @RequestParam(value = "same") Boolean same,
        @ApiParam("运维场站组ID") @RequestParam(value = "gidList", required = false) List<String> gidList) {
        log.debug("获取运维人员列表: uid = {}, same = {}, gidList = {}", uid, same, gidList);
        return RestUtils.buildListResponse(
            accRelativeService.getYwGroupOtherUser(uid, same, gidList));
    }
}
