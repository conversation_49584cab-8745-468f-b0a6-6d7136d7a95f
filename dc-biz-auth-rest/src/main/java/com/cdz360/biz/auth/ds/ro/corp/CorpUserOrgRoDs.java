package com.cdz360.biz.auth.ds.ro.corp;

import com.cdz360.biz.auth.corp.po.CorpUserOrgPo;
import com.cdz360.biz.auth.ds.ro.corp.mapper.CorpUserOrgRoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CorpUserOrgRoDs {

    @Autowired
    private CorpUserOrgRoMapper corpUserOrgRoMapper;


    public CorpUserOrgPo getCorpUserOrg(Long sysUid, Long corpId) {
        return this.corpUserOrgRoMapper.getCorpUserOrg(sysUid, corpId);
    }
}
