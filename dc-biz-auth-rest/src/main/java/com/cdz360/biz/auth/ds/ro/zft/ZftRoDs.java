package com.cdz360.biz.auth.ds.ro.zft;


import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.ds.ro.zft.mapper.ZftRoMapper;
import com.cdz360.biz.auth.zft.param.ListZftParam;
import com.cdz360.biz.auth.zft.po.ZftPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import javax.validation.constraints.NotNull;


@Slf4j
@Service
public class ZftRoDs {



	@Autowired

	private ZftRoMapper zftRoMapper;



	public ZftPo getById(Long id) {
		return this.zftRoMapper.getById(id);
	}

	public ZftPo getByTopCommId(Long topCommId) {
		return this.zftRoMapper.getByTopCommId(topCommId);
	}

	public Mono<ListResponse<ZftPo>> findAll(ListZftParam param) {
		return Mono.just(param)
				.doOnNext(p -> {
					if (null == param.getSize() || param.getSize() > 10000) {
						param.setSize(9999);
					}
				})
				.map(this.zftRoMapper::findAll)
				.map(RestUtils::buildListResponse)
				.doOnNext(res -> {
					if (null != param.getTotal() && param.getTotal()) {
						res.setTotal(this.zftRoMapper.count(param));
					} else {
						res.setTotal(0L);
					}
				});
//		if (null == param.getSize() || param.getSize() > 10000) {
//			param.setSize(9999);
//		}
//
//		List<ZftPo> result = this.zftRoMapper.findAll(param);
//		if (null != param.getTotal() && param.getTotal()) {
//			return RestUtils.buildListResponse(result, this.zftRoMapper.count(param));
//		} else {
//			return RestUtils.buildListResponse(result);
//		}
	}

	public boolean hasEnableBalance(Long topCommId, Long excludeId) {
		return this.zftRoMapper.hasEnableBalance(topCommId, excludeId) != null;
	}

	public boolean hasName(Long topCommId, Long excludeId, @NotNull String name) {
		return this.zftRoMapper.hasName(topCommId, excludeId, name) != null;
	}
}

