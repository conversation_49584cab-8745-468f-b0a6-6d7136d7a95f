package com.cdz360.biz.auth.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.model.param.ListRemindAccountParam;
import com.cdz360.biz.auth.model.param.RemoveRemindAccountParam;
import com.cdz360.biz.auth.model.vo.RemindAccountVo;
import com.cdz360.biz.auth.service.remind.RemindAccountService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.Duration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Tag(name = "提醒相关接口", description = "提醒相关接口")
@RestController
@RequestMapping("/api/remindAccount")
public class RemindAccountRest {

    @Autowired
    private RemindAccountService remindAccountService;

    @Operation(summary = "删除企客余额提醒账户")
    @PostMapping(value = "/remove")
    public ObjectResponse<Integer> removeCorpBalanceRemindAccount(
        @RequestBody RemoveRemindAccountParam param) {
        log.info("删除企客余额提醒账户: {}", param);
        return remindAccountService.removeCorpBalanceRemindAccount(param)
            .map(RestUtils::buildObjectResponse)
            .block(Duration.ofSeconds(50L));
    }

    @Operation(summary = "获取企业客户关联的账户列表")
    @PostMapping(value = "/corpBalance")
    public ListResponse<RemindAccountVo> corpBalanceRemindAccount(
        @RequestBody ListRemindAccountParam param) {
        log.info("获取企业客户关联的账户列表: {}", param);
        return remindAccountService.corpBalanceRemindAccount(param)
            .block(Duration.ofSeconds(50L));
    }

    @Operation(summary = "当前企业可用推送账户列表")
    @PostMapping(value = "/unbindCorpBalance")
    public ListResponse<RemindAccountVo> unbindCorpBalance(
        @RequestBody ListRemindAccountParam param) {
        log.info("当前企业可用推送账户列表: {}", param);
        return remindAccountService.unbindCorpBalance(param)
            .block(Duration.ofSeconds(50L));
    }

    @Operation(summary = "企客余额提醒账户列表查询(中间态)")
    @PostMapping(value = "/corpBalanceTemporary")
    public ListResponse<RemindAccountVo> corpBalanceTemporary(
        @RequestBody ListRemindAccountParam param) {
        log.info("企客余额提醒账户列表查询: {}", param);
        return remindAccountService.corpBalanceTemporary(param)
            .block(Duration.ofSeconds(50L));
    }
}
