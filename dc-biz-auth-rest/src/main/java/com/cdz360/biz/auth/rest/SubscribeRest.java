package com.cdz360.biz.auth.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.config.CurrentUser;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.service.SubscribeService;
import com.cdz360.biz.auth.subscribe.param.*;
import com.cdz360.biz.auth.subscribe.vo.*;
import com.cdz360.biz.auth.sys.vo.SysRoleSimpleVo;
import com.cdz360.biz.auth.utils.IotAssert;
import com.cdz360.biz.model.sys.vo.SubscribeOrderVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;


@Slf4j
@RestController
@RequestMapping("/api/sub")
@Tag(name = "功能订阅相关接口", description = "功能订阅")
public class SubscribeRest {

    @Autowired
    private SubscribeService subscribeService;

    /**
     * 新增功能订阅
     *
     * @param params
     * @return
     */
    @PostMapping("/add")
    public BaseResponse add(@RequestBody AddSubscribeParam params) {
        log.info("新增功能订阅,params={}", JsonUtils.toJsonString(params));
        return subscribeService.add(params);
    }

    /**
     * 功能订阅编辑
     *
     * @param params
     * @return
     */
    @PostMapping("/update")
    public BaseResponse update(@RequestBody AddSubscribeParam params) {
        log.info("编辑功能订阅,params={}", JsonUtils.toJsonString(params));
        return subscribeService.update(params);
    }

    /**
     * 编辑功能订阅
     *
     * @param subId
     * @return
     */
    @GetMapping("/updateStatus")
    public BaseResponse updateStatus(@RequestParam("sysUid") Long sysUid,
                                     @RequestParam("subId") Long subId) {
        return subscribeService.updateStatus(sysUid, subId);
    }

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @PostMapping("/getList")
    public ListResponse<SubscribeVo> getList(@RequestBody SubscribeListParam params) {
        log.info("功能订阅列表,params={}", JsonUtils.toJsonString(params));
        return subscribeService.getList(params);
    }

    /**
     * 绑定的商户信息
     *
     * @param subId
     * @return
     */
    @GetMapping("/getCommList")
    public ListResponse<CommVo> getCommList(@RequestParam("subId") Long subId) {
        IotAssert.isNotNull(subId, "ID不能为空");
        return subscribeService.getCommList(subId);
    }

    /**
     * 绑定的角色信息
     *
     * @param subId
     * @return
     */
    @GetMapping("/getRoleList")
    public ListResponse<SysRoleSimpleVo> getRoleList(@RequestParam("subId") Long subId) {
        IotAssert.isNotNull(subId, "ID不能为空");
        return subscribeService.getRoleList(subId);
    }

    @GetMapping("/getDetail")
    public ObjectResponse<SubscribeDetailVo> getDetail(@RequestParam("subId") Long subId) {
        IotAssert.isNotNull(subId, "ID不能为空");
        return subscribeService.getDetail(subId);
    }

    @GetMapping("/getListByUser")
    public ListResponse<SubscribeDetailVo> getListByUser(@CurrentUser SysUser user,
                                                         @RequestParam(value = "status", required = false) Boolean status) {
        IotAssert.isNotNull(user, "未登录的状态");
        return RestUtils.buildListResponse(subscribeService.getListByUser(user.getId(), user.getCommId(), status));
    }


    @PostMapping("/createPayOrder")
    public ObjectResponse<String> createPayOrder(@RequestBody CreatePayOrderParam params) {
        log.info("功能订阅创建订单，params={}", JsonUtils.toJsonString(params));
        return subscribeService.createPayOrder(params);
    }

    @GetMapping("/getOrderById")
    public ObjectResponse<SubscribeOrderVo> getOrderById(@RequestParam(value = "payNo") String payNo) {
        log.info("功能订阅订单号,payNo={}", payNo);
        return subscribeService.getOrderById(payNo);
    }

    @PostMapping("/getSubLogList")
    public ListResponse<SubscribeLogVo> getSubLogList(@RequestBody SubLogListParam params) {
        log.info("功能订阅记录,params={}", JsonUtils.toJsonString(params));
        return subscribeService.getSubLogList(params);
    }

    @GetMapping("/getRoleListByPayNo")
    public ListResponse<SubscribeOrderDetailVo> getRoleListByPayNo(@RequestParam(value = "payNo") String payNo) {
        log.info("功能订阅订单号,payNo={}", payNo);
        return subscribeService.getRoleListByPayNo(payNo);
    }

    @PostMapping("/chargeNotify")
    public BaseResponse chargeNotify(@RequestBody NotifyPayParams params) {
        log.info("功能订阅充值成功回调,params={}", JsonUtils.toJsonString(params));
        return subscribeService.chargeNotify(params);
    }

    @GetMapping("/addNote")
    public BaseResponse addNote(@RequestParam(value = "payNo") String payNo,
                                @RequestParam(value = "note") String note) {
        log.info("备注说明,payNo={},note={}", payNo, note);
        return subscribeService.addNote(payNo, note);
    }

    @PostMapping(value = "/download")
    public void downloadFile(@RequestParam(value = "filePath") String filePath,
                             @RequestParam(value = "fileName") String fileName,
                             HttpServletResponse response) throws Exception {
        URL url = new URL(filePath);
        URLConnection conn = url.openConnection();
        InputStream inputStream = conn.getInputStream();
        response.reset();
        response.setContentType(conn.getContentType());
        //纯下载方式 文件名应该编码成UTF-8
        response.setHeader("Content-Disposition",
                "attachment; filename=" + URLEncoder.encode(fileName,"UTF-8"));
        byte[] buffer = new byte[1024];
        int len;
        OutputStream outputStream = response.getOutputStream();
        while ((len = inputStream.read(buffer)) > 0) {
            outputStream.write(buffer, 0, len);
        }
        inputStream.close();
    }

}
