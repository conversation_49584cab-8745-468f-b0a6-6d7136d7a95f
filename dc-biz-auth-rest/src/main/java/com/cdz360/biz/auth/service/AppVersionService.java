package com.cdz360.biz.auth.service;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.biz.auth.dao.AppVersionMapper;
import com.cdz360.biz.auth.sys.po.AppVersionPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AppVersionService {

    @Autowired
    private AppVersionMapper appVersionMapper;

    public AppVersionPo getAppVersion(Long topCommId, AppClientType appType) {
        return this.appVersionMapper.getAppVersion(topCommId, appType);
    }
}
