<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cdz360.biz.auth.dao.SysRoleDao">
    <select id="findByUserId" resultType="com.cdz360.biz.auth.model.vo.SysRole">
        SELECT
        r.id ,r.pid,r.`name`,r.category, r.platform, r.tips,r.`status`,r.version,r.svaha,r.extra,r.seq,r.create_time AS
        createTime,r.update_time AS updateTime,r.create_by AS createBy,r.update_by AS updateBy
        FROM sys_user_role ur JOIN sys_role r ON ur.role_id= r.id
        WHERE ur.user_id=#{userId}
        and ur.enable=true and (ur.expireTime is null or ur.expireTime>=DATE_FORMAT(now(),'%Y-%m-%d 00:00:00'))
        ORDER BY r.seq
    </select>

    <select id="getRoleByUserId" resultType="com.cdz360.biz.auth.model.vo.SysRole">
        SELECT * from sys_role where create_by = #{userId}
    </select>
    <select id="getCorpByUserId" resultType="com.cdz360.biz.model.cus.corp.po.CorpPo">
        select
        corp.id,
        corp.topCommId,
        corp.commId
        from sys_user user
            left join t_corp_user_org user_org
            on user.id = user_org.sysUid
            left join t_corp corp on corp.id = user_org.corpId
        where user.id = #{userId} limit 1
    </select>
    <select id="findRoleHasSiteDetailAuth" resultType="com.cdz360.biz.auth.model.vo.SysRole">
        SELECT
            s1.id,
            s1.NAME
        FROM
            sys_role s1
            JOIN sys_role_menu s2 ON s1.id = s2.role_id
            JOIN sys_menu m ON m.id = s2.menu_id
        WHERE
            m.url = 'sitedetail' and (m.extra is null or m.extra='')
    </select>

</mapper>