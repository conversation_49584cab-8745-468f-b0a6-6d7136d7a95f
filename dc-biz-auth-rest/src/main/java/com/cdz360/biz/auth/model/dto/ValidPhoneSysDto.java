package com.cdz360.biz.auth.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ValidPhoneSysDto {

    @Pattern(regexp = "^1\\d{10}$", message = "不能识别为手机号")
    private String phone;
    @Min(0)
    private Long sysId;

    private Integer platform;
}
