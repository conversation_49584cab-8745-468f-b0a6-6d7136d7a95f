package com.cdz360.biz.auth.ds.ro.user;

import com.cdz360.biz.auth.ds.ro.user.mapper.CorpWxAppRoMapper;
import com.cdz360.biz.auth.user.param.ListCorpWxAppParam;
import com.cdz360.biz.auth.user.po.CorpWxAppPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class CorpWxAppRoDs {

	@Autowired
	private CorpWxAppRoMapper corpWxAppRoMapper;

	public CorpWxAppPo getById(Long id) {
		return this.corpWxAppRoMapper.getById(id);
	}

	public List<CorpWxAppPo> findAll(ListCorpWxAppParam param) {
		return this.corpWxAppRoMapper.findAll(param);
	}
}

