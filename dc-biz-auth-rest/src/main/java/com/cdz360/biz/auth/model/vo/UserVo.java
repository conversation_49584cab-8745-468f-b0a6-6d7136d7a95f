package com.cdz360.biz.auth.model.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * UserVo
 *  资金周转-用户信息
 * @since 2019/8/29
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class UserVo {
    /**
     * 主键id
     */
    private long code;
    /**
     * 账号
     */
    private String loginname;
    /**
     * 名字 可以是空字符串
     */
    private String displayname;
    /**
     * 生日 yyyy-MM-dd
     */
    private String birthday;
    /**
     * 性别（1：男 2：女;3:未知）
     */
    private int gender;
    /**
     * 电子邮件
     */
    private String email;
    /**
     * 电话
     */
    private String contactPhoneNum;
    /**
     * 状态(1：启用  2：冻结  3：删除）
     */
    private int status;
    /**
     * 身份证
     */
    private String idcard;
    /**
     * 最后登录时间 yyyy-MM-dd hh:mm:ss
     */
    private String lastLoginTime;
    /**
     * 创建时间 yyyy-MM-dd hh:mm:ss
     */
    private String createTime;
    /**
     * 修改时间 yyyy-MM-dd hh:mm:ss
     */
    private String updateTime;
    /**
     * 直属商户ID
     */
    private Long providerId;
}