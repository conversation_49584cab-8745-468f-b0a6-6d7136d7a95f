package com.cdz360.biz.auth.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.service.LoginService;
import com.cdz360.biz.auth.service.MessageBizService;
import com.cdz360.biz.auth.utils.IotAssert;
import com.cdz360.biz.model.cus.message.param.ListMessageParam;
import com.cdz360.biz.model.cus.message.po.MessagePo;
import com.cdz360.biz.model.cus.message.vo.MessageVo;
import com.cdz360.biz.model.cus.message.vo.UserMessageVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 站内信
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/msg")
@Tag(name = "站内信相关接口", description = "站内信")
public class MessageRest {


    @Autowired
    private MessageBizService messageBizService;
    @Autowired
    private LoginService loginService;

    /**
     * 添加站内信
     *
     * @param token
     * @param message
     * @return
     */
    @PostMapping("/addMessage")
    public ObjectResponse addMessage(@RequestParam(value = "token", required = false) String token,
                                     @RequestBody MessagePo message) {
        log.info("请求信息：token={},messageInfo={}", token, message);
        if (StringUtils.isNotBlank(token)) {
            SysUser sysUser = this.getUserIdByToken(token);
            IotAssert.isNotNull(sysUser, "未登录状态");
            message.setOpUid(sysUser.getId());
        } else {
            message.setOpUid(0L);
        }
        return RestUtils.buildObjectResponse(this.messageBizService.addMessage(message));
    }

    /**
     * 撤回消息
     *
     * @param token
     * @param msgId
     * @return
     */
    @GetMapping("/editMessage")
    public ObjectResponse editMessage(@RequestParam("token") String token, @RequestParam("msgId") Long msgId) {
        SysUser sysUser = this.getUserIdByToken(token);
        IotAssert.isNotNull(sysUser, "未登录状态");
        return RestUtils.buildObjectResponse(this.messageBizService.editMessage(sysUser.getId(), msgId));
    }

    /**
     * 获取站内信详情
     *
     * @param token
     * @param msgId
     * @return
     */
    @GetMapping("/getMessage")
    public ObjectResponse<UserMessageVo> getMessage(@RequestParam("token") String token, @RequestParam("msgId") Long msgId) {
        log.info("请求信息：msgId={},token={}", msgId, token);
        SysUser sysUser = this.getUserIdByToken(token);
        IotAssert.isNotNull(sysUser, "未登录状态");
        return RestUtils.buildObjectResponse(this.messageBizService.getMessageById(msgId, sysUser.getId(), sysUser.getPlatform()));
    }

    /**
     * 未读消息总数
     *
     * @param token
     * @param platform
     * @return
     */
    @GetMapping("/getUnReadCount")
    public ObjectResponse getUnReadCount(@RequestParam("token") String token, @RequestParam("platform") Long platform) {
        log.info("请求信息：token={},platform={}", token, platform);
        SysUser sysUser = this.getUserIdByToken(token);
        IotAssert.isNotNull(sysUser, "未登录状态");
        return RestUtils.buildObjectResponse(this.messageBizService.getUnReadCount(sysUser.getId(), platform));
    }

    /**
     * 站内信列表，支撑平台
     *
     * @param reqParam
     * @return
     */
    @PostMapping("/getMsgList")
    public ListResponse<MessageVo> getMsgList(@RequestBody ListMessageParam reqParam) {
        log.info("请求参数：reqParam={}", reqParam);
        return this.messageBizService.getMsgList(reqParam);
    }

    /**
     * 个人站内信列表
     *
     * @param token
     * @param reqParam
     * @return
     */
    @PostMapping("/getUserMsgList")
    public ListResponse<UserMessageVo> getUserMsgList(@RequestParam("token") String token, @RequestBody ListMessageParam reqParam) {
        log.info("请求参数：reqParam={}", reqParam);
        SysUser sysUser = this.getUserIdByToken(token);
        IotAssert.isNotNull(sysUser, "未登录状态");
        reqParam.setUid(sysUser.getId());
        return this.messageBizService.getUserMsgList(reqParam);
    }

    /**
     * 通过token获取用户id
     *
     * @param token
     * @return
     */
    public SysUser getUserIdByToken(String token) {
        log.info("token获取用户信息 ~> token:{}", token);
        String json = loginService.getUserJson(token);
        if (json == null) {
            log.warn("token获取用户信息 ~> token验证失败:{}", token);

        }
        return JsonUtils.fromJson(json, SysUser.class);
    }
}
