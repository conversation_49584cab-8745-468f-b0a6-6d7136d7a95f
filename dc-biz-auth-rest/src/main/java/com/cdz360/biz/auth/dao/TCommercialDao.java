package com.cdz360.biz.auth.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdz360.biz.auth.model.dto.TCommercialDto;
import com.cdz360.biz.auth.model.dto.UserCommericalDto;
import com.cdz360.biz.auth.model.vo.Commercial;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.model.vo.TCommercial;
import com.cdz360.biz.auth.model.vo.TCommercialUser;
import com.cdz360.biz.auth.zft.po.ZftPo;
import com.cdz360.biz.model.merchant.dto.CommercialDto;
import com.cdz360.biz.model.merchant.param.ListCommercialParam;
import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;

public interface TCommercialDao extends BaseMapper<TCommercial> {

    TCommercial selectByUserId(Long userId);

    CommercialSimpleVo findSimpleVoById(@Param("id") Long id);

    List<SysUser> selectUserByCommercials(UserCommericalDto ucd);


    List<TCommercialUser> selectByUserPageByCommercialUser(@Param("param") UserCommericalDto param);

    long countByUserPageByCommercialUser(@Param("param") UserCommericalDto param);

    long countByTCommercial(@Param("param") TCommercialDto param);

    List<TCommercial> selectPageByCom(@Param("param") TCommercialDto param);

    List<TCommercial> checkByShortName(@Param("shortName") String shortName);

    List<Commercial> findByCommIdListAndName(@Param("commIdList") List<Long> commIdList,
                                             @Param("commName") String commName);


    List<CommercialDto> getCommercialList(ListCommercialParam param);

    List<Long> getCommercialIdList();


    void updateIdChain(@Param("id") Long id,
                       @Param("commLevel") Integer commLevel,
                       @Param("idChain") String idChain);

    Integer getLeastPrivilegesCommLevel(@Param("idChain") String idChain);

    Long hasZftId(
            @Param("commIdList") List<Long> commIdList,
            @Param("excludeZftId") Long excludeZftId);

    List<Long> getCommIdList( @Param("commId") Long commId );

    List<TCommercial> getCommListByIdChain(String idChain);

    ZftPo getZftNameById(Long id);

    TCommercial getCommercialInfoByMerchant( @Param("merchants") String merchants);

    TCommercial getCommercialInfoByIdList( @Param("idList") List<Long> idList);

    int updateRefundStatus(Long commId,Boolean refundStatus);

    int updateRefundStatusByzftId(Long zftId,Boolean refundStatus);

    int editHlhtSitePayType(@Param("commId") long commId, @Param("hlhtSitePayType") String hlhtSitePayType);

}
