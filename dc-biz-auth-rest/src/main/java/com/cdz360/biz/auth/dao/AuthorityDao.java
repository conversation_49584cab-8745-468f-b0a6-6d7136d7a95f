package com.cdz360.biz.auth.dao;

import com.cdz360.biz.auth.sys.vo.Authority;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * AuthorityDao
 *  TODO
 * @since 2020/2/11 16:52
 * <AUTHOR>
 */
@Mapper
public interface AuthorityDao {

    List<Authority> getAllAuthority(@Param("sysId") Long sysId);

//    List<Authority> getAuthorityByGroupId();

    List<Authority> getAuthorityListByUid(@Param("id") Long uid);

    List<Authority> getAuthorityListByGroupIdList(@Param("idList") List<Long> idList);

    List<Authority> getAuthorityByModule(@Param("moduleList") List<String> moduleList);
}
