package com.cdz360.biz.auth.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.service.SiteGroupUserService;
import com.cdz360.biz.auth.utils.IotAssert;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.sys.param.ListSiteGroupParam;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Tag(name = "场站组用户相关接口", description = "场站组用户相关接口")
public class SiteGroupUserRest {


    @Autowired
    private SiteGroupUserService siteGroupUserService;

    @Operation(summary = "获取场站组用户列表")
    @GetMapping(value = "/api/siteGroup/user/getByGid")
    public ListResponse<SysUserVo> getUserBySiteGroupGid(
        HttpServletRequest request,
        @ApiParam("gid") @RequestParam("gid") String gid) {
        log.debug("获取场站组用户列表: gid = {}", gid);
        return RestUtils.buildListResponse(siteGroupUserService.getUserBySiteGroupGid(gid));
    }
    @Operation(summary = "获取场站组人员列表")
    @PostMapping(value = "/api/siteGroup/user/getByGidList")
    public ListResponse<SysUserVo> getByGidList(@RequestBody ListSiteGroupParam param) {
        log.debug("获取场站组人员列表: param = {}", JsonUtils.toJsonString(param));
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getGidList()),"场站组信息不能为空");
        return RestUtils.buildListResponse(siteGroupUserService.getByGidList(param));
    }
}
