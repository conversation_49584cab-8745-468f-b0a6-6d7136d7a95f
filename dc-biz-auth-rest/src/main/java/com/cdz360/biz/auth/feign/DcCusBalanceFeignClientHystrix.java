package com.cdz360.biz.auth.feign;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.corp.po.ListPointParam;
import com.cdz360.biz.auth.sys.po.PointPo;
import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2019/11/12 19:15
 */
@Slf4j
@Component
public class DcCusBalanceFeignClientHystrix implements FallbackFactory<DcCusBalanceFeignClient> {
    @Override
    public DcCusBalanceFeignClient create(Throwable cause) {
        log.error("【服务熔断】。Service = {},Message = {}", "dc-cus-balance", cause.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", "dc-cus-balance", cause.getStackTrace());

        return new DcCusBalanceFeignClient() {

//            @Override
//            public ObjectResponse<PointPo> getPoint(String type, String uid, String subUid) {
//                return null;
//            }

            @Override
            public ListResponse<PointPo> listPoint(ListPointParam param) {
                return null;
            }
        };
    }
}
