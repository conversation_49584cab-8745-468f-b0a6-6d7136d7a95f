package com.cdz360.biz.auth.ds.ro.subscribe;

import com.cdz360.biz.auth.ds.ro.subscribe.mapper.SubscribeRoMapper;
import com.cdz360.biz.auth.subscribe.param.SubLogListParam;
import com.cdz360.biz.auth.subscribe.param.SubscribeListParam;
import com.cdz360.biz.auth.subscribe.po.SubscribePo;
import com.cdz360.biz.auth.subscribe.vo.*;
import com.cdz360.biz.auth.sys.vo.SysRoleSimpleVo;
import com.cdz360.biz.model.sys.vo.SubscribeOrderVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SubscribeRoDs {

    @Autowired
    private SubscribeRoMapper subscribeRoMapper;

    public SubscribePo getById(Long id) {
        return this.subscribeRoMapper.getById(id);
    }

    public SubscribePo getSubByTitle(String title) {
        return this.subscribeRoMapper.getSubByTitle(title);
    }

    public SubscribeOrderVo getOrderById(String payNo) {
        return this.subscribeRoMapper.getOrderById(payNo);
    }

    public SubscribeDetailVo getDetail(Long subId) {
        return this.subscribeRoMapper.getDetail(subId);
    }

    public Long getCount(SubscribeListParam params) {
        return this.subscribeRoMapper.getCount(params);
    }

    public List<CommVo> getCommList(Long subId) {
        return this.subscribeRoMapper.getCommList(subId);
    }

    public List<SubscribeVo> getList(SubscribeListParam params) {
        return this.subscribeRoMapper.getList(params);
    }

    public List<SysRoleSimpleVo> getRoleList(Long subId) {
        return this.subscribeRoMapper.getRoleList(subId);
    }

    public List<SubscribeDetailVo> getListByUser(Long sysId, Long commId, Boolean status) {
        return this.subscribeRoMapper.getListByUser(sysId, commId, status);
    }

    public Long getLogCount(SubLogListParam params) {
        return this.subscribeRoMapper.getLogCount(params);
    }

    public List<SubscribeLogVo> getLogList(SubLogListParam params) {
        return this.subscribeRoMapper.getLogList(params);
    }

    public List<SubscribeOrderDetailVo> getRoleListByPayNo(String payNo) {
        return this.subscribeRoMapper.getRoleListByPayNo(payNo);
    }
}
