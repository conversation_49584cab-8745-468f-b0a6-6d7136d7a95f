package com.cdz360.biz.auth.model.dto;

import com.cdz360.base.utils.JsonUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class TCommercialDto extends PageDto {

    Integer commLevel;
    Integer commType;
    Integer commCategory;
    Integer commIndustry;
    String merchants;
    String keyWord;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
