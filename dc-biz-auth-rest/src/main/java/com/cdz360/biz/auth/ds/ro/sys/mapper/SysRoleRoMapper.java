package com.cdz360.biz.auth.ds.ro.sys.mapper;

import com.cdz360.biz.auth.model.vo.SysRole;
import com.cdz360.biz.auth.sys.param.RoleUserListParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysRoleRoMapper {
    /**
     * 获取当前登录用户是否是管理员
     *
     * @param userId
     * @return
     */
    SysRole getUserRoleById(Long userId);

    List<SysRole> getRoleByIdList(@Param("roleIdList") List<Long> roleIdList, @Param("platform") Long platform);

    Long getRoleAmountByUserId(RoleUserListParam params);

    List<SysRole> getRoleListByUserId(RoleUserListParam params);

    List<SysRole> getRoleByUserId(@Param("keyWord") String keyWord,
                                     @Param("platformList") List<Long> platform,
                                     @Param("userId") Long userId,
                                     @Param("size") Long size);
}
