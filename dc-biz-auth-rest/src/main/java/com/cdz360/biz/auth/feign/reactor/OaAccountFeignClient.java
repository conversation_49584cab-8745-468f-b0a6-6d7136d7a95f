package com.cdz360.biz.auth.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.oa.dto.account.OaAccountDto;
import com.cdz360.biz.oa.dto.account.OaGroupDto;
import com.cdz360.biz.oa.dto.account.OaMemberGroupListDto;
import com.cdz360.biz.oa.param.account.ListOaGroupParam;
import com.cdz360.biz.oa.param.account.ListOaGroupUserParam;
import com.cdz360.biz.oa.param.account.OaModifyGroupParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_OA,
        fallbackFactory = OaAccountFeignHystrix.class)
public interface OaAccountFeignClient {
    // 获取审核组列表
    @PostMapping(value = "/oa/account/groupUserList")
    Mono<ListResponse<OaAccountDto>> groupUserList(@RequestBody ListOaGroupUserParam param);

    // 获取审核组列表
    @PostMapping(value = "/oa/account/groupList")
    Mono<ListResponse<OaGroupDto>> groupList(@RequestBody ListOaGroupParam param);

    // 批量获取审核组列表
    @PostMapping(value = "/oa/account/memberGroupList")
    Mono<ListResponse<OaMemberGroupListDto>> memberGroupList(@RequestBody ListOaGroupParam param);

    // 创建/修改审核组信息
    @PostMapping(value = "/oa/account/modifyGroup")
    Mono<ObjectResponse<String>> modifyGroup(@RequestBody OaModifyGroupParam param);

    // 删除审核组信息
    @GetMapping(value = "/oa/account/removeGroup")
    Mono<ObjectResponse<OaGroupDto>> removeGroup(@RequestParam(value = "gid") String gid);
}
