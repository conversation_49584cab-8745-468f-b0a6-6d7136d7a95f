package com.cdz360.biz.auth.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.auth.dao.AuthGroupDao;
import com.cdz360.biz.auth.dao.AuthGroupRefDao;
import com.cdz360.biz.auth.dao.AuthorityDao;
import com.cdz360.biz.auth.dao.UserGroupMapper;
import com.cdz360.biz.auth.model.param.AddUserGroupRequest;
import com.cdz360.biz.auth.model.vo.AuthGroupRef;
import com.cdz360.biz.auth.sys.vo.Authority;
import com.cdz360.biz.auth.model.vo.AuthorityGroup;
import com.cdz360.biz.auth.utils.IotAssert;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * AuthorityService
 *  TODO
 * @since 2020/2/11 16:38
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuthorityService {

    @Autowired
    AuthGroupDao authGroupDao;
    @Autowired
    AuthGroupRefDao authGroupRefDao;
    @Autowired
    AuthorityDao authorityDao;

    @Autowired
    private UserGroupMapper userGroupMapper;

    @Transactional
    public BaseResponse addGroup(AuthorityGroup authorityGroup, List<Authority> authorityList) {
        if (authorityGroup.getOpId() == null) {
            authorityGroup.setOpId(1L);
        }
        if (authorityGroup.getOpName() == null) {
            authorityGroup.setOpName("sys");
        }

        // 是否存在同名组
        IotAssert.isNull(
            authGroupDao.getGroupByNameNonId(authorityGroup.getGroupName(), authorityGroup.getId()),
            "存在同名权限组，请重试");

        if (authorityGroup.getId() != null) {
            IotAssert.isTrue(authorityGroup.getId() > 3, "无法修改默认权限组，请确认后再尝试。");
            authGroupDao.updateGroup(authorityGroup);
            authGroupRefDao.deleteGroupRefByGroupId(authorityGroup.getId());
        } else {
            authGroupDao.addGroup(authorityGroup);
        }
        List<AuthGroupRef> authGroupRefList = new ArrayList<>();
        authorityList.stream().forEach(e -> {
            AuthGroupRef authGroupRef = new AuthGroupRef();
            authGroupRef.setGroupId(authorityGroup.getId()).setAuthorityCode(e.getCode())
                .setAuthorityId(e.getId())
                .setCreateTime(new Date()).setOpId(authorityGroup.getOpId())
                .setOpName(authorityGroup.getOpName());
            authGroupRefList.add(authGroupRef);
        });
        authGroupRefDao.batchAddGroupRef(authGroupRefList);
        return BaseResponse.success();
    }

    public List<AuthorityGroup> getGroups() {
        return authGroupDao.getGroupList();
    }

    public AuthorityGroup getGroupById(Long id) {
        AuthorityGroup authorityGroup = authGroupDao.getGroupById(id);
        List<AuthGroupRef> authGroupRefList = authGroupRefDao.selectByGroupId(id);
        authorityGroup.setAuthGroupRef(authGroupRefList);
        return authorityGroup;
    }

    @Transactional
    public BaseResponse deleteGroupById(Long id) {
        IotAssert.isNotNull(id, "请传入需要删除的权限组编号");
        IotAssert.isTrue(id.intValue() > 3, "无法删除默认权限组，请确认后再尝试。");

        List<String> nameList = this.getAccountListByGroupId(id, 0L, 20);
        if (CollectionUtils.isNotEmpty(nameList)) {
            String names = String.join(",", nameList);
            String msg = MessageFormat.format("该权限组已关联下列帐号,请解绑后再删除: {0}", names);
            log.warn(msg);
            // @Nathan 产品要求调整文案
            throw new DcServiceException(
                "权限组已关联其他账户无法被删除，请在账号管理中使用该权限名称进行检索并完成解绑后再次尝试。");
        }
        authGroupDao.deleteGroupById(id);
        authGroupRefDao.deleteGroupRefByGroupId(id);
        return BaseResponse.success();
    }

    public List<Authority> getAuthorityList(Long sysId) {
        return authorityDao.getAllAuthority(sysId);
    }

    public List<Authority> getAuthorityListByUid(Long uid) {

        return authorityDao.getAuthorityListByUid(uid);
    }

    public List<Authority> getAuthorityListByGroupIdList(List<Long> idList) {

        return authorityDao.getAuthorityListByGroupIdList(idList);
    }

    public int modifyUserGroupRef(AddUserGroupRequest addUserGroupRequest) {
        int ret = userGroupMapper.deleteUserGroupRefByUserId(addUserGroupRequest.getUserId());
        log.info("删除了: {}", ret);
        if (CollectionUtils.isNotEmpty(addUserGroupRequest.getAuthGroupIds())) {
            return userGroupMapper.batchInsertUserGroupRef(addUserGroupRequest.getUserId(),
                addUserGroupRequest.getAuthGroupIds(),
                addUserGroupRequest.getOpName(),
                addUserGroupRequest.getOpId());
        } else {
            log.info("此账户无任何关联的权限组");
            return 0;
        }
    }

    public List<AuthorityGroup> getAuthorityGroupListByUid(Long uid) {
        return authGroupRefDao.getAuthorityGroupListByUid(uid);
    }


    public List<Long> getAuthGroupIdListByUid(Long userId) {
        return userGroupMapper.getAuthGroupIdListByUid(userId);
    }

    public List<String> getAccountListByGroupId(Long authId, Long start, Integer size) {
        if (start == null) {
            start = 0L;
        }
        if (size == null) {
            size = 10;
        }
        return userGroupMapper.getAccountListByGroupId(authId, start, size);
    }
}
