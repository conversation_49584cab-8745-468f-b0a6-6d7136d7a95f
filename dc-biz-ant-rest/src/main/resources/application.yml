
spring:
  application:
    name: dc-biz-ant-dev
  config:
    import: "configserver:http://oam-test.iot.renwochong.com"
  profiles:
    active: test01,common,oss,rabbitmq,redis,zipkin
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    config:
      label: test01

eureka:
  instance:
    hostname: localhost
  client:
    registerWithEureka: false
    fetchRegistry: true
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: http://aaa:<EMAIL>:7001/eureka/

feign:
  hystrix:
    enabled: true

hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 60000
          semaphore:
            maxConcurrentRequests: 500
#ribbon:
#  ReadTimeout: 180000
#  ConnectTimeout: 180000

reactive:
  feign:
    loadbalancer:
      enabled: true
    circuit:
      breaker:
        enabled: false
    cloud:
      enabled: true
    ribbon:
      enabled: false
    hystrix:
      enabled: false
    logger:
      enabled: true
    metrics:
      enabled: true
    client:
      config:
        iot-meter:
          options:
            connectTimeoutMillis: 60000
        dc-biz-data-core:
          options:
            connectTimeoutMillis: 60000
            acceptCompressed: true


server:
  port: 8082
#  servlet:
#    context-path: /ant

logging:
  level:
    reactivefeign: 'DEBUG'
    com.cdz360: 'DEBUG'
    com.chargerlinkcar.core: 'DEBUG'
    org.springframework: 'WARN'
    org.springframework.cloud.config: 'INFO'

  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} - %logger{36}.%M\\(%line\\) - %msg%n"

springdoc:
  packagesToScan: com.cdz360.biz.ant.rest
  swagger-ui:
    path: /swagger-ui.html
#    enabled: false
#  api-docs:
#    enabled: false



dmpUrl: http://192.168.64.126:8890


# 分页配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql


#oss存储配置
oss:
  sts:
    endpoint: "http://oss-cn-shenzhen.aliyuncs.com"
    accessKeyId: "LTAIzhdaNgdRVbq1"
    accessKeySecret: "jCHObgae1It5keervX4ppWoEyEa8pc"
    bucketName: "dc-sz-1"
    regionId: "cn-shenzhen"
    apiVersion: "2015-04-01"
    roleArn: "acs:ram::1102139844003241:role/aliyunosstokengeneratorrole"

#静态获取banner图
IMAGEURL1: https://cl-fdfs.chargerlink.com/group1/M00/00/B4/ClCPflrwQRWAP4rBAABJapOwyUg277.png
IMAGEURL2: https://cl-fdfs.chargerlink.com/group1/M00/00/B4/ClCPflrwQZGAG_svAABdkuvJWwo274.png
IMAGEURL3: https://cl-fdfs.chargerlink.com/group1/M00/00/B4/ClCPflrwQb2ASyy1AABfHjSzjLI407.png
ADSTYPE: 1

zhuofuLoginUrl:
