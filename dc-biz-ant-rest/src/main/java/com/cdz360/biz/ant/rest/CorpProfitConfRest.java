package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.service.sysLog.CustomerSysLogService;
import com.cdz360.biz.model.trading.profit.conf.po.CorpProfitConfPo;
import com.cdz360.biz.model.trading.profit.conf.vo.CorpProfitBaseVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 *
 * @since 2/23/2021 5:21 PM
 * <AUTHOR>
 */
@RestController
@Slf4j
@Tag(name = "企业收益计算策略", description = "企业")
@RequestMapping("/api/corpProfitConf")
public class CorpProfitConfRest {

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private CustomerSysLogService customerSysLogService;

    @GetMapping("/getCorpProfitConf")
    public ObjectResponse<CorpProfitBaseVo> getCorpProfitConf(ServerHttpRequest request,
                                                              @RequestParam("corpId") Long corpId) {
        log.info("getCorpProfitConf: {}", corpId);
        return dataCoreFeignClient.getCorpProfitConf(corpId);
    }

    @PostMapping("/addCorpProfitConf")
    public BaseResponse addCorpProfitConf(ServerHttpRequest request,
                                          @RequestBody CorpProfitBaseVo params) {
        log.info("addCorpProfitConf: {}", JsonUtils.toJsonString(params));
        dataCoreFeignClient.addCorpProfitConf(params);
        customerSysLogService.editCorpProfitConf(params, request);
        return BaseResponse.success();
    }

    @GetMapping("/disableCorpProfitConf")
    public BaseResponse disableCorpProfitConf(ServerHttpRequest request,
                                              @RequestParam("corpId") Long corpId,
                                              @RequestParam("corpName") String corpName) {
        log.info("disableCorpProfitConf: {}, {}", corpId, corpName);
        dataCoreFeignClient.disableCorpProfitConf(corpId);
        customerSysLogService.disableCorpProfitConf(corpName, request);
        return BaseResponse.success();
    }
}