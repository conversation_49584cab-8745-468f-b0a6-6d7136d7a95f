package com.cdz360.biz.ant.service;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.aop.CheckAuthAround;
import com.cdz360.biz.ant.domain.User;
import com.cdz360.biz.ant.domain.vo.CusOrderBiVo;
import com.cdz360.biz.ant.domain.vo.UserAndDynamicAndCardsVo;
import com.cdz360.biz.ant.domain.vo.UserSampleVo;
import com.cdz360.biz.ant.domain.vo.UserVo;
import com.cdz360.biz.ant.feign.AntUserFeignClient;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorSiteDataCoreFeignClient;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.ant.utils.RedisUtil;
import com.cdz360.biz.model.commercialUser.param.UserRegisterParam;
import com.cdz360.biz.model.common.constant.Constant;
import com.cdz360.biz.model.cus.corp.type.LimitCycle;
import com.cdz360.biz.model.cus.user.dto.CusSampleDto;
import com.cdz360.biz.model.cus.user.dto.UserAndBalanceAndTokenVo;
import com.cdz360.biz.model.cus.user.param.AddUserParam;
import com.cdz360.biz.model.cus.user.param.ListCustomerParam;
import com.cdz360.biz.model.cus.user.param.ListCustomerSiteParam;
import com.cdz360.biz.model.order.type.OrderPayType;
import com.cdz360.biz.model.trading.cus.vo.UnliquidatedOrderVo;
import com.cdz360.biz.model.trading.order.dto.CusLastOrderSiteDto;
import com.cdz360.biz.model.trading.order.dto.CusOrderBiDto;
import com.cdz360.biz.model.trading.order.param.ListCusOrderBiParam;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import com.chargerlinkcar.framework.common.domain.BlocUserDto;
import com.chargerlinkcar.framework.common.domain.SysUser;
import com.chargerlinkcar.framework.common.domain.TCommercialUser;
import com.chargerlinkcar.framework.common.domain.UserCommRef;
import com.chargerlinkcar.framework.common.domain.request.AddUserRequest;
import com.chargerlinkcar.framework.common.domain.vo.AccountExVo;
import com.chargerlinkcar.framework.common.domain.vo.AccountInfoVo;
import com.chargerlinkcar.framework.common.domain.vo.PointPoVo;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUserVo;
import com.chargerlinkcar.framework.common.domain.vo.UserPropVO;
import com.chargerlinkcar.framework.common.feign.CommercialFeignClient;
import com.chargerlinkcar.framework.common.feign.UserFeignClient;
import com.chargerlinkcar.framework.common.service.DcUserService;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple3;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserService //implements IUserService
{


    public final static String CODE = "0";
    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;
    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private CommercialFeignClient commercialFeignClient;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private ReactorSiteDataCoreFeignClient reactorSiteDataCoreFeignClient;
    /**
     * 用户
     */
    @Autowired
    private AntUserFeignClient antUserFeignClient;

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private DcUserService dcUserService;
    @Autowired
    private BalanceService balanceService;

//    /**
//     * 分页 查询客户列表
//     *
//     * @param commIdList 当前商户及子商户id列表
//     * @param userParams 按用户名/用户ID查询条件的判断(0.用户名,1.用户ID，2:手机号,3:按特殊客户搜索)
//     * @param keyWord    用户名/用户ID/手机号
//     * @param sourceId   应用来源(1.APP,2.微信,3.平台)
//     * @param stats      状态(0.禁用，1.启用)
//     * @return
//     *   
//     */

    //    public ListResponse<UserAndDynamicVo> queryUsers(long commercialId, List<Long> commIdList, //Page<UserAndDynamicVo> page,
//                                                     Integer userParams, String keyWord,
//                                                     Long sourceId, Integer stats, Long commId,
//                                                     Long start, Integer size) {
////        Integer _index = page.getPageNum();
////
////        Integer _size = page.getPageSize();
//
//        if (commId != null) {
//            if (commIdList.contains(commId)) {
//                commIdList.clear();
//                commIdList.add(commId);
//            } else {
//                throw new DcArgumentException("参数错误");
//            }
//        }
//
//        ListResponse<UserAndDynamicVo> jsonObjectRes = antUserFeignClient.queryUsers(commercialId, userParams, keyWord, sourceId, stats, commIdList,
//                start, size);
//        return jsonObjectRes;
//
//    }


    public Mono<ListResponse<CusOrderBiVo>> getCusOrderBiList(ListCustomerParam param) {
        ListResponse<CusSampleDto> cusListRes = this.userFeignClient.getCusSampleList(param);
        FeignResponseValidate.check(cusListRes);
        if (CollectionUtils.isEmpty(cusListRes.getData())) {
            if (param.getSize() == 0 && cusListRes.getTotal() != 0L) {
                log.info("<< 获取客户列表，仅统计总数");
                return Mono.just(
                    RestUtils.buildListResponse(new ArrayList<>(), cusListRes.getTotal()));
            } else {
                log.info("<< no data");
                return Mono.just(RestUtils.buildListResponse(new ArrayList<>()));
            }
        }

        List<Long> uidList = cusListRes.getData().stream()
            .map(CusSampleDto::getId).collect(Collectors.toList());
        ListCusOrderBiParam param2 = new ListCusOrderBiParam();
        param2.setUidList(uidList);
        param2.setCommIdChain(param.getCommIdChain());

        Mono<Tuple3<Map<Long, CusOrderBiDto>, Map<Long, CusLastOrderSiteDto>, Map<Long, UnliquidatedOrderVo>>> tuple = Mono.zip(

            Mono.just(uidList)
                .map(i -> new ListCusOrderBiParam()
                    .setCommIdChain(null) // 充电统计数据忽略商户层级限制
                    .setUidList(i))
                .flatMap(req -> reactorSiteDataCoreFeignClient.getCusOrderBiList(req))
                .flatMap(biListRes -> {
                    FeignResponseValidate.check(biListRes);
                    Map<Long, CusOrderBiDto> biMap = biListRes.getData()
                        .stream().collect(Collectors.toMap(CusOrderBiDto::getUid, o -> o));
                    return Mono.just(biMap);
                }),

            reactorSiteDataCoreFeignClient.getCusOrderLastSiteInfoList(param2)
                .flatMap(cusOrderLastSiteInfoList -> {
//                    log.info("getCusOrderLastSiteInfoList");
                    FeignResponseValidate.check(cusOrderLastSiteInfoList);
                    Map<Long, CusLastOrderSiteDto> biLastOrderMap = cusOrderLastSiteInfoList.getData()
                        .stream().collect(Collectors.toMap(CusLastOrderSiteDto::getUid, o -> o));
//                    log.info("getCusOrderLastSiteInfoList-end");
                    return Mono.just(biLastOrderMap);
                }),

            reactorSiteDataCoreFeignClient.getUnliquidatedNum(param2)
                .doOnNext(FeignResponseValidate::check)
                .flatMap(data -> {
                    Map<Long, UnliquidatedOrderVo> unliquidatedMap = data.getData()
                        .stream().collect(Collectors.toMap(UnliquidatedOrderVo::getUserId, o -> o));
                    return Mono.just(unliquidatedMap);
                })
        );

        return tuple.flatMap(t -> Mono.just(new ListResponse<>(cusListRes.getData()
            .stream()
            .map(cus -> this.map2CusOrderBiVo(cus, t.getT1(), t.getT2(), t.getT3()))
            .collect(Collectors.toList()), cusListRes.getTotal())));

    }

    public ListResponse<CusSampleDto> getCusUserList(ListCustomerParam param) {
        return this.userFeignClient.getCusSampleList(param);
    }

    public Mono<ListResponse<CusSampleDto>> getCusBySiteId(ListCustomerSiteParam param) {
        ListResponse<CusSampleDto> cusListRes = this.userFeignClient.getCusBySiteId(param);
        FeignResponseValidate.check(cusListRes);
        return Mono.just(cusListRes);
    }

    public Mono<ListResponse<BlocUserDto>> getUpperCorpBySiteId(ListCustomerSiteParam param) {
        ListResponse<BlocUserDto> cusListRes = this.userFeignClient.getUpperCorpBySiteId(param);
        FeignResponseValidate.check(cusListRes);
        return Mono.just(cusListRes);
    }

    private CusOrderBiVo map2CusOrderBiVo(CusSampleDto cus,
        Map<Long, CusOrderBiDto> biMap,
        Map<Long, CusLastOrderSiteDto> lastOrderMap,
        Map<Long, UnliquidatedOrderVo> unliquidatedMap) {
        CusOrderBiVo vo = new CusOrderBiVo();
        BeanUtils.copyProperties(cus, vo);
        vo.setAvatar(cus.getImage()).setUid(cus.getId());
        CusOrderBiDto bi = biMap.get(vo.getUid());
        if (bi != null) {
            vo.setOrderNum(bi.getOrderNum())
                .setElec(bi.getElec())
                .setElecFee(bi.getElecFee())
                .setServFee(bi.getServFee());
        }
        CusLastOrderSiteDto cusLastOrderSiteDto = lastOrderMap.get(vo.getUid());
        if (cusLastOrderSiteDto != null) {
            vo.setSiteId(cusLastOrderSiteDto.getSiteId())
                .setSiteName(cusLastOrderSiteDto.getSiteName());
        }
        UnliquidatedOrderVo unliquidatedOrderVo = unliquidatedMap.get(vo.getUid());
        if (unliquidatedOrderVo != null) {
            vo.setUnliquidatedOrderNum(unliquidatedOrderVo.getUnliquidatedOrderNum());
        }
        vo.setCarNoList(cus.getCarNoList());
        return vo;
    }

    /**
     * 根据用户ID查询基本资料
     *
     * @param commIdChain
     * @param uid         用户ID
     * @return
     */

    public ObjectResponse<UserAndDynamicAndCardsVo> findDetailsByUserId(Long uid, Long topCommId,
        String commIdChain) {

        ObjectResponse<UserAndDynamicAndCardsVo> jsonObjectRes = antUserFeignClient.findUserAndDynamicByCommIdAndUid(
            uid, topCommId, commIdChain);
        log.info("用户基本资料 info={}", jsonObjectRes);
        FeignResponseValidate.check(jsonObjectRes);

        UserAndDynamicAndCardsVo userAndDynamicAndCardsVo = jsonObjectRes.getData();

        return new ObjectResponse<>(userAndDynamicAndCardsVo);

    }

    /**
     * 获取能分组的客户信息
     *
     * @param commIdList 当前商户及子商户id列表
     * @return
     */

    public ListResponse<UserSampleVo> queryNegationUsers(List<Long> commIdList) {
        ListResponse<UserSampleVo> jsonObjectRes = antUserFeignClient.queryNegationUsers(
            commIdList);
        return jsonObjectRes;

    }

    /**
     * 根据手机号和商户id查询客户信息
     *
     * @param phone
     * @param commId 当前登录账号所属的集团商户id
     * @return
     */

    public UserPropVO findByPhone(String phone, Long commId) {
        ObjectResponse<UserPropVO> jsonObjectRes = antUserFeignClient.findByPhone(phone, commId);
        return jsonObjectRes.getData();

    }

    /**
     * 根据email校验和商户id查询客户信息
     *
     * @param email
     * @param commId 当前商户id
     * @return
     */

    public UserPropVO findByEmail(String email, Long commId) {
        ObjectResponse<UserPropVO> jsonObjectRes = antUserFeignClient.findByEmail(email, commId);
        return jsonObjectRes.getData();

    }

    /**
     * 用户加入黑名单
     *
     * @param commIdChain 当前商户及子商户id列表
     * @param uid         用户id
     * @return
     */

    public BaseResponse userToBlack(Long uid, Long topCommId, String commIdChain) {
        BaseResponse jsonObjectRes = antUserFeignClient.userToBlack(uid, topCommId, commIdChain);
        FeignResponseValidate.check(jsonObjectRes);
        return jsonObjectRes;

    }

    /**
     * 用户解除黑名单
     *
     * @param uid
     * @return
     */

    public BaseResponse userUnBlack(Long uid, Long topCommId) {

        BaseResponse jsonObjectRes = antUserFeignClient.userUnBlack(uid, topCommId);
        FeignResponseValidate.check(jsonObjectRes);
        return jsonObjectRes;

    }


    /**
     * 根据客户ID查询客户信息
     *
     * @param commIdChain
     * @param uid         客户id
     * @return
     */

    public ObjectResponse<UserVo> findUserByUid(Long uid, Long topCommId, String commIdChain) {
        ObjectResponse<UserVo> jsonObjectRes = antUserFeignClient.findInfoByUid(uid, topCommId,
            commIdChain);
        return jsonObjectRes;

    }

    /**
     * 根据客户ID查询客户信息
     *
     * @param userId
     * @return
     */
//    public ObjectResponse<UserVo> findInfoByUid(long userId) {
//        ObjectResponse<UserVo> jsonObjectRes = userFeignClient.findInfoByUid(userId);
//        FeignResponseValidate.check(jsonObjectRes);
//        return jsonObjectRes;
//    }

//    /**
//     * 根据客户ID查询客户信息
//     *
//     * @param commIdList 当前商户及子商户id列表
//     * @param uid
//     * @return
//     */
//
//    public ObjectResponse findUserBycommIdListAndUid(List<Long> commIdList, Long uid) {
//        if (CollectionUtils.isEmpty(commIdList)) {
//            throw new DcServiceException("token失效");
//        }
//        ObjectResponse<UserVo> jsonObjectRes = userFeignClient.findInfoByUid(commIdList, uid);
//        return jsonObjectRes;
//
//    }

    /**
     * 获取用户信息
     *
     * @param commIdList 当前商户及子商户id列表
     * @param phone      用户手机号
     * @return
     */

    public ObjectResponse<UserVo> findUserByPhone(List<Long> commIdList, String phone) {

        ObjectResponse<UserVo> jsonObjectRes = antUserFeignClient.findUserByPhone(commIdList,
            phone);
        return jsonObjectRes;

    }

    /**
     * 是否开启多笔订单
     *
     * @param topCommId 当前商户及子商户id列表
     * @param uid       用户ID
     * @param isMulti   0-未开启;1-已开启
     * @return
     */

    public BaseResponse openMultiOrder(Long uid, boolean isMulti, Long topCommId) {

        BaseResponse jsonObjectRes = antUserFeignClient.openMultiOrder(uid, isMulti, topCommId);
        return jsonObjectRes;


    }

    /**
     * 根据phone和blocUserId查询集团用户
     *
     * @param rBlocUser(phone和blocUserId)
     * @return
     */
    public ObjectResponse<RBlocUser> selectRBlocUserByPhone(RBlocUser rBlocUser) {
        return antUserFeignClient.selectRBlocUserByPhone(rBlocUser);
    }

    /**
     * 查询用户下所有账户
     *
     * @param uid
     * @return Balance
     */

    public ListResponse<AccountExVo> getAccountByUserId(String commIdChain, Long uid,
        Long topCommId) {

        if (uid == null) {
            throw new DcArgumentException("参数错误");
        }

        ObjectResponse<UserVo> userVoObjectResponse = antUserFeignClient.findInfoByUid(uid,
            topCommId, commIdChain);
        FeignResponseValidate.check(userVoObjectResponse);
        UserVo userVo = userVoObjectResponse.getData();

        //现金账户时为集团id，集团授信时为集团授信的账户id(没变)，商户会员时为所属商户id
        Long defaultBalanceId = userVo.getBalanceId();
        Integer defaultPayType = userVo.getDefaultPayType();

        //个人账户
        PointPoVo personAccount = balanceService.getPersonalBalance(uid, userVo.getCommId());
        if (personAccount == null) {
            personAccount = new PointPoVo() {{
                setId(0L);
                setCommercialId(userVo.getCommId());
                setAvailable(DecimalUtils.ZERO);
                setPoint(DecimalUtils.ZERO);
                setFrozen(DecimalUtils.ZERO);
                setCost(DecimalUtils.ZERO);
                setFrozenCost(DecimalUtils.ZERO);
            }};
        }

        //集团授信账户
        ListResponse<RBlocUserVo> blocUserVOList = antUserFeignClient.getAuthAccountByUserId(uid,
            commIdChain);

        if (blocUserVOList == null
            || blocUserVOList.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            throw new DcServiceException("查询用户下集团授信账户失败");
        }

        //商户会员
        ListResponse<PointPoVo> commercialAccountRes = dcUserService.getCommercialBalanceByUid(
            userVo.getCommId(), commIdChain, uid//, commIdList
        );
        if (commercialAccountRes == null
            || commercialAccountRes.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            throw new DcServiceException("查询用户下商户会员失败");
        }

        List<AccountExVo> accountExVoList = new ArrayList<>();

        AccountExVo accountExVoPersonal = new AccountExVo();

        accountExVoPersonal.setPayAccountId(personAccount.getCommercialId());
        accountExVoPersonal.setDefaultPayType(String.valueOf(OrderPayType.PERSON.getCode()));
        accountExVoPersonal.setAccountName("个人账户");
        accountExVoPersonal.setAvailableAmount(
            DecimalUtils.yuan2fen(personAccount.getAvailable()));//可用余额
        accountExVoPersonal.setAmount(DecimalUtils.yuan2fen(personAccount.getPoint()));//总余额
        accountExVoPersonal.setFrozenAmount(DecimalUtils.yuan2fen(personAccount.getFrozen()));//冻结金额
        accountExVoPersonal.setCostAmount(DecimalUtils.yuan2fen(
            personAccount.getCost().subtract(personAccount.getFrozenCost())));//实际成本(实际余额)
        accountExVoPersonal.setFreeAmount(accountExVoPersonal.getAvailableAmount()
            - accountExVoPersonal.getCostAmount());//赠送余额 = 可用余额 - 实际成本（实际余额）
        accountExVoPersonal.setFrozenCostAmount(
            DecimalUtils.yuan2fen(personAccount.getFrozenCost()));//冻结实际金额
        accountExVoPersonal.setFrozenFreeAmount(accountExVoPersonal.getFrozenAmount()
            - accountExVoPersonal.getFrozenCostAmount());//冻结赠送金额 = 冻结金额 - 冻结实际金额
        //accountExVoPersonal.setEnablePayable(true);
        accountExVoPersonal.setDefaultPay(false);
        accountExVoPersonal.setUserId(uid);
        accountExVoList.add(accountExVoPersonal);

        if (!CollectionUtils.isEmpty(blocUserVOList.getData())) {
            for (RBlocUserVo rBlocUser : blocUserVOList.getData()) {
                AccountExVo accountExVo = new AccountExVo();
                accountExVo.setPayAccountId(rBlocUser.getId());
                accountExVo.setDefaultPayType(String.valueOf(OrderPayType.BLOC.getCode()));
                accountExVo.setAccountName("企业账户" + "-" + rBlocUser.getBlocUserName());
                accountExVo.setBlocAccountBalance(DecimalUtils.yuan2fen(
                    null == rBlocUser.getBlocAccountBalance() ? BigDecimal.ZERO
                        : rBlocUser.getBlocAccountBalance()));
                accountExVo.setBlocFrozenAccount(DecimalUtils.yuan2fen(
                    null == rBlocUser.getBlocFrozenAccount() ? BigDecimal.ZERO
                        : rBlocUser.getBlocFrozenAccount()));
                accountExVo.setBlocAvailableAccount(
                    accountExVo.getBlocAccountBalance() - accountExVo.getBlocFrozenAccount());

                accountExVo.setRblocLimitMoney(null == rBlocUser.getLimitMoney() ? 0
                    : DecimalUtils.yuan2fen(rBlocUser.getLimitMoney()));
                accountExVo.setRblocBalance(null == rBlocUser.getBalance() ? 0
                    : DecimalUtils.yuan2fen(rBlocUser.getBalance()));
                accountExVo.setRblocFrozenAmount(null == rBlocUser.getFrozenAmount() ? 0
                    : DecimalUtils.yuan2fen(rBlocUser.getFrozenAmount()));

                // 以下修复https://cn-pm.iot.renwochong.com/browse/BUG1118-398，可用余额为负数这显示0
                long availableAmount = Math.min(accountExVo.getBlocAvailableAccount(),
                    accountExVo.getRblocLimitMoney() - accountExVo.getRblocBalance()
                        - accountExVo.getRblocFrozenAmount());
                accountExVo.setAvailableAmount(availableAmount < 0 ? 0 : availableAmount);

                //accountExVo.setEnablePayable(true);
                accountExVo.setDefaultPay(false);
                accountExVo.setUserId(uid);

                accountExVo.setCards(rBlocUser.getCards());
                accountExVo.setVins(rBlocUser.getVins());

                accountExVoList.add(accountExVo);
            }
        }

        if (!CollectionUtils.isEmpty(commercialAccountRes.getData())) {
            for (PointPoVo pointPoVo : commercialAccountRes.getData()) {
                AccountExVo accountExVo = new AccountExVo();
                accountExVo.setPayAccountId(pointPoVo.getCommercialId());
                accountExVo.setDefaultPayType(String.valueOf(OrderPayType.MERCHANT.getCode()));
                accountExVo.setAccountName("商户会员" + "-" + pointPoVo.getCommercialName());

                accountExVo.setAvailableAmount(
                    DecimalUtils.yuan2fen(pointPoVo.getAvailable()));//可用余额
                accountExVo.setAmount(DecimalUtils.yuan2fen(pointPoVo.getPoint()));//总余额
                accountExVo.setFrozenAmount(DecimalUtils.yuan2fen(pointPoVo.getFrozen()));//冻结金额
                accountExVo.setCostAmount(DecimalUtils.yuan2fen(
                    pointPoVo.getCost().subtract(pointPoVo.getFrozenCost())));//实际成本(实际余额)
                accountExVo.setFreeAmount(accountExVo.getAvailableAmount()
                    - accountExVo.getCostAmount());//赠送余额 = 可用余额 - 实际成本（实际余额）
                accountExVo.setFrozenCostAmount(
                    DecimalUtils.yuan2fen(pointPoVo.getFrozenCost()));//冻结实际金额
                accountExVo.setFrozenFreeAmount(accountExVo.getFrozenAmount()
                    - accountExVo.getFrozenCostAmount());//冻结赠送金额 = 冻结金额 - 冻结实际金额

                //accountExVo.setEnablePayable(false);
                accountExVo.setEnable(pointPoVo.getEnable());//是否启用
                accountExVo.setDefaultPay(false);
                accountExVo.setUserId(uid);
                accountExVoList.add(accountExVo);
            }
        }

        //处理默认账户
        for (AccountExVo accountExVo : accountExVoList) {
            if (accountExVo.getDefaultPayType() != null
                && accountExVo.getDefaultPayType().equals(String.valueOf(defaultPayType))
                && accountExVo.getPayAccountId() != null
                && accountExVo.getPayAccountId().equals(defaultBalanceId)) {
                accountExVo.setDefaultPay(true);
            }
        }

        //处理商户会员是否可用

        ListResponse<AccountExVo> accountExVoListResponse = new ListResponse<AccountExVo>();
        accountExVoListResponse.setData(accountExVoList);

        return accountExVoListResponse;
    }

    /**
     * 查询用户下所有账户（将金额单位改成元，原接口暂时不改动）
     *
     * @param uid
     * @return Balance
     */

    public ListResponse<AccountInfoVo> getAccountByUserIdV2(long commId, String commIdChain,
        Long uid, Long topCommId) {
        log.debug("commId = {}, uid = {}, commIdChain = {}", commId, uid, commIdChain);
        if (uid == null) {
            throw new DcArgumentException("参数错误");
        }

        ObjectResponse<UserVo> userVoObjectResponse = antUserFeignClient.findInfoByUid(uid,
            topCommId, commIdChain);
        FeignResponseValidate.check(userVoObjectResponse);
        UserVo userVo = userVoObjectResponse.getData();

        //现金账户时为集团id，集团授信时为集团授信的账户id(没变)，商户会员时为所属商户id
        Long defaultBalanceId = userVo.getBalanceId();
        Integer defaultPayType = userVo.getDefaultPayType();

        //个人账户
        PointPoVo personAccount = null;
        if (userVo.getCommId() != null && userVo.getCommId() == commId) {
            // 仅集团商户显示平台基本账户
            personAccount = balanceService.getPersonalBalance(uid, userVo.getCommId());
            if (personAccount == null) {
                personAccount = new PointPoVo() {{
                    setId(0L);
                    setCommercialId(userVo.getCommId());
                    setAvailable(DecimalUtils.ZERO);
                    setPoint(DecimalUtils.ZERO);
                    setFrozen(DecimalUtils.ZERO);
                    setCost(DecimalUtils.ZERO);
                    setFrozenCost(DecimalUtils.ZERO);
                }};
            }
        }

        //集团授信账户
        ListResponse<Long> commIdListRes = commercialFeignClient.getSubCommIdList(commIdChain);
        FeignResponseValidate.check(commIdListRes);
        ListResponse<RBlocUserVo> blocUserVOList = antUserFeignClient.getAuthAccountByUserId(uid,
            commIdChain);

        if (blocUserVOList == null
            || blocUserVOList.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            throw new DcServiceException("查询用户下集团授信账户失败");
        }

        //商户会员
        ListResponse<PointPoVo> commercialAccountRes = dcUserService.getCommercialBalanceByUid(
            userVo.getCommId(), commIdChain, uid//, commIdList
        );
        if (commercialAccountRes == null
            || commercialAccountRes.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            throw new DcServiceException("查询用户下商户会员失败");
        }

        List<AccountInfoVo> accountInfoVoList = new ArrayList<>();

        if (personAccount != null) {
            AccountInfoVo accountInfoVoPersonal = new AccountInfoVo();

            accountInfoVoPersonal.setPayAccountId(personAccount.getCommercialId());
            accountInfoVoPersonal.setDefaultPayType(String.valueOf(OrderPayType.PERSON.getCode()));
            accountInfoVoPersonal.setAccountName("个人账户");
            accountInfoVoPersonal.setAvailableAmount(
                null == personAccount.getAvailable() ? BigDecimal.ZERO
                    : personAccount.getAvailable());//可用余额
            accountInfoVoPersonal.setAmount(
                null == personAccount.getPoint() ? BigDecimal.ZERO : personAccount.getPoint());//总余额
            accountInfoVoPersonal.setFrozenAmount(
                null == personAccount.getFrozen() ? BigDecimal.ZERO
                    : personAccount.getFrozen());//冻结金额
            accountInfoVoPersonal.setCostAmount(null == personAccount.getCost() ? BigDecimal.ZERO
                : personAccount.getCost().subtract(personAccount.getFrozenCost()));//实际成本(实际余额)
            accountInfoVoPersonal.setFreeAmount(accountInfoVoPersonal.getAvailableAmount()
                .subtract(accountInfoVoPersonal.getCostAmount()));//赠送余额 = 可用余额 - 实际成本（实际余额）
            accountInfoVoPersonal.setFrozenCostAmount(
                null == personAccount.getFrozenCost() ? BigDecimal.ZERO
                    : personAccount.getFrozenCost());//冻结实际金额
            accountInfoVoPersonal.setFrozenFreeAmount(accountInfoVoPersonal.getFrozenAmount()
                .subtract(accountInfoVoPersonal.getFrozenCostAmount()));//冻结赠送金额 = 冻结金额 - 冻结实际金额
            //accountInfoVoPersonal.setEnablePayable(true);
            accountInfoVoPersonal.setDefaultPay(false);
            accountInfoVoPersonal.setUserId(uid);
            accountInfoVoList.add(accountInfoVoPersonal);
        }

        if (!CollectionUtils.isEmpty(blocUserVOList.getData())) {
            for (RBlocUserVo rBlocUser : blocUserVOList.getData()) {
                AccountInfoVo accountInfoVo = new AccountInfoVo();
                accountInfoVo.setPayAccountId(rBlocUser.getId());
                accountInfoVo.setDefaultPayType(String.valueOf(OrderPayType.BLOC.getCode()));
                accountInfoVo.setCorpId(rBlocUser.getBlocUserId());
                accountInfoVo.setAccountName("企业账户" + "-" + rBlocUser.getBlocUserName());
                accountInfoVo.setBlocAccountBalance(
                    null == rBlocUser.getBlocAccountBalance() ? BigDecimal.ZERO
                        : rBlocUser.getBlocAccountBalance());
                accountInfoVo.setBlocFrozenAccount(
                    null == rBlocUser.getBlocFrozenAccount() ? BigDecimal.ZERO
                        : rBlocUser.getBlocFrozenAccount());
                accountInfoVo.setBlocAvailableAccount(
                    null == rBlocUser.getBlocAvailableAccount() ? BigDecimal.ZERO
                        : rBlocUser.getBlocAvailableAccount());

                accountInfoVo.setRblocLimitCycle(rBlocUser.getLimitCycle());
                accountInfoVo.setRblocLimitMoney(null == rBlocUser.getLimitMoney() ? BigDecimal.ZERO
                    : rBlocUser.getLimitMoney());
                accountInfoVo.setRblocBalance(
                    null == rBlocUser.getBalance() ? BigDecimal.ZERO : rBlocUser.getBalance());
                accountInfoVo.setRblocFrozenAmount(
                    null == rBlocUser.getFrozenAmount() ? BigDecimal.ZERO
                        : rBlocUser.getFrozenAmount());

                if (LimitCycle.UNLIMITED.equals(rBlocUser.getLimitCycle())) {

                    accountInfoVo.setAvailableAmount(accountInfoVo.getBlocAvailableAccount());
                } else {

                    BigDecimal availableAmountTemp = accountInfoVo.getRblocLimitMoney()
                        .subtract(accountInfoVo.getRblocBalance())
                        .subtract(accountInfoVo.getRblocFrozenAmount());
                    accountInfoVo.setAvailableAmount(
                        accountInfoVo.getBlocAvailableAccount().compareTo(availableAmountTemp) > 0
                            ? availableAmountTemp
                            : accountInfoVo.getBlocAvailableAccount());//取两者较小的
                }

                if (DecimalUtils.ltZero(accountInfoVo.getAvailableAmount())) {
                    accountInfoVo.setAvailableAmount(BigDecimal.ZERO);
                }

                //accountExVo.setEnablePayable(true);
                accountInfoVo.setDefaultPay(false);
                accountInfoVo.setUserId(uid);
                accountInfoVo.setVins(rBlocUser.getVins());
                accountInfoVo.setCards(rBlocUser.getCards());
                accountInfoVoList.add(accountInfoVo);
            }
        }

        if (!CollectionUtils.isEmpty(commercialAccountRes.getData())) {
            for (PointPoVo pointPoVo : commercialAccountRes.getData()) {
                AccountInfoVo accountInfoVo = new AccountInfoVo();
                accountInfoVo.setPayAccountId(pointPoVo.getCommercialId());
                accountInfoVo.setDefaultPayType(String.valueOf(OrderPayType.MERCHANT.getCode()));
                accountInfoVo.setAccountName("商户会员" + "-" + pointPoVo.getCommercialName());

                accountInfoVo.setAvailableAmount(null == pointPoVo.getAvailable() ? BigDecimal.ZERO
                    : pointPoVo.getAvailable());//可用余额
                accountInfoVo.setAmount(
                    null == pointPoVo.getPoint() ? BigDecimal.ZERO : pointPoVo.getPoint());//总余额
                accountInfoVo.setFrozenAmount(
                    null == pointPoVo.getFrozen() ? BigDecimal.ZERO : pointPoVo.getFrozen());//冻结金额
                accountInfoVo.setCostAmount(null == pointPoVo.getCost() ?
                    BigDecimal.ZERO : pointPoVo.getCost().subtract(
                    pointPoVo.getFrozenCost() == null ? BigDecimal.ZERO
                        : pointPoVo.getFrozenCost()));//实际成本(实际余额)
                accountInfoVo.setFreeAmount(accountInfoVo.getAvailableAmount()
                    .subtract(accountInfoVo.getCostAmount()));//赠送余额 = 可用余额 - 实际成本（实际余额）
                accountInfoVo.setFrozenCostAmount(
                    null == pointPoVo.getFrozenCost() ? BigDecimal.ZERO
                        : pointPoVo.getFrozenCost());//冻结实际金额
                accountInfoVo.setFrozenFreeAmount(accountInfoVo.getFrozenAmount()
                    .subtract(accountInfoVo.getFrozenCostAmount()));//冻结赠送金额 = 冻结金额 - 冻结实际金额

                //accountExVo.setEnablePayable(false);
                accountInfoVo.setEnable(pointPoVo.getEnable());//是否启用
                accountInfoVo.setDefaultPay(false);
                accountInfoVo.setUserId(uid);
                accountInfoVoList.add(accountInfoVo);
            }
        }

        //处理默认账户
        for (AccountInfoVo accountInfoVo : accountInfoVoList) {
            if (accountInfoVo.getDefaultPayType() != null
                && accountInfoVo.getDefaultPayType().equals(String.valueOf(defaultPayType))
                && accountInfoVo.getPayAccountId() != null
                && accountInfoVo.getPayAccountId().equals(defaultBalanceId)) {
                accountInfoVo.setDefaultPay(true);
            }
        }

        //处理商户会员是否可用

        ListResponse<AccountInfoVo> accountInfoVoListResponse = new ListResponse<AccountInfoVo>();
        accountInfoVoListResponse.setData(accountInfoVoList);

        return accountInfoVoListResponse;
    }


    /**
     * 修改默认扣款账户
     *
     * @param uid
     * @param accountId
     * @param defaultPayType
     * @return ObjectResponse
     */

    public ObjectResponse setDefaultAccount(Long uid, Long accountId, Integer defaultPayType) {
        ObjectResponse<User> issuccess = antUserFeignClient.setDefaultAccount(uid, accountId,
            defaultPayType);
        FeignResponseValidate.check(issuccess);
        return new ObjectResponse<>(true);
    }

    public SysUser getCurrentUser(ServerHttpRequest req) {
        return getUser(getToken(req));
    }

    public String getToken(ServerHttpRequest req) {
        String token = req.getHeaders().getFirst(Constant.CURRENT_USER_TOKEN);
        if (StringUtils.isBlank(token)) {
            token = req.getQueryParams().getFirst("token");
        }
        return token;
    }

    private SysUser getUser(String token) {
        IotAssert.isNotBlank(token, "无法获取token，请确认登陆状态。");
        String userJson = getUserJson(token);
        if (org.springframework.util.StringUtils.isEmpty(userJson)) {
            return null;
        }
        return JsonUtils.fromJson(userJson, SysUser.class);
//        return JSONObject.parseObject(userJson, SysUser.class);
    }

    private String getUserJson(String token) {
        return redisUtil.get(CheckAuthAround.TOKEN2USER_PREFIX, token);
    }

    @Transactional
    public void addUser(AddUserRequest addUserRequest, ServerHttpRequest request) {

        IotAssert.isNotBlank(addUserRequest.getMobile(), "手机号不能为空");
        IotAssert.isNotBlank(addUserRequest.getUsername(), "用户名不能为空");

        String token = getToken(request);
        IotAssert.isNotBlank(token, "token不能为空，请确认。");
        ObjectResponse<TCommercialUser> commUserRes = authCenterFeignClient.getCommercialsUserByToken(
            token);
        FeignResponseValidate.check(commUserRes);

        addUserRequest.setCommercialId(commUserRes.getData().getCommId());

        Long topCommId = AntRestUtils.getTopCommId(request);
        IotAssert.isNotNull(topCommId, "无法获取顶级商户Id，请重试。");

        UserPropVO userPropVO = findByPhone(addUserRequest.getMobile(), topCommId);
        if (userPropVO != null) {
            // 用户已存在，直接关联
            UserCommRef userCommRef = new UserCommRef();
            userCommRef.setUid(userPropVO.getUserId());

            // 为防止重复feign调用，赋值commid
            userCommRef.setCommId(addUserRequest.getCommercialId());

            FeignResponseValidate.check(addUserRef(userCommRef, request));
            return;
        }

        // 创建用户
        log.info("创建用户: {}", JsonUtils.toJsonString(addUserRequest));
        AddUserParam param = new AddUserParam(topCommId,
            addUserRequest.getCommercialId(),
            "86",
            addUserRequest.getMobile(),
            addUserRequest.getPassword(),
            addUserRequest.getUsername(),
            addUserRequest.getEmail(),
            addUserRequest.getAvatar());
        param.setSysUid(AntRestUtils.getSysUid(request))
            .setSysUserName(AntRestUtils.getSysUserName(request));
        ObjectResponse<Long> ret = antUserFeignClient.addUser(param);
        FeignResponseValidate.check(ret);

        // 因为顶级商户在创建用户的时候，会自动关联，所以自己不是顶级商户，创建用户成功后，增加关联关系
        if (!topCommId.equals(addUserRequest.getCommercialId())) {
            UserPropVO userVo = findByPhone(addUserRequest.getMobile(), topCommId);
            UserCommRef userCommRef = new UserCommRef();
            userCommRef.setUid(userVo.getUserId());

            // 为防止重复feign调用，赋值commid
            userCommRef.setCommId(addUserRequest.getCommercialId());

            BaseResponse refRet = addUserRef(userCommRef, request);
            FeignResponseValidate.check(refRet);
        }
    }

    public BaseResponse addUserRef(UserCommRef userCommRef, ServerHttpRequest request) {

        IotAssert.isNotNull(userCommRef, "请传入参数");
        IotAssert.isNotNull(userCommRef.getUid(), "请传入uid参数");

        String token = getToken(request);
        IotAssert.isNotBlank(token, "token不能为空，请确认。");

        if (userCommRef.getCommId() == null) {
            ObjectResponse<TCommercialUser> commUserRes = authCenterFeignClient.getCommercialsUserByToken(
                token);
            FeignResponseValidate.check(commUserRes);
            userCommRef.setCommId(commUserRes.getData().getCommId());
        }

        if (userCommRef.getOpId() == null &&
            userCommRef.getCreateTime() == null &&
            com.cdz360.base.utils.StringUtils.isBlank(userCommRef.getOpName())) {

            SysUser user = getCurrentUser(request);
            IotAssert.isNotNull(user, "无法获取用户信息，请确认是否登陆");

            userCommRef.setOpId(user.getId());
            userCommRef.setOpName(user.getName());
            userCommRef.setCreateTime(new Date());
        }

        return antUserFeignClient.addUserCommRef(userCommRef);
    }

    public Boolean findUserRef(Long uid, Long commId) {
        return antUserFeignClient.findUserCommRef(uid, commId).getData();
    }

    public BaseResponse setCouponAutoDeduct(Long userId, Boolean autoDeduct) {
        return antUserFeignClient.setCouponAutoDeduct(userId, autoDeduct);
    }


    /**
     * 根据邮箱创建用户
     * @param params
     * @param request
     */
    @Transactional
    public String addUserByEmail(AddUserRequest params, ServerHttpRequest request) {

        IotAssert.isNotBlank(params.getEmail(), "邮箱不能为空");
        IotAssert.isNotBlank(params.getUsername(), "用户名不能为空");
        Long commId = AntRestUtils.getCommId(request);
        Long topCommId = AntRestUtils.getTopCommId(request);
        IotAssert.isNotNull(topCommId, "无法获取顶级商户Id，请重试。");

        params.setCommercialId(commId);
        UserPropVO userPropVO = findByEmail(params.getEmail(), topCommId);
        if (userPropVO != null) {
            // 用户已存在，直接关联
            UserCommRef userCommRef = new UserCommRef();
            userCommRef.setUid(userPropVO.getUserId());

            // 为防止重复feign调用，赋值commid
            userCommRef.setCommId(commId);
            FeignResponseValidate.check(addUserRef(userCommRef, request));
            return "";
        }
        // 结合移动端生成密码
        String random = RandomStringUtils.random(8,  true, true).toUpperCase();
        UserRegisterParam userRegisterParam = new UserRegisterParam()
            .setEmail(params.getEmail())
            .setUsername(params.getUsername())
            .setTopCommId(topCommId)
            .setCommId(commId)
            .setAppClientType(AppClientType.MGM_WEB)
            .setIsFromPlatform(Boolean.TRUE)
            .setPassword(random)
            .setRepeatPassWord(random);

        ObjectResponse<UserAndBalanceAndTokenVo> ret = antUserFeignClient.registerByEmail(userRegisterParam);
        FeignResponseValidate.check(ret);

        return  random;
    }
}
