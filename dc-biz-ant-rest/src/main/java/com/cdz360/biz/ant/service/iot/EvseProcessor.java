package com.cdz360.biz.ant.service.iot;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.iot.param.ModifyEvseInfoParam;
import com.cdz360.biz.model.trading.iot.po.BsBoxPo;
import com.chargerlinkcar.framework.common.feign.DeviceDataCoreFeignClient;
import com.chargerlinkcar.framework.common.feign.SiteDataCoreFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class EvseProcessor {

    @Autowired
    private SiteDataCoreFeignClient siteDataCoreFeignClient;
    @Autowired
    private DeviceDataCoreFeignClient deviceDataCoreFeignClient;

    @Async
    public void recordEvsePlugInfo(List<String> siteIdList) {
        log.info("siteIdList = {}", JsonUtils.toJsonString(siteIdList));
        siteIdList.forEach(e -> {
            siteDataCoreFeignClient.recordEvsePlugInfo(e, null);
        });
    }

    @Async
    public void recordEvsePlugInfo(String siteId, List<String> evseNoList) {
        log.info("siteId = {}, evseNoList = {}", siteId, JsonUtils.toJsonString(evseNoList));
        evseNoList.forEach(evseNo -> {
            siteDataCoreFeignClient.recordEvsePlugInfo(siteId, evseNo);
        });
    }

    @Async
    public void batchUpdateBsBox(ModifyEvseInfoParam param) {
        log.info("batchUpdateBsBox = {}", param);
        param.getEvseNoList().forEach(evseNo -> {
            boolean changed = false;
            BsBoxPo box = new BsBoxPo();
            box.setEvseNo(evseNo);
            if (param.getUseSiteSetting() != null) {
                changed = true;
                box.setUseSiteSetting(param.getUseSiteSetting());
            }
            if (param.getPower() != null) {
                changed = true;
                box.setPower(param.getPower());
            }
            if (changed) {
                deviceDataCoreFeignClient.updateBsBox(box);
            }
        });
    }
}
