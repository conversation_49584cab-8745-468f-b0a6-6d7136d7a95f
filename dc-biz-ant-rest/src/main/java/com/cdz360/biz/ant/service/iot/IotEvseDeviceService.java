package com.cdz360.biz.ant.service.iot;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.iot.param.AddEvseParam;
import com.cdz360.biz.model.iot.param.DeviceParam;
import com.cdz360.biz.model.iot.param.EditEvseParam;
import com.cdz360.biz.model.iot.param.FindDeviceParam;
import com.cdz360.biz.model.iot.vo.DeviceVo;
import com.cdz360.biz.model.iot.vo.EvseDeviceVo;
import com.chargerlinkcar.framework.common.feign.IotBizClient;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class IotEvseDeviceService {

    @Autowired
    private IotBizClient iotBizClient;
    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;

    public Mono<ListResponse<DeviceVo>> getByEvseNo(String evseNo, String deviceName, Boolean siteAllEvse, Long start, Long size) {
        return Mono.just(iotDeviceMgmFeignClient.getByEvseNo(evseNo, deviceName, siteAllEvse, start, size));
    }

    public Mono<ObjectResponse<EvseDeviceVo>> getByDeviceNo(FindDeviceParam param) {
        return Mono.just(iotDeviceMgmFeignClient.getByDeviceNo(param));
    }

    public Mono<BaseResponse> addEvse(AddEvseParam param) {
        return Mono.just(iotBizClient.addEvse(param));
    }

    public Mono<BaseResponse> editEvse(EditEvseParam param) {
        return Mono.just(iotBizClient.editEvse(param));
    }

    public Mono<BaseResponse> addDevice(DeviceParam param) {
        return Mono.just(iotDeviceMgmFeignClient.addDevice(param));
    }

    public Mono<BaseResponse> editDevice(DeviceParam param) {
        return Mono.just(iotDeviceMgmFeignClient.editDevice(param));
    }
}
