package com.cdz360.biz.ant.rest.oa;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.feign.reactor.OaFeignClient;
import com.cdz360.biz.ant.service.oa.OaService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.oa.vo.OaRechargeInfoVo;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/oa/recharge")
public class OaRechargeRest {

    @Autowired
    private OaService oaService;
    @Autowired
    private OaFeignClient oaFeignClient;

    @Operation(summary = "获取充值申请流程信息")
    @GetMapping(value = "/processDetail")
    public Mono<ObjectResponse<OaRechargeInfoVo>> processDetail(
            ServerHttpRequest request,
            @ApiParam("流程实例ID") @RequestParam String procInstId) {
        log.info("获取充值申请流程信息. {}", LoggerHelper2.formatEnterLog(request));
        IotAssert.isNotBlank(procInstId, "流程实例ID无效");
        final Long sysUid = AntRestUtils.getSysUid(request);
        return this.oaService.processDetail(procInstId, sysUid)
                .map(RestUtils::buildObjectResponse);
    }

}
