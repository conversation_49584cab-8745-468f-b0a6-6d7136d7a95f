package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.service.AdsService;
import com.cdz360.biz.ant.service.sysLog.CommercialSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.ads.param.CreateAdsParam;
import com.cdz360.biz.model.ads.param.ListAdsParam;
import com.cdz360.biz.model.ads.param.UpdateAdsParam;
import com.cdz360.biz.model.ads.vo.AdsVo;
import com.chargerlinkcar.framework.common.rest.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2025/2/11
 */
@Slf4j
@RestController
@RequestMapping("/api/ads")
@Tag(name = "广告相关接口", description = "广告")
public class AdsRest extends BaseController {

    @Autowired
    private AdsService adsService;

    @Autowired
    private CommercialSysLogService commLogService;


    @Operation(summary = "创建广告")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public BaseResponse create(ServerHttpRequest request, @RequestBody CreateAdsParam req) {
        log.info("创建广告: {}", JsonUtils.toJsonString(req));
        CreateAdsParam.check(req);

        Long topCommId = AntRestUtils.getTopCommId(request);
        if (topCommId == null || topCommId.longValue() < 1L) {
            log.error("参数错误,缺少参数topCommId");
            throw new DcArgumentException("参数错误,缺少参数topCommId");
        }
        req.setTopCommId(topCommId);
        Long commId = AntRestUtils.getCommId(request);
        if (commId == null || commId.longValue() < 1L) {
            log.error("参数错误,缺少参数commId");
            throw new DcArgumentException("参数错误,缺少参数commId");
        }
        req.setCommId(commId);
        var res = adsService.create(req);
        commLogService.adsCreateLog(req.getName(), request);
        return res;
    }

    @Operation(summary = "广告列表")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public ListResponse<AdsVo> list(ServerHttpRequest request,
        @RequestBody ListAdsParam req) {

        Long topCommId = AntRestUtils.getTopCommId(request);
        if (topCommId == null || topCommId.longValue() < 1L) {
            log.error("参数错误,缺少参数topCommId");
            throw new DcArgumentException("参数错误,缺少参数topCommId");
        }
        req.setTopCommId(topCommId);

        log.info("广告列表: {}", JsonUtils.toJsonString(req));
        return adsService.list(req);
    }

    @Operation(summary = "更新广告")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public BaseResponse update(ServerHttpRequest request, @RequestBody UpdateAdsParam req) {
        log.info("更新广告: {}", JsonUtils.toJsonString(req));
        UpdateAdsParam.check(req);
        Long topCommId = AntRestUtils.getTopCommId(request);
        if (topCommId == null || topCommId.longValue() < 1L) {
            log.error("参数错误,缺少参数topCommId");
            throw new DcArgumentException("参数错误,缺少参数topCommId");
        }
        req.setTopCommId(topCommId);
        Long commId = AntRestUtils.getCommId(request);
        if (commId == null || commId.longValue() < 1L) {
            log.error("参数错误,缺少参数commId");
            throw new DcArgumentException("参数错误,缺少参数commId");
        }
        req.setCommId(commId);
        var res = adsService.update(req);
        commLogService.adsUpdateLog(req.getName(), request);
        return res;
    }

    @Operation(summary = "启用广告")
    @RequestMapping(value = "/active", method = RequestMethod.GET)
    public BaseResponse active(ServerHttpRequest request,
        @RequestParam("id") Long id,
        @RequestParam("name") String name) {
        log.info("启用广告: {}", id);
        var res = adsService.activeAds(id);
        commLogService.adsSwitchoverLog(name, request);
        return res;
    }

    @Operation(summary = "停用广告")
    @RequestMapping(value = "/abort", method = RequestMethod.GET)
    public BaseResponse abort(ServerHttpRequest request,
        @RequestParam("id") Long id,
        @RequestParam("name") String name) {
        log.info("停用广告: {}", id);
        var res = adsService.abortAds(id);
        commLogService.adsSwitchoverLog(name, request);
        return res;
    }

    @Operation(summary = "查询广告详情")
    @RequestMapping(value = "/getDetail", method = RequestMethod.GET)
    public ObjectResponse<AdsVo> getDetail(ServerHttpRequest request,
        @RequestParam("id") Long id) {
        log.info("查询广告详情: {}", id);
        return adsService.getAdsDetail(id);
    }
}
