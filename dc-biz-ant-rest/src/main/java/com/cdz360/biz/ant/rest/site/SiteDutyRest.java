package com.cdz360.biz.ant.rest.site;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.service.site.SiteDutyService;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 站点值班表
 * <p>
 * SiteDutyRest
 *
 * <AUTHOR>
 * @since 2019.2.16
 */
@Slf4j
@RestController
@RequestMapping("/api/siteDuty")
public class SiteDutyRest extends BaseController {


    @Autowired
    private SiteDutyService siteDutyService;

    /**
     * 查询站点值班人数
     */
    @RequestMapping("/getDutyCount")
    @ResponseBody
    public ObjectResponse<Integer> getDutyCount(@RequestParam("siteId") String siteId) {
        log.info("查询站点值班人数{}", siteId);
        return siteDutyService.getDutyCount(siteId);
    }

    /**
     * 查询站点值班表
     */
    @RequestMapping("/getSiteDuty")
    @ResponseBody
    public ObjectResponse<JsonNode> getSiteDuty(@RequestParam("siteId") String siteId) {
        log.info("查询站点值班表{}", siteId);
        return siteDutyService.getSiteDuty(siteId);
    }

    /**
     * 保存站点值班表
     *
     * @param siteId   站点ID
     * @param siteDuty json格式值班列表
     */
    @RequestMapping("/saveSiteDuty")
    @ResponseBody
    public BaseResponse saveSiteDuty(@RequestParam("siteId") String siteId,
        @RequestParam("siteDuty") String siteDuty) {
        log.info("保存站点值班表{}==={}", siteId, siteDuty);
        return siteDutyService.saveSiteDuty(siteId, siteDuty);
    }
}
