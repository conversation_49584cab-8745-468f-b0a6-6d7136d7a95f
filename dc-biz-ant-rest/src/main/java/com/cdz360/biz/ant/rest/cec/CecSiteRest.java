package com.cdz360.biz.ant.rest.cec;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.service.cec.CecSiteService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.iot.vo.ChargeV2;
import com.cdz360.biz.model.trading.site.param.ListCecSiteParam;
import com.cdz360.biz.model.trading.site.vo.PartnerSiteVo;
import com.cdz360.biz.model.trading.site.vo.SiteOrderStatsVo;
import com.chargerlinkcar.framework.common.domain.PartnerSiteDetailInfoVo;
import com.chargerlinkcar.framework.common.domain.PlugStatusCountDto;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "CEC 互联互通场站相关接口", description = "CEC 互联互通场站相关接口")
@Slf4j
@RestController
public class CecSiteRest {

    @Autowired
    private CecSiteService cecSiteService;

    // /api/site/getPagedSiteSimpleList
    @Operation(summary = "互联站点列表")
    @PostMapping(value = "/api/cec/siteList")
    public Mono<ListResponse<PartnerSiteVo>> cecSiteList(
            ServerHttpRequest request, @RequestBody ListCecSiteParam param) {
        log.info("互联站点列表: {}, param = {}",
                LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        String commIdChain = AntRestUtils.getCommIdChain(request);
        param.setCommIdChain(commIdChain);
        return cecSiteService.cecSiteList(param);
    }

    // /api/site/getSiteDetail
    @Schema(description = "互联站点详情")
    @GetMapping(value = "/api/cec/siteDetail")
    public Mono<ObjectResponse<PartnerSiteDetailInfoVo>> cecSiteDetail(
            ServerHttpRequest request,
            @Parameter(name = "场站ID(鼎充)", required = true) @RequestParam(value = "siteId") String siteId) {
        log.info("互联站点详情: {}", LoggerHelper2.formatEnterLog(request));
        return cecSiteService.cecSiteDetail(siteId);
    }

    // /api/site/getChargerStatusStatisticsBySiteId
    @Operation(summary = "互联站点枪头状态统计")
    @GetMapping(value = "/api/cec/sitePlugStatusStats")
    public Mono<ObjectResponse<PlugStatusCountDto>> cecSitePlugStatusStats(
            ServerHttpRequest request,
            @Parameter(name = "合作方编号", required = true) @RequestParam(value = "partnerCode") String partnerCode,
            @Parameter(name = "场站ID(鼎充)", required = true) @RequestParam(value = "siteId") String siteId) {
        log.info("互联站点枪头状态统计: {}", LoggerHelper2.formatEnterLog(request));
        Long topCommId = AntRestUtils.getTopCommId(request);
        return cecSiteService.cecSitePlugStatusStats(topCommId, partnerCode, siteId);
    }


    // /api/dataReport/getSiteSurvey
    @Operation(summary = "互联站点充电订单数据统计", description = "昨天、近7天、近30天的充电次数、电量、金额")
    @GetMapping(value = "/api/cec/siteOrderStats")
    public Mono<ListResponse<SiteOrderStatsVo>> cecSiteOrderStats(
            ServerHttpRequest request,
            @Parameter(name = "场站ID(鼎充)", required = true) @RequestParam(value = "siteId") String siteId) {
        log.info("互联站点充电订单数据统计: {}", LoggerHelper2.formatEnterLog(request));
        return cecSiteService.cecSiteOrderStats(siteId);
    }

    // /api/charger/getPagedChargerInfoList
    @Operation(summary = "场站枪头列表获取")
    @GetMapping(value = "/api/cec/plugList")
    public Mono<ListResponse<PlugVo>> cecPlugList(
            ServerHttpRequest request,
            @Parameter(name = "合作方编号", required = true) @RequestParam(value = "partnerCode") String partnerCode,
            @Parameter(name = "场站ID(鼎充)", required = true) @RequestParam(value = "siteId") String siteId) {
        log.info("互联站点枪头状态统计: {}", LoggerHelper2.formatEnterLog(request));
        Long topCommId = AntRestUtils.getTopCommId(request);
        return cecSiteService.cecPlugList(topCommId, partnerCode, siteId);
    }

    @Operation(summary = "枪头计费信息")
    @GetMapping(value = "/api/cec/plugPriceInfo")
    public Mono<ListResponse<ChargeV2>> cecPlugPriceInfo(
            ServerHttpRequest request,
            @Parameter(name = "合作方编号", required = true) @RequestParam(value = "partnerCode") String partnerCode,
            @Parameter(name = "枪头编号", required = true) @RequestParam(value = "plugNo") String plugNo) {
        log.info("互联站点枪头计费信息: {}", LoggerHelper2.formatEnterLog(request));
        Long topCommId = AntRestUtils.getTopCommId(request);
        return cecSiteService.cecPlugPriceInfo(topCommId, partnerCode, plugNo);
    }
}
