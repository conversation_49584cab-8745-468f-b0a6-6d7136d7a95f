package com.cdz360.biz.ant.service.order;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.BizBiFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorBizBiFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorUserFeignClient;
import com.cdz360.biz.model.bi.site.SiteUtilization;
import com.cdz360.biz.model.iot.type.SiteBiSampleType;
import com.cdz360.biz.model.trading.bi.dto.ChargerOrderBiDto;
import com.cdz360.biz.model.trading.bi.param.ListBiCommercialParam;
import com.cdz360.biz.model.trading.bi.param.ListBiSiteParam;
import com.cdz360.biz.model.trading.bi.param.ListChargeOrderBiByVinParam;
import com.cdz360.biz.model.trading.bi.vo.CommercialBiVo;
import com.cdz360.biz.model.trading.bi.vo.SiteBiVo;
import com.cdz360.biz.model.trading.bi.vo.VinBiVo;
import com.cdz360.biz.model.trading.cus.vo.CusOrderBiVo;
import com.cdz360.biz.model.trading.iot.vo.EvseStatusPowerBiVo;
import com.cdz360.biz.model.trading.iot.vo.PlugStatusBiVo;
import com.cdz360.biz.model.trading.order.dto.GeoOrderBiDto;
import com.cdz360.biz.model.trading.order.dto.OrderStartTypeBiDto;
import com.cdz360.biz.model.trading.order.vo.ChargeOrderBiVo;
import com.cdz360.biz.model.trading.order.vo.OpSummaryBiVo;
import com.cdz360.biz.model.trading.site.vo.SiteOrderAccountData;
import com.cdz360.biz.model.trading.site.vo.SiteOrderBiVo;
import com.cdz360.biz.utils.feign.his.HisOrderBiFeignClient;
import com.chargerlinkcar.framework.common.feign.ChargeOrderBiFeignClient;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.HistoryDataUtils;
import java.time.Duration;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class OrderBiService {

    @Autowired
    private BizBiFeignClient bizBiFeignClient;

    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;
//    @Autowired
//    private AntUserFeignClient userFeignClient;

    @Autowired
    private ReactorBizBiFeignClient reactorBizBiFeignClient;

    @Autowired
    private ReactorUserFeignClient reactorUserFeignClient;

    @Autowired
    private ChargeOrderBiFeignClient chargeOrderBiFeignClient;

    @Autowired
    private HisOrderBiFeignClient hisOrderBiFeignClient;

    @Autowired
    private HistoryDataUtils historyDataUtils;

    public Mono<OpSummaryBiVo> getOrderBiOfCommChain(
        String commIdChain, final String siteId, final OpSummaryBiVo result,
        final List<Integer> bizTypeList) {
        return Mono.zip(reactorBizBiFeignClient.getOrderBi(commIdChain, siteId, bizTypeList)
                    .doOnNext(FeignResponseValidate::check)
                    .map(ObjectResponse::getData),
                StringUtils.isNotBlank(siteId) ? Mono.just(0) :
                    reactorUserFeignClient.getUserCount(commIdChain, null)
                        .doOnNext(FeignResponseValidate::check)
                        .map(ObjectResponse::getData))
            .map(tuple -> {
                ChargeOrderBiVo bi = tuple.getT1();
                long uCnt = tuple.getT2().longValue();
                result.setOrderNum(bi.getOrderNum())
                    .setElec(bi.getElec())
                    .setFee(bi.getFee())
                    .setServFee(bi.getServFee())
                    .setUserCount(uCnt)
                    .setTodayOrderNum(bi.getTodayOrderNum())
                    .setTodayElec(bi.getTodayElec())
                    .setTodayFee(bi.getTodayFee())
                    .setTodayServFee(bi.getTodayServFee());
                return result;
            });
    }

//    public ObjectResponse<ChargeOrderBiVo> getOrderBi(String commIdChain, String siteId) {
//        var res = this.bizBiFeignClient.getOrderBi(commIdChain, siteId);
//        FeignResponseValidate.check(res);
//        return res;
//    }

//    public Integer getUserCount(String commIdChain) {
//        var res = this.userFeignClient.getUserCount(commIdChain, null);
//        FeignResponseValidate.check(res);
//        return res.getData();
//    }


    /**
     * 获取过去7天的充电启动方式统计
     *
     * @param commIdChain
     * @return
     */
    public ListResponse<OrderStartTypeBiDto> getOrderStartTypeBiList7(
        @Nullable String commIdChain) {
        var res = this.bizBiFeignClient.getOrderStartTypeBiList7(null, commIdChain);
        FeignResponseValidate.check(res);
        return res;
    }

    /**
     * 按时间分组统计
     *
     * @param timeType
     * @param commIdChain
     * @return
     */
    public ListResponse<ChargeOrderBiVo> getTimeGroupingOrderBiList(SiteBiSampleType timeType,
        String siteId, @Nullable String commIdChain) {
        ListResponse<SiteOrderBiVo> res = this.bizBiFeignClient.getTimeGroupingOrderBiList(timeType,
            siteId, commIdChain);
        FeignResponseValidate.check(res);
        List<ChargeOrderBiVo> list = res.getData().stream()
            .map(bi -> toChargeOrderBiVo(timeType, bi))
            .collect(Collectors.toList());
        return RestUtils.buildListResponse(list);
    }

    private ChargeOrderBiVo toChargeOrderBiVo(SiteBiSampleType timeType, SiteOrderBiVo bi) {
        ChargeOrderBiVo vo = new ChargeOrderBiVo();
        vo.setAxis(bi.getTime())
            .setOrderNum(bi.getOrderNum())
            .setElec(bi.getElec())
            .setFee(bi.getServFee().add(bi.getElecFee()));
        return vo;
    }


    /**
     * 获取过去14天的充电桩使用率数据
     *
     * @param commIdChain
     * @return
     */
    public ListResponse<SiteUtilization> getDateGroupingPlugUsageBiList(String commIdChain) {
        var res = this.bizBiFeignClient.getDateGroupingPlugUsageBiList(14, commIdChain);
        FeignResponseValidate.check(res);
        return res;
    }


    /**
     * 按客户类型统计7/30日充电订单
     *
     * @param commIdChain
     * @return
     */
    public ObjectResponse<CusOrderBiVo> getAccTypeGroupingFeeBiList(String commIdChain) {
        var res = this.bizBiFeignClient.getAccTypeGroupingFeeBiList(commIdChain);
        FeignResponseValidate.check(res);
        return res;
    }


    /**
     * 统计城市的今天,本周,本月,今年充电数据
     *
     * @param cityCode
     * @param commIdChain
     * @return
     */
    public ObjectResponse<GeoOrderBiDto> getGeoOrderBi(String provinceCode, String cityCode,
        String siteId,
        String commIdChain) {
        ObjectResponse<GeoOrderBiDto> cityRes = this.bizBiFeignClient.getGeoOrderBi(provinceCode,
            cityCode, siteId, commIdChain);
        FeignResponseValidate.check(cityRes);

        // 场站功率统计
        ListResponse<EvseStatusPowerBiVo> evsePowerRes = this.iotDeviceMgmFeignClient.getEvseStatusPowerBi(
            provinceCode,
            cityCode, siteId, commIdChain);
        log.debug("evsePowerRes = {}", evsePowerRes);
        FeignResponseValidate.check(evsePowerRes);
        Long totalPower = 0L;
        Long busyPower = 0L;
        for (EvseStatusPowerBiVo p : evsePowerRes.getData()) {
            if (p.getEvseStatus() == EvseStatus.BUSY) {
                busyPower += p.getPower();
            }
            totalPower += p.getPower();
        }
        ;
        cityRes.getData().setTotalPower(totalPower)
            .setBusyPower(busyPower);

        ListResponse<PlugStatusBiVo> evseStatusRes = this.iotDeviceMgmFeignClient.getPlugStatusBi(
            provinceCode,
            cityCode, siteId, commIdChain);
        log.debug("evseStatusRes = {}", evseStatusRes);
        FeignResponseValidate.check(evseStatusRes);
        Long onlineEvseNum = 0L;
        Long offlineEvseNum = 0L;
        Long errorEvseNum = 0L;
        for (PlugStatusBiVo vo : evseStatusRes.getData()) {
            if (EvseStatus.OFFLINE == vo.getEvseStatus()) {
                offlineEvseNum += vo.getEvseNum();
            } else if (EvseStatus.ERROR == vo.getEvseStatus()) {
                errorEvseNum += vo.getEvseNum();
            } else if (EvseStatus.OFF == vo.getEvseStatus()) {
                // ignore
            } else if (vo.getEvseStatus() != null) {
                onlineEvseNum += vo.getEvseNum();
            }
        }
        cityRes.getData().setOnlineEvseNum(onlineEvseNum)
            .setErrorEvseNum(errorEvseNum)
            .setOfflineEvseNum(offlineEvseNum);
        return cityRes;
    }

    public Mono<ListResponse<SiteOrderAccountData>> getOrderAccountBi(Integer days, String siteId) {
        return Mono.just(bizBiFeignClient.getOrderAccountBi(days, siteId));
    }

    public ListResponse<CommercialBiVo> getCommBi(@RequestBody ListBiCommercialParam param) {
        ListResponse<CommercialBiVo> res = null;
        if (historyDataUtils.checkListBiCommercialParam(param, true)) {
            res = this.chargeOrderBiFeignClient.getCommBi(param);   // 近期订单数据
        } else {
            res = hisOrderBiFeignClient.getCommBi(param)        // 历史订单数据
                .block(Duration.ofSeconds(50L));
        }
        return res;
    }


    public ListResponse<SiteBiVo> getSiteBi(ListBiSiteParam param) {
        ListResponse<SiteBiVo> res = null;
        if (historyDataUtils.checkListBiCommercialParam(param, true)) {
            res = this.chargeOrderBiFeignClient.getSiteBi(param);    // 近期订单数据
        } else {
            res = hisOrderBiFeignClient.getSiteBi(param)      // 历史订单数据
                .block(Duration.ofSeconds(50L));
        }
        return res;
    }


    public ListResponse<VinBiVo> getVinBi(ListChargeOrderBiByVinParam param) {
        ListResponse<VinBiVo> res = null;
        if (historyDataUtils.checkListBiCommercialParam(param, true)) {
            res = this.chargeOrderBiFeignClient.getVinBi(param);    // 近期订单数据
        } else {
            res = hisOrderBiFeignClient.getVinBi(param)      // 历史订单数据
                .block(Duration.ofSeconds(50L));
        }
        return res;
    }

    /**
     * 在线卡充电数据统计
     */
    public ListResponse<ChargerOrderBiDto> getBiListByOnlineCard(
        ListChargeOrderBiByVinParam param) {
        ListResponse<ChargerOrderBiDto> res = null;
        if (historyDataUtils.checkListBiCommercialParam(param, false)) {
            res = this.chargeOrderBiFeignClient.getBiListByOnlineCard(param);    // 近期订单数据
        }
        return res;
    }

    /**
     * 紧急充电卡充电数据统计
     */
    public ListResponse<ChargerOrderBiDto> getBiListByEmergencyCard(
        ListChargeOrderBiByVinParam param) {
        ListResponse<ChargerOrderBiDto> res = null;
        if (historyDataUtils.checkListBiCommercialParam(param, false)) {
//            res = this.chargeOrderBiFeignClient.getBiListByOnlineCard(param);    // 近期订单数据
            res = chargeOrderBiFeignClient.getBiListByEmergencyCard(param);
        }
        return res;
    }

}
