package com.cdz360.biz.ant.domain;

import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.biz.model.finance.type.TaxType;
import com.cdz360.biz.model.trading.order.type.ChargerOrderRefundStatus;
import com.chargerlinkcar.framework.common.domain.type.ProcessType;
import com.chargerlinkcar.framework.common.domain.vo.BalanceInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

// import com.chargerlinkcar.framework.common.domain.type.OrderStartType;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ChargerOrderWithBLOBs extends ChargerOrder implements Serializable {

    /**
     * 枪头编号
     */
    private String bcCode;
    /**
     * 站点编号
     */
    private String siteNo;


    /**
     * 充电计费方式
     */
    private Integer calculateType;

    /**
     * 开启方式
     */
    // private OrderStartType startType;
    private OrderStartType startType;

    /**
     * 订单详情页面的结算信息
     */
    private BalanceInfo balanceInfo;

    /**
     * 所属集团客户
     */
    private String blocUserName;
    /**
     * 退款状态
     */
    private ChargerOrderRefundStatus refundStatus;
    /**
     * t_charger_order
     */
    private static final long serialVersionUID = 1L;

    //结算类型
    @Schema(description = "结算类型: UNKNOWN(0)-未知,BALANCE(1)-账户余额扣减(先付费),GUARANTEE(2)-担保消费结算," +
            "POSTPAID(3)-预消费结算(后付费),PARTNER(4)-外部平台结算", format = "java.lang.Integer")
    private SettlementType settlementType;

//    @Schema(description = "本金(实际收入): 单位,元")
//    private BigDecimal principalAmount;
//
//    @Schema(description = "赠送金: 单位,元")
//    private BigDecimal freeGoldAmount;

    @Schema(description = "发票类型(税种): UNKNOWN(0)-未知," +
            "NORMAL_TAX(2)-个人普通发票,PREPAY_TAX(3)-企业普通发票," +
            "SPECIAL_VAT(5)-企业专业发票", example = "UNKNOWN")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TaxType taxType;

    @Schema(description = "税票号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String taxNo;

    @Schema(description = "订单处理类型: NORMAL(0)正常处理,ABNORMAL_UNDO(1)异常手工未处理,ABNORMAL_DONE(2)异常手工处理", example = "0")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ProcessType processType;

    @Schema(description = "已开票金额: 单位，元")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal alreadyInvoiceAmount;

    @Schema(description = "未开票金额: 单位，元")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal noInvoiceAmount;
    @Schema(description = "所属组织")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String corpOrgName;
    @Schema(description = "开始电表读数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal startElectricity ;
    @Schema(description = "结束电表读数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal endElectricity ;

    @Schema(description = "充电桩电流形式: AC -- 交流; DC -- 直流; BOTH -- 交直流一体")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private SupplyType evseSupply;

    @Schema(description = "即充即退充电关联的充值订单号(系统) 即充即退充电订单才有")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String payBillNo;

    @Schema(description = "即充即退充电关联的充值订单号(第三方系统) 即充即退充电订单才有")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String payBillTradeNo;

    @Schema(description = "即充即退充电关联的退款订单号(第三方系统) 即充即退充电订单才有")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String refundTradeNo;

    @Schema(description = "优惠券号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long couponId;

    @Schema(description = "优惠券抵扣金额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal couponAmount;

    @Schema(description = "停充超时费, 单位: 元")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal parkingFee;

    @Schema(description = "DC+绝缘检测值. 单位1Ω/V", example = "123")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer insulationPositive;

    @Schema(description = "DC-绝缘检测值. 单位1Ω/V", example = "123")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer insulationNegative;

    @Schema(description = "电费收益")
    private BigDecimal elecProfit;

    @Schema(description = "服务费收益")
    private BigDecimal servProfit;

    @Schema(description = "总收益")
    private BigDecimal totalProfit;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "合充的辅枪枪号")
    private Integer secondPlugIdx;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "辅枪充电前电表读数")
    private BigDecimal secondStartMeter;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "辅枪充电后电表读数")
    private BigDecimal secondStopMeter;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "辅枪订单总电量")
    private BigDecimal secondKwh;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "辅枪头名称")
    private String secondChargerName;

    @Schema(description = "停车减免时间（分钟）")
    private Integer parkCouponDuration;

    @Schema(description = "减免金额（元）")
    private BigDecimal parkReduceAmount;

    @Schema(description = "停车费（元）")
    private BigDecimal parkTotalAmount;

    @Schema(description = "停充码 3.7支持")
    private String stopChargeCode;

}