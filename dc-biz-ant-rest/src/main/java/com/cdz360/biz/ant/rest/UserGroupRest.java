package com.cdz360.biz.ant.rest;


import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.ant.domain.vo.GroupUserListVo;
import com.cdz360.biz.ant.domain.vo.UserGroupVo;
import com.cdz360.biz.ant.domain.vo.UserSampleVo;
import com.cdz360.biz.ant.service.UserGroupService;
import com.cdz360.biz.ant.service.UserService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.rest.BaseController;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;


/**
 * 用户相关
 *
 * <AUTHOR>
 * @since 2018-09-27 9:50
 **/
@Slf4j
@RestController
@RequestMapping("/api/userGroup")
public class UserGroupRest extends BaseController {

    @Autowired
    private UserGroupService userGroupService;

    @Autowired
    private UserService userService;


    /**
     * 根据 客户分组名称 查询 客户分组列表
     *
     * @param groupName 分组名称
     * @return
     */
    @GetMapping(value = "/queryUserGroupList")
    ListResponse<UserGroupVo> queryUserGroupList(
        ServerWebExchange exh,
        @RequestParam(value = "groupName", required = false) String groupName,
        ServerHttpRequest request) {
        List<Long> commIdList = this.getCommIdList2(request);
        if (org.springframework.util.CollectionUtils.isEmpty(commIdList)) {
            throw new DcArgumentException("参数错误");
        }
        OldPageParam page = getPage2(request, exh, false);

        return userGroupService.queryUserGroupList(commIdList, page, groupName);

    }


    /**
     * 根据 客户分组Id 查询 客户分组详情
     *
     * @param userGroupId 客户分组Id
     * @return
     */
    @GetMapping(value = "/queryUserGroupById")
    public ObjectResponse<GroupUserListVo> queryUserGroupById(
        @RequestParam(value = "userGroupId") Long userGroupId, ServerHttpRequest request) {

        List<Long> commIdList = this.getCommIdList2(request);
        if (org.springframework.util.CollectionUtils.isEmpty(commIdList)) {
            throw new DcArgumentException("参数错误");
        }

        if (CollectionUtils.isEmpty(commIdList)) {
            throw new DcServiceException("获取当前商户及子商户信息失败");
        }

        return userGroupService.queryUserGroupById(commIdList, userGroupId);

    }


    /**
     * 获取未分组的客户信息
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryNegationUsers", method = RequestMethod.GET)
    public ListResponse<UserSampleVo> queryNegationUsers(ServerHttpRequest request) {

        List<Long> commIdList = this.getCommIdList2(request);
        if (CollectionUtils.isEmpty(commIdList)) {
            throw new DcArgumentException("参数错误");
        }

        ListResponse<UserSampleVo> ObjectResponse = userService.queryNegationUsers(commIdList);

        return ObjectResponse;
    }


    /**
     * 修改客户分组
     *
     * @param groupId         分组id
     * @param groupName       分组名称
     * @param groupDescrition 分组描述
     * @param userIds         客户列表
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/updateUserGroup", method = RequestMethod.POST)
    public ObjectResponse updateUserGroup(@RequestParam(value = "groupId") Long groupId,
        String groupName, String groupDescrition,
        @RequestParam(value = "userIds", required = false) List<Long> userIds,
        ServerHttpRequest request) {
        Long commId = AntRestUtils.getCommId(request);    // this.getCommId(request);
        if (commId == null || commId < 1L) {
            throw new DcArgumentException("参数错误");
        }

        return userGroupService.updateUserGroup(commId, groupId, groupName, groupDescrition,
            userIds);
    }


    /**
     * 添加客户分组
     *
     * @param groupName       分组名称
     * @param groupDescrition 分组描述
     * @param userIds         客户列表
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/addUserGroup", method = RequestMethod.POST)
    public BaseResponse addUserGroup(String groupName, String groupDescrition,
        @RequestParam(value = "userIds", required = false) List<Long> userIds,
        ServerHttpRequest request) {

        Long commId = AntRestUtils.getCommId(request);    // this.getCommId(request);
        if (commId == null || commId < 1L) {
            throw new DcArgumentException("参数错误");
        }

        return userGroupService.addUserGroup(commId, groupName, groupDescrition, userIds);
    }


}
