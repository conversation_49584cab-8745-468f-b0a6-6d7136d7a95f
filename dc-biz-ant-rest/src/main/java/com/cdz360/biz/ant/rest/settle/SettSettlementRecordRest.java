package com.cdz360.biz.ant.rest.settle;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.feign.reactor.BiInvoiceFeignClient;
import com.cdz360.biz.model.settle.param.ListSettRecordParam;
import com.cdz360.biz.model.settle.vo.SettSettlementRecordVO;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.utils.feign.sett.SettleServerFeignClient;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@Tag(name = "结算记录2", description = "结算记录2")
@RestController
public class SettSettlementRecordRest {

    @Autowired
    private SettleServerFeignClient settleServerFeignClient;

    @Autowired
    private BiInvoiceFeignClient biInvoiceFeignClient;

    @Operation(summary = "分页查询结算划转数据明细")
    @PostMapping(value = "/api/sett/record/search/page")
    public Mono<ListResponse<SettSettlementRecordVO>> search(
        ServerHttpRequest request,
        @RequestBody ListSettRecordParam param) {
        log.info("分页查询结算划转数据明细: {}", JsonUtils.toJsonString(param));
        return settleServerFeignClient.findAllSettRec(param);
    }

    @Operation(summary = "通过结算流水号获取结算记录信息")
    @GetMapping(value = "/api/sett/record/getBySettleId")
    public Mono<ObjectResponse<SettSettlementRecordVO>> getSettRecordBySettleId(
        ServerHttpRequest request,
        @Parameter(name = "结算流水号", required = true) @RequestParam(value = "settleId") String settleId) {
        log.info("分页查询结算划转数据明细: {}", LoggerHelper2.formatEnterLog(request));
        return settleServerFeignClient.getSettRecordBySettleId(settleId);
    }

    @Schema(description = "结算单导出Excel")
    @PostMapping(value = "/api/sett/record/exportExcel")
    public Mono<ObjectResponse<ExcelPosition>> exportSettleRecord(
        ServerHttpRequest request,
        @Parameter(name = "结算流水号", required = true) @RequestParam(value = "settleId") String settleId) {
        log.info("结算单导出: {}", LoggerHelper2.formatEnterLog(request));
        return biInvoiceFeignClient.exportSettRecExcel(settleId);
    }

    @Schema(description = "结算单结算")
    @PostMapping(value = "/api/sett/record/settlement")
    public Mono<ObjectResponse<SettSettlementRecordVO>> settRecSettlement(
        ServerHttpRequest request,
        @Parameter(name = "结算流水号", required = true) @RequestParam(value = "settleId") String settleId) {
        log.info("结算单结算: {}", LoggerHelper2.formatEnterLog(request));
        return settleServerFeignClient.settManuSettlement(settleId);
    }
}
