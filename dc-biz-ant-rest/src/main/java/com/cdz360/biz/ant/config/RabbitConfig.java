package com.cdz360.biz.ant.config;
//
//import com.cdz360.biz.utils.config.AmqpProperties;
//import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
//import org.springframework.amqp.rabbit.connection.ConnectionFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.util.StringUtils;
//
//@Configuration
//public class RabbitConfig {
//    //资源owner账户 ID 信息
////    @Value("${resourceOwnerId:}")
////    private Long RESOURCE_OWNER_ID;
//    @Autowired
//    private AmqpProperties rabbitProperties;
//
//
//    @Bean
//    public ConnectionFactory connectionFactory() {
//        com.rabbitmq.client.ConnectionFactory rabbitConnectionFactory = new com.rabbitmq.client.ConnectionFactory();
//        rabbitConnectionFactory.setHost(rabbitProperties.getHost());
//        rabbitConnectionFactory.setPort(rabbitProperties.getPort());
////        if (StringUtils.startsWithIgnoreCase(rabbitProperties.getVirtualHost(), "aliyun")) {
////            // Ali AMQP认证
////            rabbitConnectionFactory.setVirtualHost(rabbitProperties.getVirtualHost());
////            rabbitConnectionFactory.setCredentialsProvider(new AliyunCredentialsProvider(rabbitProperties.getUsername(), rabbitProperties.getPassword(), RESOURCE_OWNER_ID));
////        } else {
//            // 本地RabbitMq认证
//            rabbitConnectionFactory.setVirtualHost(rabbitProperties.getVirtualHost());
//            rabbitConnectionFactory.setUsername(rabbitProperties.getUsername());
//            rabbitConnectionFactory.setPassword(rabbitProperties.getPassword());
////        }
//        rabbitConnectionFactory.setAutomaticRecoveryEnabled(true);
//        rabbitConnectionFactory.setNetworkRecoveryInterval(5000);
//
//        CachingConnectionFactory connectionFactory = new CachingConnectionFactory(rabbitConnectionFactory);
//        connectionFactory.setPublisherConfirmType(rabbitProperties.getPublisherConfirmType());
//        connectionFactory.setPublisherReturns(rabbitProperties.isPublisherReturns());
//        return connectionFactory;
//    }
//}
