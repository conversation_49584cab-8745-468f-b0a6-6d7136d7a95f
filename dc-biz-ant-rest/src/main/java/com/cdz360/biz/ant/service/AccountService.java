package com.cdz360.biz.ant.service;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.domain.UserAndBalanceVo;
import com.cdz360.biz.ant.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AccountService //implements IAccountService
{

    @Autowired
    private RedisUtil redisDao;


    /**
     * 根据token获取当前用户userId
     *
     * @param token
     * @return
     */

    public Long getUserIdByToken(String token) {
//        ParserConfig.getGlobalInstance().addAccept("com.chargerlinkcar.core.domain.vo.");
        String json = redisDao.get(token);
        log.info("CurrentUserByToken: token: {}, json:  {}", token, json);

        if (StringUtils.isEmpty(json)) {
            return null;
        }

        //从缓存中获取用户信息会出现脏读，直接从数据库中获取用户信息。
        UserAndBalanceVo userAndBalanceVo = JsonUtils.fromJson(json, UserAndBalanceVo.class);

        if (ObjectUtils.isEmpty(userAndBalanceVo)) {
            return null;
        }

        Long uid = userAndBalanceVo.getUid();
        return uid;
    }

}
