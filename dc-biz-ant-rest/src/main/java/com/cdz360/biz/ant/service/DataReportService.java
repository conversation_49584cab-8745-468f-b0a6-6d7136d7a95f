package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.feign.BizBiFeignClient;
import com.cdz360.biz.ant.feign.TradingFeignClient;
import com.cdz360.biz.ant.feign.reactor.AsyncBizBiFeignClient;
import com.cdz360.biz.model.trading.site.param.SiteReportParam;
import com.cdz360.biz.model.trading.site.vo.BiSiteGcMonthExpenseStatisticVo;
import com.cdz360.biz.model.trading.site.vo.BiSiteGcMonthIncomeVo;
import com.cdz360.biz.model.trading.site.vo.SiteBaseInfoVo;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 数据统计及报表相关
 * <p>
 * DataReportImpl
 *
 * <AUTHOR> 数据统计及报表相关
 * @since 2019.2.12
 */
@Slf4j
@Service
public class DataReportService //implements IDataReportService
{

    @Autowired
    private TradingFeignClient tradingFeignClient;

    @Autowired
    private BizBiFeignClient bizBiFeignClient;

    @Autowired
    private AsyncBizBiFeignClient asyncBizBiFeignClient;


    /**
     * 根据站点统计昨天、近7天、近30天的充电次数、电量、金额
     *
     * @param siteId
     * @return
     */

    public ListResponse<JsonNode> getSiteSurvey(String siteId, String token) {
        ListResponse<JsonNode> jsonObject = tradingFeignClient.getSiteSurvey(siteId, token);
        return jsonObject;
    }

    /**
     * 场站基础信息
     *
     * @param param
     * @return
     */
    public Mono<ObjectResponse<SiteBaseInfoVo>> getSiteBaseInfo(SiteReportParam param) {
        return asyncBizBiFeignClient.getSiteBaseInfo(param);
    }

    /**
     * 场站月运营数据
     *
     * @param param
     * @return
     */
    public Mono<ListResponse<BiSiteGcMonthIncomeVo>> getSiteMonthSurvey(SiteReportParam param) {
        return asyncBizBiFeignClient.getSiteMonthSurvey(param);
    }

    /**
     * 运营场站月支出
     *
     * @param param
     * @return
     */
    public Mono<ListResponse<BiSiteGcMonthExpenseStatisticVo>> getSiteExpenseMonthly(
        SiteReportParam param) {
        return asyncBizBiFeignClient.getSiteExpenseMonthly(param);
    }
}
