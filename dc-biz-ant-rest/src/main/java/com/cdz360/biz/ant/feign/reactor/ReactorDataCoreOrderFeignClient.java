package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.trading.order.param.PrepaidOrderListParam;
import com.cdz360.biz.model.trading.order.param.RewriteInterimParam;
import com.cdz360.biz.model.trading.order.vo.PrepaidOperationVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDataVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE,
    fallbackFactory = ReactorDataCoreOrderFeignHystrix.class)
public interface ReactorDataCoreOrderFeignClient {

    // 根据PageType获取预付订单
    @PostMapping(value = "/dataCore/orderData/getPrepaidOrderList")
    Mono<ListResponse<ChargerOrderVo>> getPrepaidOrderList(
        @RequestBody PrepaidOrderListParam param);

    // 根据PageType获取预付订单总数
    @PostMapping(value = "/dataCore/orderData/getPrepaidOrderListCount")
    Mono<ObjectResponse<Long>> getPrepaidOrderListCount(@RequestBody PrepaidOrderListParam param);

    // 预付订单操作
    @PostMapping(value = "/dataCore/orderData/prepaidOrderOperation")
    Mono<ObjectResponse<String>> prepaidOrderOperation(@RequestBody PrepaidOrderListParam param);

    // 重写InterimCode
    @PostMapping(value = "/dataCore/orderData/rewriteInterimCodeByOa")
    Mono<BaseResponse> rewriteInterimCodeByOa(@RequestBody RewriteInterimParam param);

    // 获取全部能开票的预付订单号
    @PostMapping(value = "/dataCore/orderData/getAllPrepaidOrderNos")
    Mono<ObjectResponse<PrepaidOperationVo>> getAllPrepaidOrderNos(
        @RequestBody PrepaidOrderListParam param);

    // 获取预付订单流程详情页的订单详细数据
    @PostMapping(value = "/dataCore/orderData/queryPrePaidOrderDetailList")
    Mono<ListResponse<ChargerOrderVo>> queryPrePaidOrderDetailList(
        @RequestBody PrepaidOrderListParam param);

    // 获取预付订单的统计信息
    @PostMapping(value = "/dataCore/orderData/getPrepaidOrderDataVo")
    Mono<ObjectResponse<ChargerOrderDataVo>> getPrepaidOrderDataVo(
        @RequestBody PrepaidOrderListParam param);

}
