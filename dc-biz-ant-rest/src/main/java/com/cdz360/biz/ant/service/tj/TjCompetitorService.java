package com.cdz360.biz.ant.service.tj;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.feign.reactor.BizTjFeignClient;
import com.cdz360.biz.model.tj.kc.param.ListTjCompetitorParam;
import com.cdz360.biz.model.tj.kc.param.UpdateTjCompetitorParam;
import com.cdz360.biz.model.tj.kc.vo.TjCompetitorVo;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class TjCompetitorService {

    @Autowired
    private BizTjFeignClient bizTjFeignClient;

    public Mono<ListResponse<TjCompetitorVo>> findTjCompetitor(ListTjCompetitorParam param) {
        return bizTjFeignClient.findTjCompetitor(param);
    }

    public Mono<ObjectResponse<TjCompetitorVo>> addCompetitor(UpdateTjCompetitorParam param) {
        IotAssert.isNull(param.getId(), "新增不需要添加ID");
        IotAssert.isNotBlank(param.getName(), "需要提供竞争对手名称");
        return bizTjFeignClient.saveCompetitor(param);
    }

    public Mono<ObjectResponse<TjCompetitorVo>> disableCompetitor(Long competitorId) {
        IotAssert.isNotNull(competitorId, "竞争者唯一ID不能为空");
        return bizTjFeignClient.disableCompetitor(competitorId);
    }
}
