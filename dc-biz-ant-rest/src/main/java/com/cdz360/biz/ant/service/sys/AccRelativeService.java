package com.cdz360.biz.ant.service.sys;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.feign.InspectionFeignClient;
import com.cdz360.biz.ant.service.parts.PartsService;
import com.cdz360.biz.auth.sys.param.AccRelativeParam;
import com.cdz360.biz.auth.sys.param.AddAccRelativeParam;
import com.cdz360.biz.auth.sys.param.YwUserParam;
import com.cdz360.biz.auth.sys.vo.AccRelativeVo;
import com.cdz360.biz.auth.sys.vo.AccRelativeWithVo;
import com.cdz360.biz.auth.user.po.SysUserPo;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.merchant.dto.SiteTinyDto;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.utils.feign.yw.YwOrderClient;
import com.chargerlinkcar.framework.common.domain.SysUser;
import com.chargerlinkcar.framework.common.feign.SiteDataCoreFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import java.time.Duration;
import java.util.Timer;
import java.util.TimerTask;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class AccRelativeService {

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;
    @Autowired
    private SiteDataCoreFeignClient siteDataCoreFeignClient;

    @Autowired
    private YwOrderClient ywOrderClient;

    @Autowired
    private PartsService partsService;

    @Autowired
    private com.chargerlinkcar.framework.common.feign.AuthCenterFeignClient authCenterFeignClient2;

    @Autowired
    private InspectionFeignClient inspectionFeignClient;

    public ListResponse<AccRelativeWithVo> getVoList(String token, AccRelativeParam param) {
        if (StringUtils.isNotBlank(param.getSiteName())) {
            ListSiteParam siteParam = new ListSiteParam();
            siteParam.setSiteName(param.getSiteName());
            ListResponse<SiteTinyDto> response = siteDataCoreFeignClient.getSiteTinyList(siteParam);
            FeignResponseValidate.check(response);
            if (CollectionUtils.isNotEmpty(response.getData())) {
                param.setSiteIdList(response.getData().stream().map(SiteTinyDto::getId)
                    .collect(Collectors.toList()));
            } else {
                return RestUtils.buildListResponse(null, 0L);
            }
        }

        final ListResponse<AccRelativeVo> result = authCenterFeignClient.getVoList(token, param);
        FeignResponseValidate.check(result);

        return RestUtils.buildListResponse(result.getData().stream().map(acc -> {
            AccRelativeWithVo vo = new AccRelativeWithVo();
            BeanUtils.copyProperties(acc, vo);
            final Long count = partsService.partsCount(acc.getSysUid())
                .doOnNext(FeignResponseValidate::check)
                .block(Duration.ofSeconds(50L))
                .getData();
            return vo.setPartsCount(count);
        }).collect(Collectors.toList()), result.getTotal());
    }

    private static Timer timer = null;

    public void syncOrderNum(String token) {
//        ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
//        RequestContextHolder.setRequestAttributes(sra, true);
        if (timer != null) {
            timer.cancel();
        }
        timer = new Timer("AccRelativeTimer");
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                authCenterFeignClient.syncOrderNum(token);
            }
        }, 5000L);
    }

    public ObjectResponse<AccRelativeVo> findByAccount(String token, String account,
        String commIdChain) {
        return authCenterFeignClient.findByAccount(token, account, commIdChain);
    }

    public Mono<SysUserVo> addAccRelative(String token, AddAccRelativeParam param) {
        SysUserVo targetUser = new SysUserVo();
        targetUser.setId(param.getSysUid());

        return Mono.just(authCenterFeignClient.addAccRelative(token, param))
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .flatMapMany(Flux::fromIterable)
            .doOnNext(u -> {
                if (param.getSysUid().equals(u.getId())) {
                    targetUser.setUsername(u.getUsername())
                        .setName(u.getName())
                        .setPhone(u.getPhone());
                }
            })
            /*
            理论上新增账户时，不需要转派任何工单
            .doOnNext(this::dealWithInspectionOrder)
            .flatMap(
                user -> this.dealWithYwOrder(user, param.getUpdateOpUid(), param.getUpdateOpName()))
            */
            .collectList()
            .flatMap(s -> this.fillUserInfo(targetUser));
    }

    /**
     * a -> b + c 1. Q：a在职 -> 离职, b,c ??? A: b,c 也改为离职，且abc失去登录权限 2. Q：a删除, b,c ??? A:
     * a失去运维功能(可登录)，bc账号切换功能不受影响 3. ①又新增b, 不关联a,c。则 a -> b + c关系是否受影响 A: 关系更新为：a -> c
     * <p>
     * ②又新增b, 只关联a。则 a -> b + c关系是否受影响 A: 关系更新为：a -> b
     */
    public Mono<SysUserVo> editAccRelative(String token, AddAccRelativeParam param) {
        SysUserVo targetUser = new SysUserVo();
        targetUser.setId(param.getSysUid());

        if (BooleanUtils.isFalse(param.getWork())) {
            // 若想将账号改为离职，需判断名下是否剩余工单未处理，剩余则不允许变更账号为离职
            BaseResponse baseResponse = authCenterFeignClient.checkForOutstandingOrder(token,
                param.getSysUid());
            FeignResponseValidate.check(baseResponse);
        }

        return Mono.just(authCenterFeignClient.editAccRelative(token, param))
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .flatMapMany(Flux::fromIterable)
            .doOnNext(u -> {
                if (param.getSysUid().equals(u.getId())) {
                    targetUser.setUsername(u.getUsername())
                        .setName(u.getName())
                        .setPhone(u.getPhone());
                }
            })
            /*
            名下存在工单时，不允许离职
            .doOnNext(this::dealWithInspectionOrder)
            .flatMap(
                user -> this.dealWithYwOrder(user, param.getUpdateOpUid(), param.getUpdateOpName()))
            */
            .collectList()
            .flatMap(s -> this.fillUserInfo(targetUser));
    }

    public Mono<SysUserVo> deleteBySysUid(String token, Long opUid, String opName, Long sysUid) {
        try {
            // 若名下存在工单未处理，则不允许删除账户
            BaseResponse baseResponse = authCenterFeignClient.checkForOutstandingOrder(token,
                sysUid);
            FeignResponseValidate.check(baseResponse);
        } catch (Exception ex) {
            log.error("检查工单数据异常: err = {}", ex.getMessage());
            throw new DcServiceException("请先处理完账户名下工单，再删除账户");
        }

        return Mono.just(authCenterFeignClient.deleteBySysUid(token, sysUid))
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            /*
            .doOnNext(this::dealWithInspectionOrder)
            .doOnNext(user -> this.dealWithYwOrder(user, opUid, opName).subscribe())
            */
            ;
    }

    private Mono<SysUserVo> fillUserInfo(SysUserVo user) {
        return Mono.just(user)
            .doOnNext(u -> {
                if (StringUtils.isBlank(user.getName())) {
                    ObjectResponse<SysUserPo> res = authCenterFeignClient2.getSysUserById(
                        user.getId());
                    FeignResponseValidate.check(res);

                    user.setUsername(res.getData().getUsername())
                        .setName(res.getData().getName())
                        .setPhone(res.getData().getPhone());
                }
            });
    }

    /*
    // 将user名下的巡检工单转派给他人
    private void dealWithInspectionOrder(SysUserVo user) {
        Mono.just(user)
            .map(u -> {
                RecordParam recordParam = new RecordParam()
                    .setOpUidList(List.of(user.getId()))
                    .setStatusList(List.of(SiteInspectionStatus.INIT, SiteInspectionStatus.FAIL));
                recordParam.setSize(50); // 预测剩余工单总数
                return inspectionFeignClient.getRecords(recordParam);

            })
            .doOnNext(FeignResponseValidate::check)
            .doOnNext(res -> log.info("巡检剩余工单数: {} ; {} / {}",
                user.getId(), res.getData().size(), res.getTotal()))
            .map(ListResponse::getData)
            .flatMapMany(Flux::fromIterable)
            .doOnNext(rec -> {
                GetYwUserParam ywUserParam = new GetYwUserParam();
                ywUserParam.setEquipRepairEntry(Boolean.FALSE)
                    .setUid(user.getId())
                    .setSiteId(rec.getSiteId())
                    .setSiteCommId(rec.getCommId());
                ObjectResponse<SysUserVo> ywUser =
                    authCenterFeignClient2.getYwUser(ywUserParam);
                FeignResponseValidate.check(ywUser);

                ObjectResponse<SiteInspectionRecordPo> res = inspectionFeignClient.transInspection(
                    rec.getId(), ywUser.getData().getId());
                FeignResponseValidate.check(res);
            })
            .subscribe();
    }
    */

    /*
    // 将userVo名下的运维工单转派给他人
    private Mono<BaseResponse> dealWithYwOrder(SysUserVo userVo, Long opUid, String opName) {
        return Mono.just(userVo)
            .flatMap(user -> {
                ListYwOrderParam ywOrderParam = new ListYwOrderParam();
                ywOrderParam.setMaintUid(user.getId())
                    .setOrderStatusList(
                        List.of(YwOrderStatus.NO_PASS, YwOrderStatus.INIT, YwOrderStatus.RECEIVED))
                    .setTotal(true)
                    .setSize(50); // 预测剩余工单总数
                return ywOrderClient.findYwOrder(ywOrderParam)
                    .doOnNext(FeignResponseValidate::check)
                    .doOnNext(res -> log.info("运维剩余工单数: {} ; {} / {}",
                        userVo.getId(), res.getData().size(), res.getTotal()))
                    .map(ListResponse::getData)
                    .flatMapMany(Flux::fromIterable)
                    .map(vo -> { // 转派工单
                        // 重新查找运维人
                        GetYwUserParam ywUserParam = new GetYwUserParam();
                        ywUserParam.setEquipRepairEntry(Boolean.FALSE)
                            .setUid(user.getId())
                            .setSiteId(vo.getSiteId())
                            .setSiteCommId(vo.getSiteCommId());
                        ObjectResponse<SysUserVo> ywUser = authCenterFeignClient2.getYwUser(
                            ywUserParam);
                        FeignResponseValidate.check(ywUser);

                        TransYwOrderParam result = new TransYwOrderParam()
                            .setYwOrderNo(vo.getYwOrderNo())
                            .setUid(ywUser.getData().getId())
                            .setUserName(ywUser.getData().getName())
                            .setMaintCommId(ywUser.getData().getCommId())
                            .setUserType(UserType.SYS_USER);

                        result.setOpUid(opUid)
                            .setOpName(opName)
                            .setOpType(UserType.SYS_USER);
                        return result;
                    })
                    .flatMap(ywOrderClient::transYwOrder)
                    .doOnNext(FeignResponseValidate::checkIgnoreData)
                    .doOnNext(res -> {
                        if (null != res.getData()) {
                            MessagePo messagePo = new MessagePo();
                            messagePo.setBroadcast(BroadCastType.PART)
                                .setTargetUid(List.of(res.getData().getMaintUid()))
                                .setCommIdList(List.of(user.getCommId()))
                                .setContent("<p>该场站有新的待接收运维工单，请及时处理</p>")
                                .setMsgType(MsgType.YW_REMINDER)
                                .setPlatformList(List.of(PlatformType.MANAGE))
                                .setTitle(res.getData().getSiteName()); // 场站名称
                            authCenterFeignClient.addMessage(null, messagePo);
                        } else {
                            log.error("站内信推送不成功: user = {}", JsonUtils.toJsonString(user));
                        }
                    })
                    .collectList()
                    .map(s -> RestUtils.success());
            });
    }
    */

    public Mono<ListResponse<SysUser>> findYwUserList(YwUserParam param) {
        return Mono.just(authCenterFeignClient.findYwUser(param));
    }
}
