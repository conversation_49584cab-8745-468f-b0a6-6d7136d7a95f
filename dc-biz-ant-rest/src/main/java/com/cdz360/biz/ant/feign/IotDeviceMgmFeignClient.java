package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.biz.ant.domain.request.UpgradeTaskRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, fallbackFactory = IotDeviceMgmFeignClientHystrixFactory.class)
public interface IotDeviceMgmFeignClient {

    /**
     * 海外版-发起桩升级
     *
     * @param upgradeTaskRequest
     * @return
     */
    @PostMapping("/device/upgrade/commercial/startTask")
    BaseResponse startEssTask(@RequestBody UpgradeTaskRequest upgradeTaskRequest);
}
