package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.ant.feign.reactor.ReactorSiteDataCoreFeignClient;
import com.cdz360.biz.model.geo.param.ListCitiesParam;
import com.cdz360.biz.model.geo.param.ListCountriesParam;
import com.cdz360.biz.model.geo.vo.GeoCitiesVo;
import com.cdz360.biz.model.geo.vo.GeoCountriesVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class WorldGeoService {

    @Autowired
    private ReactorSiteDataCoreFeignClient dataCoreFeignClient;

    public Mono<ListResponse<GeoCountriesVo>> findCountries(ListCountriesParam param) {
        return dataCoreFeignClient.findCountries(param);
    }

    public Mono<ListResponse<GeoCitiesVo>> findCities(ListCitiesParam param) {
        return dataCoreFeignClient.findCities(param);
    }
}
