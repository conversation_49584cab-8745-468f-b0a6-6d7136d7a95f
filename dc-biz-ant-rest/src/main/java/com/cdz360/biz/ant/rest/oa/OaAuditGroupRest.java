package com.cdz360.biz.ant.rest.oa;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.reactor.OaFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorAuthCenterFeignClient;
import com.cdz360.biz.oa.dto.account.OaAccountDto;
import com.cdz360.biz.oa.dto.account.OaGroupDto;
import com.cdz360.biz.oa.param.account.ListOaGroupParam;
import com.cdz360.biz.oa.param.account.ListOaGroupUserParam;
import com.cdz360.biz.oa.param.account.OaModifyGroupParam;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;


@Slf4j
@RestController
@RequestMapping("/oa/account")
public class OaAuditGroupRest {

    @Autowired
    private OaFeignClient oaFeignClient;

    @Autowired
    private ReactorAuthCenterFeignClient authCenterFeignClient;

    @Operation(summary = "获取审核组用户列表")
    @PostMapping(value = "/groupUserList")
    public Mono<ListResponse<OaAccountDto>> groupUserList(
        @RequestBody ListOaGroupUserParam param) {
        log.info("获取审核组用户列表. param = {}", param);
        return this.oaFeignClient.groupUserList(param);
    }

    @Operation(summary = "获取审核组列表")
    @PostMapping(value = "/groupList")
    public Mono<ListResponse<OaGroupDto>> groupList(
        @RequestBody ListOaGroupParam param) {
        log.info("获取审核组列表. param = {}", param);
        if (StringUtils.isNotBlank(param.getSk())) {
            return this.authCenterFeignClient.groupIdByKeyWord(param.getSk())
                .doOnNext(FeignResponseValidate::check)
                .map(ListResponse::getData)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(userIdList -> this.oaFeignClient.groupList(param.setMemberIds(userIdList)))
                .switchIfEmpty(Mono.just(RestUtils.buildListResponse(List.of(), 0)));
        } else {
            return this.oaFeignClient.groupList(param);
        }
    }

    @Operation(summary = "创建/修改审核组信息", description = "用于主系统向OA系统同步审核组信息")
    @PostMapping(value = "/modifyGroup")
    public Mono<BaseResponse> modifyGroup(@RequestBody OaModifyGroupParam param) {
        log.info("创建/修改审核组信息. param = {}", param);

        if (StringUtils.isBlank(param.getName())) {
            throw new DcArgumentException("审核组名称不能为空");
        }

        if (null == param.getType()) {
            throw new DcArgumentException("审核组属性不能为空");
        }
        return this.oaFeignClient.modifyGroup(param)
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .flatMap(x -> {
                param.setGid(x);
                return authCenterFeignClient.allocateSysUserGroup(param);
            })
            .map(x -> RestUtils.success());
    }

    @Operation(summary = "删除审核组信息", description = "用于主系统向OA系统同步审核组信息")
    @GetMapping(value = "/removeGroup")
    public Mono<ObjectResponse<OaGroupDto>> removeGroup(@RequestParam String gid) {
        log.info("删除审核组信息. gid = {}", gid);
        return this.oaFeignClient.removeGroup(gid);
    }
}
