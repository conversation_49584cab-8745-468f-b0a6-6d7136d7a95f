package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.iot.type.SiteBiSampleType;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.ess.vo.DayEssDataBi;
import com.cdz360.biz.model.trading.iot.param.PvProfitTrendParam;
import com.cdz360.biz.model.trading.site.param.RecordParam;
import com.cdz360.biz.model.trading.site.param.SiteReportParam;
import com.cdz360.biz.model.trading.site.vo.BiSiteGcMonthExpenseStatisticVo;
import com.cdz360.biz.model.trading.site.vo.BiSiteGcMonthIncomeVo;
import com.cdz360.biz.model.trading.site.vo.SiteBaseInfoVo;
import com.cdz360.biz.model.trading.yw.param.ListYwOrderParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.function.Function;

@Slf4j
@Component
public class AsyncBizBiFeignHystrix
    implements FallbackFactory<AsyncBizBiFeignClient> {

    @Override
    public AsyncBizBiFeignClient apply(Throwable throwable) {
        return new AsyncBizBiFeignClient() {
            @Override
            public Mono<ObjectResponse<ExcelPosition>> exportYwOrder(String ywOrderNo) {
                log.error(
                    "【服务熔断】: Service = {}, api = exportYwOrder (运维工单详情导出), ywOrderNo = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_BI, ywOrderNo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<ExcelPosition>> exportYwOrderExcel(ListYwOrderParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = exportYwOrderExcel (运维工单列表导出到EXCEL), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_BI, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<ExcelPosition>> exportInspectionRecExcel(RecordParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = exportInspectionRecExcel (巡检工单列表导出EXCEL), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_BI, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<ExcelPosition>> exportPowerProfitTrendExcel(
                PvProfitTrendParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = exportPowerProfitTrendExcel (巡检工单列表导出EXCEL), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_BI, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<ExcelPosition>> exportSiteEssBiExcel(
                SiteBiSampleType sampleType, List<DayEssDataBi> data) {
                log.error(
                    "【服务熔断】: Service = {}, api = exportSiteEssBiExcel (场站各天储能汇总数据到Excel), sampleType = {}, data.size = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_BI, sampleType, data.size());
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<BiSiteGcMonthIncomeVo>> getSiteMonthSurvey(
                SiteReportParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = getSiteMonthSurvey (场站月运营数据), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_BI, param);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<BiSiteGcMonthExpenseStatisticVo>> getSiteExpenseMonthly(
                SiteReportParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = getSiteMonthSurvey (运营场站月支出), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_BI, param);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<SiteBaseInfoVo>> getSiteBaseInfo(SiteReportParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = getSiteMonthSurvey (场站基础信息), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_BI, param);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }
        };
    }

    @Override
    public <V> Function<V, AsyncBizBiFeignClient> compose(
        Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_BI);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(
        Function<? super AsyncBizBiFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_BI);
        return null;
    }
}
