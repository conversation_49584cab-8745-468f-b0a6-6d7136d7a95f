package com.cdz360.biz.ant.service.tj;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.reactor.BizTjFeignClient;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaAnalysisParam;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisVo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisWithPointVo;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class TjAnalysisService {

    @Autowired
    private BizTjFeignClient bizTjFeignClient;

    public Mono<ListResponse<TjAreaAnalysisVo>> findTjAnalysis(ListTjAreaAnalysisParam param) {
        return bizTjFeignClient.findTjAnalysis(param);
    }

    public Mono<ObjectResponse<TjAreaAnalysisWithPointVo>> getTjAnalysisById(Long analysisId) {
        IotAssert.isNotNull(analysisId, "投建分析唯一ID不能为空");
        return bizTjFeignClient.getTjAnalysisById(analysisId);
    }

    public Mono<ObjectResponse<TjAreaAnalysisWithPointVo>> addAnalysis(
        TjAreaAnalysisWithPointVo param) {
        IotAssert.isNull(param.getId(), "投建分析区域ID不为空");
        IotAssert.isNotNull(param.getAid(), "请指定分析投建区域ID");
        IotAssert.isNotBlank(param.getName(), "投建分析名称不能为空");
        IotAssert.isNotBlank(param.getLocation(), "投建分析缩略位置坐标不能为空");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getAnalysisPointList()),
            "投建分析划分点信息不能为空");
        param.getAnalysisPointList().stream()
            .filter(x -> x.getNum() == null ||
                x.getStatus() == null ||
                StringUtils.isBlank(x.getLocation()) ||
                x.getRadius() == null).findFirst()
            .ifPresent(k -> {
                throw new DcArgumentException("投建分析划分点信息不完整");
            });
        return bizTjFeignClient.saveAnalysis(param);
    }

    public Mono<ObjectResponse<TjAreaAnalysisVo>> disableTjAreaAnalysis(Long analysisId) {
        IotAssert.isNotNull(analysisId, "投建分析唯一ID不能为空");
        return bizTjFeignClient.disableAnalysis(analysisId);
    }
}
