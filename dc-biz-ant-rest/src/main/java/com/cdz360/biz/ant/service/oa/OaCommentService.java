package com.cdz360.biz.ant.service.oa;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.feign.reactor.OaFeignClient;
import com.cdz360.biz.oa.param.MarkDeleteCommentParam;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class OaCommentService {

    @Autowired
    private OaFeignClient oaFeignClient;

    public Mono<ObjectResponse<Integer>> markDeleteComment(MarkDeleteCommentParam param) {
        IotAssert.isNotBlank(param.getProcInstId(), "请指定流程ID");
        IotAssert.isNotBlank(param.getCommentId(), "请指定留言ID");
        IotAssert.isNotNull(param.getFile(), "请指定需要标记文件列表");
        IotAssert.isNotNull(param.getFlag(), "请指定需要标记类型");
        return oaFeignClient.markDeleteComment(param);
    }
}
