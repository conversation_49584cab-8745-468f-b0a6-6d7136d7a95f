package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.cus.discount.vo.ChangeCorpCommIdDiscountList;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * @since 11/12/2020 10:56 AM
 * <AUTHOR>
 */
@Tag(name = "协议价", description = "协议价相关")
@Slf4j
@RestController
public class DiscountRest {
    @Autowired
    private com.cdz360.biz.utils.feign.user.UserFeignClient monoUserFeignClient;

    @Operation(summary = "获取保留/移除协议价场站列表")
    @GetMapping(value = "/api/discount/getMoveCorpDiscountByCorpId")
    public Mono<ObjectResponse<ChangeCorpCommIdDiscountList>> getMoveCorpDiscountByCorpId(
            @RequestParam("corpId") Long corpId,
            @RequestParam("commId") Long commId,
            ServerHttpRequest request) {

        log.info("获取保留/移除协议价场站列表: {}, corpId = {}, commId = {}",
                LoggerHelper2.formatEnterLog(request), corpId, commId);

        return monoUserFeignClient.changeCorpCommIdDiscountList(corpId, commId);
    }
}