package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.biz.ant.domain.CustomUi;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.feign.MerchantFeignClient;
import com.cdz360.biz.model.merchant.dto.CommercialDto;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * CustomUiServiceImpl
 *
 * CustomUiServiceImpl
 * <AUTHOR>
 * 
 * @since 2018.12.7
 */
@Slf4j
@Service
public class CustomUiService //implements ICustomUiService
{
    /**
     * 商户服务
     */
    @Autowired
    private MerchantFeignClient merchantFeignClient;
    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    /**
     * 保存界面设置
     *
     * @param customUi
     * @return
     */

    public ObjectResponse customUiSaveOrUpdate(CustomUi customUi, String token) {
        //调用服务
        BaseResponse jsonObject = merchantFeignClient.customUiSaveOrUpdate(token, customUi);

        if (jsonObject != null && ResultConstant.RES_SUCCESS_CODE != jsonObject.getStatus()) {
//            return jsonObject.toJavaObject(FailResultEntity.class);
            throw new DcServiceException(jsonObject.getError());
        } else {
            return new ObjectResponse<>("保存成功");
        }
    }

    /**
     * 根据条件查询界面设置
     *
     * @param customUi
     * @return
     */

    public ObjectResponse<CustomUi> queryCustomUi(CustomUi customUi) {

        //调用服务
        ObjectResponse<CustomUi> jsonObject = merchantFeignClient.queryCustomUi(customUi);
        if (jsonObject != null && ResultConstant.RES_SUCCESS_CODE == jsonObject.getStatus()) {
            return jsonObject;
        } else {
            log.info("界面设置获取失败{}", jsonObject);
            throw new DcServiceException("界面设置获取失败");
        }
    }

    public ObjectResponse<CommercialDto> getCommPlatInfo(Long commId) {
        return authCenterFeignClient.getCommPlatInfo(commId);
    }
}
