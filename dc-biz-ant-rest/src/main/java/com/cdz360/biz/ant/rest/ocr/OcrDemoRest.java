package com.cdz360.biz.ant.rest.ocr;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.rest.ocr.model.PrintedTextOCRParam;
import com.cdz360.biz.ant.rest.ocr.model.PrintedTextOCRResponse;
import com.cdz360.biz.ant.utils.RedisUtil;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/ocr/wx")
public class OcrDemoRest {

    @Autowired
    private RedisUtil redisUtil;

    // https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/img-ocr/ocr/printedTextOCR.html
    @Operation(summary = "通用印刷体识别OCR")
    @PostMapping(value = "/printedText")
    public Mono<ObjectResponse<PrintedTextOCRResponse>> printedTextOCR(
        @RequestBody PrintedTextOCRParam param) {
        log.info("通用印刷体识别OCR: param = {}", param);
        IotAssert.isNotBlank(param.getAppId(), "请指定AppID");
        IotAssert.isNotBlank(param.getUrl(), "请提供图片");
        val key = formatWxAccessTokenRedisKey(param.getAppId());
        IotAssert.isNotBlank(key, "AppID无效");
        val acc_token = redisUtil.get(key);
        IotAssert.isNotBlank(key, "AppID对应token无效");

        try (val client = HttpClientBuilder.create().build()) {
            val uriBuilder = new URIBuilder("https://api.weixin.qq.com/cv/ocr/comm")
                .setParameter("access_token", acc_token)
                .setParameter("img_url", param.getUrl());
            val response = client.execute(new HttpPost(uriBuilder.build()));
            val context = EntityUtils.toString(response.getEntity(), "UTF-8");
            log.info("OCR解析结果::: {}", context);
            return Mono.just(
                    JsonUtils.fromJson(context, PrintedTextOCRResponse.class))
                .map(RestUtils::buildObjectResponse);
        } catch (Exception e) {
            log.error("OCR解析失败: {}", e.getMessage(), e);
            throw new DcServiceException(e.getMessage());
        }

//        WebClient client = WebClient.builder().baseUrl("https://api.weixin.qq.com").build();
//        var spec = client.post()
//            .uri(u -> u.path("/cv/ocr/comm")
//                .queryParam("access_token", acc_token)
//                .queryParam("img_url", param.getUrl())
//                .build())
//            .header("Content-Type", "application/json");
//
//        return spec.retrieve()
//            .onStatus(HttpStatus::is4xxClientError,
//                error -> {
//                    log.warn("OCR识别失败. http.status = {}, res.headers = {}",
//                        error.statusCode(), error.headers().asHttpHeaders().entrySet());
//                    return Mono.error(
//                        new DcServiceException("OCR识别失败,外部接口报错", Level.INFO));
//                })
//            .onStatus(HttpStatus::is5xxServerError,
//                error -> {
//                    log.warn("OCR识别失败. http.status = {}, res.headers = {}",
//                        error.statusCode(), error.headers().asHttpHeaders().entrySet());
//                    error.bodyToMono(String.class)
//                        .doOnNext(b -> log.warn("OCR识别失败. res.body = {}", b)).subscribe();
//                    return Mono.error(
//                        new DcServiceException("OCR识别失败,外部接口报错", Level.INFO));
//                })
//            .bodyToMono(PrintedTextOCRResponse.class)
//            .map(res -> {
//                if (0 == res.getErrCode()) {
//                    return res;
//                } else {
//                    log.warn("OCR识别失败 res = {}", res);
//                    throw new DcServiceException("OCR识别失败,外部接口报错!");
//                }
//            })
//            .map(RestUtils::buildObjectResponse);
    }

    public static String formatWxAccessTokenRedisKey(String appId) {
        return "wx.accessToken." + appId;
    }
}
