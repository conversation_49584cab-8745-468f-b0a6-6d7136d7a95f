package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.ant.domain.request.UpgradeTaskRequest;
import com.chargerlinkcar.framework.common.domain.param.BoxListRequest;
import com.chargerlinkcar.framework.common.domain.vo.BoxInfoVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * <AUTHOR>
 * @since Created on 20:02 2019/4/22.
 */
@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DEVICE_REST,
    fallbackFactory = SiteFeignClientHystrixFactory.class)
public interface SiteFeignClient {


    /**
     * 查询设备信息
     *
     * @param boxListRequest
     * @return
     */
    @PostMapping("/api/box/getPagedBoxSimpleList")
    ListResponse<BoxInfoVo> getPagedBoxSimpleList(@RequestBody BoxListRequest boxListRequest);

    /**
     * 发起桩升级
     *
     * @param upgradeTaskRequest
     * @return
     */
    @PostMapping("/api/upgrade/startTask")
    BaseResponse startTask(@RequestHeader("token") String token,
        @RequestBody UpgradeTaskRequest upgradeTaskRequest);
}
