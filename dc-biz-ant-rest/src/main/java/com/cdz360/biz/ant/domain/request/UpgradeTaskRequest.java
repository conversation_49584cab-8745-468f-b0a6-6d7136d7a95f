package com.cdz360.biz.ant.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * UpgradeTaskRequest
 *  TODO
 * @since 9/21/2019 9:53 AM
 * <AUTHOR>
 */
@Data
@Schema(description = "请求桩升级参数")
public class UpgradeTaskRequest {
    @Schema(description = "场站id")
    private String siteId;
    @Schema(description = "桩编号列表")
    private List<String> evseIds;
    @Schema(description = "升级包id")
    private Long bundleId;
    @Schema(description = "升级包名称")
    private String bundleName;
    @Schema(description = "操作者id(不要传，传了也没用)", hidden = true)
    private Long opId;
    @Schema(description = "操作者名字(不要传，传了也没用)", hidden = true)
    private String OpName;
    @Schema(description = "这个参数用于再升级失败的桩")
    private Long taskId;
}