package com.cdz360.biz.ant.rest.site;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.BizBiFeignClient;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.ant.service.site.SiteIncomeExpenseService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.bi.site.ChargeFee;
import com.cdz360.biz.model.bi.site.MeterReadingBi;
import com.cdz360.biz.model.bi.site.OrderCount;
import com.cdz360.biz.model.bi.site.OrderElecDivision;
import com.cdz360.biz.model.bi.site.PlugErrorCount;
import com.cdz360.biz.model.bi.site.PlugUtilization;
import com.cdz360.biz.model.bi.site.SiteErrorCount;
import com.cdz360.biz.model.bi.site.SiteUtilization;
import com.cdz360.biz.model.bi.site.UserOrderCount;
import com.cdz360.biz.model.bi.site.UserOrderElec;
import com.cdz360.biz.model.bi.site.UserOrderFee;
import com.cdz360.biz.model.common.constant.Constant;
import com.cdz360.biz.model.iot.dto.EvseDto;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.iot.param.MeterRecordBiParam;
import com.cdz360.biz.model.iot.param.SiteBiParam;
import com.cdz360.biz.model.iot.param.SiteBiTopParam;
import com.cdz360.biz.model.iot.type.SiteBiSampleType;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.trading.meter.vo.MeterReadingTopVo;
import com.cdz360.biz.model.trading.site.param.ListBiSiteOrderParam;
import com.cdz360.biz.model.trading.site.po.BiSiteMeterSummaryDto;
import com.cdz360.biz.model.trading.site.po.BiSiteSummaryPo;
import com.cdz360.biz.model.trading.site.po.SiteDailyBiPo;
import com.cdz360.biz.model.trading.site.vo.BiSiteOrderVo;
import com.cdz360.biz.model.trading.site.vo.SiteIncomeExpenseVo;
import com.cdz360.biz.model.trading.site.vo.SiteOrderAccountBi;
import com.cdz360.biz.model.trading.site.vo.SiteOrderHlhtAccountBi;
import com.cdz360.biz.utils.feign.iot.MeterFeignClient;
import com.cdz360.biz.utils.feign.site.BiSiteOrderFeignClient;
import com.cdz360.data.cache.RedisIotReadService;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * SiteBiRest
 *
 * @since 3/18/2020 4:21 PM
 * <AUTHOR>
 */
@RequestMapping("/api/siteBi")
@RestController
@Slf4j
@Tag(name = "场站数据统计", description = "场站数据统计")
public class SiteBiRest extends BaseController {

    @Autowired
    private BizBiFeignClient ostFeignClient;

    @Autowired
    private RedisIotReadService redisIotReadService;

    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;

    @Autowired
    private BiSiteOrderFeignClient biSiteOrderFeignClient;

    @Autowired
    private MeterFeignClient meterFeignClient;

    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;

    @Autowired
    private SiteIncomeExpenseService siteIncomeExpenseService;

    @Operation(summary = "获取场站收支按月汇总信息", description = "用于OA电费支付历史台账")
    @GetMapping("/sixMonthIncomeExpense")
    public Mono<ListResponse<SiteIncomeExpenseVo>> siteSixMonthIncomeExpense(
        ServerHttpRequest request, @ApiParam("场站ID") @RequestParam("siteId") String siteId,
        @ApiParam("指定日期(年月日)") @RequestParam("month")
        @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate month) {
        log.info("获取场站收支按月汇总信息: {}", LoggerHelper2.formatEnterLog(request));
        return siteIncomeExpenseService.siteSixMonthIncomeExpense(siteId, month);
    }

    @PostMapping("/orderCountBi")
    @Operation(summary = "订单数折线数据获取")
    public ListResponse<OrderCount> orderCountBi(
        @RequestBody SiteBiParam siteBiParam,
        ServerHttpRequest request) {
        siteBiParam = this.increaseByUnit(siteBiParam);
        return ostFeignClient.orderCountBi(siteBiParam);
    }

    @PostMapping("/userOrderCountBi")
    @Operation(summary = "客户订单数排行榜获取")
    public ListResponse<UserOrderCount> userOrderCountBi(
        @RequestBody SiteBiTopParam siteBiTopParam,
        ServerHttpRequest request) {
        this.increaseByUnit(siteBiTopParam);
        return ostFeignClient.userOrderCountBi(siteBiTopParam);
    }

    @PostMapping("/chargeFeeBi")
    @Operation(summary = "充电消费柱状数据获取")
    public ListResponse<ChargeFee> chargeFeeBi(
        @RequestBody SiteBiParam siteBiParam,
        ServerHttpRequest request) {
        siteBiParam = this.increaseByUnit(siteBiParam);
        return ostFeignClient.chargeFeeBi(siteBiParam);
//        return new ListResponse<>(ret);
    }

    @PostMapping("/userChargeFeeBi")
    @Operation(summary = "客户充电消费排行榜获取")
    public ListResponse<UserOrderFee> userChargeFeeBi(
        @RequestBody SiteBiTopParam siteBiTopParam,
        ServerHttpRequest request) {
        this.increaseByUnit(siteBiTopParam);
        return ostFeignClient.userChargeFeeBi(siteBiTopParam);
    }

    @PostMapping("/chargeDivisionBi")
    @Operation(summary = "分时电量柱状数据获取")
    public ListResponse<OrderElecDivision> chargeDivisionBi(
        @RequestBody SiteBiParam siteBiParam,
        ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false) +
            " param = " + JsonUtils.toJsonString(siteBiParam));
        siteBiParam = this.increaseByUnit(siteBiParam);
        return ostFeignClient.chargeDivisionBi(siteBiParam);
    }

    @PostMapping("/userChargeDivisionBi")
    @Operation(summary = "客户分时电量排行榜获取")
    public ListResponse<UserOrderElec> userChargeDivisionBi(
        @RequestBody SiteBiTopParam siteBiTopParam,
        ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false) +
            " param = {}" + JsonUtils.toJsonString(siteBiTopParam));
        this.increaseByUnit(siteBiTopParam);
        return ostFeignClient.userChargeDivisionBi(siteBiTopParam);
    }

    @PostMapping("/utilizationBi")
    @Operation(summary = "枪时长、利用率折线图数据获取")
    public ListResponse<SiteUtilization> utilizationBi(
        @RequestBody SiteBiParam siteBiParam,
        ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false) +
            " param = " + JsonUtils.toJsonString(siteBiParam));
        siteBiParam = this.increaseByUnit(siteBiParam);
        return ostFeignClient.utilizationBi(siteBiParam);
    }

    @PostMapping("/plugUtilizationBi")
    @Operation(summary = "枪时长、利用率排行榜获取")
    public ListResponse<PlugUtilization> plugUtilizationBi(
        @RequestBody SiteBiTopParam siteBiTopParam,
        ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false) +
            " param = " + JsonUtils.toJsonString(siteBiTopParam));
        this.increaseByUnit(siteBiTopParam);

        ListResponse<PlugUtilization> result = ostFeignClient.plugUtilizationBi(siteBiTopParam);
        FeignResponseValidate.check(result);

        // 从数据库中获取场站所有桩信息
        List<EvseDto> evseList = new ArrayList<>();

        for (PlugUtilization p : result.getData()) {
            EvseVo cache = redisIotReadService.getEvseRedisCache(p.getEvseNo());
            if (null != cache) {
                p.setEvseName(cache.getName());
                p.setSupplyType(cache.getSupplyType());
            } else {
                if (CollectionUtils.isEmpty(evseList)) {
                    // 从数据库中获取
                    ListEvseParam evseParam = new ListEvseParam();
                    evseParam.setSiteIdList(List.of(p.getSiteId()));
                    ListResponse<EvseDto> response = this.iotDeviceMgmFeignClient.getEvseList(
                        evseParam);
                    evseList = response.getData();
                }

                Optional<EvseDto> dto = evseList.stream()
                    .filter(e -> p.getEvseNo().equals(e.getEvseId())).findFirst();
                if (dto.isPresent()) {
                    p.setEvseName(dto.get().getName());
                    p.setSupplyType(dto.get().getSupply());
                }
            }

            // 日均使用时长
            if (p.getDuration() != null) {
                // 统计跨度的天数
                long dayCount =
                    (siteBiTopParam.getEndTime() - siteBiTopParam.getStartTime()) / 86400000;
                long dailySecond = p.getDuration() / dayCount;
                p.setDailyChargeTime(dailySecond);
            } else {
                p.setDailyChargeTime(0L);
            }
        }

        return result;
    }

    @PostMapping("/breakdownBi")
    @Operation(summary = "枪故障次数折线图数据获取")
    public ListResponse<SiteErrorCount> breakdownBi(
        @RequestBody SiteBiParam siteBiParam,
        ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false) +
            " param = " + JsonUtils.toJsonString(siteBiParam));
        siteBiParam = this.increaseByUnit(siteBiParam);
        return ostFeignClient.breakdownBi(siteBiParam);
    }

    @PostMapping("/plugBreakdownBi")
    @Operation(summary = "枪故障次数排行榜获取")
    public ListResponse<PlugErrorCount> plugBreakdownBi(
        @RequestBody SiteBiTopParam siteBiTopParam,
        ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false) +
            " param = " + JsonUtils.toJsonString(siteBiTopParam));
        this.increaseByUnit(siteBiTopParam);
        return ostFeignClient.plugBreakdownBi(siteBiTopParam);
    }

    /**
     * 结束时间根据查询单位加一
     *
     * @param siteBiParam
     * @return
     */
    private SiteBiParam increaseByUnit(SiteBiParam siteBiParam) {
        if (siteBiParam != null &&
            siteBiParam.getSampleType() != null &&
            siteBiParam.getEndTime() != null) {
            int field = 0;
            if (SiteBiSampleType.DAY.equals(siteBiParam.getSampleType())) {
                field = Calendar.DATE;
            } else if (SiteBiSampleType.MONTH.equals(siteBiParam.getSampleType())) {
                field = Calendar.MONTH;
            } else if (SiteBiSampleType.HOUR.equals(siteBiParam.getSampleType())) {
                field = Calendar.HOUR;
            } else {
                log.error("未处理的类型: {}", siteBiParam.getSampleType());
                return siteBiParam;
            }

            // 结束时间根据查询单位加一
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date(siteBiParam.getEndTime()));
            calendar.add(field, 1);
            siteBiParam.setEndTime(calendar.getTime().getTime());

            return siteBiParam;
        } else {
            log.error("参数不正确: {}", siteBiParam);
        }
        return siteBiParam;
    }

    @PostMapping("/getSummaryListBySite")
    @Operation(summary = "按照场站获取一定时间段内电量，费用的统计，充电管理平台报表使用")
    public ObjectResponse<BiSiteSummaryPo> getSummaryListBySite(
        @RequestBody SiteBiParam siteBiParam,
        ServerHttpRequest request) {
        siteBiParam = this.increaseByUnit(siteBiParam);
        siteBiParam.setCommIdChain(super.getCommIdChain2(request));
        return ostFeignClient.getSummaryListBySite(siteBiParam);
    }

    @Operation(summary = "按照场站汇总订单信息")
    @PostMapping("/getBiSiteList")
    public ListResponse<BiSiteSummaryPo> getBiSiteList(
        @RequestBody SiteBiParam param,
        ServerHttpRequest request) {
        log.info("param = {}", param);
        param = this.increaseByUnit(param);
        List<String> gidList = AntRestUtils.getSysUserGids(request);
        if (CollectionUtils.isNotEmpty(gidList)) {
            param.setGids(gidList);
        } else {
            param.setCommIdChain(super.getCommIdChain2(request));
        }
        return ostFeignClient.getBiSiteList(param);
    }

    @Operation(summary = "抄表统计信息")
    @PostMapping("/getBiSiteMeterRecord")
    public ListResponse<BiSiteMeterSummaryDto> getBiSiteMeterRecord(
        @RequestBody MeterRecordBiParam param,
        ServerHttpRequest request) {
        log.info("param = {}", param);
        this.increaseByUnit(param);
        param.setCommIdChain(super.getCommIdChain2(request));
        return meterFeignClient.getBiSiteMeterRecord(param);
    }

    @Operation(summary = "导出抄表统计信息")
    @PostMapping("/exportBiSiteMeterRecord")
    public Mono<ObjectResponse<ExcelPosition>> exportBiSiteMeterRecord(
        @RequestBody MeterRecordBiParam param,
        ServerHttpRequest request) {
        log.info("param = {}", param);
        this.increaseByUnit(param);
        param.setCommIdChain(super.getCommIdChain2(request));

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("抄表统计")
            .setFunctionMap(DownloadFunctionType.SITE_METER_BI)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        ObjectResponse<ExcelBiPosition> res = ostFeignClient.exportBiSiteMeterRecord(param);
//        FeignResponseValidate.check(res);
//        return res;
    }

    @Operation(summary = "按照企业汇总订单信息")
    @PostMapping("/getBiCorpList")
    public ListResponse<BiSiteSummaryPo> getBiCorpList(
        @RequestBody SiteBiParam param,
        ServerHttpRequest request) {
        log.info("param = {}", param);
        param = this.increaseByUnit(param);
        param.setCommIdChain(super.getCommIdChain2(request));
        return ostFeignClient.getBiCorpList(param);
    }

    @Operation(summary = "按照当前结果导出查询数据--按照企业汇总")
    @PostMapping("/exportBiCorpList")
    public Mono<ObjectResponse<ExcelPosition>> exportBiCorpList(
        @RequestBody SiteBiParam param,
        ServerHttpRequest request) {
        log.info("param = {}", param);
        param = this.increaseByUnit(param);
        param.setCommIdChain(super.getCommIdChain2(request));

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName(param.getExFileName())
            .setFunctionMap(DownloadFunctionType.KWH_BI_CORP)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        ObjectResponse<ExcelBiPosition> res = ostFeignClient.exportBiCorpList(param);
//        FeignResponseValidate.check(res);
//        return res;
    }

    @Operation(summary = "按照当前结果导出查询数据--按照站点汇总")
    @PostMapping("/exportBiSiteList")
    public Mono<ObjectResponse<ExcelPosition>> exportBiSiteList(
        @RequestBody SiteBiParam param,
        ServerHttpRequest request) {
        log.info("param = {}", param);

        param = this.increaseByUnit(param);
        param.setCommIdChain(super.getCommIdChain2(request));

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName(param.getExFileName())
            .setFunctionMap(DownloadFunctionType.KWH_BI_SITE)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        ObjectResponse<ExcelBiPosition> res = ostFeignClient.exportBiSiteList(param);
//        FeignResponseValidate.check(res);
//        return res;
    }

    @Operation(summary = "导出消费汇总--按照场站汇总")
    @PostMapping("/exportBiFeeBySiteList")
    public Mono<ObjectResponse<ExcelPosition>> exportBiFeeBySiteList(
        @RequestBody SiteBiParam param,
        ServerHttpRequest request) {
        log.info("param = {}", param);

        param = this.increaseByUnit(param);
        param.setCommIdChain(super.getCommIdChain2(request));

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName(param.getExFileName())
            .setFunctionMap(DownloadFunctionType.FEE_BI_SITE_TOTAL)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        ObjectResponse<ExcelBiPosition> res = ostFeignClient.exportBiFeeBySiteList(param);
//        FeignResponseValidate.check(res);
//        return res;
    }

    @Operation(summary = "导出消费汇总--按照场站汇总")
    @PostMapping("/exportBiElectBySiteList")
    public Mono<ObjectResponse<ExcelPosition>> exportBiElectBySiteList(
        @RequestBody SiteBiParam param,
        ServerHttpRequest request) {
        log.info("param = {}", param);

        param = this.increaseByUnit(param);
        param.setCommIdChain(super.getCommIdChain2(request));

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName(param.getExFileName())
            .setFunctionMap(DownloadFunctionType.KWH_BI_SITE_TOTAL)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        ObjectResponse<ExcelBiPosition> res = ostFeignClient.exportBiElectBySiteList(param);
//        FeignResponseValidate.check(res);
//        return res;
    }

    @Operation(summary = "导出消费汇总--按照企业汇总")
    @PostMapping("/exportBiFeeByCorpList")
    public Mono<ObjectResponse<ExcelPosition>> exportBiFeeByCorpList(
        @RequestBody SiteBiParam param,
        ServerHttpRequest request) {
        log.info("param = {}", param);

        param = this.increaseByUnit(param);
        param.setCommIdChain(super.getCommIdChain2(request));

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName(param.getExFileName())
            .setFunctionMap(DownloadFunctionType.FEE_BI_CORP_TOTAL)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        ObjectResponse<ExcelBiPosition> res = ostFeignClient.exportBiFeeByCorpList(param);
//        FeignResponseValidate.check(res);
//        return res;
    }

    @Operation(summary = "导出电量汇总--按照企业汇总")
    @PostMapping("/exportBiElectByCorpList")
    public Mono<ObjectResponse<ExcelPosition>> exportBiElectByCorpList(
        @RequestBody SiteBiParam param,
        ServerHttpRequest request) {
        log.info("param = {}", param);

        param = this.increaseByUnit(param);
        param.setCommIdChain(super.getCommIdChain2(request));

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName(param.getExFileName())
            .setFunctionMap(DownloadFunctionType.KWH_BI_CORP_TOTAL)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        ObjectResponse<ExcelBiPosition> res = ostFeignClient.exportBiElectByCorpList(param);
//        FeignResponseValidate.check(res);
//        return res;
    }

    @PostMapping("/meterReadingBi")
    @Operation(summary = "电表抄表柱状图")
    public ListResponse<MeterReadingBi> meterReadingBi(
        @RequestBody SiteBiParam siteBiTopParam,
        ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false) +
            "抄表电损柱状图 param = {}" + JsonUtils.toJsonString(siteBiTopParam));
        this.increaseByUnit(siteBiTopParam);
        return ostFeignClient.meterReadingBi(siteBiTopParam);
    }

    @PostMapping("/meterReadingTopBi")
    @Operation(summary = "电表抄表电表排行")
    public ListResponse<MeterReadingTopVo> meterReadingTopBi(
        @RequestBody SiteBiParam siteBiTopParam,
        ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false) +
            "抄表电损电表排行 param = {}" + JsonUtils.toJsonString(siteBiTopParam));
        this.increaseByUnit(siteBiTopParam);
        return ostFeignClient.meterReadingTopBi(siteBiTopParam);
    }

    @GetMapping("/getSiteDailyPowerLine")
    @Operation(summary = "获取场站日充电功率曲线")
    public ObjectResponse<SiteDailyBiPo> getSiteDailyPowerLine(
        ServerHttpRequest request,
        @RequestParam("siteId") String siteId,
        @RequestParam("date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        log.info(LoggerHelper2.formatEnterLog(request, true));
        return ostFeignClient.getSiteDailyPowerLine(siteId, date);
    }

    @Schema(description = "场站消费统计")
    @PostMapping(value = "/biSiteOrderList")
    public Mono<ListResponse<BiSiteOrderVo>> findBiSiteOrderList(
        ServerHttpRequest request,
        @RequestBody ListBiSiteOrderParam param) {
        log.info("获取场站消费统计: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        List<String> gidList = AntRestUtils.getSysUserGids(request);
        if (CollectionUtils.isNotEmpty(gidList)) {
            param.setGidList(gidList);
        } else {
            param.setCommIdChain(AntRestUtils.getCommIdChain(request));
        }
        return biSiteOrderFeignClient.findBiSiteOrderList(param);
    }

    @Schema(description = "客群趋势数据获取")
    @PostMapping(value = "/biSiteOrderAccount")
    public Mono<ObjectResponse<SiteOrderAccountBi>> getBiSiteOrderAccount(
        ServerHttpRequest request, @RequestBody SiteBiParam param) {
        log.info("客群趋势数据获取: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return Mono.just(param)
            .doOnNext(p -> {
                if (null == param.getSampleType()) {
                    throw new DcArgumentException("采样时间需要提供");
                }

                // 结束时间调整
                if (null != p.getToTime()) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(p.getToTime());
                    calendar.add(SiteBiSampleType.MONTH.equals(param.getSampleType()) ?
                        Calendar.MONTH : Calendar.DATE, 1);
                    p.setToTime(calendar.getTime());
                }
            })
            .doOnNext(p -> {
                if (StringUtils.isBlank(p.getSiteId())) {
                    throw new DcArgumentException("请提供场站ID");
                }

                if (null == param.getFromTime() || null == param.getToTime()) {
                    throw new DcArgumentException("开始结束时间没有提供");
                }

                if (param.getFromTime().getTime() >= param.getToTime().getTime()) {
                    throw new DcArgumentException("开始时间大于等于结束时间，不合理");
                }

                if (SiteBiParam.reviseResult(param).size() > Constant.MAX_SAMPLE_SIZE) {
                    throw new DcArgumentException("时间分片大于最大值，请传入正确的开始/结束时间");
                }
            })
            .flatMap(biSiteOrderFeignClient::getBiSiteOrderAccount);
    }

    @Schema(description = "互联企客趋势数据获取")
    @PostMapping(value = "/biSiteOrderHlhtAccount")
    public Mono<ObjectResponse<SiteOrderHlhtAccountBi>> getBiSiteOrderHlhtAccount(
        ServerHttpRequest request, @RequestBody SiteBiParam param) {
        log.info("互联企客趋势数据获取: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return Mono.just(param)
            .doOnNext(p -> {
                if (null == param.getSampleType()) {
                    throw new DcArgumentException("采样时间需要提供");
                }

                // 结束时间调整
                if (null != p.getToTime()) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(p.getToTime());
                    calendar.add(SiteBiSampleType.MONTH.equals(param.getSampleType()) ?
                        Calendar.MONTH : Calendar.DATE, 1);
                    p.setToTime(calendar.getTime());
                }
            })
            .doOnNext(p -> {
                if (StringUtils.isBlank(p.getSiteId())) {
                    throw new DcArgumentException("请提供场站ID");
                }

                if (null == param.getFromTime() || null == param.getToTime()) {
                    throw new DcArgumentException("开始结束时间没有提供");
                }

                if (param.getFromTime().getTime() >= param.getToTime().getTime()) {
                    throw new DcArgumentException("开始时间大于等于结束时间，不合理");
                }

                if (SiteBiParam.reviseResult(param).size() > Constant.MAX_SAMPLE_SIZE) {
                    throw new DcArgumentException("时间分片大于最大值，请传入正确的开始/结束时间");
                }
            })
            .flatMap(biSiteOrderFeignClient::getBiSiteOrderHlhtAccount);
    }
}