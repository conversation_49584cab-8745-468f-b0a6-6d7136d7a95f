package com.cdz360.biz.ant.domain.request;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.type.EvseBizStatus;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.utils.StringUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListChargerParam extends BaseListParam {

    String commercialId;
    List<String> siteId;

    List<String> gidList;
    String plugNo;
    //@RequestParam(value = "connectorId", required = false)
    String connectorId;
    //    @RequestParam(value = "supplyType", required = false)
    SupplyType supplyType;
    //    @RequestParam(value = "serialNumber", required = false)
    String serialNumber;
    //    @RequestParam(value = "plugStatus", required = false)
    PlugStatus plugStatus;
    //    @RequestParam(value = "jobName", required = false)
    String jobName;
    Long transformerId;

    Boolean byUpdateTime;
//    Integer _index;
//    Integer _size;

    @Schema(description = "场站运营状态")
    @JsonInclude(Include.NON_NULL)
    private List<EvseBizStatus> bizStatusList;

    public void setSiteId(String siteId) {
        if (StringUtils.isNotBlank(siteId)) {
            this.siteId = List.of(siteId);
        }
    }

    public void setGidList(String gidList) {
        if (StringUtils.isNotBlank(gidList)) {
            this.gidList = List.of(gidList.split(","));
        }
    }



    public void setSiteId(List<String> siteIdList) {
        this.siteId = siteIdList;
    }
}
