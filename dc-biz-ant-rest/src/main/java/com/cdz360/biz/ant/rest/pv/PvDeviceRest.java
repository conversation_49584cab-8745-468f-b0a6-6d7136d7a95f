package com.cdz360.biz.ant.rest.pv;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.service.pv.PvDeviceService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.iot.dto.UpdateCtrlDto;
import com.cdz360.biz.model.iot.param.ListCtrlParam;
import com.cdz360.biz.model.iot.param.ListGtiParam;
import com.cdz360.biz.model.iot.vo.GtiVo;
import com.cdz360.biz.model.iot.vo.GwInfoVo;
import com.cdz360.biz.model.trading.iot.dto.CntCtrlGtiDto;
import com.cdz360.biz.model.trading.iot.vo.GtiDataInTimeVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * PvDeviceRest
 *
 * @since 8/30/2021 4:30 PM
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "光伏设备信息相关接口", description = "光伏设备信息相关接口")
@RequestMapping(value = "/api/pvDevice")
public class PvDeviceRest {

    @Autowired
    private PvDeviceService pvDeviceService;

    @Operation(summary = "控制器列表")
    @PostMapping(value = "/findCtrlList")
    public Mono<ListResponse<GwInfoVo>> findCtrlList(ServerHttpRequest request,
        @RequestBody ListCtrlParam param) {
        log.info("findCtrlList {}", JsonUtils.toJsonString(param));
        String commIdChain = AntRestUtils.getCommIdChain(request);
        if (null == commIdChain) {
            throw new DcServiceException("未登录状态");
        }
        param.setCommIdChain(commIdChain);
        return pvDeviceService.findCtrlList(param);
    }

    @Operation(summary = "获取控制器下的逆变器列表")
    @PostMapping(value = "/findGtiList")
    public Mono<ListResponse<GtiVo>> findGtiList(ServerHttpRequest request,
        @RequestBody ListGtiParam param) {
        log.info("获取控制器下的逆变器列表: param = {}", JsonUtils.toJsonString(param));
        String commIdChain = AntRestUtils.getCommIdChain(request);
        if (null == commIdChain) {
            throw new DcServiceException("未登录状态");
        }
        param.setCommIdChain(commIdChain);
        return pvDeviceService.findGtiList(param);
    }

    @Operation(summary = "新增控制器信息")
    @PostMapping(value = "/addCtrl")
    public Mono<BaseResponse> addCtrl(@RequestBody UpdateCtrlDto param) {
        log.info("新增控制器信息: param = {}", JsonUtils.toJsonString(param));
        return pvDeviceService.addCtrl(param);
    }

    @Operation(summary = "更新控制器信息")
    @PostMapping(value = "/updateCtrl")
    public Mono<BaseResponse> updateCtrl(@RequestBody UpdateCtrlDto param) {
        log.info("更新控制器信息: param = {}", JsonUtils.toJsonString(param));
        return pvDeviceService.updateCtrl(param);
    }

    @Operation(summary = "获取控制器信息")
    @PostMapping(value = "/getCtrl")
    public Mono<ObjectResponse<GwInfoVo>> getCtrl(
        @Parameter(name = "微网控制器编号") @RequestParam(value = "gwno") String gwno) {
        log.info("获取控制器信息: gwno = {}", gwno);
        return pvDeviceService.getCtrl(gwno);
    }

    @Operation(summary = "删除场站控制器")
    @PostMapping(value = "/removeCtrl")
    public Mono<BaseResponse> removeCtrl(
        @Parameter(name = "场站ID", required = true) @RequestParam(value = "siteId") String siteId,
        @Parameter(name = "控制器编号(网关编号)", required = true) @RequestParam(value = "gwno") String gwno) {
        log.info("删除场站控制器: siteId = {}, gwno = {}", siteId, gwno);
        return pvDeviceService.removeCtrl(siteId, gwno);
    }

    @Operation(summary = "下发逆变器配置信息")
    @PostMapping(value = "/sendModifyGtiCfgCmd")
    public Mono<BaseResponse> sendModifyGtiCfgCmd(
        @Parameter(name = "微网控制器编号", required = true) @RequestParam(value = "gwno") String gwno,
        @Parameter(name = "逆变器编号", required = true) @RequestParam(value = "dno") String dno,
        @Parameter(name = "配置模板ID", required = true) @RequestParam(value = "cfgId") Long cfgId) {
        log.info("下发逆变器配置信息。gwno = {}, dno = {}, cfgId = {}", gwno, dno, cfgId);
        return pvDeviceService.sendModifyGtiCfgCmd(gwno, dno, cfgId);
    }

    @Operation(summary = "光伏逆变器实时数据")
    @GetMapping(value = "/gtiInfoInTime")
    public Mono<ObjectResponse<GtiDataInTimeVo>> gtiInfoInTime(
        @Parameter(name = "逆变器设备编号", required = true) @RequestParam(value = "dno") String dno) {
        log.info("光伏逆变器实时数据: dno = {}", dno);
        return pvDeviceService.gtiInfoInTime(dno);
    }

    @Operation(summary = "统计光伏站控制器/逆变器数量")
    @GetMapping(value = "/countCtrlGti")
    public Mono<ObjectResponse<CntCtrlGtiDto>> countCtrlGti(
        @Parameter(name = "场站ID", required = true) @RequestParam("siteId") String siteId) {
        log.info("统计光伏站控制器/逆变器数量: siteId = {}", siteId);
        return pvDeviceService.countCtrlGti(siteId);
    }
}