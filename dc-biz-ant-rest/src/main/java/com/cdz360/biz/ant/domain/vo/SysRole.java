package com.cdz360.biz.ant.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.Set;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;

@Data
@ToString(callSuper = true)
public class SysRole {
    private Long id;
    private Integer pid;
    @NotNull
    private String name;
    private Integer category;

    /**
     * AppClientType
     */
    @Schema(description = "归属平台. 0,未知; 20, 充电管理平台; 21, 运营支撑平台")
    private Integer platform;
    private String categoryName;
    private String tips;
    private Integer status;
    private Integer version;
    private Long corpId;

    private Long commId;
    private Long topCommId;

    //全部权限
    private Boolean svaha;

    private String extra;
    private Set<Long> menuIds;
//    private List<SysSystem> systemList;

    /**
     * 是否为新添加的类别
     * {@link categoryName}
     */
    private Boolean isAddNewCategory;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expireTime;

}
