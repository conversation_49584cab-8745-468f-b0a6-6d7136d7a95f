package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.iot.type.SiteBiSampleType;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.ess.vo.DayEssDataBi;
import com.cdz360.biz.model.trading.iot.param.PvProfitTrendParam;
import com.cdz360.biz.model.trading.site.param.RecordParam;
import com.cdz360.biz.model.trading.site.param.SiteReportParam;
import com.cdz360.biz.model.trading.site.vo.BiSiteGcMonthExpenseStatisticVo;
import com.cdz360.biz.model.trading.site.vo.BiSiteGcMonthIncomeVo;
import com.cdz360.biz.model.trading.site.vo.SiteBaseInfoVo;
import com.cdz360.biz.model.trading.yw.param.ListYwOrderParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

import java.util.List;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_BI,
    fallbackFactory = AsyncBizBiFeignHystrix.class)
public interface AsyncBizBiFeignClient {

    @Deprecated
    // 导出运维工单详情
    @GetMapping(value = "/bi/pdf/exportYwOrder")
    Mono<ObjectResponse<ExcelPosition>> exportYwOrder(@RequestParam("ywOrderNo") String ywOrderNo);

    @Deprecated
    // 运维工单列表导出到EXCEL
    @PostMapping(value = "/bi/excel/exportYwOrderExcel")
    Mono<ObjectResponse<ExcelPosition>> exportYwOrderExcel(@RequestBody ListYwOrderParam param);

    @Deprecated
    // 巡检工单列表导出(EXCEL)
    @PostMapping(value = "/bi/excel/exportInspectionRecExcel")
    Mono<ObjectResponse<ExcelPosition>> exportInspectionRecExcel(@RequestBody RecordParam param);

    @Deprecated(since = "20220506")
    // 巡检工单列表导出(EXCEL)
    @PostMapping(value = "/bi/excel/exportPowerProfitTrendExcel")
    Mono<ObjectResponse<ExcelPosition>> exportPowerProfitTrendExcel(
        @RequestBody PvProfitTrendParam param);


    @Deprecated(since = "20220506")
    // 场站各天储能汇总数据到Excel
    @PostMapping(value = "/bi/ess/exportSiteEssBiExcel")
    Mono<ObjectResponse<ExcelPosition>> exportSiteEssBiExcel(
        @RequestParam("sampleType") SiteBiSampleType sampleType,
        @RequestBody List<DayEssDataBi> data);

    /**
     * 场站月运营数据
     *
     * @param param
     * @return
     */
    @GetMapping(value = "/bi/site/getSiteMonthSurvey")
    Mono<ListResponse<BiSiteGcMonthIncomeVo>> getSiteMonthSurvey(
        @RequestBody SiteReportParam param);

    /**
     * 运营场站月支出
     *
     * @param param
     * @return
     */
    @GetMapping("/bi/site/getSiteExpenseMonthly")
    Mono<ListResponse<BiSiteGcMonthExpenseStatisticVo>> getSiteExpenseMonthly(
        @RequestBody SiteReportParam param);

    /**
     * 场站基础信息
     *
     * @param param
     * @return
     */
    @GetMapping("/bi/site/getSiteBaseInfo")
    Mono<ObjectResponse<SiteBaseInfoVo>> getSiteBaseInfo(@RequestBody SiteReportParam param);
}
