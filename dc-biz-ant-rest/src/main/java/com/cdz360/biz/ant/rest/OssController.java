package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.model.oss.OssStsDto;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Tag(name = "文件上传", description = "文件上传")
public class OssController {

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Operation(summary = "获取文件上传的STS信息")
    @GetMapping(value = "/api/oss/getSts")
    public ObjectResponse<OssStsDto> getSts(ServerHttpRequest request) {
        log.trace(LoggerHelper2.formatEnterLog(request, false));
        return dataCoreFeignClient.getSts();
    }

    @Operation(summary = "获取文件上传的STS信息(Private)")
    @GetMapping(value = "/api/oss/getPrivateSts")
    public ObjectResponse<OssStsDto> getPrivateSts(ServerHttpRequest request) {
        log.trace(LoggerHelper2.formatEnterLog(request, false));
        return dataCoreFeignClient.getPrivateSts();
    }
}
