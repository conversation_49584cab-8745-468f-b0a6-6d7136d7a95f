package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.domain.request.CardRequest;
import com.cdz360.biz.ant.domain.request.CardRequestVo;
import com.cdz360.biz.ant.domain.vo.AccountRemainInfo;
import com.cdz360.biz.ant.domain.vo.CardListdetailVO;
import com.cdz360.biz.ant.domain.vo.CorpAuthVO;
import com.cdz360.biz.ant.domain.vo.CorpLoginVo;
import com.cdz360.biz.ant.feign.AntUserFeignClient;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.MerchantFeignClient;
import com.cdz360.biz.ant.feign.TradingFeignClient;
import com.cdz360.biz.ant.service.BlocUserService;
import com.cdz360.biz.ant.service.CardService;
import com.cdz360.biz.ant.service.CorpService;
import com.cdz360.biz.ant.service.LoginService;
import com.cdz360.biz.ant.service.order.ChargerOrderService;
import com.cdz360.biz.ant.service.sysLog.CorpSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.ant.utils.FileUtil;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.cus.corp.dto.BatchAddCreditResultItem;
import com.cdz360.biz.model.cus.corp.dto.BatchOpResult;
import com.cdz360.biz.model.cus.corp.param.BatchAddCreditUserParam;
import com.cdz360.biz.model.cus.corp.param.BatchDisableCreditUser;
import com.cdz360.biz.model.cus.corp.param.BatchModifyVinParam;
import com.cdz360.biz.model.cus.corp.po.CorpOrgLoginVo;
import com.cdz360.biz.model.cus.corp.po.CorpOrgPo;
import com.cdz360.biz.model.cus.corp.type.LimitCycle;
import com.cdz360.biz.model.cus.corp.vo.CorpOrgVO;
import com.cdz360.biz.model.cus.corp.vo.CorpVo;
import com.cdz360.biz.model.cus.settlement.vo.CorpSettlementCfgVo;
import com.cdz360.biz.model.cus.soc.param.QueryStrategyParam;
import com.cdz360.biz.model.cus.soc.param.SocStrategyDict;
import com.cdz360.biz.model.cus.soc.vo.UserSocStrategyCreditCusVo;
import com.cdz360.biz.model.cus.soc.vo.UserSocStrategyVinVo;
import com.cdz360.biz.model.trading.order.param.ListPayBillParam;
import com.cdz360.biz.model.trading.order.po.ChargerOrderTimeDivision;
import com.cdz360.biz.model.trading.order.vo.PayBillRefundVo;
import com.cdz360.biz.model.trading.order.vo.PayBillVo;
import com.cdz360.biz.model.trading.site.vo.CorpOrderCountVo;
import com.cdz360.biz.utils.feign.order.DataCoreOrderFeignClient;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.CorpListPointLogParam;
import com.chargerlinkcar.framework.common.domain.OrderCountParam;
import com.chargerlinkcar.framework.common.domain.PointLog;
import com.chargerlinkcar.framework.common.domain.PointLogBi;
import com.chargerlinkcar.framework.common.domain.RBlocUserListParam;
import com.chargerlinkcar.framework.common.domain.order.ChargerOrderWithBLOBs;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import com.chargerlinkcar.framework.common.domain.pay.PaySign;
import com.chargerlinkcar.framework.common.domain.type.PointOpType;
import com.chargerlinkcar.framework.common.domain.vo.Card;
import com.chargerlinkcar.framework.common.domain.vo.ChargerDetailVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDataVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import com.chargerlinkcar.framework.common.domain.vo.CorpCreditAccountEx;
import com.chargerlinkcar.framework.common.domain.vo.CorpCreditAccountVo;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.domain.vo.SiteSelectInfoVo;
import com.chargerlinkcar.framework.common.domain.vo.TradeDataVo;
import com.chargerlinkcar.framework.common.domain.vo.VinDto;
import com.chargerlinkcar.framework.common.domain.vo.VinParam;
import com.chargerlinkcar.framework.common.domain.vo.VinParamVo;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.service.DcCusBalanceService;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.base.Joiner;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @since 2019/12/11 14:07
 */
@RestController
@Slf4j
@Tag(name = "企业客户平台", description = "企业客户平台")
public class CorpRest extends BaseController {

    @Autowired
    private ChargerOrderService chargerOrderService;
    @Autowired
    private BlocUserService iBlocUserService;
    @Autowired
    private OrderRest orderRest;
    @Autowired
    private CorpService iCorpService;
    @Autowired
    private LoginService loginService;
    @Autowired
    private CardService cardService;
    @Autowired
    private AntUserFeignClient userFeignClient;
    @Autowired
    private MerchantFeignClient merchantFeignClient;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private ChargerOrderService chargerOrderServiceImpl;

    @Autowired
    private DcCusBalanceService dcCusBalanceService;
    @Autowired
    private CorpSysLogService corpSysLogService;

    @Autowired
    private TradingFeignClient tradingFeignClient;

    @Autowired
    private BlocUserService blocUserService;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private DataCoreOrderFeignClient dataCoreOrderFeignClient;

    @GetMapping("/api/corp/getOrgTree")
    @Operation(summary = "组织树形分页")
    public ListResponse<CorpOrgVO> getOrgTree(
        ServerWebExchange exh,
        @RequestParam(value = "_size", required = false) String size,
        @RequestParam(value = "_index", required = false) String _index,
        ServerHttpRequest request) {
        OldPageParam pageVo = getPage2(request, exh, false);
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        return iCorpService.getOrgTree(corpOrgLoginVo.getCorpPo().getId(), pageVo);
    }

    @GetMapping("/api/corp/getOrgList")
    @Operation(summary = "组织列表")
    public ListResponse<CorpOrgVO> getOrgList(ServerHttpRequest request,
        ServerWebExchange exh) {
        OldPageParam pageVo = getPage2(request, exh, false);
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        pageVo.setPageSize(100);
        return iCorpService.getOrgList(super.getToken2(request), corpOrgLoginVo.getCorpId(),
            pageVo);
    }

    @GetMapping("/api/corp/getOrgTreeAll")
    @Operation(summary = "组织列表不分页")
    public ListResponse<CorpOrgVO> getOrgTreeAll(ServerHttpRequest request,
        ServerWebExchange exh) {
        OldPageParam pageVo = getPage2(request, exh, false);
        //单企业组织上限100个
        pageVo.setPageSize(100);
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        return userFeignClient.getOrgTree(corpOrgLoginVo.getCorpPo().getId(), pageVo.getPageNum(),
            pageVo.getPageSize());
    }

    @GetMapping("/api/corp/getOrgByLevel")
    @Operation(summary = "组织列表")
    public ListResponse<CorpOrgVO> getOrgByLevel(@RequestParam("orgLevel") Integer orgLevel,
        ServerHttpRequest request) {
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        return authCenterFeignClient.getOrgByLevel(super.getToken2(request),
            corpOrgLoginVo.getCorpId(), orgLevel);
    }

    @GetMapping("/api/corp/getOrgListById")
    @Operation(summary = "通过组织id模糊匹配")
    public ListResponse<CorpOrgVO> getOrgListById(
        ServerWebExchange exh,
        @RequestParam("id") Long id, ServerHttpRequest request) {
        OldPageParam pageVo = getPage2(request, exh, false);
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        return userFeignClient.getOrgById(corpOrgLoginVo.getCorpPo().getId(), id,
            pageVo.getPageNum(), pageVo.getPageSize());
    }

    @PostMapping(value = "/api/corp/addUpdateCorpOrg")
    @Operation(summary = "新增组织")
    public BaseResponse addCorpOrg(@RequestBody CorpOrgPo corpOrgPo, ServerHttpRequest request) {
        CorpOrgLoginVo vo = getCorpByRequest(request);
        corpOrgPo.setCorpId(vo.getCorpId());
//        corpOrgPo.setL1Id(vo.getL1Id());
        BaseResponse res = authCenterFeignClient.addOrUpdateCorpOrg(super.getToken2(request),
            corpOrgPo);
        this.refreshTokenValue(request, vo.getAccount());
        corpSysLogService.addOrg(corpOrgPo.getOrgName(), request);
        return res;
    }

    @PutMapping(value = "/api/corp/addUpdateCorpOrg")
    @Operation(summary = "编辑组织")
    public BaseResponse UpdateCorpOrg(@RequestBody CorpOrgPo corpOrgPo, ServerHttpRequest request) {
        CorpOrgLoginVo vo = getCorpByRequest(request);
        corpOrgPo.setCorpId(vo.getCorpPo().getId());
        BaseResponse res = authCenterFeignClient.addOrUpdateCorpOrg(super.getToken2(request),
            corpOrgPo);
        this.refreshTokenValue(request, vo.getAccount());
        corpSysLogService.editOrg(corpOrgPo.getOrgName(), request);
        return res;
    }

    @PostMapping("/api/corp/addUpdateCorpCreditAccount")
    @Operation(summary = "新增/更新/逻辑删除授信账户")
    public BaseResponse addUpdateCorpCreditAccount(
        @RequestBody CorpCreditAccountVo corpCreditAccountVo,
        ServerHttpRequest request) {
        ///ant/api/rblocUser/updateRBlocUser
        // /ant/api/rblocUser/deleteRBlocUserById?rBlocUserId=766
        log.info("新增/更新/逻辑删除授信账户: {}", JsonUtils.toJsonString(corpCreditAccountVo));
        IotAssert.isTrue(StringUtils.isNotBlank(corpCreditAccountVo.getType()), "操作类型不能为空");

        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        IotAssert.isNotNull(corpOrgLoginVo, "请登陆后再尝试。");
        Long corpId = corpOrgLoginVo.getCorpPo().getId();
        IotAssert.isNotNull(corpId, "无法获取登陆id，请登陆后再尝试。");
        Long commId = corpOrgLoginVo.getCorpPo().getCommId();
        IotAssert.isNotNull(commId, "无法获取商户id，请登陆后再尝试。");

        CorpCreditAccountEx corpCreditAccountEx = new CorpCreditAccountEx();
        BeanUtils.copyProperties(corpCreditAccountVo, corpCreditAccountEx);
        corpCreditAccountEx.setCorpId(corpId).setCommId(commId);

        ObjectResponse<Long> res = userFeignClient.upserdelRBlocUser(corpCreditAccountEx);
        FeignResponseValidate.check(res);
        log.info("操作授信账户结果: {}", JsonUtils.toJsonString(res));

        IotAssert.isTrue(!res.getData().equals(0), "操作授信账户失败，请联系管理员。");
        corpSysLogService.addUpdateCorpCreditAccountLog(corpCreditAccountVo.getPhone(),
            corpCreditAccountVo.getType(), request);
        return BaseResponse.success();
    }

    @PostMapping("/api/corp/getCorpCreditAccountList")
    @Operation(summary = "授信账户列表")
    public ListResponse<CorpCreditAccountVo> getRBlocUserList(
        @RequestBody RBlocUserListParam rBlocUserListParam,
        ServerHttpRequest request) {
        log.info("授信账户列表: {}", JsonUtils.toJsonString(rBlocUserListParam));

        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);

        IotAssert.isNotNull(corpOrgLoginVo, "请登陆后再尝试。");
        Long corpId = corpOrgLoginVo.getCorpId();
        IotAssert.isNotNull(corpId, "无法获取登陆id，请登陆后再尝试。");
        Integer commId = corpOrgLoginVo.getCommId();
        IotAssert.isNotNull(commId, "无法获取商户id，请登陆后再尝试。");

        if (rBlocUserListParam.getStart() == null) {
            rBlocUserListParam.setStart(1L);
        }
        if (rBlocUserListParam.getSize() == null) {
            rBlocUserListParam.setSize(10);
        }

        //条件查询corpIds
        if (rBlocUserListParam.getCorpOrgId() != null) {
            rBlocUserListParam.setCorpOrgIds(rBlocUserListParam.getCorpOrgId().toString());
        } else {
            rBlocUserListParam.setCorpOrgIds(Joiner.on(",").join(corpOrgLoginVo.getOrgIds()));
        }

        rBlocUserListParam.setUserParams(4);
        ObjectResponse<CorpVo> corpRes = this.iCorpService.getCorpVo(corpOrgLoginVo.getCorpId());
        FeignResponseValidate.check(corpRes);
        ListResponse<com.chargerlinkcar.framework.common.domain.vo.RBlocUser> list = userFeignClient.getRBlocUserList(
            rBlocUserListParam.getStart().intValue(),
            rBlocUserListParam.getSize(),
            rBlocUserListParam.getUserParams(),
            rBlocUserListParam.getSk(),
            corpId,
            null,
            rBlocUserListParam.getCorpOrgIds());
        FeignResponseValidate.check(list);

        List<Long> payAccountIdList = list.getData()
            .stream()
            .map(RBlocUser::getId)
            .collect(Collectors.toList());
        Map<Long, CorpOrderCountVo> orderPayCountMap = new HashMap<>();
        if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(payAccountIdList)) {
            OrderCountParam orderCountParam = new OrderCountParam();
            orderCountParam.setCorpId(corpId).setPayAccountIdList(payAccountIdList);
            ListResponse<CorpOrderCountVo> orderPayCountRes = iCorpService.getOrderPayCount(
                orderCountParam);
            FeignResponseValidate.check(orderPayCountRes);

            orderPayCountRes.getData()
                .stream()
                .forEach(e -> {
                    orderPayCountMap.put(e.getPayAccountId(), e);
                });
        }

        List<CorpCreditAccountVo> retList = list.getData().stream().map(e -> {
            CorpCreditAccountVo ret = new CorpCreditAccountVo();
            ret.setId(e.getId());
            ret.setCorpOrgId(e.getCorpOrgId());
            ret.setCorpOrgName(e.getOrgName());
            ret.setPhone(e.getPhone());
            ret.setUserName(e.getName());
            ret.setStatus(Integer.valueOf(1).equals(e.getStatus()));
            ret.setLimitMoney(e.getLimitMoney() == null ? BigDecimal.ZERO : e.getLimitMoney());
            ret.setLimitCycle(e.getLimitCycle());
            if (LimitCycle.NONCYCLE.equals(e.getLimitCycle())) {
                // 无周期，已使用金额 = 历史订单金额
                ret.setUsedAmount(orderPayCountMap.get(e.getId()) == null ?
                    BigDecimal.ZERO :
                    orderPayCountMap.get(e.getId()).getPaidFee());
            } else {
                ret.setUsedAmount(e.getBalance() == null ? BigDecimal.ZERO : e.getBalance());
            }
            ret.setFrozenAmount(
                e.getFrozenAmount() == null ? BigDecimal.ZERO : e.getFrozenAmount());
            ret.setUserDebt(e.getUserDebt() != null && e.getUserDebt());

            ret.setUnPayOrderCount(
                orderPayCountMap.get(e.getId()) == null ?
                    0 :
                    orderPayCountMap.get(e.getId()).getOrderCount());
            ret.setPaidFee(
                orderPayCountMap.get(e.getId()) == null ?
                    BigDecimal.ZERO :
                    orderPayCountMap.get(e.getId()).getPaidFee());

            // TODO：要跟企业账户余额做对比，取小值
            if (LimitCycle.NONCYCLE.equals(e.getLimitCycle())) {
                // 无周期，可用额度 = 限额 - 已使用 - 冻结
                ret.setAvailableAmount(ret.getLimitMoney()
                    .subtract(ret.getPaidFee())
                    .subtract(ret.getFrozenAmount()));
            } else {
                ret.setAvailableAmount(
                    e.getAvailableMoney() == null ? BigDecimal.ZERO : e.getAvailableMoney());
            }

            // 后付费处理
            if (SettlementType.POSTPAID.equals(corpRes.getData().getSettlementType())) {
                // 使用授信账户的可用余额
                if (ret.getLimitCycle().equals(LimitCycle.UNLIMITED)) {
                    ret.setAvailableAmount(null); // 后付费无限制
                }
                return ret;
            }

            // 预付费处理
            if (corpRes.getData().getAvailableAmount() == null) {
                ret.setAvailableAmount(BigDecimal.ZERO);
            } else if (ret.getLimitCycle().equals(LimitCycle.UNLIMITED) ||
                DecimalUtils.gt(ret.getAvailableAmount(), corpRes.getData().getAvailableAmount())) {
                ret.setAvailableAmount(corpRes.getData().getAvailableAmount());
            }
            return ret;
        }).collect(Collectors.toList());

        return new ListResponse<>(retList, list.getTotal());
    }


    @PostMapping("/api/corp/batchDisableCreditAccount")
    @Operation(summary = "批量禁用授信账户")
    public BaseResponse batchDisableCreditAccount(ServerHttpRequest request,
        @RequestBody BatchDisableCreditUser param) {
        log.info(LoggerHelper2.formatEnterLog(request) + "批量删除授信账户. param = {}", param);
        CorpOrgLoginVo op = getCorpByRequest(request);
        iBlocUserService.batchDisableCreditAccount(param.getAccountIds(),
            op.getCorpPo().getTopCommId(),
            op.getCorpPo().getCommId(), op.getCorpId(),
            op.getCorpPo().getUid());   // TODO: 企业后台增加多用户后这个要修改
        corpSysLogService.batchDisableCreditAccount(param.getPhones(), request);
        return RestUtils.success();
    }

    @GetMapping("/api/corp/corpUserLogOut")
    @Operation(summary = "企业用户登出")
    public BaseResponse corpUserLogOut(ServerHttpRequest request) {
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        iBlocUserService.logOut(corpOrgLoginVo);
        return BaseResponse.newInstance();
    }

    @PostMapping("/api/corp/corpUserLogin")
    @Operation(summary = "企业用户登录")
    public ObjectResponse<CorpLoginVo> blocUserLogin(@RequestBody CorpAuthVO user) {
        return iBlocUserService.loginBlocUserForCorpPlatform(user.getUsername(),
            user.getPassword());
    }

    @PostMapping("/api/corp/getCorpByToken")
    @Operation(summary = "通过token获得corpVO")
    public ObjectResponse<CorpOrgLoginVo> getCorpByToken(@RequestParam("token") String token) {
        return new ObjectResponse<>(loginService.getUser(token));
    }

    /**
     * 解决：企业的组织架构变化后，redis中相关数据未随之改变的问题
     *
     * @param account
     */
    private void refreshTokenValue(ServerHttpRequest request, String account) {
        try {
            authCenterFeignClient.refreshCorpTokenValue(super.getToken2(request), account);
        } catch (Exception e) {
            log.info("重置企业客户token失败, {}", e.getMessage(), e);
        } finally {
            log.info("refresh end");
        }
    }


    @Operation(summary = "交易统计列表")
    @PostMapping("/api/corp/tradeLogList")
    public ListResponse<PointLog> tradeLogList(
        ServerHttpRequest request,
        @RequestBody CorpListPointLogParam param) {
        log.info("交易统计列表: {}", JsonUtils.toJsonString(param));
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        IotAssert.isNotNull(corpOrgLoginVo, "请登陆后再尝试。");
        IotAssert.isNotNull(corpOrgLoginVo.getId(), "无法获取登陆id，请登陆后再尝试。");
        Long commId = corpOrgLoginVo.getCorpPo().getCommId();
        IotAssert.isNotNull(commId, "无法获取商户id，请登陆后再尝试。");
        Long topCommId = corpOrgLoginVo.getCorpPo().getTopCommId();
        IotAssert.isNotNull(topCommId, "无法获取商户top id，请登陆后再尝试。");
        Long uid = corpOrgLoginVo.getCorpPo().getUid();
        IotAssert.isNotNull(uid, "无法获取企业uid，请登陆后再尝试。");

        param.setTopCommId(topCommId)
            .setCommId(topCommId)
            .setUid(uid);

//        Page<PointLog> page = new Page<>();
//        page.setPageSize(param.getSize() == null ? 10 : param.getSize());
//        page.setPageNum(param.getStart() == null ? 1 : param.getStart().intValue());

        // 默认仅显示增加和减少的流水
        if (CollectionUtils.isEmpty(param.getLogTypes())) {
            param.setLogTypes(List.of(
                PointOpType.DECREASE.code(),
                PointOpType.INCREASE.code(),
                PointOpType.REFUND.code()));
        } else if (param.getLogTypes().contains(PointOpType.DECREASE.code())) {
            // 退款包含在减少内
            param.getLogTypes().add(PointOpType.REFUND.code());
        }

        param.setPayAccountType(PayAccountType.CORP);
        ListResponse<PointLog> res = dcCusBalanceService.listPointLog(param);
//        ListResponse<PointLog> res = dcCusBalanceService.listPointLog(page,
//                PayAccountType.PERSONAL,
//                topCommId,
//                topCommId,
//                uid,//blocUserVo.getId(),
//                0,
//                CollectionUtils.isEmpty(param.getLogTypes()) ?
//                        List.of(PointOpType.DECREASE.code(), PointOpType.INCREASE.code()) ://默认仅显示增加和减少的流水
//                        param.getLogTypes(),
//                param.getOrderTypes(),
//                param.getCreateTimeFrom(),
//                param.getCreateTimeTo(),
//                param.getOrderNo(),
//                null);

//        FeignResponseValidate.check(res);

//        res.getData().stream().map(e ->  {
//            PointLogPo pointLogPo = new PointLogPo();
//            BeanUtils.copyProperties(e, pointLogPo);
//        })
        return res;
    }

    @Operation(summary = "导出交易统计")
    @PostMapping("/api/corp/exportExcelTrade")
    public ObjectResponse<ExcelPosition> exportExcelTrade(
        @RequestBody CorpListPointLogParam searchParam,
        ServerHttpRequest request,
        ServerHttpResponse response) {
        log.info("导出交易统计: {}", JsonUtils.toJsonString(searchParam));
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        IotAssert.isNotNull(corpOrgLoginVo, "请登陆后再尝试。");
        IotAssert.isNotNull(corpOrgLoginVo.getCorpPo().getId(), "无法获取登陆id，请登陆后再尝试。");
        Long commId = corpOrgLoginVo.getCorpPo().getCommId();
        IotAssert.isNotNull(commId, "无法获取商户id，请登陆后再尝试。");
        Long topCommId = corpOrgLoginVo.getCorpPo().getTopCommId();
        IotAssert.isNotNull(topCommId, "无法获取商户top id，请登陆后再尝试。");
        Long uid = corpOrgLoginVo.getCorpPo().getUid();
        IotAssert.isNotNull(uid, "无法获取企业uid，请登陆后再尝试。");

        searchParam.setTopCommId(topCommId)
            .setCommId(topCommId)
            .setUid(uid);

        // 默认仅显示增加和减少的流水
        if (CollectionUtils.isEmpty(searchParam.getLogTypes())) {
            searchParam.setLogTypes(List.of(
                PointOpType.DECREASE.code(),
                PointOpType.INCREASE.code(),
                PointOpType.REFUND.code()));
        } else if (searchParam.getLogTypes().contains(PointOpType.DECREASE.code())) {
            // 退款包含在减少内
            searchParam.getLogTypes().add(PointOpType.REFUND.code());
        }

        searchParam.setPayAccountType(PayAccountType.CORP);
        ExcelPosition excelPosition = chargerOrderServiceImpl.exportExcelTrade(searchParam);
        log.info("导出交易统计 excel 位置: {}", JsonUtils.toJsonString(excelPosition));

        return new ObjectResponse<>(excelPosition);
    }

    @Operation(summary = "查看交易统计数据")
    @PostMapping("/api/corp/tradeLogListBi")
    public ObjectResponse<TradeDataVo> tradeLogListBi(
        @RequestBody CorpListPointLogParam searchParam,
        ServerHttpRequest request) {
        log.info("查看交易统计数据: {}", JsonUtils.toJsonString(searchParam));
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        IotAssert.isNotNull(corpOrgLoginVo, "请登陆后再尝试。");
        IotAssert.isNotNull(corpOrgLoginVo.getCorpPo().getId(), "无法获取登陆id，请登陆后再尝试。");
        Long commId = corpOrgLoginVo.getCorpPo().getCommId();
        IotAssert.isNotNull(commId, "无法获取商户id，请登陆后再尝试。");
        Long topCommId = corpOrgLoginVo.getCorpPo().getTopCommId();
        IotAssert.isNotNull(topCommId, "无法获取商户top id，请登陆后再尝试。");
        Long uid = corpOrgLoginVo.getCorpPo().getUid();
        IotAssert.isNotNull(uid, "无法获取企业uid，请登陆后再尝试。");

        ListResponse<PointLogBi> res = dcCusBalanceService.listPointLogBi(
            PayAccountType.PERSONAL,
            topCommId,
            topCommId,
            uid,
            0,
            CollectionUtils.isEmpty(searchParam.getLogTypes()) ?
                List.of(PointOpType.DECREASE.code(), PointOpType.INCREASE.code()) ://默认仅显示增加和减少的流水
                searchParam.getLogTypes(),
            searchParam.getOrderTypes(),
            searchParam.getCreateTimeFrom(),
            searchParam.getCreateTimeTo(),
            searchParam.getOrderNo(),
            null);

        log.info("查看交易统计数据 res: {}", JsonUtils.toJsonString(res));

        FeignResponseValidate.check(res);

        TradeDataVo tradeDataVo = new TradeDataVo();
        tradeDataVo.setDecrDelta(BigDecimal.ZERO)
            .setDecrDeltaCost(BigDecimal.ZERO)
            .setIncrDelta(BigDecimal.ZERO)
            .setIncrDeltaCost(BigDecimal.ZERO);
        if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(res.getData())) {
            Map<Integer, List<PointLogBi>> biMap =
                res.getData().stream().collect(Collectors.groupingBy(PointLogBi::getLogType));
            if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(biMap.get(Integer.valueOf(1)))) {
                // 流水增加
                IotAssert.isTrue(biMap.get(Integer.valueOf(1)).size() == 1, "交易统计数据不正确");
                tradeDataVo.setIncrDelta(biMap.get(Integer.valueOf(1)).get(0).getDelta());
                tradeDataVo.setIncrDeltaCost(biMap.get(Integer.valueOf(1)).get(0).getDeltaCost());
            }
            if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(biMap.get(Integer.valueOf(2)))) {
                // 流水增加
                IotAssert.isTrue(biMap.get(Integer.valueOf(2)).size() == 1, "交易统计数据不正确");
                tradeDataVo.setDecrDelta(biMap.get(Integer.valueOf(2)).get(0).getDelta());
                tradeDataVo.setDecrDeltaCost(biMap.get(Integer.valueOf(2)).get(0).getDeltaCost());
            }
        }
        return new ObjectResponse(tradeDataVo);
    }

    @Operation(summary = "根据条件查询订单统计数据")
    @PostMapping("/api/corp/order/getChargerOrderData")
    public ObjectResponse<ChargerOrderDataVo> getChargerOrderData(
        @RequestBody ChargerOrderParam searchParam,
        ServerHttpRequest request) {
        log.info("集团充电订单列表: {}", JsonUtils.toJsonString(searchParam));

        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        IotAssert.isNotNull(corpOrgLoginVo, "请登陆后再尝试。");
        IotAssert.isNotNull(corpOrgLoginVo.getCorpPo().getId(), "无法获取登陆id，请登陆后再尝试。");
        Long commId = corpOrgLoginVo.getCorpPo().getCommId();
        IotAssert.isNotNull(commId, "无法获取商户id，请登陆后再尝试。");
        Long blocUserId = corpOrgLoginVo.getCorpPo().getId();
        searchParam.setBlocUserId(blocUserId);

        //List<Long> commIdList = corpOrgLoginVo.getComIds();
        IotAssert.isTrue(!StringUtils.isBlank(corpOrgLoginVo.getCommIdChain()),
            "商户和子商户获取失败，请重新登陆");

//        ListResponse<Long> jsonObjectCommIdList = merchantFeignClient.getCommIdListByCommId(commId);
//        FeignResponseValidate.check(jsonObjectCommIdList);

        //searchParam.setCommIdList(commIdList);
//        searchParam.setSiteCommIdChain(corpOrgLoginVo.getCommIdChain());

        IotAssert.isTrue(!CollectionUtils.isEmpty(corpOrgLoginVo.getOrgIds()),
            "无法获取组织列表请登陆后再尝试。");
        searchParam.setCorpOrgIds(CollectionUtils.isEmpty(searchParam.getCorpOrgIds()) ?
            corpOrgLoginVo.getOrgIds() :
            searchParam.getCorpOrgIds());

        return chargerOrderService.getChargerOrderData(searchParam);
    }

    @Operation(summary = "集团充电订单列表")
    @PostMapping("/api/corp/order/queryChargeOrderList")
    public ListResponse<ChargerOrderVo> queryChargeOrderList(ServerHttpRequest request,
        ServerWebExchange exh,
        @RequestBody ChargerOrderParam searchParam) {

        log.info("查询集团充电订单列表: {}", searchParam);

        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        IotAssert.isNotNull(corpOrgLoginVo, "请登陆后再尝试。");
        IotAssert.isNotNull(corpOrgLoginVo.getId(), "无法获取登陆id，请登陆后再尝试。");
        Integer commId = corpOrgLoginVo.getCommId();
        IotAssert.isNotNull(commId, "无法获取商户id，请登陆后再尝试。");
        Long blocUserId = corpOrgLoginVo.getCorpPo().getId();
        searchParam.setBlocUserId(blocUserId);

        //List<Long> commIdList = corpOrgLoginVo.getComIds();
        IotAssert.isTrue(!StringUtils.isBlank(corpOrgLoginVo.getCommIdChain()),
            "商户和子商户获取失败，请重新登陆");

//        ListResponse<Long> jsonObjectCommIdList = merchantFeignClient.getCommIdListByCommId(commId);
//        FeignResponseValidate.check(jsonObjectCommIdList);

        //searchParam.setCommIdList(commIdList);
//        searchParam.setSiteCommIdChain(corpOrgLoginVo.getCommIdChain());

        IotAssert.isTrue(!CollectionUtils.isEmpty(corpOrgLoginVo.getOrgIds()),
            "无法获取组织列表请登陆后再尝试。");
        searchParam.setCorpOrgIds(CollectionUtils.isEmpty(searchParam.getCorpOrgIds()) ?
            corpOrgLoginVo.getOrgIds() :
            searchParam.getCorpOrgIds());

        return chargerOrderService.queryChargeOrderList(searchParam);
    }

    @Operation(summary = "集团充电订单导出")
    @PostMapping("/api/corp/order/exportExcelByChargeOrderListV2")
    public Mono<ObjectResponse<ExcelPosition>> exportExcelByChargeOrderListV2(
        ServerWebExchange exh,
        @RequestBody ChargerOrderParam searchParam,
        ServerHttpRequest request) {
        log.info("集团充电订单导出: {}", JsonUtils.toJsonString(searchParam));

        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        IotAssert.isNotNull(corpOrgLoginVo, "请登陆后再尝试。");
        IotAssert.isNotNull(corpOrgLoginVo.getCorpPo().getId(), "无法获取登陆id，请登陆后再尝试。");
        Long commId = corpOrgLoginVo.getCorpPo().getCommId();
        IotAssert.isNotNull(commId, "无法获取商户id，请登陆后再尝试。");
        Long blocUserId = corpOrgLoginVo.getCorpPo().getId();
        searchParam.setBlocUserId(blocUserId);

        //List<Long> commIdList = corpOrgLoginVo.getComIds();
        IotAssert.isTrue(!StringUtils.isBlank(corpOrgLoginVo.getCommIdChain()),
            "商户和子商户获取失败，请重新登陆");

//        ListResponse<Long> jsonObjectCommIdList = merchantFeignClient.getCommIdListByCommId(commId);
//        FeignResponseValidate.check(jsonObjectCommIdList);

//        searchParam.setCommIdList(
//                CollectionUtils.isEmpty(searchParam.getCorpOrgIds()) ?
//                        commIdList :
//                        searchParam.getCorpOrgIds());
//        searchParam.setSiteCommIdChain(corpOrgLoginVo.getCommIdChain());

        IotAssert.isTrue(!CollectionUtils.isEmpty(corpOrgLoginVo.getOrgIds()),
            "无法获取组织列表请登陆后再尝试。");
        searchParam.setCorpOrgIds(CollectionUtils.isEmpty(searchParam.getCorpOrgIds()) ?
            corpOrgLoginVo.getOrgIds() :
            searchParam.getCorpOrgIds());

        searchParam.setPlatform(23); // 标记来自企业客户平台WEB的请求

        return orderRest.exportExcelByChargeOrderListV2(exh, searchParam, request);
    }

    /**
     * 查询分时订单列表
     *
     * @param orderNo
     */
    @Operation(summary = "查询分时订单列表")
    @RequestMapping(value = "/api/corp/order/queryOrderTimeDivisionList")
    public ListResponse<ChargerOrderTimeDivision> queryOrderTimeDivisionList(String orderNo,
        ServerHttpRequest request) {
        log.info(">> 查询分时订单列表, orderNo={}", orderNo);

        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        IotAssert.isNotNull(corpOrgLoginVo, "请登陆后再尝试。");
        IotAssert.isTrue(com.cdz360.base.utils.StringUtils.isNotBlank(orderNo), "请传入订单编号。");

        ListResponse<ChargerOrderTimeDivision> resultEntity = chargerOrderService.queryOrderTimeDivisionList(
            orderNo);

        return resultEntity;
    }

    /**
     * 获取订单的充电实况 -- 电压电流采样情况
     *
     * @param orderNo
     */
    @Operation(summary = "获取订单的充电实况 -- 电压电流采样情况")
    @GetMapping(value = "/api/corp/order/samplingInfo")
    public Mono<ListResponse<ChargerDetailVo>> queryOrderInfo(
        @Parameter(name = "充电订单号") @RequestParam(value = "orderNo") String orderNo,
        ServerHttpRequest request) {
//        log.info(">> 获取订单信息, orderNo={}", orderNo);
        log.info(">> 获取订单信息" + LoggerHelper2.formatEnterLog(request));

        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        IotAssert.isNotNull(corpOrgLoginVo, "请登陆后再尝试。");
        IotAssert.isTrue(com.cdz360.base.utils.StringUtils.isNotBlank(orderNo), "请传入订单编号。");

//        ListResponse<ChargerDetailVo> listResponse =
        return dataCoreOrderFeignClient.getOrderSamplingInfo(orderNo)
            .doOnNext(res -> {
                log.info("<< Feign 调用成功返回: status = {}, size = {}",
                    res.getStatus(),
                    res.getData() != null ? res.getData().size() : null);
            });

//        return listResponse;
    }

    /**
     * 获取该商户列表下的客户 订单详情
     *
     * @param orderNo 订单NO
     * @return headerparam token
     */
    @Operation(summary = "获取该商户列表下的客户 订单详情")
    @GetMapping("/api/corp/order/queryOrderDetail")
    public Mono<ObjectResponse<ChargerOrderWithBLOBs>> queryOrderDetail(
        @RequestParam(value = "orderNo", required = false) String orderNo,
        ServerHttpRequest request) {

        List<String> gids = AntRestUtils.getSysUserGids(request);

        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        IotAssert.isNotNull(corpOrgLoginVo, "请登陆后再尝试。");
        IotAssert.isTrue(com.cdz360.base.utils.StringUtils.isNotBlank(orderNo), "请传入订单编号。");
        //List<Long> commIdList = corpOrgLoginVo.getComIds();
        IotAssert.isTrue(!StringUtils.isBlank(corpOrgLoginVo.getCommIdChain()),
            "商户和子商户获取失败，请重新登陆");

        return chargerOrderService.queryOrderDetail2(
            corpOrgLoginVo.getCommIdChain(), orderNo, null, gids);
    }

    /**
     * 订单的资金流水
     *
     * @param request
     * @return
     */
    @Operation(summary = "订单的资金流水")
    @GetMapping(value = "/api/corp/order/queryPointLogByOrderNo")
    public Mono<ListResponse<PointLog>> queryPointLogByOrderNo(ServerHttpRequest request,
        @Parameter(name = "订单号") @RequestParam(value = "orderNo") String orderNo) {
        log.info("查询集团充电订单列表: {}", orderNo);

        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        IotAssert.isNotNull(corpOrgLoginVo, "请登陆后再尝试。");
        IotAssert.isNotNull(corpOrgLoginVo.getCorpPo().getId(), "无法获取登陆id，请登陆后再尝试。");
        Long topCommId = corpOrgLoginVo.getCorpPo().getTopCommId();
        IotAssert.isNotNull(topCommId, "无法获取top商户id，请登陆后再尝试。");

        // List<Long> commIdList = corpOrgLoginVo.getComIds();
        //IotAssert.isTrue(!CollectionUtils.isEmpty(commIdList), "商户和子商户获取失败，请重新登陆");

//        ListResponse<PointLog> pointLogListResponse =
        return chargerOrderService.queryPointLogByOrderNo(topCommId, orderNo,
            corpOrgLoginVo.getCommIdChain(), null, null);

//        return pointLogListResponse;
    }

    @GetMapping("/api/corp/getOrganization")
    @Operation(summary = "查询本身及下属组织信息")
    public ListResponse<CorpOrgVO> getOrganization(ServerHttpRequest request,
        ServerWebExchange exh,
        @RequestParam(value = "_index", required = false) String _index,
        @RequestParam(value = "_size", required = false) String _size) {
        // TODO: 2019/12/24 @WZ 后续权限功能上线后，此处逻辑要调整
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        OldPageParam pageVo = getPage2(request, exh, false);
        return iCorpService.getOrgList(super.getToken2(request), corpOrgLoginVo.getId(), pageVo);
    }


    @GetMapping("/api/corp/getAvailableSite")
    @Operation(summary = "查询可用场站列表")
    public ListResponse<SiteSelectInfoVo> getAvailableSite(
        ServerHttpRequest request,
        @RequestParam(value = "siteName", required = false) String siteName,
        @RequestParam(value = "includedHlhtSite", required = false) Boolean includedHlhtSite) {
        log.debug("查询可用场站列表: {}", LoggerHelper2.formatEnterLog(request));
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        return iCorpService.getAvailableSite(corpOrgLoginVo.getCommIdChain(),
            corpOrgLoginVo.getGids(), siteName, includedHlhtSite);
    }

    @GetMapping("/api/corp/getAvailableMobile")
    @Operation(summary = "通过组织名称ID得到可用手机号")
    public ListResponse<RBlocUser> getAvailableMobile(ServerHttpRequest request,
        @RequestParam("corpOrgId") Long corpOrgId) {
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        RBlocUser rBlocUser = new RBlocUser();
        rBlocUser.setBlocUserId(corpOrgLoginVo.getCorpPo().getId());
        rBlocUser.setCorpOrgId(corpOrgId);
        return userFeignClient.findByCondition(rBlocUser);
    }

    @PostMapping("/api/corp/onlineCardsList")
    @Operation(summary = "查询在线卡列表")
    public ListResponse<CardListdetailVO> onlineCardsList(ServerHttpRequest request,
        @RequestBody CardRequest cardRequest) {
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        cardRequest.setCorpId(corpOrgLoginVo.getCorpId());
        cardRequest.setCommIdChain(corpOrgLoginVo.getCommIdChain());
        cardRequest.setGids(corpOrgLoginVo.getGids());
        cardRequest.setOrgIds(corpOrgLoginVo.getOrgIds());
        cardRequest.setTopCommId(AntRestUtils.getTopCommId(request));

        ListResponse<CardListdetailVO> cardListdetailVOListResponse = iCorpService.onlineCardsList(
            cardRequest);
        List<CardListdetailVO> cardListDetailVOS = cardListdetailVOListResponse.getData();
        List<CardListdetailVO> result;
        if (corpOrgLoginVo.getOrgLevel().equals(1)) {
            return cardListdetailVOListResponse;
        } else {
            result = cardListDetailVOS.stream().filter(e -> {
                boolean display = false;
                for (Long temp : corpOrgLoginVo.getOrgIds()
                ) {
                    if (temp.equals(e.getCorpOrgId())) {
                        display = true;
                        break;
                    }
                }
                return display;
            }).collect(Collectors.toList());
        }
        return new ListResponse<>(result);
    }

    @PostMapping("/api/corp/exportOnlineCardsByCorp")
    @Operation(summary = "企业平台在线卡导出")
    public ObjectResponse<ExcelPosition> exportOnlineCardsByCorp(ServerHttpRequest request,
        @RequestBody CardRequest cardRequest) {
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        cardRequest.setCorpId(corpOrgLoginVo.getCorpId());
        cardRequest.setCommIdChain(corpOrgLoginVo.getCommIdChain());
        cardRequest.setOrgIds(corpOrgLoginVo.getOrgIds());
        cardRequest.setTopCommId(AntRestUtils.getTopCommId(request));

        return iCorpService.exportOnlineCardsByCorp(cardRequest);

    }

    @Operation(summary = "在线卡新增单张卡片（发卡给客户）")
    @PostMapping("/api/corp/grantCard")
    public BaseResponse grantCard(ServerHttpRequest request,
        @RequestBody CardRequest cardRequest) throws ParseException {
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        cardRequest.setCommId(corpOrgLoginVo.getCorpPo().getCommId());
        cardRequest.setCorpId(corpOrgLoginVo.getCorpPo().getId());
//        ListResponse<Long> jsonObjectCommIdList = merchantFeignClient.getCommIdListByCommId(corpOrgLoginVo.getCorpPo().getCommId());
//        FeignResponseValidate.check(jsonObjectCommIdList);
        BaseResponse res = iCorpService.grantCard(cardRequest, corpOrgLoginVo.getCommIdChain(),
            corpOrgLoginVo.getGids());
        corpSysLogService.grantCard(List.of(cardRequest.getCardChipNo()), request);
        return res;
    }

    /**
     * 解析企业平台在线卡excel文件
     *
     * @return
     */
    @Operation(summary = "解析企业平台在线卡excel文件")
    @PostMapping("/api/corp/parseCorpCardExcel")
    public Mono<ObjectResponse<JsonNode>> parseCorpCardExcel(ServerHttpRequest request,
        @RequestPart FilePart file) {
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        if (null == file) {
            log.info("没有上传excel文件");
            throw new DcServiceException("没有提供excel文件, 文件字段使用 { file: xxx }");
        }

        String fileName = file.filename();
        if (null == fileName) {
            log.info("没有上传excel源文件");
            throw new DcServiceException("请上传excel文件, 文件字段使用 { file: xxx }");
        }

        if (fileName.lastIndexOf(".") == -1) {
            log.info("上传excel源文件没有后缀: file name={}", fileName);
            throw new DcServiceException("请上传正确的excel文件, excel文件后缀不存在");
        }

        String fileType = fileName.substring(fileName.lastIndexOf("."));
        if (!(".xls".equals(fileType) || ".xlsx".equals(fileType))) {
            log.info("上传excel文件后缀不支持, excel文件后缀支持: *.xls 或 *.xlsx, file name={}.",
                fileName);
            throw new DcServiceException(
                "请上传正确的excel文件, excel文件后缀支持: *.xls 或 *.xlsx");
        }

        return cardService.parseCorpCardExcel(file, corpOrgLoginVo.getCorpPo().getId());
    }

    @Operation(summary = "批量新增卡")
    @PostMapping("/api/corp/batchGrantCard")
    public BaseResponse batchGrantCard(ServerHttpRequest request,
        @RequestBody CardRequestVo cardRequestVo) {
        log.info("批量新增卡 cardRequestVo: {}, cardRequestList.size: {}",
            JsonUtils.toJsonString(cardRequestVo), cardRequestVo.getCardRequestList().size());
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
//        ListResponse<Long> jsonObjectCommIdList = merchantFeignClient.getCommIdListByCommId(corpOrgLoginVo.getCorpPo().getCommId());
//        FeignResponseValidate.check(jsonObjectCommIdList);
        BaseResponse res = iCorpService.batchGrantCard(cardRequestVo.getCardRequestList(),
            corpOrgLoginVo.getCorpPo().getId(), corpOrgLoginVo.getCorpPo().getCommId(),
            cardRequestVo.getStations(), corpOrgLoginVo.getGids(), corpOrgLoginVo.getCommIdChain());
        corpSysLogService.grantCard(
            cardRequestVo.getCardRequestList().stream()
                .map(CardRequest::getCardChipNo).collect(Collectors.toList()),
            request);
        return res;
    }

    @Operation(summary = "根据cardChipNo查询卡")
    @GetMapping("/api/corp/queryCardByCardChipNo")
    public ObjectResponse<Card> queryCardByCardChipNo(ServerHttpRequest request,
        @RequestParam("cardChipNo") String cardChipNo) {
        return iCorpService.queryCardByCardChipNo(cardChipNo);
    }

    @Operation(summary = "根据cardNo查询卡")
    @GetMapping("/api/corp/queryCardByCardNo")
    public ObjectResponse<Card> queryCardByCardNo(ServerHttpRequest request,
        @RequestParam("cardNo") String cardNo) {
        return iCorpService.queryCardByCardNo(cardNo);
    }

    @Operation(summary = "修改在线卡")
    @PostMapping("/api/corp/modifyCard")
    public BaseResponse modifyCard(ServerHttpRequest request,
        @RequestBody CardRequest cardRequest) throws ParseException {
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        cardRequest.setCommId(corpOrgLoginVo.getCorpPo().getCommId());
        cardRequest.setCorpId(corpOrgLoginVo.getCorpPo().getId());
//        ListResponse<Long> jsonObjectCommIdList = merchantFeignClient.getCommIdListByCommId(corpOrgLoginVo.getCorpPo().getCommId());
//        FeignResponseValidate.check(jsonObjectCommIdList);
        BaseResponse res = iCorpService.modifyCard(cardRequest, corpOrgLoginVo.getGids(),
            corpOrgLoginVo.getCommIdChain());
        corpSysLogService.modifyCard(List.of(cardRequest.getCardChipNo()), request);
        return res;
    }

    @Operation(summary = "在线卡挂失)")
    @GetMapping("/api/corp/reportLossCard")
    public BaseResponse reportLossCard(ServerHttpRequest request,
        @RequestParam("cardNo") String cardNo,
        @RequestParam("cardChipNo") String cardChipNo) {
        BaseResponse res = iCorpService.reportLossCard(cardNo);
        corpSysLogService.modifyCardStatus(cardChipNo, request);
        return res;
    }

    @Operation(summary = "在线卡激活")
    @GetMapping("/api/corp/reportActivateCard")
    public BaseResponse reportActivateCard(ServerHttpRequest request,
        @RequestParam("cardNo") String cardNo,
        @RequestParam("cardChipNo") String cardChipNo) {
        BaseResponse res = iCorpService.reportActivateCard(cardNo);
        corpSysLogService.modifyCardStatus(cardChipNo, request);
        return res;
    }

    @Operation(summary = "删除在线卡")
    @PostMapping("/api/corp/delCard")
    public BaseResponse delCard(ServerHttpRequest request,
        @RequestBody List<String> cardNoList) {
        BaseResponse res = iCorpService.delCard(cardNoList);
        corpSysLogService.delCard(cardNoList, request);
        return res;
    }

    private CorpOrgLoginVo getCorpByRequest(ServerHttpRequest request) {
        return loginService.getUserByRequest(request);
    }

    @Operation(summary = "查询vin码列表")
    @PostMapping("/api/corp/selectVinOnCorp")
    public ListResponse<VinDto> selectVinOnCorp(ServerHttpRequest request,
        @RequestBody VinParam vinParam) {
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        vinParam.setCorpId(corpOrgLoginVo.getCorpId());
        vinParam.setCommIdChain(corpOrgLoginVo.getCommIdChain());

        //添加企业组织限制
        if (corpOrgLoginVo != null) {
            vinParam.setOrgIds(corpOrgLoginVo.getOrgIds());
        }
        log.info("vinParam = {}", vinParam);
        ListResponse<VinDto> vinDtoListResponse = iCorpService.selectVinOnCorp(vinParam);
        List<VinDto> vinDtos = vinDtoListResponse.getData();
        List<VinDto> result;
        if (corpOrgLoginVo.getOrgLevel().equals(1)) {
            return vinDtoListResponse;
        } else {
            result = vinDtos.stream().filter(e -> {
                boolean isThisOrg = false;
                for (Long temp : corpOrgLoginVo.getOrgIds()
                ) {
                    if (temp.equals(e.getCorpOrgId())) {
                        isThisOrg = true;
                        break;
                    }
                }
                return isThisOrg;
            }).collect(Collectors.toList());
        }
        return new ListResponse<>(result);
    }


    @Operation(summary = "企业平台导出VIN")
    @PostMapping("/api/corp/exportVinListOnCorp")
    public ObjectResponse<ExcelPosition> exportVinListOnCorp(ServerHttpRequest request,
        @RequestBody VinParam vinParam) {
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        vinParam.setCorpId(corpOrgLoginVo.getCorpId());
        vinParam.setCommIdChain(corpOrgLoginVo.getCommIdChain());

        //添加企业组织限制
        if (corpOrgLoginVo != null) {
            vinParam.setOrgIds(corpOrgLoginVo.getOrgIds());
        }
        log.info("vinParam = {}", vinParam);
        return iCorpService.exportVinListOnCorp(vinParam);
    }

    @Operation(summary = "新增单个VIN")
    @PostMapping("/api/corp/grantVin")
    public BaseResponse grantVin(ServerHttpRequest request,
        @RequestBody VinParam vinParam) throws ParseException {
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        vinParam.setCommId(corpOrgLoginVo.getCorpPo().getTopCommId());
        vinParam.setSubCommId(corpOrgLoginVo.getCorpPo().getCommId());
        vinParam.setCorpId(corpOrgLoginVo.getCorpPo().getId());
//        ListResponse<Long> jsonObjectCommIdList = merchantFeignClient.getCommIdListByCommId(corpOrgLoginVo.getCorpPo().getCommId());
//        FeignResponseValidate.check(jsonObjectCommIdList);
        BaseResponse res = iCorpService.grantVin(vinParam, corpOrgLoginVo.getGids(),
            corpOrgLoginVo.getCommIdChain());
        corpSysLogService.grantVin(List.of(vinParam.getVin()), request);
        return res;
    }

    /**
     * 解析企业平台VIN码excel文件
     *
     * @return
     */
    @Operation(summary = "解析企业平台VIN码excel文件")
    @PostMapping("/api/corp/parseCorpVinExcel")
    public Mono<ObjectResponse<JsonNode>> parseCorpVinExcel(ServerHttpRequest request,
        @RequestPart FilePart file) {
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        FileUtil.checkExcelFile(file);
//        log.info("corpOrgLoginVo = {}", corpOrgLoginVo);
        return cardService.parseCorpVinExcel(file,
            corpOrgLoginVo.getCorpPo().getId(),
            corpOrgLoginVo.getCorpPo().getTopCommId());
    }

    @Operation(summary = "解析企业平台批量导入授信账户excel文件")
    @PostMapping("/api/corp/parseCorpCreditExcel")
    public Mono<ObjectResponse<BatchOpResult<BatchAddCreditResultItem>>> parseCorpCreditExcel(
        ServerHttpRequest request,
        @RequestPart FilePart file) {
        log.info(LoggerHelper2.formatEnterLog(request, false));
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        FileUtil.checkExcelFile(file);
        var result = this.iCorpService.parseCorpCreditExcel(
            corpOrgLoginVo.getCorpPo().getTopCommId(),
            corpOrgLoginVo.getCorpPo().getCommId(),
            corpOrgLoginVo.getCorpId(),
            file);
        return result;
//        return RestUtils.buildObjectResponse(result);
    }

    @Operation(summary = "企业平台批量新增授信账户")
    @PostMapping("/api/corp/batchAddCreditAccount")
    public BaseResponse batchAddCreditAccount(ServerHttpRequest request,
        @RequestBody BatchAddCreditUserParam param) {
        log.info(LoggerHelper2.formatEnterLog(request) + "批量新增授信账户");
        if (param.getOrgId() == null || param.getOrgId() < 1L) {
            throw new DcArgumentException("参数错误，请选择归属的组织");
        }
        CorpOrgLoginVo op = getCorpByRequest(request);
        param.setTopCommId(op.getCorpPo().getTopCommId())
            .setCommId(op.getCorpPo().getCommId())
            .setCorpId(op.getCorpId())
            .setOpUid(op.getId());

        this.iCorpService.batchAddCreditAccount(param);
        corpSysLogService.batchAddCreditAccount(
            param.getUserList().stream()
                .map(BatchAddCreditUserParam.CreditUser::getPhone).collect(Collectors.toList()),
            request);
        return RestUtils.success();
    }

    @Operation(summary = "批量新增VIN")
    @PostMapping("/api/corp/batchGrantVin")
    public BaseResponse batchGrantVin(ServerHttpRequest request,
        @RequestBody VinParamVo vinParamVo) {
        log.info("批量新增卡 vinParamVo: {}, vinParamList.size: {}",
            JsonUtils.toJsonString(vinParamVo), vinParamVo.getVinParamList().size());
        if (vinParamVo.getVinParamList().size() > 2000) {
            log.warn("导入VIN数量不能超过2000条");
            throw new DcArgumentException("导入VIN数量不能超过2000条");
        }
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
//        ListResponse<Long> jsonObjectCommIdList = merchantFeignClient.getCommIdListByCommId(corpOrgLoginVo.getCorpPo().getCommId());
//        FeignResponseValidate.check(jsonObjectCommIdList);
        BaseResponse res = iCorpService.batchGrantVin(vinParamVo.getVinParamList(),
            corpOrgLoginVo.getCorpPo().getId(), corpOrgLoginVo.getCorpPo().getTopCommId(),
            corpOrgLoginVo.getCorpPo().getCommId(),
            vinParamVo.getStations(), corpOrgLoginVo.getGids(), corpOrgLoginVo.getCommIdChain());
        corpSysLogService.grantVin(
            vinParamVo.getVinParamList().stream()
                .map(VinParam::getVin).collect(Collectors.toList()),
            request);
        return res;
    }


    @Operation(summary = "企业平台批量修改卡/VIN绑定的场站")
    @PostMapping("/api/corp/batchModifyVin")
    public BaseResponse batchModifyVin(ServerHttpRequest request,
        @RequestBody BatchModifyVinParam param) {
        log.info(LoggerHelper2.formatEnterLog(request) + "param = {}", param);
        if (param.getType() == null) {
            throw new DcArgumentException("参数错误. type不能为空");
        } else if (com.cdz360.base.utils.CollectionUtils.isEmpty(param.getAccountList())) {
            log.info(LoggerHelper2.formatLeaveLog(request));
            return RestUtils.success();
        } else if (com.cdz360.base.utils.CollectionUtils.isEmpty(param.getSiteIdList())) {
            throw new DcArgumentException("请选择场站");
        }
        CorpOrgLoginVo op = getCorpByRequest(request);
        param.setTopCommId(op.getCorpPo().getTopCommId())
            .setCommId(op.getCorpPo().getCommId())
            .setCorpId(op.getCorpId())
            .setOpUid(op.getId());
        this.iBlocUserService.batchModifyVin(param);
        if (NumberUtils.equals(param.getType(), 1)) {
            corpSysLogService.modifyCard(param.getCardChipNoList(), request);
        } else if (NumberUtils.equals(param.getType(), 2)) {
            corpSysLogService.modifyVin(param.getAccountList(), request);
        }
        return RestUtils.success();
    }


    @Operation(summary = "修改VIN")
    @PostMapping("/api/corp/modifyVin")
    public BaseResponse modifyVin(ServerHttpRequest request,
        @RequestBody VinParam vinParam) throws ParseException {
        log.info("vinParam = {}", vinParam);
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        vinParam.setCommId(corpOrgLoginVo.getCorpPo().getTopCommId());
        vinParam.setSubCommId(corpOrgLoginVo.getCorpPo().getCommId());
        vinParam.setCorpId(corpOrgLoginVo.getCorpPo().getId());
//        ListResponse<Long> jsonObjectCommIdList = merchantFeignClient.getCommIdListByCommId(corpOrgLoginVo.getCorpPo().getCommId());
//        FeignResponseValidate.check(jsonObjectCommIdList);
        BaseResponse res = iCorpService.modifyVin(vinParam, corpOrgLoginVo.getGids(),
            corpOrgLoginVo.getCommIdChain());
        corpSysLogService.modifyVin(List.of(vinParam.getVin()), request);
        return res;
    }

    // 不同集团商户间，VIN码可以重复，所以用ID进行操作
    @Operation(summary = "删除VIN")
    @PostMapping("/api/corp/delVin")
    public BaseResponse delVin(ServerHttpRequest request,
        @RequestBody List<Long> idList) {
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        List<String> vinList = iCorpService.delVin(idList,
            corpOrgLoginVo.getCorpPo().getTopCommId(), corpOrgLoginVo.getCorpPo().getId());
        corpSysLogService.delVin(vinList, request);
        return vinList != null ? RestUtils.success() : RestUtils.serverBusy();
    }

    @Operation(summary = "停用VIN")
    @GetMapping("/api/corp/blockUpVin")
    public BaseResponse blockUpVin(ServerHttpRequest request,
        @RequestParam("id") Long id,// 不同集团商户间，VIN码可以重复，所以用ID进行操作
        @RequestParam("vin") String vin) {
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        BaseResponse res = iCorpService.disableVin(id, vin,
            corpOrgLoginVo.getCorpPo().getTopCommId(), corpOrgLoginVo.getCorpPo().getId());
        corpSysLogService.modifyVinStatus(vin, request);
        return res;
    }

    @Operation(summary = "启用VIN")
    @GetMapping("/api/corp/enableVin")
    public BaseResponse enableVin(ServerHttpRequest request,
        @RequestParam("id") Long id,// 不同集团商户间，VIN码可以重复，所以用ID进行操作
        @RequestParam("vin") String vin) {
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        BaseResponse res = iCorpService.enableVin(id, vin,
            corpOrgLoginVo.getCorpPo().getTopCommId(), corpOrgLoginVo.getCorpPo().getId());
        corpSysLogService.modifyVinStatus(vin, request);
        return res;
    }


    @Operation(summary = "获取企业账户信息")
    @GetMapping("/api/corp/getCorpAccountInfo")
    public ObjectResponse<CorpVo> getCorpAccountInfo(ServerHttpRequest request) {
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        ObjectResponse<CorpVo> res = this.iCorpService.getCorpVo(corpOrgLoginVo.getCorpId());
        return res;
    }

    @Operation(summary = "获取企业结算配置信息")
    @GetMapping(value = "/api/corp/getCorpSettlementCfg")
    public ObjectResponse<CorpSettlementCfgVo> getCorpSettlementCfg(ServerHttpRequest request) {
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        return this.iCorpService.getCorpSettlementCfg(corpOrgLoginVo.getCorpId());
    }

    @Operation(summary = "获取当前账户所在组织以及子组织")
    @GetMapping("/api/corp/getOrgByUserId")
    public ListResponse<CorpOrgVO> getOrgByUserId(ServerHttpRequest request,
        @RequestParam("corpId") Long corpId,
        @RequestParam("cusId") Integer cusId) {
        return authCenterFeignClient.getOrgByUserId(super.getToken2(request), corpId, cusId);
    }

    @Operation(summary = "企业客户在线充值")
    @GetMapping(value = "/api/corp/createDepositOrder")
    public ObjectResponse<PaySign> createDepositOrder(
        ServerHttpRequest request,
        @Parameter(name = "充值方式", required = true) @RequestParam(value = "payChannel") PayChannel payChannel,
        @Parameter(name = "充值金额", required = true) @RequestParam(value = "amount") BigDecimal amount) {
        log.info("企业客户在线充值: {}", LoggerHelper2.formatEnterLog(request));
        CorpOrgLoginVo corpInfo = getCorpByRequest(request);

        String ip = AntRestUtils.getIpAddress(request);
        ObjectResponse<PaySign> result = iCorpService.createDepositOrder(ip, corpInfo, payChannel,
            amount);
        corpSysLogService.createDepositOrder(corpInfo.getCorpPo().getCorpName(), amount,
            request);// 记录日志
        return result;
    }

    @Operation(summary = "管理平台功能订阅消费")
    @GetMapping(value = "/api/corp/createSubscribeOrder")
    public ObjectResponse<PaySign> createSubscribeOrder(
        ServerHttpRequest request,
        @Parameter(name = "充值方式", required = true) @RequestParam(value = "payChannel") PayChannel payChannel,
        @Parameter(name = "充值单号", required = true) @RequestParam(value = "payNo") String payNo) {
        log.info("功能订阅在线充值: {}", LoggerHelper2.formatEnterLog(request));
        String ip = AntRestUtils.getIpAddress(request);

        // 功能订阅唯一收款账户  上海鼎充
        Long topCommId = 34474L;
        ObjectResponse<PaySign> result = iCorpService.createSubscribeOrder(
            AntRestUtils.getCommId(request), topCommId, ip, payChannel, payNo);
        return result;
    }


    @Operation(summary = "查询充值单支付状态")
    @GetMapping(value = "/api/corp/payOrderQuery")
    public ObjectResponse<PayBillVo> payOrderQuery(
        ServerHttpRequest request,
        @Parameter(name = "充值单号(平台充值单号)", required = true)
        @RequestParam(value = "payTradeNo") String payTradeNo) {
        log.info("查询充值单支付状态: {}", LoggerHelper2.formatEnterLog(request));
        CorpOrgLoginVo corpInfo = getCorpByRequest(request);
        return iCorpService.payOrderQuery(payTradeNo);
    }

    @Operation(summary = "创建企业soc策略")
    @PostMapping("/api/corp/createCorpStrategy")
    public BaseResponse createCorpStrategy(ServerHttpRequest request,
        @RequestBody SocStrategyDict param) {
        log.info("创建企业soc策略: {}", LoggerHelper2.formatEnterLog(request));
        CorpOrgLoginVo corpInfo = getCorpByRequest(request);
        param.setCorpId(corpInfo.getCorpId());
//        param.getSocStrategy().setCorpId(corpInfo.getCorpId());
//        dataCoreFeignClient.createCorpSocStrategy(param);
        BaseResponse corpSocStrategy = blocUserService.createCorpSocStrategy(param);
        corpSysLogService.createCorpStrategy(param.getName(), request);
        return corpSocStrategy;
    }

    @Operation(summary = "修改企业soc策略")
    @PostMapping("/api/corp/updateCorpStrategy")
    public BaseResponse updateCorpStrategy(ServerHttpRequest request,
        @RequestBody SocStrategyDict param) {
        log.info("修改企业soc策略: {}", LoggerHelper2.formatEnterLog(request));
        CorpOrgLoginVo corpInfo = getCorpByRequest(request);
        param.setCorpId(corpInfo.getCorpId());
//        param.getSocStrategy().setCorpId(corpInfo.getCorpId());
//        return dataCoreFeignClient.updateCorpStrategy(param);
        BaseResponse corpSocStrategy = blocUserService.updateCorpStrategy(param);
        corpSysLogService.updateCorpStrategy(param.getName(), request);
        return corpSocStrategy;
    }

    @Operation(summary = "删除企业soc策略")
    @GetMapping("/api/corp/deleteCorpStrategy")
    public BaseResponse deleteCorpStrategy(ServerHttpRequest request,
        @RequestParam(value = "id") Long id,
        @RequestParam("name") String name) {
        log.info("删除企业soc策略: {}", id);
//        CorpOrgLoginVo corpInfo = getCorpByRequest(request);
//        param.getSocStrategy().setCorpId(corpInfo.getCorpId());
//        return dataCoreFeignClient.deleteCorpStrategy(id);
        BaseResponse baseResponse = blocUserService.deleteCorpStrategy(id);
        corpSysLogService.deleteCorpStrategy(name, request);
        return baseResponse;
    }

    @Operation(summary = "获取企业soc策略")
    @PostMapping("/api/corp/queryCorpStrategy")
    public ListResponse<SocStrategyDict> queryCorpStrategy(ServerHttpRequest request,
        @RequestBody QueryStrategyParam param) {
        log.info("获取企业soc策略: {}", LoggerHelper2.formatEnterLog(request));
        CorpOrgLoginVo corpInfo = getCorpByRequest(request);
        param.setCorpId(corpInfo.getCorpId());
        return blocUserService.queryStrategy(param);
    }

    @Operation(summary = "获取企业soc策略-授信账户")
    @PostMapping("/api/corp/queryCorpStrategyCreditCus")
    public ListResponse<UserSocStrategyCreditCusVo> queryCorpStrategyCreditCus(
        ServerHttpRequest request,
        @RequestBody QueryStrategyParam param) {
        log.info("获取企业soc策略-授信账户: {}, {}", LoggerHelper2.formatEnterLog(request),
            JsonUtils.toJsonString(param));
        CorpOrgLoginVo corpInfo = getCorpByRequest(request);
        param.setCorpId(corpInfo.getCorpId());
        return blocUserService.queryCorpStrategyCreditCus(param);
    }

    @Operation(summary = "获取企业soc策略-VIN")
    @PostMapping("/api/corp/queryCorpStrategyVin")
    public ListResponse<UserSocStrategyVinVo> queryCorpStrategyVin(ServerHttpRequest request,
        @RequestBody QueryStrategyParam param) {
        log.info("获取企业soc策略-授信账户: {}, {}", LoggerHelper2.formatEnterLog(request),
            JsonUtils.toJsonString(param));
        CorpOrgLoginVo corpInfo = getCorpByRequest(request);
        param.setCorpId(corpInfo.getCorpId());
        return blocUserService.queryCorpStrategyVin(param);
    }

    @Operation(summary = "新增企业soc策略-授信账户")
    @PostMapping("/api/corp/addCorpStrategyCreditCus")
    public ObjectResponse<Integer> addCorpStrategyCreditCus(ServerHttpRequest request,
        @RequestBody List<QueryStrategyParam> params) {
        log.info("新增企业soc策略-授信账户: {}, {}", LoggerHelper2.formatEnterLog(request),
            JsonUtils.toJsonString(params));
//        CorpOrgLoginVo corpInfo = getCorpByRequest(request);
//        param.setCorpId(corpInfo.getCorpId());
        ObjectResponse<Integer> integerObjectResponse = blocUserService.addCorpStrategyCreditCus(
            params);
        corpSysLogService.updateCorpStrategy(params.get(0).getStrategyName(), request);
        return integerObjectResponse;
    }

    @Operation(summary = "删除企业soc策略-授信账户")
    @PostMapping("/api/corp/removeCorpStrategyCreditCus")
    public ObjectResponse<Integer> removeCorpStrategyCreditCus(ServerHttpRequest request,
        @RequestBody List<QueryStrategyParam> params) {
        log.info("删除企业soc策略-授信账户: {}, {}", LoggerHelper2.formatEnterLog(request),
            JsonUtils.toJsonString(params));
//        CorpOrgLoginVo corpInfo = getCorpByRequest(request);
//        param.setCorpId(corpInfo.getCorpId());
        ObjectResponse<Integer> integerObjectResponse = blocUserService.removeCorpStrategyCreditCus(
            params);
        corpSysLogService.updateCorpStrategy(params.get(0).getStrategyName(), request);
        return integerObjectResponse;
    }

    @Operation(summary = "新增企业soc策略-vin")
    @PostMapping("/api/corp/addCorpStrategyVin")
    public ObjectResponse<Integer> addCorpStrategyVin(ServerHttpRequest request,
        @RequestBody List<QueryStrategyParam> params) {
        log.info("新增企业soc策略-授信账户: {}, {}", LoggerHelper2.formatEnterLog(request),
            JsonUtils.toJsonString(params));
//        CorpOrgLoginVo corpInfo = getCorpByRequest(request);
//        param.setCorpId(corpInfo.getCorpId());
        ObjectResponse<Integer> integerObjectResponse = blocUserService.addCorpStrategyVin(params);
        corpSysLogService.updateCorpStrategy(params.get(0).getStrategyName(), request);
        return integerObjectResponse;
    }

    @Operation(summary = "删除企业soc策略-vin")
    @PostMapping("/api/corp/removeCorpStrategyVin")
    public ObjectResponse<Integer> removeCorpStrategyVin(ServerHttpRequest request,
        @RequestBody List<QueryStrategyParam> params) {
        log.info("删除企业soc策略-vin: {}, {}", LoggerHelper2.formatEnterLog(request),
            JsonUtils.toJsonString(params));
//        CorpOrgLoginVo corpInfo = getCorpByRequest(request);
//        param.setCorpId(corpInfo.getCorpId());
        ObjectResponse<Integer> integerObjectResponse = blocUserService.removeCorpStrategyVin(
            params);
        corpSysLogService.updateCorpStrategy(params.get(0).getStrategyName(), request);
        return integerObjectResponse;
    }


    @Operation(summary = "企业客户账户信息统计")
    @PostMapping(value = "/api/corp/corpAccountRemainInfo")
    public Mono<ObjectResponse<AccountRemainInfo>> getCorpAccRemainInfo(
        ServerHttpRequest request) {
        log.info("企业客户账户信息统计: {}", LoggerHelper2.formatEnterLog(request, false));

        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        if (null == corpOrgLoginVo || null == corpOrgLoginVo.getCorpPo()) {
            log.warn("企业客户登录失效: {}", JsonUtils.toJsonString(corpOrgLoginVo));
            throw new DcArgumentException("用户登录已失效，请重新登录");
        }

        return blocUserService.getCorpAccountRemainInfo(corpOrgLoginVo.getCorpPo())
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "企业客户退款", description = "企业管理平台<确认退款>")
    @PostMapping(value = "/api/corp/corpAccountRefund")
    public Mono<BaseResponse> corpAccountRefund(
        ServerHttpRequest request, @RequestBody AccountRemainInfo param) {
        log.info("企业客户退款: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        if (null == corpOrgLoginVo || null == corpOrgLoginVo.getCorpPo()) {
            log.warn("企业客户登录失效: {}", JsonUtils.toJsonString(corpOrgLoginVo));
            throw new DcArgumentException("用户登录已失效，请重新登录");
        }

        return blocUserService.corpAccountRefund(corpOrgLoginVo.getCorpPo(), param)
            .doOnNext(res -> corpSysLogService.corpAccountRefund(
                corpOrgLoginVo.getCorpPo().getCorpName(), request))
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "企业客户获取充值列表", description = "企业客户退款页面使用")
    @PostMapping(value = "/api/corp/corpPayBillList")
    public Mono<ListResponse<PayBillRefundVo>> getCorpPayBillList(
        ServerHttpRequest request, @RequestBody ListPayBillParam param) {
        log.info("企业客户获取充值列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        if (null == corpOrgLoginVo || null == corpOrgLoginVo.getCorpPo()) {
            log.warn("企业客户登录失效: {}", JsonUtils.toJsonString(corpOrgLoginVo));
            throw new DcArgumentException("用户登录已失效，请重新登录");
        }

        param.setTopCommId(corpOrgLoginVo.getCorpPo().getTopCommId());
        param.setUserId(corpOrgLoginVo.getCorpPo().getUid());
        return blocUserService.getCorpPayBillList(param);
    }

}
