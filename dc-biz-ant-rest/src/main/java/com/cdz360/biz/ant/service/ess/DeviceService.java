package com.cdz360.biz.ant.service.ess;


import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.trading.ess.param.AddEssCfgParam;
import com.cdz360.biz.model.trading.ess.param.AddGtiCfgParam;
import com.cdz360.biz.model.trading.ess.param.ListDevCfgParam;
import com.cdz360.biz.model.trading.ess.vo.DevCfgVo;
import com.cdz360.biz.model.trading.ess.vo.EssDetailVo;
import com.cdz360.biz.model.trading.iot.vo.DevGtiCfgVo;
import com.cdz360.biz.utils.feign.iot.DeviceFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;


@Slf4j
@Service
public class DeviceService {

    @Autowired
    DeviceFeignClient deviceFeignClient;

    public Mono<ObjectResponse<Long>> addEss(AddEssCfgParam param) {
        return deviceFeignClient.addEss(param);
    }

    public Mono<ObjectResponse<Long>> editEss(AddEssCfgParam param) {
        return deviceFeignClient.editEss(param);
    }

    public Mono<BaseResponse> addGtiCfg(AddGtiCfgParam param) {
        return deviceFeignClient.addGtiCfg(param);
    }

    public Mono<ListResponse<DevCfgVo>> getDevCfgList(ListDevCfgParam param) {
        return deviceFeignClient.getDevCfgList(param);
    }

    public Mono<BaseResponse> editGtiCfg(AddGtiCfgParam param) {
        return deviceFeignClient.editGtiCfg(param);
    }

    public Mono<BaseResponse> delDevCfg(AddGtiCfgParam param) {
        return deviceFeignClient.delDevCfg(param);
    }

    public Mono<ObjectResponse<EssDetailVo>> getDevCfgDetail(Long id, String commIdChain) {
        return deviceFeignClient.getDevCfgDetail(id, commIdChain);
    }

    public Mono<ObjectResponse<DevGtiCfgVo>> getGtiCfgDetail(Long id, String commIdChain) {
        return deviceFeignClient.getGtiCfgDetail(id, commIdChain);
    }
}
