package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.corp.type.CorpType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.domain.BlocUser;
import com.cdz360.biz.ant.domain.First;
import com.cdz360.biz.ant.domain.vo.BlocUserVo;
import com.cdz360.biz.ant.domain.vo.BlocWalletVO;
import com.cdz360.biz.ant.service.BlocUserService;
import com.cdz360.biz.ant.service.sysLog.CorpSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.auth.corp.param.BalanceRemindParam;
import com.cdz360.biz.model.cus.corp.vo.CorpSimpleVo;
import com.cdz360.biz.model.cus.soc.vo.SocCorpVo;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.BlocUserDto;
import com.chargerlinkcar.framework.common.domain.vo.AccountInfoVo;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;

/**
 * 集团功能
 *
 * <AUTHOR>
 *  集团功能
 * @since 2018.11.21
 */
@Slf4j
@RestController
@RequestMapping("/api/blocUser")
public class BlocUserRest extends BaseController {

    @Autowired
    private BlocUserService blocUserService;
    @Autowired
    private CorpSysLogService corpSysLogService;


    /**
     * 获取当前用户下的集团列表
     */
    @GetMapping("/queryBlocUser")
    public ListResponse<BlocUserVo> queryBlocUser(
        ServerHttpRequest request,
        ServerWebExchange exh,
        @Parameter(name = "所属商户ID") @RequestParam(value = "commId", required = false) Long commId,
        @Parameter(name = "集团客户名称/账户/联系人名称/手机号") @RequestParam(value = "keyWord", required = false) String keyWord,
        @Parameter(name = "企业名称查询") @RequestParam(value = "corpName", required = false) String corpName,
        @Parameter(name = "企业类型查询") @RequestParam(value = "corpType", required = false) CorpType corpType,
        @Parameter(name = "企业客户状态") @RequestParam(value = "enable", required = false) Boolean enable,
        @Parameter(name = "用于转换成idChain") @RequestParam(value = "leaderCommId", required = false) Long leaderCommId,
        @Parameter(name = "结算类型") @RequestParam(value = "settlementType", required = false) Long settlementType,
        @Parameter(name = "平台开票方式") @RequestParam(value = "invoiceWay", required = false) String invoiceWay,
        @Parameter(name = "引流人") @RequestParam(value = "referrer", required = false) String referrer,
        @Parameter(name = "corpId列表") @RequestParam(value = "corpIdList", required = false) List<Long> corpIdList) {
        log.debug(LoggerHelper2.formatEnterLog(request));
        String token = getToken2(request);
        //根据token获取商户ID集合
        OldPageParam page = getPage2(request, exh, false);

        String commIdChain = super.getCommIdChain2(request);
        if (leaderCommId != null) {
            ObjectResponse<Commercial> commercial = commercialFeignClient.getCommercial(
                leaderCommId);
            FeignResponseValidate.check(commercial);
            commIdChain = commercial.getData().getIdChain();
            log.debug("leaderCommId -> idChain: {} -> {}", leaderCommId,
                commercial.getData().getIdChain());
        }

        return blocUserService.queryBlocUser(request, page, keyWord, commId, corpName, corpType,
            commIdChain, enable, settlementType, invoiceWay,
            referrer,
            corpIdList);
    }

    @Operation(summary = "获取场站下已配置soc的企业")
    @GetMapping("/queryBlocUserForSiteSoc")
    public ListResponse<SocCorpVo> queryBlocUserForSiteSoc(
        ServerHttpRequest request,
        @Parameter(name = "场站id") @RequestParam(value = "siteId") String siteId) {
        return blocUserService.queryBlocUserForSiteSoc(siteId);
    }

    /**
     * 通过idChain获取商户子商户企业
     *
     * @param request
     * @return
     */
    @GetMapping("/getCorpByCommId")
    public ListResponse<CorpSimpleVo> getCorpByCommId(ServerHttpRequest request,
        @RequestParam(value = "corpId", required = false) Long corpId) {
        log.debug(LoggerHelper2.formatEnterLog(request));

        return blocUserService.getCorpByCommId(AntRestUtils.getToken2(request),
            super.getCommIdChain2(request), corpId);
    }

    /**
     * 根据集团Id查询集团基础信息(新增余额字段)
     *
     * @param blocUserId 集团id
     * @return
     */
    @ResponseBody
    @GetMapping("/getBlocUserByBlocUserId")
    public ObjectResponse<BlocWalletVO> getBlocUserByBlocUserId(ServerHttpRequest request,
        Long blocUserId) {
        BlocWalletVO bloc = blocUserService.getBlocUserByBlocUserId(request, blocUserId);
        return RestUtils.buildObjectResponse(bloc);
    }

    /**
     * 新增集团
     *
     * @param blocUser
     * @return
     */
    @ResponseBody
    @PostMapping("/insertBlocUser")
    public BaseResponse insertBlocUser(ServerHttpRequest request,
        @RequestBody @Validated(First.class) BlocUser blocUser) {
//        String token = getToken(request);
        BaseResponse res = blocUserService.insertBlocUser(request, blocUser,
            AntRestUtils.getTopCommId(request));
        corpSysLogService.insertBlocUserLog(blocUser.getBlocUserName(), request);
        return res;
    }

    /**
     * 更新集团信息
     *
     * @param blocUser
     * @return
     */
    @ResponseBody
    @PostMapping("/updateBlocUser")
    public BaseResponse updateBlocUser(ServerHttpRequest request,
        @RequestBody @Validated(First.class) BlocUser blocUser) {
        BaseResponse res = blocUserService.updateBlocUser(request, blocUser);
        corpSysLogService.updateBlocUserLog(blocUser.getBlocUserName(), request);
        return res;
    }

    /**
     * 删除集团(禁用集团)
     *
     * @param blocUserId
     * @return
     */
    @ResponseBody
    @GetMapping("/deleteBlocUserById")
    public BaseResponse deleteBlocUserById(ServerHttpRequest request, Long blocUserId) {
        BlocUserDto res = blocUserService.deleteBlocUserById(blocUserId);
        corpSysLogService.deleteOrEnableLog(res.getBlocUserName(), request);
        return res != null ? RestUtils.success() : RestUtils.serverBusy();
    }

    /**
     * 启用集团
     *
     * @param blocUserId
     * @return
     */
    @ResponseBody
    @GetMapping("/enableBlocUserById")
    public BaseResponse enableBlocUserById(ServerHttpRequest request, Long blocUserId) {
        BlocUserDto dto = blocUserService.enableBlocUserById(blocUserId);
        corpSysLogService.deleteOrEnableLog(dto.getBlocUserName(), request);
        return dto != null ? RestUtils.success() : RestUtils.serverBusy();
    }

    /**
     * 通过token查询本身及下属商户的集团客户 新增紧急卡-集团客户下拉
     */
    @GetMapping("/selectSubBlocUserByCommIdChain")
    public ListResponse<BlocUserDto> selectSubBlocUserByCommIdChain(
        ServerHttpRequest request,
        @Parameter(name = "是否包含互联企业 null or true 表示包含; false 表示不包含")
        @RequestParam(value = "includedHlhtCorp", required = false) Boolean includedHlhtCorp,
        @Parameter(name = "商户ID") @RequestParam(value = "commId", required = false) Long commId) {
        String commIdChain = super.getCommIdChain2(request);
        return blocUserService.selectSubBlocUserByCommIdChain(commIdChain, includedHlhtCorp,
            commId);
    }

    /**
     * 集团客户下拉，用于运营支撑平台
     */
    @GetMapping("/selectSubBlocUserByTokenOnOperate")
    public ListResponse<BlocUserDto> selectSubBlocUserByTokenOnOperate(ServerHttpRequest request) {
        return blocUserService.selectSubBlocUserByTokenOnOperate();
    }

    /**
     * 根据集团id获取集团账户信息
     *
     * @param blocUserId
     * @return
     */
    @Operation(summary = "根据集团id获取集团账户信息")
    @GetMapping("/getBlocUserAccount")
    public ObjectResponse<AccountInfoVo> getBlocUserAccount(
        @Parameter(name = "集团id") @RequestParam("blocUserId") Long blocUserId,
        ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return blocUserService.getBlocUserAccount(blocUserId);
    }

    @Operation(summary = "根据企业id设置续费提醒金额")
    @PostMapping("/setRenewReminderAmount")
    public BaseResponse setRenewReminderAmount(ServerHttpRequest request,
        @RequestBody BalanceRemindParam param) {
        log.info("根据企业id设置续费提醒金额: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        BaseResponse res = blocUserService.setRenewReminderAmount(param);
        corpSysLogService.setRenewReminderAmount(param.getCorpName(), request);
        return res;
    }
}
