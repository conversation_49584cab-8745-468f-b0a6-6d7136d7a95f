package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.AntUserFeignClient;
import com.cdz360.biz.ant.feign.BizBiFeignClient;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.VinFeignClient;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.auth.user.type.LogOpType;
import com.cdz360.biz.model.cus.SiteAuthVin.po.SiteAuthVinPo;
import com.cdz360.biz.model.cus.SiteAuthVin.vo.SiteListVo;
import com.cdz360.biz.model.cus.site.po.SitePo;
import com.cdz360.biz.model.cus.site.vo.MoveCorpSiteAuthList;
import com.cdz360.biz.model.cus.vin.param.VinSearchParam;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.vin.po.SiteAuthVinLogPo;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.domain.vo.*;
import com.chargerlinkcar.framework.common.feign.CommercialFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Collections;
import java.util.List;

/**
 * VinServiceImpl
 *
 * @since 2019/5/15 10:01
 * <AUTHOR>
 */
@Slf4j
@Service
public class VinService //implements IVinService
{

    @Autowired
    private CorpService corpService;
    @Autowired
    private VinFeignClient vinFeignClient;
    @Autowired
    private AntUserFeignClient userFeignClient;
    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;
    @Autowired
    private BizBiFeignClient bizBiFeignClient;

    @Autowired
    private CommercialFeignClient commercialFeignClient;

    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;

    public BaseResponse create(VinParam vinParam) {
        return vinFeignClient.create(vinParam);
    }


    public BaseResponse delete(VinParam vinParam) {
        List<String> noSettlementVinList = corpService.getNoSettlementVin(
            Collections.singletonList(vinParam.getVin()), vinParam.getCommId());
        if (CollectionUtils.isNotEmpty(noSettlementVinList)) {
            throw new DcServiceException("VIN码存在订单尚未结算，请处理后再进行操作");
        }
        return vinFeignClient.delete(vinParam.getId());
    }


    public BaseResponse update(VinParam vinParam) {
        if (StringUtils.equals(vinParam.getType(), LogOpType.MODIFY.name())) {

            ObjectResponse<VinDto> oldVinRes = vinFeignClient.getById(vinParam.getId());
            FeignResponseValidate.check(oldVinRes);
            VinDto oldVin = oldVinRes.getData();
            if (!oldVin.getVin().equals(vinParam.getVin())) {
                ObjectResponse<VinDto> vinDtoObjectResponse = vinFeignClient.selectByVin(
                    vinParam.getVin().toUpperCase(),
                    vinParam.getCommId());
                IotAssert.isNull(vinDtoObjectResponse.getData(), "修改失败，VIN码已被使用");
            }

            List<String> noSettlementVinList = corpService.getNoSettlementVin(
                Collections.singletonList(vinParam.getVin()), vinParam.getCommId());
            if (CollectionUtils.isNotEmpty(noSettlementVinList)) {
                throw new DcServiceException("VIN码存在订单尚未结算，请处理后再进行操作");
            }
        }
        return vinFeignClient.update(vinParam);
    }


    public ListResponse<VinDto> select(VinSearchParam vinSearchParam, Long leaderCommId) {

        if (leaderCommId != null) {
            ObjectResponse<Commercial> commercial = commercialFeignClient.getCommercial(
                leaderCommId);
            FeignResponseValidate.check(commercial);
            vinSearchParam.setCommIdChain(commercial.getData().getIdChain());
            log.debug("vin leaderCommId -> idChain: {} -> {}", leaderCommId,
                commercial.getData().getIdChain());
        }

        return vinFeignClient.select(vinSearchParam);
    }

    public Mono<ObjectResponse<ExcelPosition>> exportVinList(Long sysUid,
        VinSearchParam vinSearchParam, Long leaderCommId) {
        if (leaderCommId != null) {
            ObjectResponse<Commercial> commercial = commercialFeignClient.getCommercial(
                leaderCommId);
            FeignResponseValidate.check(commercial);
            vinSearchParam.setCommIdChain(commercial.getData().getIdChain());
            log.debug("vin leaderCommId -> idChain: {} -> {}", leaderCommId,
                commercial.getData().getIdChain());
        }

        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("车辆VIN码信息导出")
            .setFunctionMap(DownloadFunctionType.VIN_RECORD)
            .setReqParam(JsonUtils.toJsonString(vinSearchParam));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return bizBiFeignClient.exportVinForManage(vinSearchParam);
    }

    public ObjectResponse<VinDto2> getVinDto2ById(Long vinId, String commIdChain) {
        return vinFeignClient.getVinDto2ById(vinId, commIdChain);
    }

    public ObjectResponse<BatteryVo> getBatteryVo(String vin, String commIdChain) {
        return dataCoreFeignClient.getBatteryVo(vin, commIdChain);
    }

    public ListResponse<VinDto> selectAllVinList(String token, Long userId) {
        return userFeignClient.selectAllVinList(token, userId);
    }

    public ObjectResponse<MoveCorpSiteAuthList> getMoveCorpVINByCorpId(Long corpId, Long commId) {
        return userFeignClient.getMoveCorpVINByCorpId(corpId, commId);
    }

    public ObjectResponse<SiteAuthVinLogPo> getVinListByEvse(String evseId) {
        return userFeignClient.getVinListByEvse(evseId);
    }

    public ListResponse<SiteAuthVinPo> getVinListBySiteId(String siteId) {
        return userFeignClient.getVinListBySiteId(siteId);
    }

    public ListResponse<SiteListVo> getSiteListByVin(String siteId, Long start, Long size) {
        return userFeignClient.getSiteListByVin(siteId, start, size);
    }

    public ObjectResponse<Integer> siteAuthVin(SiteAuthVinParam param) {
        // 按场站下发，下发到场站下所有桩
        // 只需要提供场站列表
        return userFeignClient.siteAuthVin(param);
    }

    public ObjectResponse<Integer> siteAuthVinOnSite(SiteAuthVinParam param) {

        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getSiteIdList()), "请传入下发的场站id");
        IotAssert.isTrue(param.getSiteIdList().size() == 1, "场站id仅允许1个");

        if (CollectionUtils.isEmpty(param.getEvseNoList())) {
            log.info("未指定桩列表，尝试下发到场站所有桩");
        } else {
            log.info("指定桩列表，尝试下发到场站指定桩");
        }
        return this.siteAuthVin(param);
    }

    public ListResponse<SitePo> getCommonSiteList(VinParam param) {
        return userFeignClient.getCommonSiteList(param);
    }

}