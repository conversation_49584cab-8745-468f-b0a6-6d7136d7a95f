package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.service.EvseManagerService;
import com.cdz360.biz.ant.service.sysLog.SiteSysLogService;
import com.cdz360.biz.model.iot.param.ModifyEvseCfgParam;
import com.cdz360.biz.model.trading.evse.EvseInfo;
import com.chargerlinkcar.framework.common.domain.CommercialSample;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "桩管家接口", description = "桩管家接口")
@Slf4j
@RestController
public class EvseManagerRest extends BaseController {

    @Autowired
    private EvseManagerService evseManagerService;
    @Autowired
    private SiteSysLogService siteSysLogService;

    @Operation(summary = "获取桩信息")
    @GetMapping(value = "/api/evseManager/getEvseInfo")
    public ObjectResponse<EvseInfo> getEvseInfo(@RequestParam(value = "evseNo") String evseNo) {
        log.info("getEvseInfo evseNo: {}", evseNo);
        return evseManagerService.getEvseInfo(evseNo);
    }

    @Operation(summary = "按场站下发默认配置(场站下所有桩)")
    @GetMapping(value = "/api/evseManager/downDefultSetting2AllEvse")
    public BaseResponse downDefultSetting2AllEvse(ServerHttpRequest request,
                                                  @RequestParam("siteId") String siteId,
                                                  @RequestParam("siteName") String siteName) {
        log.info("downDefultSetting2AllEvse siteId: {} siteName: {}", siteId, siteName);
        IotAssert.isNotNull(siteName, "siteName不能为空");
        BaseResponse res = evseManagerService.downDefultSetting2AllEvse(siteId);
        siteSysLogService.downDefultSetting2AllEvseLog(siteName, request);
        return res;
    }

    @Operation(summary = "按场站下发默认计费模板(场站下所有桩)")
    @GetMapping(value = "/api/evseManager/downDefultTemplate2AllEvse")
    public BaseResponse downDefultTemplate2AllEvse(ServerHttpRequest request,
                                                   @RequestParam("siteId") String siteId) {
        log.info("downDefultTemplate2AllEvse siteId: {}", siteId);
        // 获取操作人
        CommercialSample comm = this.getCommercialSample2(request);
        if (ObjectUtils.isEmpty(comm)) {
            log.warn("当前操作用户信息不存在，请重新登录");
            throw new DcArgumentException("当前操作用户信息不存在，请重新登录");
        }
        ModifyEvseCfgParam res = evseManagerService.downDefultTemplate2AllEvse(siteId, comm.getId());
        siteSysLogService.downDefultTemplate2AllEvse(res.getName(), res.getSiteName(), request);
        return res != null ? RestUtils.success() : RestUtils.serverBusy();
    }

    @Operation(summary = "按桩重新下发场站默认配置")
    @GetMapping(value = "/api/evseManager/downSettingByEvse")
    public BaseResponse downSettingByEvse(ServerHttpRequest request,
                                          @RequestParam("evseNo") String evseNo) {
        log.info("downSettingByEvse evseNo: {}", evseNo);
        BaseResponse res = evseManagerService.downDefultTemplate2AllEvse(evseNo);
        siteSysLogService.downSettingByEvseLog(evseNo, request);
        return res;
    }

    @Operation(summary = "重新下发紧急充电卡（传入evseNo时，则只下发该桩）")
    @GetMapping(value = "/api/evseManager/sendWhiteCard")
    public BaseResponse sendWhiteCard(ServerHttpRequest request,
                                      @RequestParam(value = "siteId") String siteId,
                                      @RequestParam(value = "siteName") String siteName,
                                      @RequestParam(value = "evseNo", required = false) String evseNo) {
        log.info("sendWhiteCard siteId: {} siteName: {} evseNo: {}", siteId, siteName, evseNo);
        IotAssert.isNotNull(siteName, "siteName不能为空");
        List<String> res = evseManagerService.sendWhiteCard(siteId, evseNo);
        siteSysLogService.sendWhiteCardLog(res, siteName, request);
        return res != null ? RestUtils.success() : RestUtils.serverBusy();
    }
}
