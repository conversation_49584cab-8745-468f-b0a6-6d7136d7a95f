package com.cdz360.biz.ant.domain.vo;

import com.cdz360.biz.model.trading.iot.vo.EvseInfoVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AntEvseInfoVo extends EvseInfoVo {

    @Schema(description = "是否使用场站默认设置")
    private Boolean useSiteSetting;
}
