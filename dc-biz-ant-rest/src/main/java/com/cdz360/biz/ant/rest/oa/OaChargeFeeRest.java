package com.cdz360.biz.ant.rest.oa;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.feign.reactor.OaFeignClient;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.oa.param.ChargeFeeParam;
import com.cdz360.biz.oa.param.RechargeParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Api(value = "电价下发OA申请")
@Slf4j
@RestController
@RequestMapping("/oa/chargeFee")
public class OaChargeFeeRest {

    @Autowired
    private OaFeignClient oaFeignClient;

    @Deprecated(since = "20230721")
    @ApiOperation(value = "电价下发申请提交")
    @PostMapping("/startProcess")
    public Mono<ObjectResponse<String>> startChargeFeeProcess(
        ServerHttpRequest request, @RequestBody ChargeFeeParam param) {
        log.info("电价下发申请提交: {}", JsonUtils.toJsonString(param));
        ChargeFeeParam.checkField(param);

        RechargeParam rechargeParam = new RechargeParam();
        rechargeParam.setTopCommId(AntRestUtils.getTopCommId(request));
        rechargeParam.setTenantIdChain(AntRestUtils.getCommIdChain(request));
        rechargeParam.setOUid(AntRestUtils.getSysUid(request));
        rechargeParam.setOName(AntRestUtils.getSysUserName(request));
        rechargeParam.setOPhone(AntRestUtils.getSysUserPhone(request));
        rechargeParam.setFormVariables(BeanMap.create(param));
        return this.oaFeignClient.startChargeFeeProcess(rechargeParam);
    }
}
