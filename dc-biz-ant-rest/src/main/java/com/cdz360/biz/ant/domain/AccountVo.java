package com.cdz360.biz.ant.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class AccountVo implements Serializable {
    /**
     * 用户UID
     */
    private Long uid;

    /**
     * 用户所属商户id
     */
    private Long appCommId;
    /**
     *
     */
    private String username;

    public String getUsername() {
//        if (Base64Util.isBase64(username)) {
//            return Base64Util.getFromBase64(username);
//        } else {
            return username;
//        }
    }

    /**
     * 手机号
     */
    private String phone;

    /**
     * 头像
     */
    private String image;

    /**
     * 手机号地区编码
     */
    private String nationalCode;

    /**
     * 是否第一次登录(1.是,0.否)
     */
    private Integer isFirstLogin;

    /**
     * 移车电话
     */
    private String movePhone;

    private static final long serialVersionUID = 1L;


}