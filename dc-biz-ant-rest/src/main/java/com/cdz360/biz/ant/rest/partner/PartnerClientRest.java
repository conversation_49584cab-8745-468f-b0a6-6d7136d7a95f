package com.cdz360.biz.ant.rest.partner;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.domain.request.AddAccountRequest;
import com.cdz360.biz.ant.domain.request.EditAccountRequest;
import com.cdz360.biz.ant.service.partner.PartnerClientService;
import com.cdz360.biz.model.trading.hlht.vo.ClientOperaterVo;
import com.cdz360.biz.model.trading.hlht.vo.HlhtSiteCommVo;
import com.cdz360.biz.model.trading.hlht.vo.OperatorVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping(value = "/api/partner/client")
@Tag(name = "客户侧互联相关接口", description = "客户侧互联")
public class PartnerClientRest {

    @Autowired
    private PartnerClientService service;

    @Operation(summary = "互联运营商列表")
    @GetMapping(value = "/getList")
    public Mono<ListResponse<OperatorVo>> partnerList(@RequestParam(value = "start") Long start,
                                                      @RequestParam(value = "size") Integer size,
                                                      @Parameter(name = "运营商标识, 模糊匹配")
                                                      @RequestParam(value = "code", required = false) String code,
                                                      @Parameter(name = "客户侧运营商名称, 模糊匹配")
                                                      @RequestParam(value = "name", required = false) String name,
                                                      @Parameter(name = "完整结算企业名称, 精确匹配")
                                                      @RequestParam(value = "corpName", required = false) String corpName,
                                                      @Parameter(name = "完整站点名称, 精确匹配")
                                                      @RequestParam(value = "siteName", required = false) String siteName) {
        return service.partnerList(start, size, code, name, corpName, siteName);
    }

    @Operation(summary = "获取详情")
    @GetMapping(value = "/getDetail")
    public Mono<ObjectResponse<ClientOperaterVo>> getDetail(@RequestParam(value = "partnerId") Long partnerId) {
        log.info("getDetail partnerId: {}", partnerId);
        return service.getDetail(partnerId);
    }

    @Operation(summary = "修改停充超时配置")
    @GetMapping(value = "/editBillingBack")
    public Mono<BaseResponse> editBillingBack(@RequestParam(value = "partnerId") Long partnerId,
                                              @RequestParam(value = "billingBack") Boolean billingBack) {
        log.info("editBillingBack partnerId: {}, billBack: {}", partnerId, billingBack);
        return service.editBillingBack(partnerId, billingBack);
    }

    @Operation(summary = "启用禁用结算账户")
    @GetMapping(value = "/switchAccountStatus")
    public Mono<BaseResponse> switchAccountStatus(@RequestParam(value = "accountId") Long accountId) {
        log.info("switchAccountStatus accountId: {}", accountId);
        return service.switchAccountStatus(accountId);
    }

    @Operation(summary = "新增结算账户")
    @PostMapping(value = "/addAccount")
    public Mono<BaseResponse> addAccount(@RequestBody AddAccountRequest request) {
        log.info("addAccount request: {}", request);
        return service.addAccount(request);
    }

    @Operation(summary = "修改结算账户")
    @PostMapping(value = "/editAccount")
    public Mono<BaseResponse> editAccount(@RequestBody EditAccountRequest request) {
        log.info("editAccount request: {}", request);
        return service.editAccount(request);
    }

    @GetMapping(value = "/getSiteByPartnerCode")
    public ObjectResponse<HlhtSiteCommVo> getSiteByPartnerCode(@RequestParam(value = "partnerCode") String code) {
        log.info("code:{}",code);
        return service.getSiteByPartnerCode(code);
    }

}
