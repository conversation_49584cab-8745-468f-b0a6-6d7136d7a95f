package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.trading.prerun.dto.PrerunMixDto;
import com.cdz360.biz.model.trading.prerun.param.PrerunCheckParam;
import com.cdz360.biz.model.trading.prerun.param.PrerunSearchParam;
import com.cdz360.biz.model.trading.prerun.vo.PrerunVo;
import com.cdz360.biz.utils.feign.prerun.PrerunFeignClient;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 *  开通调试工单
 * @since 6/23/2022 10:55 AM
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "开通调试工单", description = "Test Run")
@RequestMapping("/api/prerun")
public class PrerunRest {

    @Autowired
    private PrerunFeignClient prerunFeignClient;

    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;

//    @Operation(summary = "创建开通调试工单")
//    @PostMapping(value = "/openPrerun")
//    public Mono<ObjectResponse<PrerunMixDto>> openPrerun(
//        ServerHttpRequest request, @RequestBody PrerunMixDto param) {
//        log.info("创建开通调试工单: {}", JsonUtils.toJsonString(param));
//        return prerunFeignClient.openPrerun(param);
//    }

    @Operation(summary = "创建开通调试工单")
    @PostMapping(value = "/createPrerun")
    public Mono<ObjectResponse<PrerunMixDto>> createPrerun(
        ServerHttpRequest request, @RequestBody PrerunMixDto param) {
        log.info("创建开通调试工单: {}", JsonUtils.toJsonString(param));

        Long opUid = AntRestUtils.getSysUid(request);
        String opName = AntRestUtils.getSysUserName(request);
        IotAssert.isNotNull(opUid, "请登录");
        IotAssert.isNotNull(param.getPrerun(), "请传入工单");
        param.getPrerun().setPrerunnerUid(opUid);
        param.getPrerun().setPrerunnerName(opName);

        return prerunFeignClient.createPrerun(param);
    }

    @Operation(summary = "修改开通调试工单")
    @PostMapping(value = "/updatePrerun")
    public Mono<ObjectResponse<PrerunMixDto>> updatePrerun(
        ServerHttpRequest request, @RequestBody PrerunMixDto param) {
        log.info("修改开通调试工单: {}", JsonUtils.toJsonString(param));
        return prerunFeignClient.updatePrerun(param);
    }

    @Operation(summary = "提交开通调试工单")
    @PostMapping(value = "/submitPrerun")
    public Mono<ObjectResponse<PrerunVo>> submitPrerun(
        ServerHttpRequest request, @RequestBody PrerunCheckParam param) {
        log.info("提交开通调试工单: {}", JsonUtils.toJsonString(param));
        return prerunFeignClient.submitPrerun(param);
    }

    @Operation(summary = "查找场站最近一个有效工单,或精确获取")
    @GetMapping(value = "/getLatestPrerun")
    public Mono<ObjectResponse<PrerunMixDto>> getLatestPrerun(
        ServerHttpRequest request, @RequestParam(value = "siteId", required = false) String siteId,
        @RequestParam(value = "prerunId", required = false) Long prerunId) {
        log.info("查找场站最近一个有效工单: {}, {}", siteId, prerunId);
        return prerunFeignClient.getLatestPrerun(siteId, prerunId);
    }

    @Operation(summary = "列表查询")
    @PostMapping(value = "/searchPrerunList")
    public Mono<ListResponse<PrerunMixDto>> searchPrerunList(
        ServerHttpRequest request, @RequestBody PrerunSearchParam param) {
        log.info("查询开通调试工单: {}", JsonUtils.toJsonString(param));
        AppClientType appClientType = AntRestUtils.getAppClientType(request);
        if (AppClientType.MGM_WX_LITE.equals(appClientType)) {
            log.info("使用{}访问, 入参增加创建者.", appClientType.getDesc());
            param.setPrerunnerUid(AntRestUtils.getSysUid(request));
        } else {
            log.info("使用{}访问.", appClientType.getDesc());
            if (CollectionUtils.isEmpty(param.getGids())) {
                List<String> gids = AntRestUtils.getSysUserGids(request);
                if (CollectionUtils.isNotEmpty(gids)) {
                    param.setGids(gids);
                } else {
                    param.setCommIdChain(AntRestUtils.getCommIdChainAndFilter(request));
                }
            }
        }
        return prerunFeignClient.searchPrerunList(param);
    }

    @Operation(summary = "质检调试工单")
    @PostMapping(value = "/checkPrerun")
    public Mono<ObjectResponse<Boolean>> checkPrerun(
        ServerHttpRequest request, @RequestBody PrerunCheckParam param) {
        log.info("质检调试工单: {}", JsonUtils.toJsonString(param));

        Long opUid = AntRestUtils.getSysUid(request);
        String opName = AntRestUtils.getSysUserName(request);
        IotAssert.isNotNull(opUid, "请登录");

        return prerunFeignClient.checkPrerun(param.setOpName(opName).setOpUid(opUid));
    }

    @Operation(summary = "取消调试工单")
    @PostMapping(value = "/cancelPrerun")
    public Mono<ObjectResponse<Boolean>> cancelPrerun(
        ServerHttpRequest request, @RequestBody PrerunCheckParam param) {
        log.info("取消调试工单: {}", JsonUtils.toJsonString(param));
        return prerunFeignClient.cancelPrerun(param);
    }

    @Operation(summary = "删除调试工单")
    @PostMapping(value = "/deletePrerun")
    public Mono<ObjectResponse<Boolean>> deletePrerun(
        ServerHttpRequest request, @RequestBody PrerunCheckParam param) {
        log.info("删除调试工单: {}", JsonUtils.toJsonString(param));
        return prerunFeignClient.deletePrerun(param);
    }

    @Operation(summary = "导出调试工单详情")
    @GetMapping(value = "/exportPrerunDetailPdf")
    public Mono<ObjectResponse<ExcelPosition>> exportPrerunDetailPdf(
        ServerHttpRequest request,
        @ApiParam(value = "下载导出文件名") @RequestParam(value = "exFileName", required = false) String exFileName,
        @Parameter(name = "调试工单id", required = true) @RequestParam("prerunId") Long prerunId) {
        log.info("导出调试工单详情PDF: prerunId = {}", prerunId);

        IotAssert.isNotNull(prerunId, "调试工单编号无效");

        Long sysUid = AntRestUtils.getSysUid(request);
        ObjectNode reqParam = new ObjectMapper().createObjectNode();
        reqParam.put("prerunId", prerunId);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.PDF)
            .setUid(sysUid)
            .setExFileName(StringUtils.isNotBlank(exFileName) ? exFileName : "开通调试详情")
            .setFunctionMap(DownloadFunctionType.PRERUN_DETAIL)
            .setReqParam(reqParam.toString());
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return ywOrderService.exportYwOrderPdf(ywOrderNo);
    }

    @Operation(summary = "导出调试工单列表excel")
    @PostMapping(value = "/exportPrerunListExcel")
    public Mono<ObjectResponse<ExcelPosition>> exportPrerunListExcel(
        ServerHttpRequest request,
        @RequestBody PrerunSearchParam param) {
        log.info("导出调试工单列表excel: param = {}", JsonUtils.toJsonString(param));

        IotAssert.isNotNull(param, "调试工单查询入参无效");

        Long sysUid = AntRestUtils.getSysUid(request);
        ObjectNode reqParam = new ObjectMapper().createObjectNode();
        reqParam.put("param", JsonUtils.toJsonString(param));
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName(StringUtils.isNotBlank(param.getExFileName()) ? param.getExFileName()
                : "开通调试工单")
            .setFunctionMap(DownloadFunctionType.PRERUN_LIST)
            .setReqParam(reqParam.toString());
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return ywOrderService.exportYwOrderPdf(ywOrderNo);
    }
}