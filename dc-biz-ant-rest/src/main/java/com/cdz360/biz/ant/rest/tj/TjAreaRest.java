package com.cdz360.biz.ant.rest.tj;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.service.tj.TjAreaService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.sys.constant.SiteGroupOwnType;
import com.cdz360.biz.model.sys.constant.SiteGroupType;
import com.cdz360.biz.model.sys.param.ListSiteGroupParam;
import com.cdz360.biz.model.sys.vo.TjSiteGroupVo;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaParam;
import com.cdz360.biz.model.tj.kc.vo.TjAreaVo;
import com.cdz360.biz.utils.feign.auth.AuthSiteGroupFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "投建区域相关操作接口", description = "投建区域相关操作接口")
@Slf4j
@RestController
@RequestMapping("/api/tj/area")
public class TjAreaRest {

    @Autowired
    private AuthSiteGroupFeignClient authSiteGroupFeignClient;

    @Autowired
    private TjAreaService tjAreaService;

    @Operation(summary = "获取投建场站组")
    @PostMapping("/siteGroup")
    public Mono<ListResponse<TjSiteGroupVo>> tjSiteGroup(
        ServerHttpRequest request,
        @RequestBody BaseListParam param) {
        log.info("获取投建场站组 {} param = {}",
            LoggerHelper2.formatEnterLog(request, false), param);
        val search = new ListSiteGroupParam();
        BeanUtils.copyProperties(param, search);
        search.setGidList(AntRestUtils.getSysUserGids(request))
            .setTypeList(List.of(SiteGroupType.TJ))
            .setOwnTypeList(List.of(SiteGroupOwnType.ZY));
        search.setEnable(true);
        return this.authSiteGroupFeignClient.findSiteGroup(search)
            .doOnNext(FeignResponseValidate::check)
            .map(x -> RestUtils.buildListResponse(x.getData().stream()
                    .map(g -> new TjSiteGroupVo().setGid(g.getGid()).setName(g.getName()))
                    .collect(Collectors.toUnmodifiableList()),
                x.getTotal() == null ? 0L : x.getTotal()));
    }

    @Operation(summary = "获取当前用户所有的投建区域")
    @PostMapping(value = "/getAllOwnArea")
    public Mono<ListResponse<TjAreaVo>> getAllOwnArea(
        ServerHttpRequest request, @RequestBody BaseListParam param) {
        log.info("获取用户的投建区域 {} param = {}",
            LoggerHelper2.formatEnterLog(request, false), param);
        val areaParam = new ListTjAreaParam();
        BeanUtils.copyProperties(param, areaParam);
        areaParam.setUid(AntRestUtils.getSysUid(request));
        return tjAreaService.getAllOwnArea(areaParam);
    }

    @Operation(summary = "获取用户的投建区域")
    @PostMapping(value = "/findUserArea")
    public Mono<ListResponse<TjAreaVo>> findUserArea(
        ServerHttpRequest request, @RequestBody ListTjAreaParam param) {
        log.info("获取用户的投建区域 {} param = {}",
            LoggerHelper2.formatEnterLog(request, false), param);
        return tjAreaService.findUserArea(param);
    }

    @Operation(summary = "获取投建区域列表")
    @PostMapping(value = "/findArea")
    public Mono<ListResponse<TjAreaVo>> findArea(
        ServerHttpRequest request, @RequestBody ListTjAreaParam param) {
        log.info("获取用户的投建区域 {} param = {}",
            LoggerHelper2.formatEnterLog(request, false), param);
        param.setGidList(AntRestUtils.getSysUserGids(request));
        return tjAreaService.findArea(param);
    }

    @Operation(summary = "获取投建区域")
    @GetMapping(value = "/getTjAreaByAid")
    public Mono<ObjectResponse<TjAreaVo>> getTjAreaByAid(
        ServerHttpRequest request,
        @ApiParam("投建区域唯一ID") @RequestParam("aid") Long aid) {
        log.info("获取投建区域 {}",
            LoggerHelper2.formatEnterLog(request));
        return tjAreaService.getTjAreaByAid(aid)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "新建投建区域")
    @PostMapping(value = "/addTjArea")
    public Mono<ObjectResponse<TjAreaVo>> addTjArea(
        ServerHttpRequest request, @RequestBody TjAreaVo area) {
        log.info("新建投建区域 {} param = {}",
            LoggerHelper2.formatEnterLog(request, false), area);
        return tjAreaService.addTjArea(area);
    }

    @Operation(summary = "编辑投建区域")
    @PostMapping(value = "/editTjArea")
    public Mono<ObjectResponse<TjAreaVo>> editTjArea(
        ServerHttpRequest request, @RequestBody TjAreaVo area) {
        log.info("编辑投建区域 {} param = {}",
            LoggerHelper2.formatEnterLog(request, false), area);
        return tjAreaService.editTjArea(area);
    }

    @Operation(summary = "删除投建区域")
    @GetMapping(value = "/disableTjArea")
    public Mono<ObjectResponse<TjAreaVo>> editTjArea(
        ServerHttpRequest request, @ApiParam("投建区域唯一ID") @RequestParam("aid") Long aid) {
        log.info("删除投建区域 {}",
            LoggerHelper2.formatEnterLog(request));
        return tjAreaService.disableTjArea(aid);
    }
}
