package com.cdz360.biz.ant.rest.iot;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.biz.ant.service.iot.IotOfflineEvseService;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.iot.param.ModifyEvseInfoParam;
import com.cdz360.biz.model.iot.param.OfflineEvseParam;
import com.cdz360.biz.model.iot.vo.OfflineEvseImportVo;
import com.cdz360.biz.model.iot.vo.OfflineEvseVo;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.iot.vo.EvseInfoVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "脱机桩相关接口", description = "脱机桩")
public class IotOfflineEvseRest {

    @Autowired
    private IotOfflineEvseService iotOfflineEvseService;

    @Operation(summary = "获取脱机桩列表")
    @PostMapping("/api/OfflineEvse/getEvseList")
    public ListResponse<EvseInfoVo> getOfflineEvseList(@RequestBody ListEvseParam param) {
        throw new DcServiceException("该功能已废弃，请联系平台相关人员处理");
//        log.info("param = {}", param);
//        return iotOfflineEvseService.getOfflineEvseList(param);
    }

    @Operation(summary = "更新脱机桩信息")
    @PostMapping("/api/OfflineEvse/updateEvse")
    public BaseResponse updateEvseInfo(@RequestBody ModifyEvseInfoParam param) {
        throw new DcServiceException("该功能已废弃，请联系平台相关人员处理");
//        log.info(" param = {}", param);
//        return iotOfflineEvseService.updateEvseInfo(param);
    }

    @Operation(summary = "移除脱机桩")
    @PostMapping("/api/OfflineEvse/remove")
    public BaseResponse removeOfflineEvse(@RequestBody List<OfflineEvseParam> param) {
        throw new DcServiceException("该功能已废弃，请联系平台相关人员处理");
//        log.info(" param.size = {}", param.size());
//        return iotOfflineEvseService.removeOfflineEvse(param);
    }

    @Operation(summary = "解析支撑平台脱机桩excel文件")
    @PostMapping("/api/OfflineEvse/parseExcel")
    public Mono<ObjectResponse<OfflineEvseImportVo>> parseExcel(ServerHttpRequest request,
        @RequestPart FilePart file) {
        throw new DcServiceException("该功能已废弃，请联系平台相关人员处理");
//        FileUtil.checkExcelFile(file);
//        return iotOfflineEvseService.parseOfflineEvseExcel(file);
    }

    @Operation(summary = "批量新增脱机桩")
    @PostMapping("/api/OfflineEvse/batchAdd")
    public BaseResponse batchAddOfflineEvse(@RequestBody List<OfflineEvseVo> param) {
        throw new DcServiceException("该功能已废弃，请联系平台相关人员处理");
//        log.info("batchAddOfflineEvse param.size = {}", param.size());
//        return iotOfflineEvseService.batchAddOfflineEvse(param);
    }

    @Operation(summary = "导出脱机桩列表")
    @PostMapping("/api/OfflineEvse/export")
    public ObjectResponse<ExcelPosition> export(@RequestBody ListEvseParam param) {
        throw new DcServiceException("该功能已废弃，请联系平台相关人员处理");
//        log.info("offlineEvseExport param: {}", param);
//        return iotOfflineEvseService.export(param);
    }

}
