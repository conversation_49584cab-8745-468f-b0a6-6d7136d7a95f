package com.cdz360.biz.ant.service.pv;

import com.aliyun.oss.model.OSSObject;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.domain.dto.LinePvRtData;
import com.cdz360.biz.ant.domain.dto.PvInv;
import com.cdz360.biz.ant.domain.request.PvGroupParam;
import com.cdz360.biz.ant.domain.request.PvGroupParam.Group;
import com.cdz360.biz.ant.domain.vo.PvRtDailyPower;
import com.cdz360.biz.ant.domain.vo.PvRtDataGroupItem;
import com.cdz360.biz.ant.domain.vo.PvRtDataGroupSampling;
import com.cdz360.biz.ant.domain.vo.PvRtDataPowerSampling;
import com.cdz360.biz.model.ess.dto.LineEssRtData;
import com.cdz360.biz.model.iot.param.ListGtiParam;
import com.cdz360.biz.model.iot.type.NetType;
import com.cdz360.biz.model.iot.vo.GtiVo;
import com.cdz360.biz.model.trading.iot.dto.PvRtDataDto;
import com.cdz360.biz.utils.feign.iot.GtiFeignClient;
import com.cdz360.biz.utils.service.OssArchiveService;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class PvRtDataService {

    private static final DateTimeFormatter TIME_POINT_FORMATTER =
        DateTimeFormatter.ofPattern("HH:mm");
    private static final DateTimeFormatter TIME_FORMATTER =
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Autowired
    private OssArchiveService ossArchiveService;
    @Autowired
    private RedisPvRtDataService redisPvRtDataService;
    @Autowired
    private GtiFeignClient gtiFeignClient;

    /**
     * 指定微网控制器列表在指定时间范围的功率采集
     *
     * @param gwnoList 微网控制器编号列表
     * @param fromDate 开始日期
     * @param toDate   结束日期(不包含)
     * @param dis      采样间隔, 单位: 分钟(采样点与前一个采样点的间隔时间)
     * @return
     */
    public Flux<PvRtDailyPower> rangeDayPowerSampling(
        List<String> gwnoList, LocalDate fromDate, LocalDate toDate, int dis) {
        List<LocalDate> dateList = DateUtil.rangeDate(fromDate, toDate);
        return Flux.fromIterable(dateList)
            .flatMap(date -> {
                return this.dayPowerSampling(gwnoList, date, dis).collectList()
                    .map(e -> {
                        PvRtDailyPower data = new PvRtDailyPower();
                        data.setDate(date);
                        data.setPowerSamplingList(e);
                        return data;
                    });
            });
    }

    /**
     * 指定微网控制器列表某天的功率采集
     *
     * @param gwnoList 微网控制器编号列表
     * @param date     日期
     * @param dis      采样间隔, 单位: 分钟(采样点与前一个采样点的间隔时间)
     * @return
     */
    public Flux<PvRtDataPowerSampling> dayPowerSampling(List<String> gwnoList, LocalDate date,
        int dis) {
        return Flux.fromIterable(gwnoList)
            .flatMap(gwno -> this.dayPowerSampling(gwno, date, dis))
            .collectList()
            .map(list -> {
                Map<String, Long> collect = list.stream().collect(Collectors.groupingBy(
                    PvRtDataPowerSampling::getTime,
                    Collectors.summingLong(PvRtDataPowerSampling::getOutPower)));

                List<PvRtDataPowerSampling> result = new ArrayList<>();
                for (String time : collect.keySet()) {
                    result.add(new PvRtDataPowerSampling()
                        .setTime(time)
                        .setOutPower(collect.get(time)));
                }
                return result.stream()
                    .sorted(Comparator.comparing(PvRtDataPowerSampling::getTime));
            })
            .flatMapMany(Flux::fromStream);
    }

    /**
     * 微网控制器某一天的功率采集
     *
     * @param gwno 微网控制器编号
     * @param date 日期
     * @param dis  采样间隔, 单位: 分钟(采样点与前一个采样点的间隔时间)
     * @return
     */
    public Flux<PvRtDataPowerSampling> dayPowerSampling(String gwno, LocalDate date, int dis) {
        final List<LocalDateTime> times = splitDay(date, dis);
        log.debug("时段: time = {}", times.size());

        return Mono.just(new ListGtiParam().setGwno(gwno))
            .flatMap(gtiFeignClient::findGtiList)
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .flatMap(gtiList -> {
                List<String> dnoList = gtiList.stream()
                    .map(GtiVo::getDno).distinct().collect(Collectors.toList());

                if (LocalDate.now().isEqual(date)) {
                    return this.redisPvRtDataService.dayPowerSampling4Redis(dnoList, date, times)
                        .collectList();
                }

                return this.dayPowerSampling4File(gtiList, date, times)
                    .collectList();
            })
            .flatMapMany(Flux::fromIterable);
    }

    private Flux<PvRtDataPowerSampling> dayPowerSampling4File(
        List<GtiVo> gtiList, LocalDate date, final List<LocalDateTime> times) {
        if (CollectionUtils.isEmpty(gtiList)) {
            return Flux.fromIterable(times)
                .map(time -> new PvRtDataPowerSampling()
                    .setTime(time.format(TIME_POINT_FORMATTER))
                    .setOutPower(0L));
        }

        return Flux.fromIterable(gtiList)
            .flatMap(gti -> this.dayPowerSampling4File(gti, date, times))
            .collectList()
            .map(list -> {
                Map<String, Long> collect = list.stream().collect(Collectors.groupingBy(
                    PvRtDataPowerSampling::getTime,
                    Collectors.summingLong(PvRtDataPowerSampling::getOutPower)));

                List<PvRtDataPowerSampling> result = new ArrayList<>();
                for (String time : collect.keySet()) {
                    result.add(new PvRtDataPowerSampling()
                        .setTime(time)
                        .setOutPower(collect.get(time)));
                }
                return result.stream()
                    .sorted(Comparator.comparing(PvRtDataPowerSampling::getTime));
            })
            .flatMapMany(Flux::fromStream);
    }

    private Flux<PvRtDataPowerSampling> dayPowerSampling4File(
        GtiVo gti, LocalDate date, final List<LocalDateTime> times) {

        Mono<Optional<OSSObject>> mono;
        boolean subordinateToEss = gti.getOwnEquipId() != null && gti.getOwnEquipId() > 0L;
        if (subordinateToEss) {
            mono = ossArchiveService.getOSSDeviceRtData(gti.getSiteId(), gti.getEssDno(),
                gti.getOwnEquipId(), NetType.ESS, date);
        } else {
            mono = ossArchiveService.getOSSDeviceRtData(gti.getSiteId(), gti.getDno(),
                null, NetType.PV, date);
        }

        return mono.filter(Optional::isPresent)
            .map(Optional::get)
            .map(ossObject -> {
                List<PvRtDataPowerSampling> samplingList = new ArrayList<>();
                try (InputStream gzipStream = new GZIPInputStream(ossObject.getObjectContent());
                    Reader decoder = new InputStreamReader(gzipStream, Charset.defaultCharset());
                    BufferedReader reader = new BufferedReader(decoder)) {

                    boolean hasLine = true;
                    Pair<BigDecimal, LocalDateTime> last = null;
                    for (LocalDateTime time : times) {
                        if (null != last) {
                            LocalDateTime temp = last.getRight()
                                .withSecond(0)
                                .withNano(0);
                            if (time.isBefore(temp)) {
                                addZeroSampling(samplingList, time);
                                continue;
                            } else if (time.isEqual(temp)) {
                                samplingList.add(new PvRtDataPowerSampling()
                                    .setTime(time.format(TIME_POINT_FORMATTER))
                                    .setOutPower(
                                        this.outPowerFormat(last.getLeft())));
                                continue;
                            }
                        }

                        while (hasLine) {
                            String line = null;
                            try {
                                line = reader.readLine();
                            } catch (IOException e) {
                                // nothing
                                log.error("err = {}", e.getMessage(), e);
                            }

                            if (line == null) {
                                hasLine = false;
                                break;
                            }

                            Pair<BigDecimal, LocalDateTime> rtData;
                            if (subordinateToEss) {
                                LineEssRtData<PvInv> tempData = lineData(line, PvInv.class);
                                if (null == tempData || tempData.getTime() == null
                                    || tempData.getRtData() == null) {
                                    continue;
                                }
                                BigDecimal outPower =
                                    tempData.getRtData().getFeedingPower() != null ?
                                        BigDecimal.valueOf(tempData.getRtData().getFeedingPower())
                                        : null;
                                rtData = Pair.of(outPower, tempData.getTime());
                            } else {
                                LinePvRtData tempData = lineData(line);
                                if (null == tempData || tempData.getTime() == null
                                    || tempData.getRtData() == null) {
                                    continue;
                                }
                                rtData = Pair.of(tempData.getRtData().getOutPower(),
                                    tempData.getTime());
                            }

                            LocalDateTime temp = rtData.getRight()
                                .withSecond(0)
                                .withNano(0);

                            if (temp.isBefore(time)) {
                                continue;
                            } else if (temp.isEqual(time)) {
                                samplingList.add(new PvRtDataPowerSampling()
                                    .setTime(time.format(TIME_POINT_FORMATTER))
                                    .setOutPower(
                                        this.outPowerFormat(rtData.getLeft())));
                            } else if (temp.isAfter(time)) {
                                if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(samplingList)
                                    && samplingList.get(samplingList.size() - 1).getOutPower()
                                    != null
                                    && samplingList.get(samplingList.size() - 1).getOutPower()
                                    > 0) {
                                    BigDecimal lastP = last != null && last.getLeft() != null
                                        ? last.getLeft() : BigDecimal.ZERO;
                                    BigDecimal value = DecimalUtils.add(lastP,
                                            rtData.getLeft())
                                        .divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP);
                                    samplingList.add(new PvRtDataPowerSampling()
                                        .setTime(time.format(TIME_POINT_FORMATTER))
                                        .setOutPower(this.outPowerFormat(value)));
                                } else {
                                    addZeroSampling(samplingList, time);
                                }
                            }
                            last = rtData;
                            rtData = null;
                            break;
                        }

                        if (!hasLine) {
                            addZeroSampling(samplingList, time);
                        }
                    }
                } catch (Exception e) {
                    // nothing
                    log.error("err = {}", e.getMessage(), e);
                }
                return samplingList;
            })
            .flatMapMany(Flux::fromIterable)
            .switchIfEmpty(Flux.fromIterable(times)
                .map(time -> new PvRtDataPowerSampling()
                    .setTime(time.format(TIME_POINT_FORMATTER))
                    .setOutPower(0L)));

    }

    private Long outPowerFormat(BigDecimal value) {
        return value != null ? value.longValue() : 0;
    }

    private static void addZeroSampling(List<PvRtDataPowerSampling> list, LocalDateTime time) {
        list.add(new PvRtDataPowerSampling()
            .setTime(time.format(TIME_POINT_FORMATTER))
            .setOutPower(0L));
    }

    private static void addZeroGroupSampling(List<PvRtDataGroupSampling> list, LocalDateTime time,
        List<Group> pvGroupList) {
        list.add(new PvRtDataGroupSampling()
            .setTime(time.format(TIME_POINT_FORMATTER))
            .setItems(pvGroupList.stream()
                .map(e -> new PvRtDataGroupItem().setIdx(e.getIdx()).setValue(BigDecimal.ZERO))
                .collect(Collectors.toList())));
    }

    private static List<LocalDateTime> splitDay(LocalDate date, int dis) {
        List<LocalDateTime> times = new ArrayList<>();
        LocalDateTime start = date.atStartOfDay();
        LocalDateTime end = date.plusDays(1).atStartOfDay();
        LocalDateTime now = LocalDateTime.now();
        while (start.isBefore(end) && start.isBefore(now)) {
            times.add(start);
            start = start.plusMinutes(dis);
        }
        return times;
    }

    private static List<LocalDateTime> splitDayByHour(LocalDate date, int dis) {
        List<LocalDateTime> times = new ArrayList<>();
        LocalDateTime start = date.atStartOfDay();
        LocalDateTime end = date.plusDays(1).atStartOfDay();
        LocalDateTime now = LocalDateTime.now();
        while (start.isBefore(end) && start.isBefore(now)) {
            times.add(start);
            start = start.plusHours(dis);
        }
        return times;
    }

    private static LinePvRtData lineData(String line) {
        String[] split = line.split(" \\| ");
        if (split.length == 3) {
            LinePvRtData rtData = new LinePvRtData();
            rtData.setTime(LocalDateTime.parse(split[0], TIME_FORMATTER));

            String[] serial = split[1].split("/");
            rtData.setDno(serial[0])
                .setSid(Integer.valueOf(serial[1]));

            rtData.setRtData(
                JsonUtils.fromJson(split[2].replaceAll("\r|\n", ""), PvRtDataDto.class));
            return rtData;
        }

        return null;
    }

    private static <T> LineEssRtData<T> lineData(String line, Class<T> tClass) {
        String[] split = line.split(" \\| ");
        if (split.length == 3) {
            LineEssRtData<T> rtData = new LineEssRtData<T>();
            rtData.setTime(LocalDateTime.parse(split[0], TIME_FORMATTER));

            String[] serial = split[1].split("/");
            rtData.setDno(serial[0])
                .setEquipId(Integer.valueOf(serial[1]));

            rtData.setRtData(JsonUtils.fromJson(split[2], tClass));
            return rtData;
        }

        return null;
    }

    public Flux<PvRtDataGroupSampling> dayGroupSampling(@NonNull PvGroupParam param,
        final int dis) {
        LocalDate date = DateUtil.dateToLocalDate(param.getDate());
        final List<LocalDateTime> times = splitDayByHour(date, dis);

        IotAssert.isTrue(com.cdz360.base.utils.CollectionUtils.isNotEmpty(param.getGroupList()),
            "参数错误");
        Map<String, List<Group>> collect = param.getGroupList().stream()
            .collect(Collectors.groupingBy(Group::getDno));

        return Flux.fromIterable(collect.keySet())
            .flatMap(dno -> dayGroupSampling(param.getType(), param.getSiteId(),
                dno, collect.get(dno),
                date, times));
    }

    private Flux<PvRtDataGroupSampling> dayGroupSampling(final Integer type, final String siteId,
        final String dno, final List<Group> pvGroupList,
        final LocalDate date, final List<LocalDateTime> times) {

        if (StringUtils.isBlank(dno)) {
            return Flux.fromIterable(times)
                .map(time -> new PvRtDataGroupSampling()
                    .setTime(time.format(TIME_POINT_FORMATTER))
                    .setItems(pvGroupList.stream()
                        .map(e -> new PvRtDataGroupItem().setIdx(e.getIdx())
                            .setValue(BigDecimal.ZERO))
                        .collect(Collectors.toList())));
        }

        if (LocalDate.now().equals(date)) {
            return redisPvRtDataService.dayGroupSampling4Redis(type, dno,
                pvGroupList, date, times);
        }

        Mono<Optional<OSSObject>> mono;
        Optional<Group> optional = pvGroupList.stream()
            .filter(e -> StringUtils.isNotBlank(e.getEssDno()) && e.getOwnEquipId() != null
                && e.getOwnEquipId() > 0L)
            .findFirst();
        if (optional.isPresent()) {
            Group temp = optional.get();
            mono = ossArchiveService.getOSSDeviceRtData(siteId, temp.getEssDno(),
                temp.getOwnEquipId(), NetType.ESS, date);
        } else {
            mono = ossArchiveService.getOSSDeviceRtData(siteId, dno,
                null, NetType.PV, date);
        }

        return mono.filter(Optional::isPresent)
            .filter(Optional::isPresent)
            .map(Optional::get)
            .map(ossObject -> {
                List<PvRtDataGroupSampling> samplingList = new ArrayList<>();
                try (InputStream gzipStream = new GZIPInputStream(ossObject.getObjectContent());
                    Reader decoder = new InputStreamReader(gzipStream, Charset.defaultCharset());
                    BufferedReader reader = new BufferedReader(decoder)) {

                    boolean hasLine = true;
                    Pair<Object, LocalDateTime> last = null;
                    for (LocalDateTime time : times) {
                        if (null != last) {
                            LocalDateTime temp = last.getRight()
                                .withSecond(0)
                                .withNano(0);
                            if (time.isBefore(temp)) {
                                addZeroGroupSampling(samplingList, time, pvGroupList);
                                continue;
                            } else if (time.isEqual(temp)) {
                                samplingList.add(new PvRtDataGroupSampling()
                                    .setTime(time.format(TIME_POINT_FORMATTER))
                                    .setItems(RedisPvRtDataService.achieveValue(type,
                                        pvGroupList, optional.isPresent(), last.getLeft())));
                                continue;
                            }
                        }

                        while (hasLine) {
                            String line = null;
                            try {
                                line = reader.readLine();
                            } catch (IOException e) {
                                // nothing
                                log.error("err = {}", e.getMessage(), e);
                            }

                            if (line == null) {
                                hasLine = false;
                                break;
                            }

                            Pair<Object, LocalDateTime> rtData;
                            if (optional.isPresent()) {
                                LineEssRtData<PvInv> tempData = lineData(line, PvInv.class);
                                if (null == tempData || tempData.getTime() == null
                                    || tempData.getRtData() == null) {
                                    continue;
                                }
                                rtData = Pair.of(tempData.getRtData(), tempData.getTime());
                            } else {
                                LinePvRtData tempData = lineData(line);
                                if (null == tempData || tempData.getTime() == null
                                    || tempData.getRtData() == null) {
                                    continue;
                                }
                                rtData = Pair.of(tempData.getRtData(), tempData.getTime());
                            }

                            LocalDateTime temp = rtData.getRight()
                                .withSecond(0)
                                .withNano(0);

                            if (temp.isBefore(time)) {
                                continue;
                            } else if (temp.isEqual(time)) {
                                samplingList.add(new PvRtDataGroupSampling()
                                    .setTime(time.format(TIME_POINT_FORMATTER))
                                    .setItems(RedisPvRtDataService.achieveValue(type,
                                        pvGroupList, optional.isPresent(), rtData.getLeft())));
                            } else if (temp.isAfter(time)) {
                                boolean tempBoo = last != null &&
                                    com.cdz360.base.utils.CollectionUtils.isNotEmpty(samplingList)
                                    && samplingList.get(samplingList.size() - 1).getItems()
                                    .stream().anyMatch(e -> DecimalUtils.gtZero(e.getValue()));
                                if (tempBoo) { //前一个值是非零的
                                    samplingList.add(new PvRtDataGroupSampling()
                                        .setTime(time.format(TIME_POINT_FORMATTER))
                                        .setItems(RedisPvRtDataService.achieveValue(type,
                                            pvGroupList, optional.isPresent(), last.getLeft(),
                                            rtData.getLeft())));
                                } else {
                                    addZeroGroupSampling(samplingList, time,
                                        pvGroupList);
                                }
                            }
                            last = rtData;
                            rtData = null;
                            break;
                        }

                        if (!hasLine) {
                            addZeroGroupSampling(samplingList, time, pvGroupList);
                        }
                    }
                } catch (Exception e) {
                    // nothing
                    log.error("err = {}", e.getMessage(), e);
                }
                return samplingList;
            })
            .flatMapMany(Flux::fromIterable)
            .switchIfEmpty(Flux.fromIterable(times)
                .map(time -> new PvRtDataGroupSampling()
                    .setTime(time.format(TIME_POINT_FORMATTER))
                    .setItems(pvGroupList.stream().map(e ->
                            new PvRtDataGroupItem().setIdx(e.getIdx()).setValue(BigDecimal.ZERO))
                        .collect(Collectors.toList()))));

    }

}
