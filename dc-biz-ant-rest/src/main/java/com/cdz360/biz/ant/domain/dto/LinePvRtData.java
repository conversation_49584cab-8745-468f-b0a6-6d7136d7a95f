package com.cdz360.biz.ant.domain.dto;

import com.cdz360.biz.model.trading.iot.dto.PvRtDataDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "解析文件行对象信息")
@Data
@Accessors(chain = true)
public class LinePvRtData {

    @Schema(description = "采集时间点")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private LocalDateTime time;

    @Schema(description = "逆变器序编号")
    private String dno;

    @Schema(description = "逆变器地址ID")
    private Integer sid;

    @Schema(description = "逆变器运行数据")
    private PvRtDataDto rtData;
}
