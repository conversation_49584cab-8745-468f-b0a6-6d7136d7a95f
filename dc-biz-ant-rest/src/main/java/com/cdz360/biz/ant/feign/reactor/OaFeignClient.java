package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.oa.dto.ProcessInstanceExDto;
import com.cdz360.biz.oa.dto.account.OaAccountDto;
import com.cdz360.biz.oa.dto.account.OaGroupDto;
import com.cdz360.biz.oa.param.AddSignTaskParam;
import com.cdz360.biz.oa.param.ApproveParam;
import com.cdz360.biz.oa.param.BillingProcessParam;
import com.cdz360.biz.oa.param.CreateProcessInstanceParam;
import com.cdz360.biz.oa.param.DepositInvoiceProcessParam;
import com.cdz360.biz.oa.param.FastSearchParam;
import com.cdz360.biz.oa.param.ListTaskParam;
import com.cdz360.biz.oa.param.MarkDeleteCommentParam;
import com.cdz360.biz.oa.param.OaProcessTagParam;
import com.cdz360.biz.oa.param.OaStartProcessParam;
import com.cdz360.biz.oa.param.PrepaidInvoiceProcessParam;
import com.cdz360.biz.oa.param.RechargeParam;
import com.cdz360.biz.oa.param.UserApproveParam;
import com.cdz360.biz.oa.param.account.ListOaGroupParam;
import com.cdz360.biz.oa.param.account.ListOaGroupUserParam;
import com.cdz360.biz.oa.param.account.OaModifyGroupParam;
import com.cdz360.biz.oa.vo.FormModelVo;
import com.cdz360.biz.oa.vo.OaRechargeInfoVo;
import com.cdz360.biz.oa.vo.OaTaskVo;
import com.cdz360.biz.oa.vo.ProcessDefinitionVo;
import com.cdz360.biz.oa.vo.ProcessInstanceVo;
import java.util.Map;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_OA,
    fallbackFactory = OaFeignHystrix.class)
public interface OaFeignClient {

    // 获取审核组列表
    @PostMapping(value = "/oa/account/groupUserList")
    Mono<ListResponse<OaAccountDto>> groupUserList(@RequestBody ListOaGroupUserParam param);

    // 获取审核组列表
    @PostMapping(value = "/oa/account/groupList")
    Mono<ListResponse<OaGroupDto>> groupList(@RequestBody ListOaGroupParam param);

    // 创建/修改审核组信息
    @PostMapping(value = "/oa/account/modifyGroup")
    Mono<ObjectResponse<String>> modifyGroup(@RequestBody OaModifyGroupParam param);

    // 删除审核组信息
    @GetMapping(value = "/oa/account/removeGroup")
    Mono<ObjectResponse<OaGroupDto>> removeGroup(@RequestParam(value = "gid") String gid);

    // 用户获取自己申请的任务列表
    @PostMapping(value = "/oa/task/assignee/getApplyTasks")
    Mono<ListResponse<OaTaskVo>> getAssigneeApplyTasks(@RequestBody ListTaskParam param);

    // 用户获取自己审核的任务列表
    @PostMapping(value = "/oa/task/assignee/getAuditTasks")
    Mono<ListResponse<OaTaskVo>> getAssigneeAuditTasks(@RequestBody ListTaskParam param);

    // 用户获取任务列表
    @PostMapping(value = "/oa/task/assignee/getTasks")
    Mono<ListResponse<OaTaskVo>> getAssigneeTasks(@RequestBody ListTaskParam param);

    // 审核人执行批准操作
    @PostMapping(value = "/oa/task/assignee/handleTask")
    Mono<BaseResponse> assigneeHandleTask(@RequestBody ApproveParam param);

    // 任务向后加签
    @PostMapping(value = "/oa/task/addSignAfter")
    Mono<ObjectResponse<String>> addSignTaskAfter(@RequestBody AddSignTaskParam param);

    // 流程审核人附言
    @PostMapping(value = "/oa/task/assignee/handleTaskComment")
    Mono<BaseResponse> assigneeHandleTaskComment(ApproveParam param);

    // 获取充值申请流程信息
    @GetMapping(value = "/oa/recharge/processDetail")
    Mono<ObjectResponse<OaRechargeInfoVo>> processDetail(
        @RequestParam("procInstId") String procInstId,
        @RequestParam("oUid") String oUid);

    // 流程定义列表
    @GetMapping(value = "/process-api/repository/process-definitions")
    Mono<ListResponse<ProcessDefinitionVo>> processDefinitions(
        @RequestParam("params") Map<String, String> params);

    // 流程定义启动表单数据
    @GetMapping(value = "/process-api/repository/process-definitions/{processDefinitionId}/start-form")
    Mono<String> getProcessDefinitionStartForm(
        @PathVariable("processDefinitionId") String processDefinitionId);

    // 流程定义信息
    @GetMapping(value = "/process-api/repository/process-definitions/{processDefinitionId}")
    Mono<ProcessDefinitionVo> getProcessDefinitionInfo(
        @PathVariable("processDefinitionId") String processDefinitionId);

    // 获取充值申请流程信息
    @GetMapping(value = "/oa/account/getOaGroupById")
    Mono<ObjectResponse<OaGroupDto>> getOaGroupById(@RequestParam("gid") String gid);

    // 启动新的流程实例
    @PostMapping(value = "/oa/process-instance/start")
    Mono<ObjectResponse<ProcessInstanceVo>> startNewProcessInstance(
        @RequestBody CreateProcessInstanceParam param);

    // 获取流程实例的开始表单
    @GetMapping(value = "/oa/process-instance/startFormInfo")
    Mono<ObjectResponse<FormModelVo>> procInstStartFormInfo(
        @RequestParam("processInstanceId") String processInstanceId);

    // 获取相关审批流
    @PostMapping(value = "/oa/process-instance/fastSearch")
    Mono<ListResponse<ProcessInstanceExDto>> fastSearch(@RequestBody FastSearchParam param);

    // 记录标签
    @PostMapping(value = "/oa/process-instance/saveTag")
    Mono<BaseResponse> saveTag(@RequestBody OaProcessTagParam param);

    // 批量记录标签
    @PostMapping(value = "/oa/process-instance/batchSaveTag")
    Mono<BaseResponse> batchSaveTag(@RequestBody OaProcessTagParam param);

    @Deprecated(since = "20230721")
    // 启动电价下发申请
    @PostMapping(value = "/oa/charge-fee/startProcess")
    Mono<ObjectResponse<String>> startChargeFeeProcess(@RequestBody RechargeParam param);


    @PostMapping(value = "/oa/task/assignee/userHandleTask")
    Mono<BaseResponse> userHandleTask(@RequestBody UserApproveParam params);

    // 创建企客对账开票流程
    @PostMapping(value = "/oa/billing/startProcess")
    Mono<ObjectResponse<String>> startBillingProcess(@RequestBody BillingProcessParam params);

    // 创建预付订单开票流程
    @PostMapping(value = "/oa/prepaidInvoice/startProcess")
    Mono<ObjectResponse<String>> startPrepaidProcess(
        @RequestBody PrepaidInvoiceProcessParam params);

    // 创建企客、个人、商户充值开票流程
    @PostMapping(value = "/oa/depositInvoice/startProcess")
    Mono<ObjectResponse<String>> depositInvoiceStartProcess(
        @RequestBody DepositInvoiceProcessParam params);

    // 审批流标记删除
    @PostMapping(value = "/oa/comment/markDelete")
    Mono<ObjectResponse<Integer>> markDeleteComment(
        @RequestBody MarkDeleteCommentParam param);

    // 启动新的流程实例
    @PostMapping(value = "/oa/process-instance/startProcess")
    Mono<ObjectResponse<ProcessInstanceVo>> startOaProcess(
        @RequestBody OaStartProcessParam param);

    // 流程实例驳回后重新提交
    @PostMapping(value = "/oa/process-instance/resubmitProcess")
    Mono<ObjectResponse<ProcessInstanceVo>> resubmitOaProcess(
        @RequestBody OaStartProcessParam param);
}
