package com.cdz360.biz.ant.rest.sys;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.auth.user.param.SysUserLogParam;
import com.cdz360.biz.auth.user.vo.SysUserLogVo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class SysLogRest {
    @Autowired
    private AuthCenterFeignClient client;

    @PostMapping("/api/sys/user/getLoginLog")
    public ListResponse<SysUserLogVo> getLoginLog(ServerHttpRequest request,
                                                  @RequestBody SysUserLogParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}", param);
        param.setCommIdChain(AntRestUtils.getCommIdChain(request));
        return client.getLoginLog(param);
    }

    @PostMapping("/api/sys/user/getOpLog")
    public ListResponse<SysUserLogVo> getOpLog(ServerHttpRequest request,
                                               @RequestBody SysUserLogParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}", param);
        param.setCommIdChain(AntRestUtils.getCommIdChain(request));
        return client.getOpLog(param);
    }

}
