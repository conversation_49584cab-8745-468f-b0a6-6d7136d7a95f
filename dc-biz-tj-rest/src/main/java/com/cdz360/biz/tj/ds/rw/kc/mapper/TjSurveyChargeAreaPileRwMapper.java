package com.cdz360.biz.tj.ds.rw.kc.mapper;

import com.cdz360.biz.model.tj.kc.po.TjSurveyChargeAreaPilePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjSurveyChargeAreaPileRwMapper {

    TjSurveyChargeAreaPilePo getById(@Param("id") Long id, @Param("lock") boolean lock);

    int insertTjSurveyChargeAreaPile(TjSurveyChargeAreaPilePo tjSurveyChargeAreaPilePo);

    int updateTjSurveyChargeAreaPile(TjSurveyChargeAreaPilePo tjSurveyChargeAreaPilePo);

    int disableTjSurveyChargeAreaPileByAreaId(@Param("areaId") Long areaId);

    int disableTjSurveyChargeAreaPileBySurveyNo(@Param("surveyNo") String surveyNo);

}

