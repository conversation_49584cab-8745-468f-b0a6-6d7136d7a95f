package com.cdz360.biz.tj.ds.ro.kc.ds;


import com.cdz360.biz.model.tj.kc.param.ListTjMaterialCostParam;
import com.cdz360.biz.model.tj.kc.po.TjMaterialCostPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationExpensesPo;
import com.cdz360.biz.model.tj.kc.vo.TjMaterialCostVo;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjMaterialCostRoMapper;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveyOperationExpensesRoMapper;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjSurveyOperationExpensesRoDs {


    @Autowired

    private TjSurveyOperationExpensesRoMapper tjSurveyOperationExpensesRoMapper;


    public TjSurveyOperationExpensesPo getById(Long id) {

        return this.tjSurveyOperationExpensesRoMapper.getById(id);

    }

    public TjSurveyOperationExpensesPo findTjSurveyOperationExpensesBySurveyNo(String surveyNo) {
        return this.tjSurveyOperationExpensesRoMapper.findTjSurveyOperationExpensesBySurveyNo(surveyNo);
    }

}

