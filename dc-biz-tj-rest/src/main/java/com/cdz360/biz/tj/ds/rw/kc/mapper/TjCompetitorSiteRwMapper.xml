<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.rw.kc.mapper.TjCompetitorSiteRwMapper">

  <resultMap id="RESULT_TJ_COMPETITOR_SITE_PO"
    type="com.cdz360.biz.model.tj.kc.po.TjCompetitorSitePo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="competitorId" jdbcType="BIGINT" property="competitorId"/>
    <result column="gdId" jdbcType="VARCHAR" property="gdId"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="pCode" jdbcType="VARCHAR" property="pCode"/>
    <result column="pName" jdbcType="VARCHAR" property="pName"/>
    <result column="cityCode" jdbcType="VARCHAR" property="cityCode"/>
    <result column="cityName" jdbcType="VARCHAR" property="cityName"/>
    <result column="adCode" jdbcType="VARCHAR" property="adCode"/>
    <result column="adName" jdbcType="VARCHAR" property="adName"/>
    <result column="address" jdbcType="VARCHAR" property="address"/>
    <result column="locationText" property="location"
      typeHandler="com.cdz360.biz.ds.trading.PointTypeHandler"/>
    <result column="dcEvseNum" jdbcType="INTEGER" property="dcEvseNum"/>
    <result column="dcPlugNum" jdbcType="INTEGER" property="dcPlugNum"/>
    <result column="acEvseNum" jdbcType="INTEGER" property="acEvseNum"/>
    <result column="acPlugNum" jdbcType="INTEGER" property="acPlugNum"/>
    <result column="power" jdbcType="DECIMAL" property="power"/>
    <result column="minElecFee" jdbcType="DECIMAL" property="minElecFee"/>
    <result column="maxElecFee" jdbcType="DECIMAL" property="maxElecFee"/>
    <result column="minServFee" jdbcType="DECIMAL" property="minServFee"/>
    <result column="maxServFee" jdbcType="DECIMAL" property="maxServFee"/>
    <result column="attachUpdateTime" jdbcType="TIMESTAMP" property="attachUpdateTime"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_COMPETITOR_SITE_PO">
    select *, ST_AsText(site.`location`) locationText from t_tj_competitor_site where id = #{id}
    <if test="lock == true">
      for update
    </if>
  </select>

  <insert id="batchUpset">
    insert into t_tj_competitor_site(`competitorId`, `gdId`, `name`,
    `pCode`, `pName`, `cityCode`, `cityName`, `adCode`, `adName`, `address`, `location`,
    `createTime`, `updateTime`)
    <foreach collection="poList" open="values" close=""
      separator="," item="po">
      (#{po.competitorId}, #{po.gdId}, #{po.name}, #{po.pCode}, #{po.pName},
      #{po.cityCode}, #{po.cityName}, #{po.adCode}, #{po.adName},
      #{po.address},
      ST_GeomFromText(#{po.location, jdbcType=JAVA_OBJECT,
      typeHandler=com.cdz360.biz.ds.trading.PointTypeHandler}),
      now(), now())
    </foreach>
    ON DUPLICATE KEY UPDATE
    name = values(name),
    pCode = values(pCode),
    pName = values(pName),
    cityCode = values(cityCode),
    cityName = values(cityName),
    adCode = values(adCode),
    adName = values(adName),
    address = values(address),
    location = values(location),
    updateTime=now()
  </insert>

  <insert id="insertTjCompetitorSite" useGeneratedKeys="true" keyProperty="id"
    keyColumn="id" parameterType="com.cdz360.biz.model.tj.kc.po.TjCompetitorSitePo">
    insert into t_tj_competitor_site (`competitorId`,
    `gdId`,
    `name`,
    `pCode`,
    `pName`,
    `cityCode`,
    `cityName`,
    `adCode`,
    `adName`,
    `address`,
    `location`,
    `createTime`,
    `updateTime`)
    values (#{competitorId},
    #{gdId},
    #{name},
    #{pCode},
    #{pName},
    #{cityCode},
    #{cityName},
    #{adCode},
    #{adName},
    #{address},
    ST_GeomFromText(#{location, jdbcType=JAVA_OBJECT,
    typeHandler=com.cdz360.biz.ds.trading.PointTypeHandler}),
    now(),
    now())
  </insert>

  <update id="updateTjCompetitorSite"
    parameterType="com.cdz360.biz.model.tj.kc.po.TjCompetitorSitePo">
    update t_tj_competitor_site set
    <if test="name != null">
      name = #{name},
    </if>
    <if test="pCode != null">
      pCode = #{pCode},
    </if>
    <if test="pName != null">
      pName = #{pName},
    </if>
    <if test="cityCode != null">
      cityCode = #{cityCode},
    </if>
    <if test="cityName != null">
      cityName = #{cityName},
    </if>
    <if test="adCode != null">
      adCode = #{adCode},
    </if>
    <if test="adName != null">
      adName = #{adName},
    </if>
    <if test="address != null">
      address = #{address},
    </if>
    <if test="location != null">
      location = #{location},
    </if>
    <if test="dcEvseNum != null">
      dcEvseNum = #{dcEvseNum},
    </if>
    <if test="dcPlugNum != null">
      dcPlugNum = #{dcPlugNum},
    </if>
    <if test="acEvseNum != null">
      acEvseNum = #{acEvseNum},
    </if>
    <if test="acPlugNum != null">
      acPlugNum = #{acPlugNum},
    </if>
    <if test="power != null">
      power = #{power},
    </if>
    <if test="minElecFee != null">
      minElecFee = #{minElecFee},
    </if>
    <if test="maxElecFee != null">
      maxElecFee = #{maxElecFee},
    </if>
    <if test="minServFee != null">
      minServFee = #{minServFee},
    </if>
    <if test="maxServFee != null">
      maxServFee = #{maxServFee},
    </if>
    <if test="attachUpdateTime != null">
      attachUpdateTime = #{attachUpdateTime},
    </if>
    updateTime = now()
    where id = #{id}
  </update>
</mapper>

