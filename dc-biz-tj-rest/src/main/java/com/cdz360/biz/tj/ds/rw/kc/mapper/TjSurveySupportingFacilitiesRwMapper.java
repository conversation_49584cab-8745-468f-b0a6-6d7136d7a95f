package com.cdz360.biz.tj.ds.rw.kc.mapper;

import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjSurveySupportingFacilitiesRwMapper {

    TjSurveySupportingFacilitiesPo getById(@Param("id") Long id, @Param("lock") boolean lock);

    int insertTjSurveySupportingFacilities(TjSurveySupportingFacilitiesPo tjSurveySupportingFacilitiesPo);

    int updateTjSurveySupportingFacilities(TjSurveySupportingFacilitiesPo tjSurveySupportingFacilitiesPo);

    int disableTjSurveySupportingFacilitiesBySurveyNo(String surveyNo);

}

