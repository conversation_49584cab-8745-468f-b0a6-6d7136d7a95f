package com.cdz360.biz.tj.ds.rw.kc.ds;


import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationExpensesPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationIncomePo;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjSurveyOperationExpensesRwMapper;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjSurveyOperationIncomeRwMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjSurveyOperationIncomeRwDs {


    @Autowired

    private TjSurveyOperationIncomeRwMapper tjSurveyOperationIncomeRwMapper;


    public TjSurveyOperationIncomePo getById(Long id, boolean lock) {

        return this.tjSurveyOperationIncomeRwMapper.getById(id, lock);

    }


    public boolean insertTjSurveyOperationIncome(TjSurveyOperationIncomePo tjSurveyOperationIncomePo) {
        return this.tjSurveyOperationIncomeRwMapper.insertTjSurveyOperationIncome(tjSurveyOperationIncomePo) > 0;
    }


    public boolean updateTjSurveyOperationIncome(TjSurveyOperationIncomePo tjSurveyOperationIncomePo) {
        return this.tjSurveyOperationIncomeRwMapper.updateTjSurveyOperationIncome(tjSurveyOperationIncomePo) > 0;
    }

    public boolean disableTjSurveyOperationIncome(String surveyNo) {
        return this.tjSurveyOperationIncomeRwMapper.disableTjSurveyOperationIncome(surveyNo) > 0;
    }

}

