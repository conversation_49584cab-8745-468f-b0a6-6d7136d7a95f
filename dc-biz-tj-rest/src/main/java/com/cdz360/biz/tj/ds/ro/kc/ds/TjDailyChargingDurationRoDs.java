package com.cdz360.biz.tj.ds.ro.kc.ds;


import com.cdz360.biz.model.common.param.BaseListParam;
import com.cdz360.biz.model.tj.kc.param.ListTjDailyChargingDurationParam;
import com.cdz360.biz.model.tj.kc.param.ListTjMaterialCostParam;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo;
import com.cdz360.biz.model.tj.kc.po.TjMaterialCostPo;
import com.cdz360.biz.model.tj.kc.vo.TjMaterialCostVo;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjDailyChargingDurationRoMapper;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjMaterialCostRoMapper;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjDailyChargingDurationRoDs {


    @Autowired

    private TjDailyChargingDurationRoMapper tjDailyChargingDurationRoMapper;


    public TjDailyChargingDurationPo getById(Long id) {

        return this.tjDailyChargingDurationRoMapper.getById(id);

    }

    public List<TjDailyChargingDurationPo> findTjDailyChargingDuration(
        ListTjDailyChargingDurationParam param) {
        return this.tjDailyChargingDurationRoMapper.findTjDailyChargingDuration(param);
    }

    public long countTjDailyChargingDuration(ListTjDailyChargingDurationParam param) {
        return this.tjDailyChargingDurationRoMapper.countTjDailyChargingDuration(param);
    }

}

