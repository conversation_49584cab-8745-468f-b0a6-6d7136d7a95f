package com.cdz360.biz.tj.ds.rw.kc.ds;


import com.cdz360.biz.model.tj.kc.po.TjMaterialCostPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyPo;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjMaterialCostRwMapper;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjSurveyRwMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjMaterialCostRwDs {


    @Autowired

    private TjMaterialCostRwMapper tjMaterialCostRwMapper;


    public TjMaterialCostPo getById(Long id, boolean lock) {

        return this.tjMaterialCostRwMapper.getById(id, lock);

    }


    public boolean insertTjMaterialCost(TjMaterialCostPo tjMaterialCostPo) {
        return this.tjMaterialCostRwMapper.insertTjMaterialCost(tjMaterialCostPo) > 0;
    }


    public boolean updateTjMaterialCost(TjMaterialCostPo tjMaterialCostPo) {
        return this.tjMaterialCostRwMapper.updateTjMaterialCost(tjMaterialCostPo) > 0;
    }

    public boolean disableTjMaterialCost(Long id) {
        return this.tjMaterialCostRwMapper.disableTjMaterialCost(id) > 0;
    }

}

