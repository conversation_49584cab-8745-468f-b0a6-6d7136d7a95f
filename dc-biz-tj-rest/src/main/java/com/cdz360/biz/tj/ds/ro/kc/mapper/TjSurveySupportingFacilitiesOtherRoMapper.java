package com.cdz360.biz.tj.ds.ro.kc.mapper;

import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesOtherPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjSurveySupportingFacilitiesOtherRoMapper {

    TjSurveySupportingFacilitiesOtherPo getById(@Param("id") Long id);

    List<TjSurveySupportingFacilitiesOtherPo> findTjSurveySupportingFacilitiesOtherBySurveyNo(@Param("surveyNo") String surveyNo);

}

