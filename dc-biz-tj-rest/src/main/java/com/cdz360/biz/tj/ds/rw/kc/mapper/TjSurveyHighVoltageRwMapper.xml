<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.rw.kc.mapper.TjSurveyHighVoltageRwMapper">

  <resultMap id="RESULT_TJ_SURVEY_HIGH_VOLTAGE_PO" type="com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="surveyNo" jdbcType="VARCHAR" property="surveyNo"/>
    <result column="capacity" jdbcType="INTEGER" property="capacity"/>
    <result column="cableDistance" jdbcType="INTEGER" property="cableDistance"/>
    <result column="poleTopSwitch" jdbcType="INTEGER" property="poleTopSwitch"/>
    <result column="ringMainUnit" jdbcType="INTEGER" property="ringMainUnit"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_SURVEY_HIGH_VOLTAGE_PO">
    select * from t_tj_survey_operation_expenses where id = #{id} and enable = true
    <if test="lock == true">
      for update
    </if>
  </select>

  <insert id="insertTjSurveyHighVoltage"
    parameterType="com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo">
    insert into t_tj_survey_high_voltage (`surveyNo`,
    <if test="null != capacity">
      `capacity`,
    </if>
    <if test="null != cableDistance">
      `cableDistance`,
    </if>
    <if test="null != poleTopSwitch">
      `poleTopSwitch`,
    </if>
    <if test="null != ringMainUnit">
      `ringMainUnit`,
    </if>
    `enable`,
    `createTime`,
    `updateTime`)
    values (#{surveyNo},
    <if test="null != capacity">
      #{capacity},
    </if>
    <if test="null != cableDistance">
      #{cableDistance},
    </if>
    <if test="null != poleTopSwitch">
      #{poleTopSwitch},
    </if>
    <if test="null != ringMainUnit">
      #{ringMainUnit},
    </if>
    true,
    now(),
    now())
  </insert>

  <update id="updateTjSurveyHighVoltage" parameterType="com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo">
    update t_tj_survey_high_voltage set
    <if test="capacity != null">
      capacity = #{capacity},
    </if>
    <if test="cableDistance != null">
      cableDistance = #{cableDistance},
    </if>
    <if test="poleTopSwitch != null">
      poleTopSwitch = #{poleTopSwitch},
    </if>
    <if test="ringMainUnit != null">
      ringMainUnit = #{ringMainUnit},
    </if>
    <if test="ringMainUnit != null">
      ringMainUnit = #{ringMainUnit},
    </if>
    <if test="enable != null">
      enable = #{enable},
    </if>
    updateTime = now()
    where id = #{id}
  </update>

  <update id="disableTjSurveyHighVoltageBySurveyNo">
    update t_tj_survey_high_voltage set
    enable = false,
    updateTime = now()
    where surveyNo = #{surveyNo}
  </update>


</mapper>

