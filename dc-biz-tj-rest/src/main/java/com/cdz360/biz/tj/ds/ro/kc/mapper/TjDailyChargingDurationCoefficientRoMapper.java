package com.cdz360.biz.tj.ds.ro.kc.mapper;

import com.cdz360.biz.model.tj.kc.param.ListTjMaterialCostParam;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationCoefficientPo;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjDailyChargingDurationCoefficientRoMapper {

    TjDailyChargingDurationCoefficientPo getById(@Param("id") Long id);

    TjDailyChargingDurationCoefficientPo findTjDailyChargingDurationCoefficient();

}

