package com.cdz360.biz.tj.ds.ro.kc.ds;


import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationIncomePo;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveyHighVoltageRoMapper;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveyOperationIncomeRoMapper;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjSurveyHighVoltageRoDs {


    @Autowired

    private TjSurveyHighVoltageRoMapper tjSurveyHighVoltageRoMapper;


    public TjSurveyHighVoltagePo getById(Long id) {

        return this.tjSurveyHighVoltageRoMapper.getById(id);

    }

    public List<TjSurveyHighVoltagePo> findTjSurveyHighVoltageBySurveyNo(String surveyNo) {
        return this.tjSurveyHighVoltageRoMapper.findTjSurveyHighVoltageBySurveyNo(surveyNo);
    }

}

