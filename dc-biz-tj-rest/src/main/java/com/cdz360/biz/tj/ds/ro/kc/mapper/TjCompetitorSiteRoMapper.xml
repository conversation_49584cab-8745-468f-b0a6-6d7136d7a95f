<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.ro.kc.mapper.TjCompetitorSiteRoMapper">

  <resultMap id="RESULT_TJ_COMPETITOR_SITE_PO"
    type="com.cdz360.biz.model.tj.kc.po.TjCompetitorSitePo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="competitorId" jdbcType="BIGINT" property="competitorId"/>
    <result column="gdId" jdbcType="VARCHAR" property="gdId"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="pCode" jdbcType="VARCHAR" property="pCode"/>
    <result column="pName" jdbcType="VARCHAR" property="pName"/>
    <result column="cityCode" jdbcType="VARCHAR" property="cityCode"/>
    <result column="cityName" jdbcType="VARCHAR" property="cityName"/>
    <result column="adCode" jdbcType="VARCHAR" property="adCode"/>
    <result column="adName" jdbcType="VARCHAR" property="adName"/>
    <result column="address" jdbcType="VARCHAR" property="address"/>
    <result column="locationText" property="location"
      typeHandler="com.cdz360.biz.ds.trading.PointTypeHandler"/>
    <result column="dcEvseNum" jdbcType="INTEGER" property="dcEvseNum"/>
    <result column="dcPlugNum" jdbcType="INTEGER" property="dcPlugNum"/>
    <result column="acEvseNum" jdbcType="INTEGER" property="acEvseNum"/>
    <result column="acPlugNum" jdbcType="INTEGER" property="acPlugNum"/>
    <result column="power" jdbcType="DECIMAL" property="power"/>
    <result column="minElecFee" jdbcType="DECIMAL" property="minElecFee"/>
    <result column="maxElecFee" jdbcType="DECIMAL" property="maxElecFee"/>
    <result column="minServFee" jdbcType="DECIMAL" property="minServFee"/>
    <result column="maxServFee" jdbcType="DECIMAL" property="maxServFee"/>
    <result column="attachUpdateTime" jdbcType="TIMESTAMP" property="attachUpdateTime"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <resultMap id="RESULT_TJ_COMPETITOR_SITE_VO"
    type="com.cdz360.biz.model.tj.kc.vo.TjCompetitorSiteVo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="competitorId" jdbcType="BIGINT" property="competitorId"/>
    <result column="competitorName" jdbcType="VARCHAR" property="competitorName"/>
    <result column="gdId" jdbcType="VARCHAR" property="gdId"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="pCode" jdbcType="VARCHAR" property="pCode"/>
    <result column="pName" jdbcType="VARCHAR" property="pName"/>
    <result column="cityCode" jdbcType="VARCHAR" property="cityCode"/>
    <result column="cityName" jdbcType="VARCHAR" property="cityName"/>
    <result column="adCode" jdbcType="VARCHAR" property="adCode"/>
    <result column="adName" jdbcType="VARCHAR" property="adName"/>
    <result column="address" jdbcType="VARCHAR" property="address"/>
    <result column="locationText" property="location"
      typeHandler="com.cdz360.biz.ds.trading.PointTypeHandler"/>
    <result column="dcEvseNum" jdbcType="INTEGER" property="dcEvseNum"/>
    <result column="dcPlugNum" jdbcType="INTEGER" property="dcPlugNum"/>
    <result column="acEvseNum" jdbcType="INTEGER" property="acEvseNum"/>
    <result column="acPlugNum" jdbcType="INTEGER" property="acPlugNum"/>
    <result column="power" jdbcType="DECIMAL" property="power"/>
    <result column="minElecFee" jdbcType="DECIMAL" property="minElecFee"/>
    <result column="maxElecFee" jdbcType="DECIMAL" property="maxElecFee"/>
    <result column="minServFee" jdbcType="DECIMAL" property="minServFee"/>
    <result column="maxServFee" jdbcType="DECIMAL" property="maxServFee"/>
    <result column="attachUpdateTime" jdbcType="TIMESTAMP" property="attachUpdateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_COMPETITOR_SITE_PO">
    select *, ST_AsText(`location`) locationText from t_tj_competitor_site where id = #{id}
  </select>

  <sql id="FIND_CONDITION_SQL">
    <where>
      <if test="null != competitorId">
        and competitorId = #{competitorId}
      </if>
      <if test="null != withinAid">
        AND withinArea.aid = #{withinAid}
        and withinArea.aid is not null
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( withinAidList )">
        <foreach collection="withinAidList" index="index" item="item"
          open=" AND withinArea.aid in (" separator="," close=")">
          #{item}
        </foreach>
        and withinArea.aid is not null
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
        <foreach collection="siteIdList" index="index" item="item"
          open=" AND site.id in (" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="null != analysisId and null != analysisPointNum">
        and ST_Distance_Sphere(site.location, ap.location) <![CDATA[<=]]> (ap.radius * 1000)
        and withinArea.aid is not null
      </if>
      <choose>
        <when test="@com.cdz360.base.utils.StringUtils@isNotBlank( petIdSetString )">
          and find_in_set(pet.id, #{petIdSetString})
        </when>
        <when test="null != petIdSetString"> <!-- 空字符串 -->
          and pet.id in (0)
        </when> <!-- end otherwise -->
      </choose>
    </where>
  </sql>

  <select id="findTjCompetitorSite"
    parameterType="com.cdz360.biz.model.tj.kc.param.ListTjCompetitorSiteParam"
    resultMap="RESULT_TJ_COMPETITOR_SITE_VO">
    select site.*, pet.name competitorName, ST_AsText(site.`location`) locationText
    from t_tj_competitor_site site
    left join t_tj_competitor pet on pet.id = site.competitorId
    <choose>
      <when test="null != analysisId and null != analysisPointNum">
        left join t_tj_area_analysis_point ap on
        ap.analysisId = #{analysisId} and ap.num = #{analysisPointNum}
        left join t_tj_area_analysis ana on ana.id = ap.analysisId
        left join t_tj_area withinArea on withinArea.aid = ana.aid and ST_Within(site.location,
        withinArea.geo)
      </when>
      <when test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( withinAidList ) or
       (null != withinAid)">
        left join t_tj_area withinArea on ST_Within(site.location, withinArea.geo)
      </when>
    </choose>
    <include refid="FIND_CONDITION_SQL"/>
    <choose>
      <when test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( sorts )">
        <foreach item="sort" collection="sorts"
          open="order by" separator="," close=" ">
          ${sort.columnsString} ${sort.order}
        </foreach>
      </when>
      <otherwise>
        order by id desc
      </otherwise>
    </choose>
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <otherwise>
        limit #{size}
      </otherwise>
    </choose>
  </select>

  <select id="countTjCompetitorSite"
    parameterType="com.cdz360.biz.model.tj.kc.param.ListTjCompetitorSiteParam"
    resultType="java.lang.Long">
    select count(site.id)
    from t_tj_competitor_site site
    <choose>
      <when test="null != analysisId and null != analysisPointNum">
        left join t_tj_area_analysis_point ap on
        ap.analysisId = #{analysisId} and ap.num = #{analysisPointNum}
        left join t_tj_area_analysis ana on ana.id = ap.analysisId
        left join t_tj_area withinArea on withinArea.aid = ana.aid
        and ST_Within(site.location, withinArea.geo)
      </when>
      <when test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( withinAidList )">
        left join t_tj_area withinArea on ST_Within(site.location, withinArea.geo)
      </when>
    </choose>
    <include refid="FIND_CONDITION_SQL"/>
  </select>

</mapper>

