package com.cdz360.biz.tj.ds.ro.kc.ds;


import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesOtherPo;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveyHighVoltageRoMapper;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveySupportingFacilitiesOtherRoMapper;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjSurveySupportingFacilitiesOtherRoDs {


    @Autowired

    private TjSurveySupportingFacilitiesOtherRoMapper tjSurveySupportingFacilitiesOtherRoMapper;


    public TjSurveySupportingFacilitiesOtherPo getById(Long id) {

        return this.tjSurveySupportingFacilitiesOtherRoMapper.getById(id);

    }

    public List<TjSurveySupportingFacilitiesOtherPo> findTjSurveySupportingFacilitiesOtherBySurveyNo(String surveyNo) {
        return this.tjSurveySupportingFacilitiesOtherRoMapper.findTjSurveySupportingFacilitiesOtherBySurveyNo(surveyNo);
    }

}

