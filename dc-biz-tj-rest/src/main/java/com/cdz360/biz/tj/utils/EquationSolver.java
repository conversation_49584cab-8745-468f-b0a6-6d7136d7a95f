package com.cdz360.biz.tj.utils;

import com.cdz360.biz.model.tj.kc.vo.TjYearCashVo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import org.apache.commons.math3.analysis.UnivariateFunction;
import org.apache.commons.math3.analysis.solvers.BisectionSolver;
import org.apache.commons.math3.exception.TooManyEvaluationsException;

public class EquationSolver {


    /**
     * @return
     * @throws TooManyEvaluationsException
     */
    public static double calculateEquation(TjYearCashVo tjYearCashVo, BigDecimal financeRate)
        throws TooManyEvaluationsException {

        /**
         * 定义方程 x为累计现金流量
         * 累计现金流量 = 本期经营活动现金流量 + 本期投资活动现金流量 + 上期累计现金流量
         * 本期经营活动现金流量 = (税前运营收入+税前运营补贴收入+建站补贴)-(通道费+平台托管费+运维费+引流费用+电费支出+电损支出+服务费分成+场地租金+其他运营费用+管理费用+财务费用+增值税+企业所得税)
         * 本期投资活动现金流量 = -(高压投资额 + 低压投资额（铝芯线缆） + 设备投资额 + 设备更新投入 + 后期投入)
         * 财务费用 = max(0,-0.0395x)
         * 企业所得税 = max(0,累计税前利润总额（含当期）< 0? 0:累计税前利润总额（含当期）*0.15-累计企业所得税（不含当期）)
         * 税前利润总额 = (税前运营收入+税前运营补贴收入+建站补贴)-(通道费+平台托管费+运维费+引流费用+电费支出+电损支出+服务费分成+场地租金+其他运营费用+管理费用+财务费用+增值税)-(折旧（高低压)+折旧（设备）+折旧（设备更新投入）)
         * 上期累计现金流量 = 查询上一年累计现金流量
         */
        UnivariateFunction function = new UnivariateFunction() {
            public double value(double x) {
                //本期经营活动现金流量
                //本期流入现金(税前运营收入+税前运营补贴收入+建站补贴)
                BigDecimal currentPeriodIncomeCash = tjYearCashVo.getOperationIncomeFee()
                    .add(tjYearCashVo.getOperationSubsidyFee())
                    .add(tjYearCashVo.getStationSubsidyFee());
                //本期流出现金(通道费+平台托管费+运维费+引流费用+电费支出+电损支出+服务费分成+场地租金+其他运营费用+管理费用+增值税)（财务费用跟企业所得税跟累计现金流有关系不参与普通计算）
                BigDecimal currentPeriodExpensesCash = tjYearCashVo.getChannelFee()
                    .add(tjYearCashVo.getPlatformFee()).add(tjYearCashVo.getMaintFee())
                    .add(tjYearCashVo.getFlowFee()).add(tjYearCashVo.getEleFee())
                    .add(tjYearCashVo.getEleLossFee()).add(tjYearCashVo.getServiceSharingFee())
                    .add(tjYearCashVo.getRentFee()).add(tjYearCashVo.getOtherOperationFee())
                    .add(tjYearCashVo.getManageFee()).add(tjYearCashVo.getVatFee());
                //本期流出现金-财务费用
                double financeCash = Math.max(0,
                    BigDecimal.ZERO.subtract(financeRate).doubleValue() * x);
                //折旧费用 折旧（高低压)+折旧（设备）+折旧（设备更新投入）
                BigDecimal zjCash = tjYearCashVo.getZjHighAndLowVoltageFee()
                    .add(tjYearCashVo.getZjEquipmentsFee())
                    .add(tjYearCashVo.getZjUpdateEquipmentsFee());
                //税前利润总额
                double profitBeforeTaxCash =
                    currentPeriodIncomeCash.doubleValue() - zjCash.doubleValue()
                        - currentPeriodExpensesCash.doubleValue() - financeCash;
                //本期流出现金-企业所得税

                //本期投资活动现金流量
                BigDecimal currentPeriodTzCash = BigDecimal.ZERO.subtract(
                    tjYearCashVo.getTzHighVoltageFee().add(tjYearCashVo.getTzLowVoltageFee()).add(
                            tjYearCashVo.getTzEquipmentsFee()
                                .add(tjYearCashVo.getTzUpdateEquipmentsFee()))
                        .add(tjYearCashVo.getTzOtherFee()));
                // 方程式
                double a = currentPeriodTzCash.doubleValue() + tjYearCashVo.getTotalBeforeCashFee()
                    .doubleValue() + currentPeriodIncomeCash.doubleValue()
                    - currentPeriodExpensesCash.doubleValue() - financeCash - (profitBeforeTaxCash
                    <= 0 ? 0
                    : (tjYearCashVo.getTotalBeforeProfitBeforeTaxFee()
                        .add(new BigDecimal(String.valueOf(profitBeforeTaxCash)))
                        .compareTo(BigDecimal.ZERO)
                        <= 0 ? 0
                        : tjYearCashVo.getTotalBeforeProfitBeforeTaxFee()
                            .add(new BigDecimal(String.valueOf(profitBeforeTaxCash)))
                            .multiply(new BigDecimal("0.15")).doubleValue()
                            - tjYearCashVo.getTotalBeforeCitFee().doubleValue())) - x;
                return a;
            }
        };

        // 使用BisectionSolver来求解方程式
        BisectionSolver solver = new BisectionSolver();
        double x = solver.solve(1000, function, -10000*10000.0, 10000*10000);
        return x;
    }

    public static void main(String[] args) {
        TjYearCashVo tjYearCashVo1 = new TjYearCashVo();
        tjYearCashVo1.setTzHighVoltageFee(new BigDecimal("40"));//高压投资额
        tjYearCashVo1.setTzLowVoltageFee(new BigDecimal("10"));//低压投资额
        tjYearCashVo1.setTzEquipmentsFee(new BigDecimal("12"));//设备投资额
        tjYearCashVo1.setTzUpdateEquipmentsFee(new BigDecimal("0"));//设备更新投资额
        tjYearCashVo1.setTzOtherFee(new BigDecimal("0"));//后期投入
        tjYearCashVo1.setZjHighAndLowVoltageFee(new BigDecimal("5.16"));//折旧（高低压)
        tjYearCashVo1.setZjEquipmentsFee(new BigDecimal("1.19"));//折旧（设备）
        tjYearCashVo1.setZjUpdateEquipmentsFee(new BigDecimal("0"));//折旧（设备更新投入）
        tjYearCashVo1.setOperationIncomeFee(new BigDecimal("17.23"));//税前运营收入费用
        tjYearCashVo1.setOperationSubsidyFee(new BigDecimal("0"));//税前运营补贴收入费用
        tjYearCashVo1.setStationSubsidyFee(new BigDecimal("0"));//建站补贴费用
        tjYearCashVo1.setChannelFee(new BigDecimal("0.1"));//通道费
        tjYearCashVo1.setPlatformFee(new BigDecimal("0.31"));//平台托管费
        tjYearCashVo1.setMaintFee(new BigDecimal("0"));//运维费
        tjYearCashVo1.setFlowFee(new BigDecimal("0.24"));//引流费用
        tjYearCashVo1.setEleFee(new BigDecimal("12.31"));//电费支出
        tjYearCashVo1.setEleLossFee(new BigDecimal("0.86"));//电损支出
        tjYearCashVo1.setServiceSharingFee(new BigDecimal("0.38"));//服务费分成
        tjYearCashVo1.setRentFee(new BigDecimal("1.2"));//场地租金
        tjYearCashVo1.setOtherOperationFee(new BigDecimal("0"));//其他运营费用
        tjYearCashVo1.setManageFee(new BigDecimal("0"));//管理费用
        tjYearCashVo1.setVatFee(new BigDecimal("0"));//增值税
//        tjYearCashVo1.setProfitBeforeTaxFee(new BigDecimal("-7.01"));//税前利润总额
        tjYearCashVo1.setTotalBeforeCashFee(new BigDecimal("0"));//累计现金流量（不含当期）
        tjYearCashVo1.setTotalBeforeProfitBeforeTaxFee(new BigDecimal("0"));//累计税前利润总额（不含当期）
        tjYearCashVo1.setTotalBeforeCitFee(new BigDecimal("0"));//累计企业所得税（不含当期）
        double x1 = new EquationSolver().calculateEquation(tjYearCashVo1, new BigDecimal("0.0395"));
        System.out.println("第一年*********:" + String.format("%.4f", x1));

        TjYearCashVo tjYearCashVo2 = new TjYearCashVo();
        tjYearCashVo2.setTzHighVoltageFee(new BigDecimal("0"));//高压投资额
        tjYearCashVo2.setTzLowVoltageFee(new BigDecimal("0"));//低压投资额
        tjYearCashVo2.setTzEquipmentsFee(new BigDecimal("0"));//设备投资额
        tjYearCashVo2.setTzUpdateEquipmentsFee(new BigDecimal("0"));//设备更新投资额
        tjYearCashVo2.setTzOtherFee(new BigDecimal("0"));//后期投入
        tjYearCashVo2.setZjHighAndLowVoltageFee(new BigDecimal("5.16"));//折旧（高低压)
        tjYearCashVo2.setZjEquipmentsFee(new BigDecimal("1.19"));//折旧（设备）
        tjYearCashVo2.setZjUpdateEquipmentsFee(new BigDecimal("0"));//折旧（设备更新投入）
        tjYearCashVo2.setOperationIncomeFee(new BigDecimal("26.61"));//税前运营收入费用
        tjYearCashVo2.setOperationSubsidyFee(new BigDecimal("0"));//税前运营补贴收入费用
        tjYearCashVo2.setStationSubsidyFee(new BigDecimal("0"));//建站补贴费用
        tjYearCashVo2.setChannelFee(new BigDecimal("0.16"));//通道费
        tjYearCashVo2.setPlatformFee(new BigDecimal("0.36"));//平台托管费
        tjYearCashVo2.setMaintFee(new BigDecimal("0"));//运维费
        tjYearCashVo2.setFlowFee(new BigDecimal("0.41"));//引流费用
        tjYearCashVo2.setEleFee(new BigDecimal("18.14"));//电费支出
        tjYearCashVo2.setEleLossFee(new BigDecimal("1.27"));//电损支出
        tjYearCashVo2.setServiceSharingFee(new BigDecimal("0.68"));//服务费分成
        tjYearCashVo2.setRentFee(new BigDecimal("1.2"));//场地租金
        tjYearCashVo2.setOtherOperationFee(new BigDecimal("0"));//其他运营费用
        tjYearCashVo2.setManageFee(new BigDecimal("0"));//管理费用
        tjYearCashVo2.setVatFee(new BigDecimal("0"));//增值税
//        tjYearCashVo2.setProfitBeforeTaxFee(new BigDecimal("-4.36"));//税前利润总额
        tjYearCashVo2.setTotalBeforeCashFee(new BigDecimal("-62.65"));//累计现金流量（不含当期）
        tjYearCashVo2.setTotalBeforeProfitBeforeTaxFee(new BigDecimal("-7.01"));//累计税前利润总额（不含当期）
        tjYearCashVo2.setTotalBeforeCitFee(new BigDecimal("0"));//累计企业所得税（不含当期）
        double x2 = new EquationSolver().calculateEquation(tjYearCashVo2, new BigDecimal("0.0395"));
        System.out.println("第二年*********:" + String.format("%.4f", x2));

        TjYearCashVo tjYearCashVo3 = new TjYearCashVo();
        tjYearCashVo3.setTzHighVoltageFee(new BigDecimal("0"));//高压投资额
        tjYearCashVo3.setTzLowVoltageFee(new BigDecimal("0"));//低压投资额
        tjYearCashVo3.setTzEquipmentsFee(new BigDecimal("0"));//设备投资额
        tjYearCashVo3.setTzUpdateEquipmentsFee(new BigDecimal("0"));//设备更新投资额
        tjYearCashVo3.setTzOtherFee(new BigDecimal("0"));//后期投入
        tjYearCashVo3.setZjHighAndLowVoltageFee(new BigDecimal("5.16"));//折旧（高低压)
        tjYearCashVo3.setZjEquipmentsFee(new BigDecimal("1.19"));//折旧（设备）
        tjYearCashVo3.setZjUpdateEquipmentsFee(new BigDecimal("0"));//折旧（设备更新投入）
        tjYearCashVo3.setOperationIncomeFee(new BigDecimal("34.99"));//税前运营收入费用
        tjYearCashVo3.setOperationSubsidyFee(new BigDecimal("0"));//税前运营补贴收入费用
        tjYearCashVo3.setStationSubsidyFee(new BigDecimal("0"));//建站补贴费用
        tjYearCashVo3.setChannelFee(new BigDecimal("0.21"));//通道费
        tjYearCashVo3.setPlatformFee(new BigDecimal("0.31"));//平台托管费
        tjYearCashVo3.setMaintFee(new BigDecimal("0.26"));//运维费
        tjYearCashVo3.setFlowFee(new BigDecimal("0.56"));//引流费用
        tjYearCashVo3.setEleFee(new BigDecimal("23.33"));//电费支出
        tjYearCashVo3.setEleLossFee(new BigDecimal("1.63"));//电损支出
        tjYearCashVo3.setServiceSharingFee(new BigDecimal("0.95"));//服务费分成
        tjYearCashVo3.setRentFee(new BigDecimal("1.2"));//场地租金
        tjYearCashVo3.setOtherOperationFee(new BigDecimal("0"));//其他运营费用
        tjYearCashVo3.setManageFee(new BigDecimal("0"));//管理费用
        tjYearCashVo3.setVatFee(new BigDecimal("0"));//增值税
//        tjYearCashVo3.setProfitBeforeTaxFee(new BigDecimal("-2.04"));//税前利润总额
        tjYearCashVo3.setTotalBeforeCashFee(new BigDecimal("-60.66"));//累计现金流量（不含当期）
        tjYearCashVo3.setTotalBeforeProfitBeforeTaxFee(new BigDecimal("-11.37"));//累计税前利润总额（不含当期）
        tjYearCashVo3.setTotalBeforeCitFee(new BigDecimal("0"));//累计企业所得税（不含当期）
        double x3 = new EquationSolver().calculateEquation(tjYearCashVo3, new BigDecimal("0.0395"));
        System.out.println("第三年*********:" + String.format("%.4f", x3));

        TjYearCashVo tjYearCashVo4 = new TjYearCashVo();
        tjYearCashVo4.setTzHighVoltageFee(new BigDecimal("0"));//高压投资额
        tjYearCashVo4.setTzLowVoltageFee(new BigDecimal("0"));//低压投资额
        tjYearCashVo4.setTzEquipmentsFee(new BigDecimal("0"));//设备投资额
        tjYearCashVo4.setTzUpdateEquipmentsFee(new BigDecimal("0"));//设备更新投资额
        tjYearCashVo4.setTzOtherFee(new BigDecimal("0"));//后期投入
        tjYearCashVo4.setZjHighAndLowVoltageFee(new BigDecimal("5.16"));//折旧（高低压)
        tjYearCashVo4.setZjEquipmentsFee(new BigDecimal("1.19"));//折旧（设备）
        tjYearCashVo4.setZjUpdateEquipmentsFee(new BigDecimal("0"));//折旧（设备更新投入）
        tjYearCashVo4.setOperationIncomeFee(new BigDecimal("45.14"));//税前运营收入费用
        tjYearCashVo4.setOperationSubsidyFee(new BigDecimal("0"));//税前运营补贴收入费用
        tjYearCashVo4.setStationSubsidyFee(new BigDecimal("0"));//建站补贴费用
        tjYearCashVo4.setChannelFee(new BigDecimal("0.27"));//通道费
        tjYearCashVo4.setPlatformFee(new BigDecimal("0.38"));//平台托管费
        tjYearCashVo4.setMaintFee(new BigDecimal("0.32"));//运维费
        tjYearCashVo4.setFlowFee(new BigDecimal("0.8"));//引流费用
        tjYearCashVo4.setEleFee(new BigDecimal("28.51"));//电费支出
        tjYearCashVo4.setEleLossFee(new BigDecimal("2"));//电损支出
        tjYearCashVo4.setServiceSharingFee(new BigDecimal("1.38"));//服务费分成
        tjYearCashVo4.setRentFee(new BigDecimal("1.2"));//场地租金
        tjYearCashVo4.setOtherOperationFee(new BigDecimal("0"));//其他运营费用
        tjYearCashVo4.setManageFee(new BigDecimal("0"));//管理费用
        tjYearCashVo4.setVatFee(new BigDecimal("0"));//增值税
//        tjYearCashVo4.setProfitBeforeTaxFee(new BigDecimal("2.03"));//税前利润总额
        tjYearCashVo4.setTotalBeforeCashFee(new BigDecimal("-56.34"));//累计现金流量（不含当期）
        tjYearCashVo4.setTotalBeforeProfitBeforeTaxFee(new BigDecimal("-13.41"));//累计税前利润总额（不含当期）
        tjYearCashVo4.setTotalBeforeCitFee(new BigDecimal("0"));//累计企业所得税（不含当期）
        double x4 = new EquationSolver().calculateEquation(tjYearCashVo4, new BigDecimal("0.0395"));
        System.out.println("第四年*********:" + String.format("%.4f", x4));

        TjYearCashVo tjYearCashVo5 = new TjYearCashVo();
        tjYearCashVo5.setTzHighVoltageFee(new BigDecimal("0"));//高压投资额
        tjYearCashVo5.setTzLowVoltageFee(new BigDecimal("0"));//低压投资额
        tjYearCashVo5.setTzEquipmentsFee(new BigDecimal("0"));//设备投资额
        tjYearCashVo5.setTzUpdateEquipmentsFee(new BigDecimal("0"));//设备更新投资额
        tjYearCashVo5.setTzOtherFee(new BigDecimal("0"));//后期投入
        tjYearCashVo5.setZjHighAndLowVoltageFee(new BigDecimal("5.16"));//折旧（高低压)
        tjYearCashVo5.setZjEquipmentsFee(new BigDecimal("1.19"));//折旧（设备）
        tjYearCashVo5.setZjUpdateEquipmentsFee(new BigDecimal("0"));//折旧（设备更新投入）
        tjYearCashVo5.setOperationIncomeFee(new BigDecimal("53.35"));//税前运营收入费用
        tjYearCashVo5.setOperationSubsidyFee(new BigDecimal("0"));//税前运营补贴收入费用
        tjYearCashVo5.setStationSubsidyFee(new BigDecimal("0"));//建站补贴费用
        tjYearCashVo5.setChannelFee(new BigDecimal("0.32"));//通道费
        tjYearCashVo5.setPlatformFee(new BigDecimal("0.45"));//平台托管费
        tjYearCashVo5.setMaintFee(new BigDecimal("0.38"));//运维费
        tjYearCashVo5.setFlowFee(new BigDecimal("0.94"));//引流费用
        tjYearCashVo5.setEleFee(new BigDecimal("33.7"));//电费支出
        tjYearCashVo5.setEleLossFee(new BigDecimal("2.36"));//电损支出
        tjYearCashVo5.setServiceSharingFee(new BigDecimal("1.64"));//服务费分成
        tjYearCashVo5.setRentFee(new BigDecimal("1.2"));//场地租金
        tjYearCashVo5.setOtherOperationFee(new BigDecimal("0"));//其他运营费用
        tjYearCashVo5.setManageFee(new BigDecimal("0"));//管理费用
        tjYearCashVo5.setVatFee(new BigDecimal("0"));//增值税
//        tjYearCashVo5.setProfitBeforeTaxFee(new BigDecimal("4.55"));//税前利润总额
        tjYearCashVo5.setTotalBeforeCashFee(new BigDecimal("-47.96"));//累计现金流量（不含当期）
        tjYearCashVo5.setTotalBeforeProfitBeforeTaxFee(new BigDecimal("-11.38"));//累计税前利润总额（不含当期）
        tjYearCashVo5.setTotalBeforeCitFee(new BigDecimal("0"));//累计企业所得税（不含当期）
        double x5 = new EquationSolver().calculateEquation(tjYearCashVo5, new BigDecimal("0.0395"));
        System.out.println("第五年*********:" + String.format("%.4f", x5));

        TjYearCashVo tjYearCashVo6 = new TjYearCashVo();
        tjYearCashVo6.setTzHighVoltageFee(new BigDecimal("0"));//高压投资额
        tjYearCashVo6.setTzLowVoltageFee(new BigDecimal("0"));//低压投资额
        tjYearCashVo6.setTzEquipmentsFee(new BigDecimal("0"));//设备投资额
        tjYearCashVo6.setTzUpdateEquipmentsFee(new BigDecimal("0"));//设备更新投资额
        tjYearCashVo6.setTzOtherFee(new BigDecimal("0"));//后期投入
        tjYearCashVo6.setZjHighAndLowVoltageFee(new BigDecimal("5.16"));//折旧（高低压)
        tjYearCashVo6.setZjEquipmentsFee(new BigDecimal("1.19"));//折旧（设备）
        tjYearCashVo6.setZjUpdateEquipmentsFee(new BigDecimal("0"));//折旧（设备更新投入）
        tjYearCashVo6.setOperationIncomeFee(new BigDecimal("61.56"));//税前运营收入费用
        tjYearCashVo6.setOperationSubsidyFee(new BigDecimal("0"));//税前运营补贴收入费用
        tjYearCashVo6.setStationSubsidyFee(new BigDecimal("0"));//建站补贴费用
        tjYearCashVo6.setChannelFee(new BigDecimal("0.37"));//通道费
        tjYearCashVo6.setPlatformFee(new BigDecimal("0.52"));//平台托管费
        tjYearCashVo6.setMaintFee(new BigDecimal("0.44"));//运维费
        tjYearCashVo6.setFlowFee(new BigDecimal("1.09"));//引流费用
        tjYearCashVo6.setEleFee(new BigDecimal("38.88"));//电费支出
        tjYearCashVo6.setEleLossFee(new BigDecimal("2.72"));//电损支出
        tjYearCashVo6.setServiceSharingFee(new BigDecimal("1.89"));//服务费分成
        tjYearCashVo6.setRentFee(new BigDecimal("1.2"));//场地租金
        tjYearCashVo6.setOtherOperationFee(new BigDecimal("0"));//其他运营费用
        tjYearCashVo6.setManageFee(new BigDecimal("0"));//管理费用
        tjYearCashVo6.setVatFee(new BigDecimal("0.54"));//增值税
//        tjYearCashVo6.setProfitBeforeTaxFee(new BigDecimal("6.6"));//税前利润总额
        tjYearCashVo6.setTotalBeforeCashFee(new BigDecimal("-37.06"));//累计现金流量（不含当期）
        tjYearCashVo6.setTotalBeforeProfitBeforeTaxFee(new BigDecimal("-6.835"));//累计税前利润总额（不含当期）
        tjYearCashVo6.setTotalBeforeCitFee(new BigDecimal("0"));//累计企业所得税（不含当期）
        double x6 = new EquationSolver().calculateEquation(tjYearCashVo6, new BigDecimal("0.0395"));
        System.out.println("第六年*********:" + String.format("%.4f", x6));

        TjYearCashVo tjYearCashVo7 = new TjYearCashVo();
        tjYearCashVo7.setTzHighVoltageFee(new BigDecimal("0"));//高压投资额
        tjYearCashVo7.setTzLowVoltageFee(new BigDecimal("0"));//低压投资额
        tjYearCashVo7.setTzEquipmentsFee(new BigDecimal("0"));//设备投资额
        tjYearCashVo7.setTzUpdateEquipmentsFee(new BigDecimal("0"));//设备更新投资额
        tjYearCashVo7.setTzOtherFee(new BigDecimal("0"));//后期投入
        tjYearCashVo7.setZjHighAndLowVoltageFee(new BigDecimal("5.16"));//折旧（高低压)
        tjYearCashVo7.setZjEquipmentsFee(new BigDecimal("1.19"));//折旧（设备）
        tjYearCashVo7.setZjUpdateEquipmentsFee(new BigDecimal("0"));//折旧（设备更新投入）
        tjYearCashVo7.setOperationIncomeFee(new BigDecimal("69.77"));//税前运营收入费用
        tjYearCashVo7.setOperationSubsidyFee(new BigDecimal("0"));//税前运营补贴收入费用
        tjYearCashVo7.setStationSubsidyFee(new BigDecimal("0"));//建站补贴费用
        tjYearCashVo7.setChannelFee(new BigDecimal("0.42"));//通道费
        tjYearCashVo7.setPlatformFee(new BigDecimal("0.59"));//平台托管费
        tjYearCashVo7.setMaintFee(new BigDecimal("0.5"));//运维费
        tjYearCashVo7.setFlowFee(new BigDecimal("1.23"));//引流费用
        tjYearCashVo7.setEleFee(new BigDecimal("44.06"));//电费支出
        tjYearCashVo7.setEleLossFee(new BigDecimal("3.08"));//电损支出
        tjYearCashVo7.setServiceSharingFee(new BigDecimal("2.14"));//服务费分成
        tjYearCashVo7.setRentFee(new BigDecimal("1.2"));//场地租金
        tjYearCashVo7.setOtherOperationFee(new BigDecimal("0"));//其他运营费用
        tjYearCashVo7.setManageFee(new BigDecimal("0"));//管理费用
        tjYearCashVo7.setVatFee(new BigDecimal("1.97"));//增值税
//        tjYearCashVo7.setProfitBeforeTaxFee(new BigDecimal("7.78"));//税前利润总额
        tjYearCashVo7.setTotalBeforeCashFee(new BigDecimal("-24.1"));//累计现金流量（不含当期）
        tjYearCashVo7.setTotalBeforeProfitBeforeTaxFee(new BigDecimal("-0.234593"));//累计税前利润总额（不含当期）
        tjYearCashVo7.setTotalBeforeCitFee(new BigDecimal("0"));//累计企业所得税（不含当期）
        double x7 = new EquationSolver().calculateEquation(tjYearCashVo7, new BigDecimal("0.0395"));
        System.out.println("第七年*********:" + String.format("%.4f", x7));

        TjYearCashVo tjYearCashVo8 = new TjYearCashVo();
        tjYearCashVo8.setTzHighVoltageFee(new BigDecimal("0"));//高压投资额
        tjYearCashVo8.setTzLowVoltageFee(new BigDecimal("0"));//低压投资额
        tjYearCashVo8.setTzEquipmentsFee(new BigDecimal("-1.2"));//设备投资额
        tjYearCashVo8.setTzUpdateEquipmentsFee(new BigDecimal("12"));//设备更新投资额
        tjYearCashVo8.setTzOtherFee(new BigDecimal("0"));//后期投入
        tjYearCashVo8.setZjHighAndLowVoltageFee(new BigDecimal("5.16"));//折旧（高低压)
        tjYearCashVo8.setZjEquipmentsFee(new BigDecimal("1.19"));//折旧（设备）
        tjYearCashVo8.setZjUpdateEquipmentsFee(new BigDecimal("0"));//折旧（设备更新投入）
        tjYearCashVo8.setOperationIncomeFee(new BigDecimal("73.87"));//税前运营收入费用
        tjYearCashVo8.setOperationSubsidyFee(new BigDecimal("0"));//税前运营补贴收入费用
        tjYearCashVo8.setStationSubsidyFee(new BigDecimal("0"));//建站补贴费用
        tjYearCashVo8.setChannelFee(new BigDecimal("0.44"));//通道费
        tjYearCashVo8.setPlatformFee(new BigDecimal("0.62"));//平台托管费
        tjYearCashVo8.setMaintFee(new BigDecimal("0.56"));//运维费
        tjYearCashVo8.setFlowFee(new BigDecimal("1.31"));//引流费用
        tjYearCashVo8.setEleFee(new BigDecimal("46.66"));//电费支出
        tjYearCashVo8.setEleLossFee(new BigDecimal("3.27"));//电损支出
        tjYearCashVo8.setServiceSharingFee(new BigDecimal("2.26"));//服务费分成
        tjYearCashVo8.setRentFee(new BigDecimal("1.2"));//场地租金
        tjYearCashVo8.setOtherOperationFee(new BigDecimal("0"));//其他运营费用
        tjYearCashVo8.setManageFee(new BigDecimal("0"));//管理费用
        tjYearCashVo8.setVatFee(new BigDecimal("0.84"));//增值税
//        tjYearCashVo8.setProfitBeforeTaxFee(new BigDecimal("10.08"));//税前利润总额
        tjYearCashVo8.setTotalBeforeCashFee(new BigDecimal("-11.1"));//累计现金流量（不含当期）
        tjYearCashVo8.setTotalBeforeProfitBeforeTaxFee(new BigDecimal("7.54274"));//累计税前利润总额（不含当期）
        tjYearCashVo8.setTotalBeforeCitFee(new BigDecimal("1.13"));//累计企业所得税（不含当期）
        double x8 = new EquationSolver().calculateEquation(tjYearCashVo8, new BigDecimal("0.0395"));
        System.out.println("第八年*********:" + String.format("%.4f", x8));

        TjYearCashVo tjYearCashVo9 = new TjYearCashVo();
        tjYearCashVo9.setTzHighVoltageFee(new BigDecimal("0"));//高压投资额
        tjYearCashVo9.setTzLowVoltageFee(new BigDecimal("0"));//低压投资额
        tjYearCashVo9.setTzEquipmentsFee(new BigDecimal("0"));//设备投资额
        tjYearCashVo9.setTzUpdateEquipmentsFee(new BigDecimal("0"));//设备更新投资额
        tjYearCashVo9.setTzOtherFee(new BigDecimal("0"));//后期投入
        tjYearCashVo9.setZjHighAndLowVoltageFee(new BigDecimal("0"));//折旧（高低压)
        tjYearCashVo9.setZjEquipmentsFee(new BigDecimal("0"));//折旧（设备）
        tjYearCashVo9.setZjUpdateEquipmentsFee(new BigDecimal("1.19"));//折旧（设备更新投入）
        tjYearCashVo9.setOperationIncomeFee(new BigDecimal("77.98"));//税前运营收入费用
        tjYearCashVo9.setOperationSubsidyFee(new BigDecimal("0"));//税前运营补贴收入费用
        tjYearCashVo9.setStationSubsidyFee(new BigDecimal("0"));//建站补贴费用
        tjYearCashVo9.setChannelFee(new BigDecimal("0.47"));//通道费
        tjYearCashVo9.setPlatformFee(new BigDecimal("0.66"));//平台托管费
        tjYearCashVo9.setMaintFee(new BigDecimal("0"));//运维费
        tjYearCashVo9.setFlowFee(new BigDecimal("1.38"));//引流费用
        tjYearCashVo9.setEleFee(new BigDecimal("49.25"));//电费支出
        tjYearCashVo9.setEleLossFee(new BigDecimal("3.45"));//电损支出
        tjYearCashVo9.setServiceSharingFee(new BigDecimal("2.39"));//服务费分成
        tjYearCashVo9.setRentFee(new BigDecimal("1.2"));//场地租金
        tjYearCashVo9.setOtherOperationFee(new BigDecimal("0"));//其他运营费用
        tjYearCashVo9.setManageFee(new BigDecimal("0"));//管理费用
        tjYearCashVo9.setVatFee(new BigDecimal("2.27"));//增值税
//        tjYearCashVo9.setProfitBeforeTaxFee(new BigDecimal("15.72"));//税前利润总额
        tjYearCashVo9.setTotalBeforeCashFee(new BigDecimal("-6.98"));//累计现金流量（不含当期）
        tjYearCashVo9.setTotalBeforeProfitBeforeTaxFee(new BigDecimal("17.62"));//累计税前利润总额（不含当期）
        tjYearCashVo9.setTotalBeforeCitFee(new BigDecimal("2.643"));//累计企业所得税（不含当期）
        double x9 = new EquationSolver().calculateEquation(tjYearCashVo9, new BigDecimal("0.0395"));
        System.out.println("第九年*********:" + String.format("%.4f", x9));

        TjYearCashVo tjYearCashVo10 = new TjYearCashVo();
        tjYearCashVo10.setTzHighVoltageFee(new BigDecimal("-4"));//高压投资额
        tjYearCashVo10.setTzLowVoltageFee(new BigDecimal("-1"));//低压投资额
        tjYearCashVo10.setTzEquipmentsFee(new BigDecimal("0"));//设备投资额
        tjYearCashVo10.setTzUpdateEquipmentsFee(new BigDecimal("-9.3"));//设备更新投资额
        tjYearCashVo10.setTzOtherFee(new BigDecimal("0"));//后期投入
        tjYearCashVo10.setZjHighAndLowVoltageFee(new BigDecimal("0"));//折旧（高低压)
        tjYearCashVo10.setZjEquipmentsFee(new BigDecimal("0"));//折旧（设备）
        tjYearCashVo10.setZjUpdateEquipmentsFee(new BigDecimal("1.19"));//折旧（设备更新投入）
        tjYearCashVo10.setOperationIncomeFee(new BigDecimal("82.08"));//税前运营收入费用
        tjYearCashVo10.setOperationSubsidyFee(new BigDecimal("0"));//税前运营补贴收入费用
        tjYearCashVo10.setStationSubsidyFee(new BigDecimal("0"));//建站补贴费用
        tjYearCashVo10.setChannelFee(new BigDecimal("0.49"));//通道费
        tjYearCashVo10.setPlatformFee(new BigDecimal("0.69"));//平台托管费
        tjYearCashVo10.setMaintFee(new BigDecimal("0"));//运维费
        tjYearCashVo10.setFlowFee(new BigDecimal("1.45"));//引流费用
        tjYearCashVo10.setEleFee(new BigDecimal("51.84"));//电费支出
        tjYearCashVo10.setEleLossFee(new BigDecimal("3.63"));//电损支出
        tjYearCashVo10.setServiceSharingFee(new BigDecimal("2.52"));//服务费分成
        tjYearCashVo10.setRentFee(new BigDecimal("1.2"));//场地租金
        tjYearCashVo10.setOtherOperationFee(new BigDecimal("0"));//其他运营费用
        tjYearCashVo10.setManageFee(new BigDecimal("0"));//管理费用
        tjYearCashVo10.setVatFee(new BigDecimal("3.88"));//增值税
//        tjYearCashVo10.setProfitBeforeTaxFee(new BigDecimal("15.18"));//税前利润总额
        tjYearCashVo10.setTotalBeforeCashFee(new BigDecimal("7.57"));//累计现金流量（不含当期）
        tjYearCashVo10.setTotalBeforeProfitBeforeTaxFee(new BigDecimal("33.339"));//累计税前利润总额（不含当期）
        tjYearCashVo10.setTotalBeforeCitFee(new BigDecimal("5"));//累计企业所得税（不含当期）
        double x10 = new EquationSolver().calculateEquation(tjYearCashVo10,
            new BigDecimal("0.0395"));
        System.out.println("第十年*********:" + String.format("%.4f", x10));
    }

}
