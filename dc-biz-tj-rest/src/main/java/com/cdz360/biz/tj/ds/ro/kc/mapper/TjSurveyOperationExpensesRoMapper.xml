<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveyOperationExpensesRoMapper">

  <resultMap id="RESULT_TJ_SURVEY_OPERATION_EXPENSES_PO" type="com.cdz360.biz.model.tj.kc.po.TjSurveyOperationExpensesPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="surveyNo" jdbcType="VARCHAR" property="surveyNo"/>
    <result column="serviceFeeSharing" jdbcType="DECIMAL" property="serviceFeeSharing"/>
    <result column="serviceFeeExcludeAttract" jdbcType="BOOLEAN" property="serviceFeeExcludeAttract"/>
    <result column="fixedRent" jdbcType="BOOLEAN" property="fixedRent"/>
    <result column="rentFee" jdbcType="DECIMAL" property="rentFee"/>
    <result column="everyFewYears" jdbcType="DECIMAL" property="everyFewYears"/>
    <result column="increase" jdbcType="DECIMAL" property="increase"/>
    <result column="otherFee" jdbcType="DECIMAL" property="otherFee"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_SURVEY_OPERATION_EXPENSES_PO">
    select * from t_tj_survey_operation_expenses where id = #{id}
  </select>

  <select id="findTjSurveyOperationExpensesBySurveyNo"
    resultMap="RESULT_TJ_SURVEY_OPERATION_EXPENSES_PO">
    select * from t_tj_survey_operation_expenses where surveyNo = #{surveyNo} and enable=true
  </select>


</mapper>

