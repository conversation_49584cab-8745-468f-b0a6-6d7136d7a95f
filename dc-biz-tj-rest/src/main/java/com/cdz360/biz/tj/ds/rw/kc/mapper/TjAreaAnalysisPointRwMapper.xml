<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.rw.kc.mapper.TjAreaAnalysisPointRwMapper">

  <resultMap id="RESULT_TJ_AREA_ANALYSIS_POINT_PO"
    type="com.cdz360.biz.model.tj.kc.po.TjAreaAnalysisPointPo">
    <result column="analysisId" jdbcType="BIGINT" property="analysisId"/>
    <result column="num" property="num"/>
    <result column="status" property="status"/>
    <result column="locationText" property="location"
      typeHandler="com.cdz360.biz.ds.trading.PointTypeHandler"/>
    <result column="radius" property="radius"/>
    <result column="address" jdbcType="VARCHAR" property="address"/>
  </resultMap>

  <insert id="batchInsertTjAreaAnalysisPoint" parameterType="java.util.List">
    INSERT INTO t_tj_area_analysis_point (
    `analysisId`, `num`, `status`, `location`, `radius`, `address`)
    VALUES
    <foreach collection="poList" item="item" index="index" separator=",">
      (#{item.analysisId}, #{item.num}, #{item.status},
      ST_GeomFromText(#{item.location, jdbcType=JAVA_OBJECT,
      typeHandler=com.cdz360.biz.ds.trading.PointTypeHandler}),
      #{item.radius}, #{item.address})
    </foreach>
  </insert>

  <insert id="insertTjAreaAnalysisPoint" useGeneratedKeys="true" keyProperty="id"
    keyColumn="id" parameterType="com.cdz360.biz.model.tj.kc.po.TjAreaAnalysisPointPo">
    insert into t_tj_area_analysis_point (
    `analysisId`, `num`, `status`, `location`, `radius`, `address`)
    values (#{analysisId}, #{num}, #{status},
    ST_GeomFromText(#{location, jdbcType=JAVA_OBJECT,
    typeHandler=com.cdz360.biz.ds.trading.PointTypeHandler}),
    #{radius}, #{address})
  </insert>

  <update id="updateTjAnalysisPointStatus"
    parameterType="com.cdz360.biz.model.tj.kc.param.UpdateAnalysisPointStatusParam">
    update t_tj_area_analysis_point
    set status = #{status}
    where analysisId = #{analysisId}
    <foreach collection="numList" item="num"
      open="and num in (" separator="," close=")">
      #{num}
    </foreach>
  </update>

  <delete id="deleteByAnalysisId">
    delete from t_tj_area_analysis_point
    where analysisId = #{analysisId}
  </delete>

</mapper>

