package com.topower.repository;

import com.topower.domain.InvoiceChargeOrderPo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@SuppressWarnings("unused")
@Repository
public interface ChargeOrderRepository extends
    JpaRepository<InvoiceChargeOrderPo, String>, JpaSpecificationExecutor<InvoiceChargeOrderPo> {

}