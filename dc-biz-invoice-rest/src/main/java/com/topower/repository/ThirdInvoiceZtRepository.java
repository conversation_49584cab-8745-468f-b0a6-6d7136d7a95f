package com.topower.repository;

import com.topower.domain.ThirdInvoiceZt;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;


/**
 * Spring Data  repository for the ThirdInvoiceZt entity.
 */
@SuppressWarnings("unused")
@Repository
@Transactional//执行修改方法时一定要添加这个注解和@Modifying注解
public interface ThirdInvoiceZtRepository extends
    JpaRepository<ThirdInvoiceZt, Long>, JpaSpecificationExecutor<ThirdInvoiceZt> {

    ThirdInvoiceZt getOneByUuid(String uuid);
}
