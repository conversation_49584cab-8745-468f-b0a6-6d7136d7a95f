package com.topower.util;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

//import java.net.URLConnection;

public class HttpUtil {

    private static final Logger log = LoggerFactory.getLogger(HttpUtil.class);

    private static final int TIMEOUT_IN_MILLIONS = 120000;


    /**
     * 向指定URL发送GET方法的请求
     *
     * @param url
     *            发送请求的URL
     * @param map
     *            请求Map参数，请求参数应该是 {"name1":"value1","name2":"value2"}的形式。
     * @param charset
     *             发送和接收的格式
     * @return URL 所代表远程资源的响应结果
     */
    public static String sendGet(String url, Map<String,Object> map, String charset){
        StringBuffer sb=new StringBuffer();
        //构建请求参数
        if(map!=null&&map.size()>0){
            Iterator it=map.entrySet().iterator(); //定义迭代器
            while(it.hasNext()){
                Map.Entry  er= (Map.Entry) it.next();
                sb.append(er.getKey());
                sb.append("=");
                sb.append(er.getValue());
                sb.append("&");
            }
        }
        return  sendGet(url,sb.toString(), charset);
    }


    /**
     * 向指定URL发送POST方法的请求
     *
     * @param url
     *            发送请求的URL
     * @param map
     *            请求Map参数，请求参数应该是 {"name1":"value1","name2":"value2"}的形式。
     * @param charset
     *             发送和接收的格式
     * @return URL 所代表远程资源的响应结果
     */
    public static String sendPost(String url, Map<String,Object> map,String charset){
        StringBuffer sb=new StringBuffer();
        //构建请求参数
        if(map!=null&&map.size()>0){
            for (Map.Entry<String, Object> e : map.entrySet()) {
                //传递数组
                if (e.getValue() instanceof List) {
                    List list = (List)e.getValue();
                    if (list != null && list.size() > 0) {
                        for (Object v : list) {
                            sb.append(e.getKey());
                            sb.append("=");
                            sb.append(v.toString());
                            sb.append("&");
                        }
                    }
                } else {
                    sb.append(e.getKey());
                    sb.append("=");
                    sb.append(e.getValue());
                    sb.append("&");
                }

            }
        }
        return  sendPost(url,sb.toString(),charset);
    }


    /**
     * 向指定URL发送GET方法的请求
     *
     * @param url
     *            发送请求的URL
     * @param param
     *            请求参数，请求参数应该是 {@code name1=value1&name2=value2} 的形式。
     * @param charset
     *             发送和接收的格式
     * @return URL 所代表远程资源的响应结果
     */
    public static String sendGet(String url, String param,String charset) {
        String result = "";
        String line;
        StringBuffer sb=new StringBuffer();
        BufferedReader in = null;
        try {
            String urlNameString = url + "?" + param;
            URL realUrl = new URL(urlNameString);
            // 打开和URL之间的连接
            HttpURLConnection conn = (HttpURLConnection)realUrl.openConnection();
            // 设置通用的请求属性 设置请求格式
            conn.setRequestProperty("contentType", charset);
            conn.setRequestProperty("content-type", "application/x-www-form-urlencoded");
            //设置超时时间
            conn.setConnectTimeout(TIMEOUT_IN_MILLIONS);
            conn.setReadTimeout(TIMEOUT_IN_MILLIONS);
            // 建立实际的连接
            conn.connect();
            // 定义 BufferedReader输入流来读取URL的响应,设置接收格式
            in = new BufferedReader(new InputStreamReader(
                conn.getInputStream(),charset));
            while ((line = in.readLine()) != null) {
                sb.append(line);
            }
            result=sb.toString();
        } catch (Exception e) {
            log.error("发送GET请求出现异常！", e);
            e.printStackTrace();
        }
        // 使用finally块来关闭输入流
        finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception e2) {
                e2.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 向指定 URL 发送POST方法的请求
     *
     * @param url
     *            发送请求的 URL
     * @param param
     *            请求参数，请求参数应该是 {@code name1=value1&name2=value2} 的形式。
     * @param charset
     *             发送和接收的格式
     * @return 所代表远程资源的响应结果
     */
    public static String sendPost(String url, String param,String charset) {
        PrintWriter out = null;
        BufferedReader in = null;
        String result = "";
        String line;
        StringBuffer sb=new StringBuffer();
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            //URLConnection conn = realUrl.openConnection();
            HttpURLConnection conn = (HttpURLConnection)realUrl.openConnection();
            // 设置通用的请求属性 设置请求格式
            conn.setRequestProperty("contentType", charset);
            conn.setRequestProperty("content-type", "application/x-www-form-urlencoded");
            //设置超时时间
            conn.setConnectTimeout(TIMEOUT_IN_MILLIONS);
            conn.setReadTimeout(TIMEOUT_IN_MILLIONS);
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(conn.getOutputStream());
            // 发送请求参数
            out.print(param);
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应    设置接收格式
            in = new BufferedReader(
                new InputStreamReader(conn.getInputStream(),charset));
            while ((line = in.readLine()) != null) {
                sb.append(line);
            }
            result=sb.toString();
        } catch (Exception e) {
            log.error("发送 POST请求出现异常!", e);
            e.printStackTrace();
        }
        //使用finally块来关闭输出流、输入流
        finally{
            try{
                if(out!=null){
                    out.close();
                }
                if(in!=null){
                    in.close();
                }
            }
            catch(IOException ex){
                ex.printStackTrace();
            }
        }
        return result;
    }


//    /**
//     * post 发送json请求
//     * from ditools
//     **/
//    public static String sendPostJson(String url, String json) {
//        String res = "";
//        URL _url = null;
//        HttpURLConnection connection = null;
//        OutputStreamWriter out = null;
//        InputStream ins = null;
//        try {
//            _url = new URL(url);// 创建连接
//            connection = (HttpURLConnection) _url.openConnection();
//
//            connection.setConnectTimeout(TIMEOUT_IN_MILLIONS);
//            connection.setReadTimeout(TIMEOUT_IN_MILLIONS);
//            connection.setDoOutput(true);
//            connection.setDoInput(true);
//            connection.setUseCaches(false);
//            connection.setInstanceFollowRedirects(true);
//            connection.setRequestMethod("POST"); // 设置请求方式
//            //connection.setRequestProperty("Accept", "application/json"); // 设置接收数据的格式
//            connection.setRequestProperty("Content-Type", "application/json"); // 设置发送数据的格式
//            //    connection.setRequestProperty(diconfigbean.getApiKey(), diconfigbean.getApiKeyValue());
//            connection.connect();
//            out = new OutputStreamWriter(connection.getOutputStream(), "UTF-8"); // utf-8编码
//            out.append(json);
//
//            out.flush();
//            out.close();
//            // 读取响应
//            int length = (int) connection.getContentLength();// 获取长度
//            ins = connection.getInputStream();
//            if (length != -1) {
//                byte[] data = new byte[length];
//                byte[] temp = new byte[1024];
//                int readLen = 0;
//                int destPos = 0;
//                while ((readLen = ins.read(temp)) > 0) {
//                    System.arraycopy(temp, 0, data, destPos, readLen);
//                    destPos += readLen;
//                }
//                // utf-8编码
//                res = new String(data, "UTF-8");
//            } else {
//                ins.close();
//            }
//
//        } catch (Exception ex) {
//            log.error("发送 POST请求JSON出现异常!", ex);
//            ex.printStackTrace();
//        } finally {
//            out = null;
//            ins = null;
//        }
//        return res;
//    }

    public static String sendPostJson(String url, String json) {
        DataOutputStream out = null;
        BufferedReader in = null;
        String result = "";
        try {
            URL realUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) realUrl.openConnection();
            conn.setConnectTimeout(TIMEOUT_IN_MILLIONS);
            conn.setReadTimeout(TIMEOUT_IN_MILLIONS);
            conn.setDoOutput(true);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("User-Agent", "Fiddler");
            conn.setRequestProperty("Content-Type", "application/json");
            conn.setRequestProperty("Charset", "utf-8");
            //conn.setRequestProperty("Authorization", Constant.TOKEN_ID);
            conn.setChunkedStreamingMode(0);//禁止超时后自动重发请求
            // System.setProperty("http.keepAlive", "false");

            if (!json.trim().equals("")) {
                out = new DataOutputStream(conn.getOutputStream());
                byte[] content = json.getBytes("utf-8");//
                out.write(content, 0, content.length);
            }
            in = new BufferedReader(
                new InputStreamReader(conn.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                //result += line;
                result +=  new String(line.getBytes(), "utf-8");
            }
            return result;
        } catch (Exception e) {
            log.error("发送 POST请求JSON出现异常!", e);
            e.printStackTrace();
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
        return result;

    }

    public static String sendPostJson(String url, HashMap<String, Object> headers, String json) {
        log.info("sendPostJson url: {}, headers: {}, json: {}", url, headers, json);
        DataOutputStream out = null;
        BufferedReader in = null;
        String result = "";
        try {
            URL realUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) realUrl.openConnection();
            conn.setConnectTimeout(TIMEOUT_IN_MILLIONS);
            conn.setReadTimeout(TIMEOUT_IN_MILLIONS);
            conn.setDoOutput(true);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("User-Agent", "Fiddler");
            conn.setRequestProperty("Content-Type", "application/json");
            conn.setRequestProperty("Charset", "utf-8");
            //conn.setRequestProperty("Authorization", Constant.TOKEN_ID);
            conn.setChunkedStreamingMode(0);//禁止超时后自动重发请求
            // System.setProperty("http.keepAlive", "false");
            for (Map.Entry item : headers.entrySet()) {
                conn.setRequestProperty(item.getKey().toString(),item.getValue().toString());//设置header
            }

            if (!json.trim().equals("")) {
                out = new DataOutputStream(conn.getOutputStream());
                byte[] content = json.getBytes("utf-8");//
                out.write(content, 0, content.length);
            }
            in = new BufferedReader(
                new InputStreamReader(conn.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                //result += line;
                result +=  new String(line.getBytes(), "utf-8");
            }
            log.info("sendPostJson result: {}", result);
            return result;
        } catch (Exception e) {
            log.error("发送 POST请求JSON出现异常!", e);
            e.printStackTrace();
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
        return result;

    }

    /**
     * 主函数
     */
    public static void main(String[] args) {
//        String getUrl="http://int.dpool.sina.com.cn/iplookup/iplookup.php";
//        String postUrl="http://gc.ditu.aliyun.com/geocoding";
//        String param="format=json&ip=*************";
//        String param1="a=苏州市";
//        Map<String,Object> map=new HashMap<String,Object>();
//        map.put("format", "json");
//        map.put("ip", "*************");
//        Map<String,Object> map1=new HashMap<String,Object>();
//        map1.put("a", "苏州市");
//        System.out.println("Get请求1:"+HttpUtil.sendGet(getUrl, param,"utf-8"));
//        System.out.println("Get请求2:"+HttpUtil.sendGet(getUrl, map,"utf-8"));
//        System.out.println("Post请求1:"+HttpUtil.sendPost(postUrl, param1,"utf-8"));
//        System.out.println("Post请求2:"+HttpUtil.sendPost(postUrl, map,"utf-8"));

       // String signSourceData = "client_id=" + "4d7U7dq2gC50MU" + "&key=" + key; // 通过 md5 算法获得签名串， webservice 中的第 2 个参数

        //测试金蝶接口
        String signSourceData =  "4d7U7dq2gC50MU" + "N8O28nJ4iRqW8xMsanxFLCDkCl0nw8"+"1463468225650" ; // 通过 md5 算法获得签名串， webservice 中的第 2 个参数
        log.info("retun signSourceData=" + signSourceData);
        signSourceData =  org.apache.commons.codec.digest.DigestUtils.md5Hex(signSourceData);

        System.out.println(signSourceData);

        String result = HttpUtil.sendPostJson("https://api-dev.piaozone.com/base/oauth/token", "{\"client_id\":\"c0c6hjlgnKCic\",\"sign\":\"8b9c78fccedbb72eff1106c57505b8f6\",\"timestamp\":1463468225650}");
        System.out.println(result);
    }

}
