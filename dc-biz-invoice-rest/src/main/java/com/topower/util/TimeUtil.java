package com.topower.util;

import static java.time.format.DateTimeFormatter.ISO_LOCAL_DATE;
import static java.time.temporal.ChronoField.HOUR_OF_DAY;
import static java.time.temporal.ChronoField.MINUTE_OF_HOUR;
import static java.time.temporal.ChronoField.SECOND_OF_MINUTE;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.lang.Nullable;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @see <a href="https://my.oschina.net/benhaile/blog/193956">深入解析日期和时间-JSR310</a>
  * @since 17/1/3
 */
public abstract class TimeUtil {
    public static final ZoneId SYSTEM_DEFAULT = ZoneId.systemDefault();

    @Documented
    @Retention(RetentionPolicy.RUNTIME)
    @Target({ElementType.METHOD, ElementType.FIELD, ElementType.PARAMETER, ElementType.ANNOTATION_TYPE})
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    public @interface Ymd {
    }

    @Documented
    @Retention(RetentionPolicy.RUNTIME)
    @Target({ElementType.METHOD, ElementType.FIELD, ElementType.PARAMETER, ElementType.ANNOTATION_TYPE})
    @DateTimeFormat(pattern = "HH:mm:ss")
    public @interface Hms {
    }

    @Documented
    @Retention(RetentionPolicy.RUNTIME)
    @Target({ElementType.METHOD, ElementType.FIELD, ElementType.PARAMETER, ElementType.ANNOTATION_TYPE})
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public @interface YmdHms {
    }

    private static final DateTimeFormatter UI_YMD = ISO_LOCAL_DATE;
    private static final DateTimeFormatter UI_HMS =
        new DateTimeFormatterBuilder()
            .appendValue(HOUR_OF_DAY, 2)
            .appendLiteral(':')
            .appendValue(MINUTE_OF_HOUR, 2)
            .appendLiteral(':')
            .appendValue(SECOND_OF_MINUTE, 2)
            .toFormatter();
    private static final DateTimeFormatter UI_YMD_HMS =
        new DateTimeFormatterBuilder()
            .append(UI_YMD)
            .appendLiteral(' ')
            .append(UI_HMS)
            .toFormatter();

    /**
     * Format time to text.
     *
     * @param time time
     * @return hms
     */
    public static String timeToHms(@Nullable LocalTime time) {
        return time == null ? "" : time.format(UI_HMS);
    }

    /**
     * Parse text to time.
     *
     * @param hms hms
     * @return null if parsing error
     */
    public static LocalTime timeFromHms(@Nullable String hms) {
        return StringUtils.isEmpty(hms) ? null : LocalTime.parse(hms, UI_HMS);
    }

    /**
     * Format date to text.
     *
     * @param date date
     * @return ymd
     */
    public static String dateToYmd(@Nullable LocalDate date) {
        return date == null ? "" : date.format(UI_YMD);
    }

    /**
     * Format date to text.
     *
     * @param date dateTime
     * @return ymd
     */
    public static String dateTimeToYmd(@Nullable LocalDateTime date) {
        return date == null ? "" : date.format(UI_YMD);
    }

    /**
     * Parse text to date.
     *
     * @param ymd ymd
     * @return null if parsing error
     */
    public static LocalDate dateFromYmd(@Nullable String ymd) {
        return StringUtils.isEmpty(ymd) ? null : LocalDate.parse(ymd, UI_YMD);
    }

    /**
     * Parse text to date.
     *
     * @param ymd ymd
     * @return null if parsing error
     */
    public static LocalDateTime dateTimeFromYmd(@Nullable String ymd) {
        LocalDate localDate = dateFromYmd(ymd);
        return localDate == null ? null : LocalDateTime.of(localDate, LocalTime.MIN);
    }

    /**
     * Parse text to date. 解析成该天最大的时间
     *
     * @param ymd ymd
     * @return null if parsing error
     */
    public static LocalDateTime dateTimeFromYmdMAX(@Nullable String ymd) {
        LocalDate localDate = dateFromYmd(ymd);
        return localDate == null ? null : LocalDateTime.of(localDate, LocalTime.MAX);
    }

    public static LocalDateTime dateTimeFromYmdHms(@Nullable String ymdHms) {
        LocalDateTime localDateTime = StringUtils.isEmpty(ymdHms) ? null : LocalDateTime.parse(ymdHms, UI_YMD_HMS);
        return localDateTime;
    }



    /**
     * Format date-time to text.
     *
     * @param dateTime dateTime
     * @return hms
     */
    public static String dateTimeToHms(@Nullable LocalDateTime dateTime) {
        return dateTime == null ? "" : dateTime.format(UI_HMS);
    }

    /**
     * Format date-time to text.
     *
     * @param dateTime dateTime
     * @return ymd
     */
    public static String dateTimeToYmdHms(@Nullable LocalDateTime dateTime) {
        return dateTime == null ? "" : dateTime.format(UI_YMD_HMS);
    }

    /**
     * @param localDate local-date
     * @return sql-date
     */
    public static java.sql.Date toSqlDate(@Nullable LocalDate localDate) {
        return localDate == null ? null : java.sql.Date.valueOf(localDate);
    }

    /**
     * @param localDateTime local-date-time
     * @return sql-timestamp
     */
    public static java.sql.Timestamp toSqlTimestamp(@Nullable LocalDateTime localDateTime) {
        return localDateTime == null ? null : java.sql.Timestamp.valueOf(localDateTime);
    }

    /**
     * @param dateTime date-time
     * @return local-date-time
     */
    public static LocalDateTime toLocalDateTime(@Nullable java.util.Date dateTime) {
        return dateTime == null ? null : LocalDateTime.ofInstant(dateTime.toInstant(), ZoneId.systemDefault());
    }

    /**
     * @return the number of seconds from the epoch of 1970-01-01T00:00:00Z.
     */
    public static long now() {
        return LocalDateTime.now(ZoneOffset.UTC).toEpochSecond(ZoneOffset.UTC);
    }

    public static ZonedDateTime toZonedDateTime(Date date) {
        if (null == date) return null;
        return ZonedDateTime.ofInstant(date.toInstant(), SYSTEM_DEFAULT);
    }

    public static String toYmdhms(Date date) {
        if (date == null) return null;
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        return format.format(date);
    }

}
