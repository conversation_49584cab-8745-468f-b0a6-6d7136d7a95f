package com.topower.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "设置场站可开票标记实体")
public class InvoicedSiteValidYNDTO implements Serializable {

    @Schema(description = "场站ID列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> ids;
//    private List<Long> ids;

    @Schema(description = "是否可开票 true: 可开票; false: 不可开票")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean invoicedValid;
}
