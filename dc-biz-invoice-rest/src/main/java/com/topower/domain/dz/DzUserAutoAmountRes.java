//package com.topower.domain.dz;
//
//import com.topower.domain.dto.InvoicedUserAutoAmountDTO;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * <AUTHOR>
// *  //请求东正的用户相关接口返回实体
// * @since 2019/4/24
// **/
//public class DzUserAutoAmountRes {
//    /**
//     * data : {"rows":[{"index":0,"size":0,"id":59108,"commId":34474,"username":"bnVsbA==","pwd":null,"name":null,"sex":null,"sourceId":null,"lastLogin":null,"regTime":null,"stats":1,"phone":"15689736905","email":null,"isBond":null,"image":null,"wechant":null,"microblog":null,"qq":null,"province":310000,"city":310100,"district":310117,"street":null,"address":null,"postCode":null,"nationalCode":null,"allowOperator":null,"createTime":"2018-11-14T09:27:56.000+0000","isFirstLogin":null,"lng":null,"lat":null,"vin":null,"certified":null,"brithday":null,"movePhone":null,"loginIp":null,"isMulti":null,"status":null,"integral":null,"userLevel":null,"refundsFailReasons":null,"refundsFailReasonsStatus":null,"sourceType":null,"aliUserId":null,"aliAccessToken":null,"wechatOpenid":null,"dcInvoiceId":null,"userId":null,"invoicedAmount":null,"monthDay":null,"auto":null}],"total":157}
//     * message : null
//     * code : 0
//     * errmsg : null
//     * errcode : 0
//     */
//
//    private List<DataBean> data;
//    private String error;
//    private int status;
//    private long total;
//
//    public List<DataBean> getData() {
//        return data;
//    }
//
//    public void setData(List<DataBean> data) {
//        this.data = data;
//    }
//
//    public String getError() {
//        return error;
//    }
//
//    public void setError(String error) {
//        this.error = error;
//    }
//
//    public int getStatus() {
//        return status;
//    }
//
//    public void setStatus(int status) {
//        this.status = status;
//    }
//
//    public long getTotal() {
//        return total;
//    }
//
//    public void setTotal(long total) {
//        this.total = total;
//    }
//
//    public static class DataBean {
//        /**
//         * rows : [{"index":0,"size":0,"id":59108,"commId":34474,"username":"bnVsbA==","pwd":null,"name":null,"sex":null,"sourceId":null,"lastLogin":null,"regTime":null,"stats":1,"phone":"15689736905","email":null,"isBond":null,"image":null,"wechant":null,"microblog":null,"qq":null,"province":310000,"city":310100,"district":310117,"street":null,"address":null,"postCode":null,"nationalCode":null,"allowOperator":null,"createTime":"2018-11-14T09:27:56.000+0000","isFirstLogin":null,"lng":null,"lat":null,"vin":null,"certified":null,"brithday":null,"movePhone":null,"loginIp":null,"isMulti":null,"status":null,"integral":null,"userLevel":null,"refundsFailReasons":null,"refundsFailReasonsStatus":null,"sourceType":null,"aliUserId":null,"aliAccessToken":null,"wechatOpenid":null,"dcInvoiceId":null,"userId":null,"invoicedAmount":null,"monthDay":null,"auto":null}]
//         * total : 157
//         */
//
//        /**
//         * index : 0
//         * size : 0
//         * id : 59108
//         * commId : 34474
//         * username : bnVsbA==
//         * pwd : null
//         * name : null
//         * sex : null
//         * sourceId : null
//         * lastLogin : null
//         * regTime : null
//         * stats : 1
//         * phone : 15689736905
//         * email : null
//         * isBond : null
//         * image : null
//         * wechant : null
//         * microblog : null
//         * qq : null
//         * province : 310000
//         * city : 310100
//         * district : 310117
//         * street : null
//         * address : null
//         * postCode : null
//         * nationalCode : null
//         * allowOperator : null
//         * createTime : 2018-11-14T09:27:56.000+0000
//         * isFirstLogin : null
//         * lng : null
//         * lat : null
//         * vin : null
//         * certified : null
//         * brithday : null
//         * movePhone : null
//         * loginIp : null
//         * isMulti : null
//         * status : null
//         * integral : null
//         * userLevel : null
//         * refundsFailReasons : null
//         * refundsFailReasonsStatus : null
//         * sourceType : null
//         * aliUserId : null
//         * aliAccessToken : null
//         * wechatOpenid : null
//         * dcInvoiceId : null
//         * invoicedAmount : null
//         * userId : null
//         * monthDay : null
//         * auto : null
//         */
//
//        private int index;
//        private int size;
//        private long id;
//        private long commId;
//        private String username;
//        private String pwd;
//        private String name;
//        private String sex;
//        private long sourceId;
//        private String lastLogin;
//        private String regTime;
//        private String stats;
//        private String phone;
//        private String email;
//        private String isBond;
//        private String image;
//        private String wechant;
//        private String microblog;
//        private String qq;
//        private String province;
//        private String city;
//        private String district;
//        private String street;
//        private String address;
//        private String postCode;
//        private String nationalCode;
//        private String allowOperator;
//        private String createTime;
//        private String isFirstLogin;
//        private String lng;
//        private String lat;
//        private String vin;
//        private String certified;
//        private String brithday;
//        private String movePhone;
//        private String loginIp;
//        private String isMulti;
//        private Boolean status;
//        private String integral;
//        private String userLevel;
//        private String refundsFailReasons;
//        private String refundsFailReasonsStatus;
//        private String sourceType;
//        private String aliUserId;
//        private String aliAccessToken;
//        private String wechatOpenid;
//        private String dcInvoiceId;
//        private int invoicedAmount;
//        private String userId;
//        private int monthDay;
//        private Boolean auto;
//
//        public int getIndex() {
//            return index;
//        }
//
//        public void setIndex(int index) {
//            this.index = index;
//        }
//
//        public int getSize() {
//            return size;
//        }
//
//        public void setSize(int size) {
//            this.size = size;
//        }
//
//        public long getId() {
//            return id;
//        }
//
//        public void setId(long id) {
//            this.id = id;
//        }
//
//        public long getCommId() {
//            return commId;
//        }
//
//        public void setCommId(long commId) {
//            this.commId = commId;
//        }
//
//        public String getUsername() {
//            return username;
//        }
//
//        public void setUsername(String username) {
//            this.username = username;
//        }
//
//        public String getPwd() {
//            return pwd;
//        }
//
//        public void setPwd(String pwd) {
//            this.pwd = pwd;
//        }
//
//        public String getName() {
//            return name;
//        }
//
//        public void setName(String name) {
//            this.name = name;
//        }
//
//        public String getSex() {
//            return sex;
//        }
//
//        public void setSex(String sex) {
//            this.sex = sex;
//        }
//
//        public long getSourceId() {
//            return sourceId;
//        }
//
//        public void setSourceId(long sourceId) {
//            this.sourceId = sourceId;
//        }
//
//        public String getLastLogin() {
//            return lastLogin;
//        }
//
//        public void setLastLogin(String lastLogin) {
//            this.lastLogin = lastLogin;
//        }
//
//        public String getRegTime() {
//            return regTime;
//        }
//
//        public void setRegTime(String regTime) {
//            this.regTime = regTime;
//        }
//
//        public String getStats() {
//            return stats;
//        }
//
//        public void setStats(String stats) {
//            this.stats = stats;
//        }
//
//        public String getPhone() {
//            return phone;
//        }
//
//        public void setPhone(String phone) {
//            this.phone = phone;
//        }
//
//        public String getEmail() {
//            return email;
//        }
//
//        public void setEmail(String email) {
//            this.email = email;
//        }
//
//        public String getIsBond() {
//            return isBond;
//        }
//
//        public void setIsBond(String isBond) {
//            this.isBond = isBond;
//        }
//
//        public String getImage() {
//            return image;
//        }
//
//        public void setImage(String image) {
//            this.image = image;
//        }
//
//        public String getWechant() {
//            return wechant;
//        }
//
//        public void setWechant(String wechant) {
//            this.wechant = wechant;
//        }
//
//        public String getMicroblog() {
//            return microblog;
//        }
//
//        public void setMicroblog(String microblog) {
//            this.microblog = microblog;
//        }
//
//        public String getQq() {
//            return qq;
//        }
//
//        public void setQq(String qq) {
//            this.qq = qq;
//        }
//
//        public String getProvince() {
//            return province;
//        }
//
//        public void setProvince(String province) {
//            this.province = province;
//        }
//
//        public String getCity() {
//            return city;
//        }
//
//        public void setCity(String city) {
//            this.city = city;
//        }
//
//        public String getDistrict() {
//            return district;
//        }
//
//        public void setDistrict(String district) {
//            this.district = district;
//        }
//
//        public String getStreet() {
//            return street;
//        }
//
//        public void setStreet(String street) {
//            this.street = street;
//        }
//
//        public String getAddress() {
//            return address;
//        }
//
//        public void setAddress(String address) {
//            this.address = address;
//        }
//
//        public String getPostCode() {
//            return postCode;
//        }
//
//        public void setPostCode(String postCode) {
//            this.postCode = postCode;
//        }
//
//        public String getNationalCode() {
//            return nationalCode;
//        }
//
//        public void setNationalCode(String nationalCode) {
//            this.nationalCode = nationalCode;
//        }
//
//        public String getAllowOperator() {
//            return allowOperator;
//        }
//
//        public void setAllowOperator(String allowOperator) {
//            this.allowOperator = allowOperator;
//        }
//
//        public String getCreateTime() {
//            return createTime;
//        }
//
//        public void setCreateTime(String createTime) {
//            this.createTime = createTime;
//        }
//
//        public String getIsFirstLogin() {
//            return isFirstLogin;
//        }
//
//        public void setIsFirstLogin(String isFirstLogin) {
//            this.isFirstLogin = isFirstLogin;
//        }
//
//        public String getLng() {
//            return lng;
//        }
//
//        public void setLng(String lng) {
//            this.lng = lng;
//        }
//
//        public String getLat() {
//            return lat;
//        }
//
//        public void setLat(String lat) {
//            this.lat = lat;
//        }
//
//        public String getVin() {
//            return vin;
//        }
//
//        public void setVin(String vin) {
//            this.vin = vin;
//        }
//
//        public String getCertified() {
//            return certified;
//        }
//
//        public void setCertified(String certified) {
//            this.certified = certified;
//        }
//
//        public String getBrithday() {
//            return brithday;
//        }
//
//        public void setBrithday(String brithday) {
//            this.brithday = brithday;
//        }
//
//        public String getMovePhone() {
//            return movePhone;
//        }
//
//        public void setMovePhone(String movePhone) {
//            this.movePhone = movePhone;
//        }
//
//        public String getLoginIp() {
//            return loginIp;
//        }
//
//        public void setLoginIp(String loginIp) {
//            this.loginIp = loginIp;
//        }
//
//        public String getIsMulti() {
//            return isMulti;
//        }
//
//        public void setIsMulti(String isMulti) {
//            this.isMulti = isMulti;
//        }
//
//        public Boolean getStatus() {
//            return status;
//        }
//
//        public void setStatus(Boolean status) {
//            this.status = status;
//        }
//
//        public String getIntegral() {
//            return integral;
//        }
//
//        public void setIntegral(String integral) {
//            this.integral = integral;
//        }
//
//        public String getUserLevel() {
//            return userLevel;
//        }
//
//        public void setUserLevel(String userLevel) {
//            this.userLevel = userLevel;
//        }
//
//        public String getRefundsFailReasons() {
//            return refundsFailReasons;
//        }
//
//        public void setRefundsFailReasons(String refundsFailReasons) {
//            this.refundsFailReasons = refundsFailReasons;
//        }
//
//        public String getRefundsFailReasonsStatus() {
//            return refundsFailReasonsStatus;
//        }
//
//        public void setRefundsFailReasonsStatus(String refundsFailReasonsStatus) {
//            this.refundsFailReasonsStatus = refundsFailReasonsStatus;
//        }
//
//        public String getSourceType() {
//            return sourceType;
//        }
//
//        public void setSourceType(String sourceType) {
//            this.sourceType = sourceType;
//        }
//
//        public String getAliUserId() {
//            return aliUserId;
//        }
//
//        public void setAliUserId(String aliUserId) {
//            this.aliUserId = aliUserId;
//        }
//
//        public String getAliAccessToken() {
//            return aliAccessToken;
//        }
//
//        public void setAliAccessToken(String aliAccessToken) {
//            this.aliAccessToken = aliAccessToken;
//        }
//
//        public String getWechatOpenid() {
//            return wechatOpenid;
//        }
//
//        public void setWechatOpenid(String wechatOpenid) {
//            this.wechatOpenid = wechatOpenid;
//        }
//
//        public String getDcInvoiceId() {
//            return dcInvoiceId;
//        }
//
//        public void setDcInvoiceId(String dcInvoiceId) {
//            this.dcInvoiceId = dcInvoiceId;
//        }
//
//        public int getInvoicedAmount() {
//            return invoicedAmount;
//        }
//
//        public void setInvoicedAmount(int invoicedAmount) {
//            this.invoicedAmount = invoicedAmount;
//        }
//
//        public String getUserId() {
//            return userId;
//        }
//
//        public void setUserId(String userId) {
//            this.userId = userId;
//        }
//
//        public int getMonthDay() {
//            return monthDay;
//        }
//
//        public void setMonthDay(int monthDay) {
//            this.monthDay = monthDay;
//        }
//
//        public Boolean getAuto() {
//            return auto;
//        }
//
//        public void setAuto(Boolean auto) {
//            this.auto = auto;
//        }
//    }
//
//    public static List<InvoicedUserAutoAmountDTO> transInvoicedUserAutoAmountDTOs(DzUserAutoAmountRes res) {
//        List<InvoicedUserAutoAmountDTO> dtos = null;
//        if (res != null && res.getStatus() == 0) {
//            dtos = new ArrayList<>();
//            InvoicedUserAutoAmountDTO dto = null;
//            List<DataBean> rows = res.getData();
//            for (DataBean row : rows) {
//
//                dto = new InvoicedUserAutoAmountDTO();
//                dto.setUserId(row.getId());
//                dto.setCommId(row.getCommId());
//                dto.setInvoicedAmount(row.getInvoicedAmount());
//                dto.setMonthDay(row.getMonthDay());
//                dto.setAuto(row.getAuto() != null ? row.getAuto() : false);
//                dtos.add(dto);
//            }
//        }
//
//        return dtos;
//    }
//
//    public static int getTotal(DzUserAutoAmountRes res) {
//        int total = 0;
//        if (res != null && res.getStatus() == 0) {
//            total = (int)res.getTotal();
//        }
//
//        return total;
//    }
//
//}
