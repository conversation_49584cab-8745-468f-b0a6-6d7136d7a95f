package com.topower.domain.hangtian;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "全电发票交付请求")
@Data
@Accessors(chain = true)
public class HangTianDeliveryReq {

    @Schema(description = "纳税人识别号(15/18/20)", requiredMode = RequiredMode.REQUIRED)
    @JsonProperty(value = "nsrsbh")
    private String nsrsbh;

    @Schema(description = "发票号码", requiredMode = RequiredMode.REQUIRED)
    @JsonProperty(value = "fphm")
    private String fphm;

    @Schema(description = "开票日期", example = "2022-08-16 17:38:53")
    @JsonProperty(value = "kprq")
    private String kprq;
}
