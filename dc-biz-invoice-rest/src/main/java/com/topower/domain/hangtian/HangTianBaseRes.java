package com.topower.domain.hangtian;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "航天信息开票请求响应")
@Data
@Accessors(chain = true)
public class HangTianBaseRes {

    @Schema(description = "返回结果", requiredMode = RequiredMode.REQUIRED)
    @JsonProperty(value = "return_info")
    private HangTianReturnInfo returnInfo;

    @Schema(description = "返回数据: 业务接口响应报文的Base64编码")
    private String data;

    public boolean applySucceed() {
        return null != returnInfo && "0000".equals(returnInfo.getReturnCode());
    }
}
