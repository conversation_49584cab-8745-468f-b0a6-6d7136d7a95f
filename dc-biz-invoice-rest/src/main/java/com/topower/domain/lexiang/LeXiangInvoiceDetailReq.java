package com.topower.domain.lexiang;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "乐享开票详情请求参数")
@Data
@Accessors(chain = true)
public class LeXiangInvoiceDetailReq {

    @Schema(description = "所属企业uuid")
    @JsonProperty("ssqyuuid")
    private String ssqyuuid;

    @Schema(description = "发票 uuid")
    @JsonProperty("fpuuid")
    private String fpuuid;

    @Schema(description = "第三方订单号")
    @JsonProperty("dsfddh")
    private String dsfddh;

}
