package com.topower.domain.lexiang;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "乐享企业用户信息")
@Data
@Accessors(chain = true)
public class LeXiangCorpUser {

    @Schema(description = "创建时间")
    @JsonProperty("cjsj")
    private String createTime;

    @Schema(description = "修改时间")
    @JsonProperty("xgsj")
    private String modifyTime;

    @Schema(description = "企业ID")
    @JsonProperty("uuid")
    private String uuid;

    @Schema(description = "父级ID")
    @JsonProperty("fjuuid")
    private String parentUuid;

    @Schema(description = "父级ID列表")
    @JsonProperty("fjuuids")
    private String parentUuidList;

    @Schema(description = "暂无描述")
    @JsonProperty("ssqdid")
    private String occupancyId;

    @Schema(description = "企业名称")
    @JsonProperty("qymc")
    private String corpName;

    @Schema(description = "纳税人识别号")
    @JsonProperty("nsrsbh")
    private String taxpayerId;

    @Schema(description = "所在地区")
    @JsonProperty("szdq")
    private String region;

    @Schema(description = "所在地区编码")
    @JsonProperty("szdqbm")
    private String regionCode;

    @Schema(description = "邮政编码")
    @JsonProperty("yzbm")
    private String zipCode;

    @Schema(description = "负责人")
    @JsonProperty("fzr")
    private String master;

    @Schema(description = "电话")
    @JsonProperty("dh")
    private String phone;

    @Schema(description = "传真")
    @JsonProperty("cz")
    private String fax;

    @Schema(description = "邮箱")
    @JsonProperty("yx")
    private String email;

    @Schema(description = "联系人")
    @JsonProperty("lxr")
    private String contact;

    @Schema(description = "联系地址")
    @JsonProperty("lxdz")
    private String contactAddress;

    @Schema(description = "联系电话")
    @JsonProperty("lxdh")
    private String contactPhone;

    @Schema(description = "法人姓名")
    @JsonProperty("frxm")
    private String legalName;

    @Schema(description = "归属地址")
    @JsonProperty("gsdz")
    private String ascriptionAddress;

    @Schema(description = "销售人")
    @JsonProperty("xsr")
    private String seller;

    @Schema(description = "开户行")
    @JsonProperty("khh")
    private String bank;

    @Schema(description = "开户行账号")
    @JsonProperty("khhzh")
    private String bankAcc;

    @Schema(description = "收款人")
    @JsonProperty("skr")
    private String payee;

    @Schema(description = "复核人")
    @JsonProperty("fhr")
    private String reviewer;

    @Schema(description = "开票人")
    @JsonProperty("kpr")
    private String biller;

    @Schema(description = "适用会计准则")
    @JsonProperty("sykjzz")
    private String accountingStandards;

    @Schema(description = "纳税性质")
    @JsonProperty("nsxz")
    private String taxation;

    @Schema(description = "核定税率")
    @JsonProperty("hdsl")
    private String taxRate;

    @Schema(description = "进出口企业标志")
    @JsonProperty("jckqybz")
    private String corpFlag;

    @Schema(description = "海关代码")
    @JsonProperty("hgdm")
    private String customsCode;

    @Schema(description = "进料加工业务标志")
    @JsonProperty("jljgywbz")
    private String jljgywbz;

    @Schema(description = "企业类型")
    @JsonProperty("qylx")
    private String corpType;

    @Schema(description = "号段启用标志")
    @JsonProperty("hdqybz")
    private String hdqybz;

    @Schema(description = "项目启用标志")
    @JsonProperty("xmqybz")
    private String xmqybz;

    @Schema(description = "自动开票启用标志")
    @JsonProperty("zdkpqybz")
    private String enableAutoBill;

    @Schema(description = "拆分规则")
    @JsonProperty("cfgz")
    private String cfgz;

    @Schema(description = "公司组织机构数组")
    @JsonProperty("zzjgList")
    private List<Object> zzjgList;

    @Schema(description = "渠道名称")
    @JsonProperty("qdmc")
    private String qdmc;

    @Schema(description = "渠道电话")
    @JsonProperty("qddh")
    private String qddh;

    @Schema(description = "渠道服务器")
    @JsonProperty("qdfwq")
    private String qdfwq;
}
