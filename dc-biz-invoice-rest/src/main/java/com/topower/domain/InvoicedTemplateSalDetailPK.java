//package com.topower.domain;
//
//import jakarta.persistence.Column;
//import jakarta.persistence.Id;
//import java.io.Serializable;
//import java.util.Objects;
//
///**
// * <AUTHOR>
// *
// * @since 2019/2/18
// **/
//public class InvoicedTemplateSalDetailPK implements Serializable {
//    private String code;
//
//    @Column(name = "code")
//    @Id
//    public String getCode() {
//        return code;
//    }
//
//    public void setCode(String code) {
//        this.code = code;
//    }
//
//
//    @Override
//    public boolean equals(Object o) {
//        if (this == o) return true;
//        if (o == null || getClass() != o.getClass()) return false;
//        InvoicedTemplateSalDetailPK that = (InvoicedTemplateSalDetailPK) o;
//        return Objects.equals(code, that.code);
//    }
//
//    @Override
//    public int hashCode() {
//        return Objects.hash(code);
//    }
//}
