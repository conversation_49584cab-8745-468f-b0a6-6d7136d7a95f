package com.topower.domain.jinren;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "金壬开票申请请求发票明细")
@Data
@Accessors(chain = true)
public class JinRenInvoiceDetail {

    @Schema(description = "明细行序号", requiredMode = RequiredMode.REQUIRED)
    @JsonProperty(value = "Xh")
    private String Xh;

    @Schema(description = "商品或项目名称", requiredMode = RequiredMode.REQUIRED)
    @JsonProperty(value = "Spmc")
    private String Spmc;

    @Schema(description = "不含税单价")
    @JsonProperty(value = "Dj")
    private BigDecimal Dj;

    @Schema(description = "含税单价")
    @JsonProperty(value = "Hsdj")
    private BigDecimal Hsdj;

    @Schema(description = "商品数量")
    @JsonProperty(value = "Sl")
    private String Sl;

    @Schema(description = "商品计量单位", requiredMode = RequiredMode.REQUIRED)
    @JsonProperty(value = "Jldw")
    private String Jldw;

    @Schema(description = "商品规格型号", requiredMode = RequiredMode.REQUIRED)
    @JsonProperty(value = "Ggxh")
    private String Ggxh;

    @Schema(description = "金额", requiredMode = RequiredMode.REQUIRED)
    @JsonProperty(value = "Je")
    private BigDecimal Je;

    @Schema(description = "税额", requiredMode = RequiredMode.REQUIRED)
    @JsonProperty(value = "Se")
    private BigDecimal Se;

    @Schema(description = "价税合计", requiredMode = RequiredMode.REQUIRED)
    @JsonProperty(value = "Hsje")
    private BigDecimal Hsje;

    @Schema(description = "税收分类编码，示例 1090511030000000000", requiredMode = RequiredMode.REQUIRED)
    @JsonProperty(value = "spfldm")
    private String spfldm;

    @Schema(description = "税率，示例 0.13", requiredMode = RequiredMode.REQUIRED)
    @JsonProperty(value = "Slv")
    private BigDecimal Slv;

    @Schema(description = "零税率标识 空正常税率，1：免税，2：不征税，3：0税率")
    @JsonProperty(value = "Lslbz")
    private String Lslbz;
}
