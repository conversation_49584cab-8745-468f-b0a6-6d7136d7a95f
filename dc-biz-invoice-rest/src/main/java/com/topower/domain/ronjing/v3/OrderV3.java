package com.topower.domain.ronjing.v3;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.math.BigDecimal;

public class OrderV3 {
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orderDate;//订单日期,来源系统订单时间，"YYYYMM-DD HH24:MI:SS"格式
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal totalAmount;//{18,2}价税合计，小数点后2位小数，该栏目打印在发票上
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal amountWithoutTax;//{18,2}不含税金额
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal taxAmount;//{18,2}税额
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String taxWay;//征税方式，0-普通征税，1-减按征税，2-差额征税
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String taxMark;//计价方式（0、含税；1、不含税)

    public String getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(String orderDate) {
        this.orderDate = orderDate;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getAmountWithoutTax() {
        return amountWithoutTax;
    }

    public void setAmountWithoutTax(BigDecimal amountWithoutTax) {
        this.amountWithoutTax = amountWithoutTax;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public String getTaxWay() {
        return taxWay;
    }

    public void setTaxWay(String taxWay) {
        this.taxWay = taxWay;
    }

    public String getTaxMark() {
        return taxMark;
    }

    public void setTaxMark(String taxMark) {
        this.taxMark = taxMark;
    }
}
