//package com.topower.web.rest.vm;
//
///**
// * <AUTHOR>
// */
//public class ResultVM {
//    private Object data;
//    private String msg;
//    private Integer code;
//
//    public Object getData() {
//        return data;
//    }
//
//    public ResultVM data(Object data) {
//        this.data = data;
//        return this;
//    }
//
//    public void setData(Object data) {
//        this.data = data;
//    }
//
//    public String getMsg() {
//        return msg;
//    }
//
//    public ResultVM msg(String msg) {
//        this.msg = msg;
//        return this;
//    }
//
//    public void setMsg(String msg) {
//        this.msg = msg;
//    }
//
//    public Integer getCode() {
//        return code;
//    }
//
//    public ResultVM code(Integer code) {
//        this.code = code;
//        return this;
//    }
//
//    public void setCode(Integer code) {
//        this.code = code;
//    }
//
//    public static ResultVM success(Object data) {
//        return new ResultVM().data(data).code(200).msg("SUCCESS");
//    }
//
//    public static ResultVM success() {
//        return new ResultVM().code(200).msg("SUCCESS");
//    }
//
//    public static ResultVM fail(Object data) {
//        return new ResultVM().data(data).code(500).msg("FAIL");
//    }
//
//    public static ResultVM fail(String msg) {
//        return new ResultVM().code(500).msg(msg);
//    }
//}
