package com.topower.web.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import com.topower.domain.dto.InvoicedChargerOrderDTO;
import com.topower.domain.dz.DzChargerOrderRes;
import com.topower.service.DzService;
import com.topower.web.rest.util.InvoiceRestUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.UnsupportedEncodingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * REST controller for managing InvoicedModel.
 */
@Tag(name = "用户可开票充电订单列表")
@Slf4j
@RestController
@RequestMapping("/api")
public class InvoicedChargerOrderResource {

    @Autowired
    private DzService dzService;

    // 已经废弃: 使用替换接口 /api/invoiced-charger-orders2cV4
    @Operation(summary = "[内嵌网页使用]移动端用户查询可开票充电订单列表")
    @GetMapping("/invoiced-charger-orders2c")
    public ListResponse<InvoicedChargerOrderDTO> getAllInvoicedChargerOrders2c(
        ServerHttpRequest request,
        @Parameter(name = "用户ID") @RequestParam(value = "customerId") Long customerId,
        @Parameter(name = "发票状态值") @RequestParam(value = "status", required = false) Integer status,
        @Parameter(name = "是否已经关联了开票申请") @RequestParam(value = "invoicedFlag", required = false) Boolean invoicedFlag,
//是否已经关联了开票申请
        @Parameter(name = "关键词查询") @RequestParam(value = "keyword", required = false) String keyword,
        @RequestParam(value = "page", required = false) int page,
        @RequestParam(value = "size", required = false) int size) {
        log.info(
            "[内嵌网页使用]用户获取可开票充电订单列表: " + LoggerHelper2.formatEnterLog(request));

        DzChargerOrderRes chargerOrdersRes = dzService.findChargerOrders2c(
            customerId, status, invoicedFlag, keyword, page, size);

        return InvoiceRestUtils.buildInvoiceListResponse(
            DzChargerOrderRes.transInvoicedChargerOrderDTOs(chargerOrdersRes),
            DzChargerOrderRes.getTotal(chargerOrdersRes));
    }

    @Operation(summary = "[applet/ant]获取用户可开票充电订单列表")
    @GetMapping("/invoiced-charger-ordersV4")
    public ListResponse<InvoicedChargerOrderDTO> getAllInvoicedChargerOrdersV4(
        ServerHttpRequest request,
        @RequestParam(value = "customerId", required = false) Long customerId,
        @RequestParam(value = "status", required = false) Integer status,
        @RequestParam(value = "invoicedFlag", required = false) Boolean invoicedFlag,//是否已经关联了开票申请
        @RequestParam(value = "keyword", required = false) String keyword,
        @RequestParam(value = "startDate", required = false) String startDate,
        @RequestParam(value = "endDate", required = false) String endDate,
        @RequestParam(value = "page", required = false) int page,
        @RequestParam(value = "size", required = false) int size)
        throws UnsupportedEncodingException {
        log.info(LoggerHelper2.formatEnterLog(request));

        DzChargerOrderRes chargerOrdersRes = dzService.findChargerOrders(customerId,
            status,
            invoicedFlag,
            keyword,
            startDate,
            endDate,
            page,
            size);
        return InvoiceRestUtils.buildInvoiceListResponse(
            DzChargerOrderRes.transInvoicedChargerOrderDTOs(chargerOrdersRes),
            chargerOrdersRes.getTotal());
    }

    @Operation(summary = "[applet]获取用户可申请开票的充电记录")
    @GetMapping("/invoiced-charger-orders2cV4")
    public ListResponse<InvoicedChargerOrderDTO> getAllInvoicedChargerOrders2cV4(
        ServerHttpRequest request,
        @RequestParam(value = "customerId", required = true) Long customerId,
        @RequestParam(value = "status", required = false) Integer status,
        @RequestParam(value = "invoicedFlag", required = false) Boolean invoicedFlag,//是否已经关联了开票申请
        @RequestParam(value = "keyword", required = false) String keyword,
        @RequestParam(value = "page", required = false) int page,
        @RequestParam(value = "size", required = false) int size) {
        log.info(LoggerHelper2.formatEnterLog(request));

        DzChargerOrderRes chargerOrdersRes = dzService.findChargerOrders2c(
            customerId,
            status,
            invoicedFlag,
            keyword,
            page,
            size);
        return InvoiceRestUtils.buildInvoiceListResponse(
            DzChargerOrderRes.transInvoicedChargerOrderDTOs(chargerOrdersRes),
            chargerOrdersRes.getTotal());
    }
}
