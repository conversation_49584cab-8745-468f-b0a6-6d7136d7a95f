package com.topower.web.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.JsonUtils;
import com.topower.domain.dto.InvoicedUserAutoAmountDTO;
import com.topower.service.impl.InvoicedUserServiceImpl;
import com.topower.web.rest.util.InvoiceRestUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Optional;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Tag(name = "用户开票设置相关接口")
@RestController
@RequestMapping("/api")
public class InvoicedUserResource {

    @Autowired
    private InvoicedUserServiceImpl invoicedUserService;

    @Operation(summary = "[ant]查询用户开票金额、周期 和自动开票标识 b端c端都是以发票数据库数据为准")
    @GetMapping("/invoiced-usersV4/getInvoicedUserAutoAmount")
    public ObjectResponse<InvoicedUserAutoAmountDTO> getInvoicedUserAutoAmountDTOV4(
        ServerHttpRequest request,
        @RequestParam(value = "userId") Long userId,
        @RequestParam(value = "commId") Long commId) {
//        log.debug("REST request to getInvoicedUserAutoAmountDTO : {}", userId);
        log.debug("[ant]查询用户自动开票信息: uid = {}, commId = {}", userId, commId);
//        if (commercialId == null) {
//            Commercial commercial = dzInvoicedMerchantService.getCommercialByToken(token);
//            if (commercial == null) {
//                throw new DcTokenException("用户信息不存在");
//            }
//            commercialId = commercial.getId();
//        }
        Optional<InvoicedUserAutoAmountDTO> invoicedUserAutoAmountDTO = invoicedUserService.findInvoicedUserAutoAmountDTO(
            userId);

        InvoicedUserAutoAmountDTO result = null;
        if (invoicedUserAutoAmountDTO.isEmpty()) {//查询不到数据的时候，直接新建一条记录
            result = new InvoicedUserAutoAmountDTO();
            result.setUserId(userId);
            result.setCommId(commId);
            result.setAuto(false);
            result.setMonthDay(0);
            result.setInvoicedAmount(0);
            invoicedUserService.setUserAutoInvoice(result);
//            invoicedUserAutoAmountDTO = invoicedUserService.findInvoicedUserAutoAmountDTO(userId);
        } else {
            result = invoicedUserAutoAmountDTO.get();
        }

//        InvoicedUserAutoAmountDTO res = new InvoicedUserAutoAmountDTO();
//        if (invoicedUserAutoAmountDTO.isPresent()) {
//            res = invoicedUserAutoAmountDTO.get();
//        } else {
//            throw new DcServiceException("数据为空");
//        }

        return InvoiceRestUtils.buildInvoiceObjectResponse(result);//这部分的数据存放在我们自己这儿，不用调东正的接口
    }

    @Operation(summary = "[ant]设置用户开票金额和周期、自动开票标识 b端")
    @PostMapping("/invoiced-usersV4/saveInvoicedUserAutoAmount")
    public BaseResponse saveInvoicedUserAutoAmountV4(
        ServerHttpRequest request,
        @Valid @RequestBody InvoicedUserAutoAmountDTO dto) {
        log.debug("设置用户开票金额和周期、自动开票标识 : {}", JsonUtils.toJsonString(dto));
        if (dto.getUserId() == null) {
            throw new DcArgumentException("用户ID不能为空");
        }
        invoicedUserService.saveInvoicedUserAutoAmountDTO(dto);//本地数据和东正数据都得修改

        return new BaseResponse();
    }
}
