package com.topower.web.rest;

import com.topower.WechatProperties;
import com.topower.common.WechatUtils;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2019/1/24
 **/
@RestController
@RequestMapping("/api")
@EnableConfigurationProperties({WechatProperties.class})
@Configuration
public class WechatController {

    private final Logger log = LoggerFactory.getLogger(WechatController.class);

    @Autowired
    private WechatProperties wechatProperties;

    /**
     * 获得微信配置信息
     *
     * @param url
     * @return
     */
    @GetMapping(value = "/wechat/wechatConfig")
    public Map wechatConfig(@RequestParam(value = "url", required = false) String url) {
        //1、获取AccessToken
        String accessToken = WechatUtils.getAccessToken();

        //2、获取Ticket
        String jsapi_ticket = WechatUtils.getTicket(accessToken);

        //3、时间戳和随机字符串
        String noncestr = UUID.randomUUID().toString().replace("-", "").substring(0, 16);//随机字符串
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);//时间戳

        System.out.println(
            "accessToken:" + accessToken + "\njsapi_ticket:" + jsapi_ticket + "\n时间戳："
                + timestamp + "\n随机字符串：" + noncestr);

        //5、将参数排序并拼接字符串
        String str =
            "jsapi_ticket=" + jsapi_ticket + "&noncestr=" + noncestr + "&timestamp=" + timestamp
                + "&url=" + url;

        //6、将字符串进行sha1加密
        String signature = WechatUtils.SHA1(str);
        System.out.println("参数：" + str + "\n签名：" + signature);

        HashMap map = new HashMap();
        map.put("appid", wechatProperties.getAppId());
        map.put("timestamp", timestamp);
        map.put("noncestr", noncestr);
        map.put("signature", signature);
        return map;

    }

//    @GetMapping(value = "/wechat/tokenVarify")
//    public void tokenVarify(@Context ServerHttpRequest request,
//        @Context ServerHttpResponse response) {
//        boolean isGet = request.getMethod().name().toLowerCase().equals("get");
//        PrintWriter print;
//        if (isGet) {
//            // 微信加密签名
//            String signature = request.getQueryParams().getFirst("signature");
//            // 时间戳
//            String timestamp = request.getQueryParams().getFirst("timestamp");
//            // 随机数
//            String nonce = request.getQueryParams().getFirst("nonce");
//            // 随机字符串
//            String echostr = request.getQueryParams().getFirst("echostr");
//            // 通过检验signature对请求进行校验，若校验成功则原样返回echostr，表示接入成功，否则接入失败
//            if (signature != null && WXCheckoutUtil.checkSignature(signature, timestamp, nonce)) {
//                try {
//                    print = response.getWriter();
//                    print.write(echostr);
//                    print.flush();
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//            }
//        }
//    }

}
