package com.topower;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 容津 相关的配置参数
 */
@ConfigurationProperties(prefix = "rongjing")
public class RongJingProperties {

    private Integer version;
    private String url;
    private String appid;
    private String key;
    private String clientNo;

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getClientNo() {
        return clientNo;
    }

    public void setClientNo(String clientNo) {
        this.clientNo = clientNo;
    }
}
