package com.topower.common;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 数字位数转化
 */
public class CommonNumUtil {

    //金额位数
    private static int DEC_MON = 2;


    /**
     * 将显示格式转换成JDE格式（乘以位数）
     * 金额
     * 
     * @param money
     * @return
     */
    public static String toJdeMoney(String money) {
        try {
            BigDecimal dec = new BigDecimal(money);
            dec = dec.multiply(new BigDecimal(Math.pow(10, DEC_MON)));
            dec = dec.setScale(0, RoundingMode.HALF_UP);
            return dec.toString();
        } catch (Exception e) {
            return "0";
        }
    }


    /**
     * 将JDE格式转成实际显示格式（除以位数）
     * 金额
     * 
     * @param money
     * @return
     */
    public static String fromJdeMoney(String money) {
        try {
            BigDecimal dec = new BigDecimal(money);
            dec = dec.divide(new BigDecimal(Math.pow(10, DEC_MON)));
            dec = dec.setScale(DEC_MON, RoundingMode.HALF_UP);

            BigDecimal tmp = new BigDecimal(dec.longValue()).multiply(new BigDecimal(Math.pow(10, DEC_MON)));
            if (tmp.toString().equals(money)) {
                return String.valueOf(dec.longValue());
            } else {
                return dec.toString();
            }
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 转成整型
     * 
     * @param str
     * @return
     */
    public static Integer toInteger(String str) {
        try {
            return (str == null || "".equals(str)) ? 0 : Integer.parseInt(str);
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 转成长整型
     * 
     * @param str
     * @return
     */
    public static Long toLong(String str) {
        try {
            return (str == null || "".equals(str)) ? 0L : Long.parseLong(str);
        } catch (Exception e) {
            return 0L;
        }
    }

}
