package com.topower.service.impl;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.biz.model.invoice.type.ProductType;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoicedTemplateSalDetailDTO;
import com.topower.domain.InvoicedTemplateSalDetail;
import com.topower.domain.dto.DescDTO;
import com.topower.repository.InvoicedTemplateSalDetailRepository;
import com.topower.service.InvoicedTemplateSalDetailService;
import com.topower.service.mapper.InvoicedTemplateSalDetailMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing InvoicedTemplateSalDetail.
 */
@Slf4j
@Service
@Transactional
public class InvoicedTemplateSalDetailServiceImpl implements InvoicedTemplateSalDetailService {
    @Autowired
    private InvoicedTemplateSalDetailRepository repo;
    @Autowired
    private InvoicedTemplateSalDetailMapper mapper;

    @Override
    public DescDTO findInvoicedProductDesc(Long commercialId) {
        List<String> distinctNames = repo.findDistinctNames(commercialId);

        StringBuffer sb = new StringBuffer();
        if (!CollectionUtils.isEmpty(distinctNames)) {
            distinctNames.stream().forEach(name -> {
                sb.append(name + ";");
            });
        }

        DescDTO descDTO = new DescDTO();
        String res = sb.toString();
        res = res.substring(0, res.length()-1);
        descDTO.setDesc(res);

        return descDTO;
    }

//    //使用、分隔
//    @Override
//    public DescDTO findInvoicedProductDescs(Long commercialId) {
//        List<String> distinctNames = repo.findDistinctNames(commercialId);
//
//        StringBuffer sb = new StringBuffer();
//        if (!CollectionUtils.isEmpty(distinctNames)) {
//            distinctNames.forEach(name -> {
//                sb.append(name).append("、");
//            });
//        }
//
//        DescDTO descDTO = new DescDTO();
//        String res = sb.toString();
//        res = res.substring(0, res.length()-1);
//        descDTO.setDesc(res);
//
//        return descDTO;
//    }

    @Override
    public Page<InvoicedTemplateSalDetailDTO> findAllByCommercialIdAndInvoiceType(
            Long commercialId, String invoiceType, Pageable pageable) {
//        log.debug("Request to get all InvoicedTemplateSalDetails by invoiceType: {}", invoiceType);

        //}, pageable).map(mapper::toDto);
        return repo.findAll(
                (Specification<InvoicedTemplateSalDetail>) (root, criteriaQuery, criteriaBuilder) ->
                        criteriaQuery.where(criteriaBuilder.and(
                                criteriaBuilder.equal(root.get("invoiceType"), invoiceType),
                                criteriaBuilder.equal(root.get("commercialId"), commercialId)
                        )).getRestriction(), pageable).map(mapper::toDto);
    }

//    @Override
//    public Optional<InvoicedTemplateSalDetailDTO> findByCode(String code) {
//        InvoicedTemplateSalDetailPK pk = new  InvoicedTemplateSalDetailPK();
//        pk.setCode(code);
//        return repo.findById(pk).map(mapper::toDto);
//    }

//    @Override
//    public InvoicedTemplateSalDetailDTO save(InvoicedTemplateSalDetailDTO invoicedTemplateSalDetailDTO) {
//        log.debug("Request to save InvoicedTemplateSalDetail : {}", invoicedTemplateSalDetailDTO);
//
//        if (StringUtils.isEmpty(invoicedTemplateSalDetailDTO.getCode())) {
//            //处理创建时间
//            invoicedTemplateSalDetailDTO.setCreateDate(ZonedDateTime.now());
//            String maxCode = repo.findMaxCode();
//            invoicedTemplateSalDetailDTO.setCode(String.valueOf(Integer.parseInt(maxCode) + 1));
//            invoicedTemplateSalDetailDTO.setVersion("0");
//        }
//
//        InvoicedTemplateSalDetail invoicedTemplateSalDetail = mapper.toEntity(invoicedTemplateSalDetailDTO);
//        invoicedTemplateSalDetail = repo.save(invoicedTemplateSalDetail);
//        return mapper.toDto(invoicedTemplateSalDetail);
//    }

//    @Override
//    public void deleteByCode(String code) {
//        repo.deleteByCode(code);
//    }

    @Override
    public boolean checkSaveDataExists(InvoicedTemplateSalDetailDTO invoicedTemplateSalDetailDTO) {
        boolean exists = false;
        if (StringUtils.isEmpty(invoicedTemplateSalDetailDTO.getCode())) {
            exists = repo.checkInsertExists(invoicedTemplateSalDetailDTO.getInvoiceType(), invoicedTemplateSalDetailDTO.getProductCode(), invoicedTemplateSalDetailDTO.getProductName(), invoicedTemplateSalDetailDTO.getCommercialId()) == 1;
        } else {
            exists = repo.checkUpfateExists(invoicedTemplateSalDetailDTO.getCode(), invoicedTemplateSalDetailDTO.getInvoiceType(), invoicedTemplateSalDetailDTO.getProductCode(), invoicedTemplateSalDetailDTO.getProductName(), invoicedTemplateSalDetailDTO.getCommercialId()) == 1;
        }
        return exists;
    }

    @Transactional
    @Override
    public Integer saveAll(List<InvoicedTemplateSalDetailDTO> dtoList) {
        // 商品行模板约束
        if (!this.valid(dtoList)) {
            log.info("保存信息校验不通过");
            throw new DcArgumentException("保存失败：配置校验不通过.");
        }

        // 查出所有相关商品行信息
        Integer i = repo.deleteByInvoiceTypeAndCommercialId(
                dtoList.get(0).getInvoiceType(), dtoList.get(0).getCommercialId());

        // 初始值
        dtoList.forEach(dto -> {
            // 处理创建时间
            dto.setCreateDate(ZonedDateTime.now());
            dto.setCode(UUID.randomUUID().toString().replaceAll("-", ""));
            dto.setVersion("0");
        });

        return repo.saveAll(mapper.toEntity(dtoList)).size();
    }

    @Override
    public List<InvoicedTemplateSalDetailDTO> getInvoicedTemplateSalDetailsByIByRefIds(
        List<Long> refIds) {
        return repo.findByRefIdIn(refIds)
            .stream()
            .map(mapper::toDto)
            .collect(Collectors.toList());
    }

    private boolean valid(List<InvoicedTemplateSalDetailDTO> dtoList) {
        List<ProductType> collect = dtoList.stream().map(InvoicedTemplateSalDetailDTO::getProductType)
                .distinct().collect(Collectors.toList());

        if (collect.size() != dtoList.size()) {
            log.info("商品类型不匹配");
            return false;
        }

        if (collect.size() == 1 &&
                !collect.get(0).equals(ProductType.ACTUAL_FEE)) {
            log.info("商品行仅有一个的时候只能为: 实际金额消费");
            return false;
        }

        if (collect.size() == 2 &&
                collect.contains(ProductType.ACTUAL_FEE)) {
            log.info("商品行有两个的时候不能含有: 实际金额消费");
            return false;
        }

        return true;
    }
}
