package com.topower.service.impl;

import com.cdz360.biz.model.invoice.type.InvoiceType;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoicedModelDTO;
import com.topower.domain.InvoicedModel;
import com.topower.repository.InvoicedModelRepository;
import com.topower.service.InvoicedModelService;
import com.topower.service.mapper.InvoicedModelMapper;
import com.topower.specification.Sorts;
import com.topower.specification.Specifications;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing InvoicedModel.
 */
@Slf4j
@Service
@Transactional
public class InvoicedModelServiceImpl implements InvoicedModelService {
    @Autowired
    private InvoicedModelRepository repo;

    @Autowired
    private InvoicedModelMapper mapper;

    /**
     * Save a invoicedModel.
     *
     * @param dto the entity to save
     * @return the persisted entity
     */
    @Override
    public InvoicedModelDTO save(InvoicedModelDTO dto) {
        log.debug("Request to save InvoicedModel : {}", dto);
        InvoicedModel model = mapper.toEntity(dto);
        model.setCreatedDate(ZonedDateTime.now());

        // 已有数据
        List<InvoicedModelDTO> list = this.findAllByUserId(dto.getUserId());
        List<InvoicedModelDTO> common = new ArrayList<>();
        List<InvoicedModelDTO> tmp = new ArrayList<>();
        switch (dto.getInvoiceType()) {
            case PER_COMMON: // 个人普票
                common = list.stream().filter(
                    item -> "PER_COMMON".equals(item.getInvoiceType().toString())
                ).collect(Collectors.toList());

                // 确认是否已经存在该条记录
                tmp = common.stream().filter(
                    item ->
                        dto.getName().equals(item.getName())
                ).collect(Collectors.toList());
                break;
            case ENTER_COMMON: // 企业普票
                common = list.stream().filter(
                    item -> "ENTER_COMMON".equals(item.getInvoiceType().toString())
                ).collect(Collectors.toList());

                // 确认是否已经存在该条记录
                tmp = common.stream().filter(
                    item ->
                        dto.getName().equals(item.getName()) ||
                            dto.getTin().equals(item.getTin())
                ).collect(Collectors.toList());
                break;
            case ENTER_PROFESSION: // 企业专票
                common = list.stream().filter(
                    item -> "ENTER_PROFESSION".equals(item.getInvoiceType().toString())
                ).collect(Collectors.toList());

                // 确认是否已经存在该条记录
                tmp = common.stream().filter(
                    item ->
                        dto.getName().equals(item.getName()) ||
                            dto.getTin().equals(item.getTin())
                ).collect(Collectors.toList());
                break;
        }

        //模板已经存在时，用最新的数据更新模板
        if(tmp.size() > 0) {
            model.setId(tmp.get(0).getId());
        }

        //Assert.isTrue(tmp.size() == 0, "Model record already exit.");

        // 更新用户开票记录
//        if (common.size() >= 3) { // 只保留最近三条记录
//            for (InvoicedModelDTO item: common) {
//                if (item.getSeqNum() < 3) {
//                    item.setDefault_(false);
//                    item.setSeqNum(item.getSeqNum() + 1); // 编号往后移动一位
//                    repo.save(mapper.toEntity(item));
//                    continue;
//                }
//
//                delete(item.getId());
//            }
//        } else {
//            for (InvoicedModelDTO item: common) {
//                item.setDefault_(false);
//                item.setSeqNum(item.getSeqNum() + 1); // 编号往后移动一位
//                repo.save(mapper.toEntity(item));
//            }
//        }

//        model.default_(true).seqNum(1);
        model = repo.save(model);
        return mapper.toDto(model);
    }

    @Override
    public InvoicedModelDTO saveDefaultFlag(InvoicedModelDTO dto) {
        log.debug("Request to saveDefaultFlag InvoicedModel : {}", dto);
        InvoicedModel model = mapper.toEntity(dto);
        model.setCreatedDate(ZonedDateTime.now());

        // 已有数据
        List<InvoicedModelDTO> list = this.findAllByUserId(dto.getUserId());
        List<InvoicedModelDTO> common = new ArrayList<>();
        List<InvoicedModelDTO> tmp = new ArrayList<>();
        switch (dto.getInvoiceType()) {
            case PER_COMMON: // 个人普票
                common = list.stream().filter(
                    item -> "PER_COMMON".equals(item.getInvoiceType().toString())
                ).collect(Collectors.toList());

                // 确认是否已经存在该条记录
                tmp = common.stream().filter(
                    item ->
                        dto.getName().equals(item.getName())
                ).collect(Collectors.toList());
                break;
            case ENTER_COMMON: // 企业普票
                common = list.stream().filter(
                    item -> "ENTER_COMMON".equals(item.getInvoiceType().toString())
                ).collect(Collectors.toList());

                // 确认是否已经存在该条记录
                tmp = common.stream().filter(
                    item ->
                        dto.getName().equals(item.getName()) &&
                            dto.getTin().equals(item.getTin())
                ).collect(Collectors.toList());
                break;
            case ENTER_PROFESSION: // 企业专票
                common = list.stream().filter(
                    item -> "ENTER_PROFESSION".equals(item.getInvoiceType().toString())
                ).collect(Collectors.toList());

                // 确认是否已经存在该条记录
                tmp = common.stream().filter(
                    item ->
                        dto.getName().equals(item.getName()) &&
                            dto.getTin().equals(item.getTin())
                ).collect(Collectors.toList());
                break;
        }

        //判断模板是否存在，
        //存在则更新为默认（注意更新时需要把数据库中的id 值赋给model）
        //不存在，则先插入保存为默认
        if (tmp.size() > 0) {
            model.setId(tmp.get(0).getId());
        }
        model.setDefaultFlag(true);
        model = repo.save(model);

        //将用户下的其他模板记录设置为 非默认的
        repo.cancelDefaultFlagByUserIdExclude(model.getUserId(), model.getId());

        return mapper.toDto(model);
    }

    @Override
    public InvoicedModelDTO save2(InvoicedModelDTO dto) {
        log.debug("Request to save2 InvoicedModel : {}", dto);
        InvoicedModel model = mapper.toEntity(dto);
        model.setCreatedDate(ZonedDateTime.now());

        // 查看是否存在同名的模板
        InvoicedModel exist = repo.getFirstByUserIdAndNameAndInvoiceType(
                model.getUserId(), model.getName(), model.getInvoiceType());
        if (null != exist) {
            log.info("模板已重名, 覆盖原来模板");
//            throw new DcArgumentException("开票模板已重名，请调整名称或编辑旧的");
            model.setId(exist.getId());
        }

        model = repo.save(model);
        if (model.getDefaultFlag()) {
            //将用户下的其他模板记录设置为 非默认的
            repo.cancelDefaultFlagByUserIdExclude(model.getUserId(), model.getId());
        }

        return mapper.toDto(model);
    }

    /**
     * Get all the invoicedModels.
     *
     * @param pageable the pagination information
     * @return the list of entities
     */
    @Override
    @Transactional(readOnly = true)
    public Page<InvoicedModelDTO> findAll(Pageable pageable) {
        log.debug("Request to get all InvoicedModels");
        return repo.findAll(pageable)
            .map(mapper::toDto);
    }


    /**
     * Get one invoicedModel by id.
     *
     * @param id the id of the entity
     * @return the entity
     */
    @Override
    @Transactional(readOnly = true)
    public Optional<InvoicedModelDTO> findOne(Long id) {
        log.debug("Request to get InvoicedModel : {}", id);
        return repo.findById(id)
            .map(mapper::toDto);
    }

    /**
     * Delete the invoicedModel by id.
     *
     * @param id the id of the entity
     */
    @Override
    public void delete(Long id) {
        log.debug("Request to delete InvoicedModel : {}", id);
        repo.deleteById(id);
    }

    /**
     * Get all the invoicedModels by userId.
     * @param userId the id of user
     * @return the list of entities
     */
    @Override
    public List<InvoicedModelDTO> findAllByUserId(Long userId) {
        log.debug("Request to get all invoiced models by user id : {}", userId);

        return mapper.toDto(repo.findAllByUserId(userId));
    }

    /**
     * Get defaulte invoicedModel by userId.
     * @param userId the id of user
     * @return default entity
     */
    @Override
    public InvoicedModelDTO findDefaultByUserId(Long userId) {
        log.debug("Request to get default invoiced models by user id : {}", userId);
        InvoicedModel invoicedModel = repo.findByUserIdAndDefaultFlag(userId, true);

        //没有默认模板，就把时间最小的模板取出来
        if (invoicedModel == null) {
            List<InvoicedModel> invoicedModels = repo.findAllByUserIdOrderByCreatedDateDesc(userId);
            if (!CollectionUtils.isEmpty(invoicedModels)) {
                invoicedModel = invoicedModels.get(0);
            }
        }

        return mapper.toDto(invoicedModel);
    }

    @Override
    public Page<InvoicedModelDTO> findAllByUserId(Long userId, Pageable pageable) {
        log.debug("Request to get all InvoicedModels");
        return repo.findByUserId(userId, pageable)
                .map(mapper::toDto);
    }

    @Override
    public Page<InvoicedModelDTO> findAllByUserIdAndType(Long userId,
        InvoiceType invoiceType,
        Pageable pageable) {
        log.debug("Request to get all InvoicedModels");
        return repo.findByUserIdAndInvoiceType(userId, invoiceType, pageable)
            .map(mapper::toDto);

//        Specification<InvoicedModel> specification = Specifications.<InvoicedModel>and()
//            .eq("userId", userId)
//            .eq("invoiceType", invoiceType)
//            .build();
//        return repo.findAll(specification, pageable)
//            .map(mapper::toDto);
    }

//    @Override
//    public Page<InvoicedModelDTO> findAllByUserIdAndTypeSorted(Long userId,
//        InvoiceType invoiceType,
//        Pageable pageable) {
//        log.debug("Request to get all InvoicedModels sort desc by id");
////        Sort sort = Sort.by(Sort.Direction.DESC, "id");
//        final PageRequest sortPage = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(),
//            Sorts.builder().desc("id").build());
//        return repo.findByUserIdAndInvoiceType(userId, invoiceType, sortPage)
//            .map(mapper::toDto);

//        Specification<InvoicedModel> specification = Specifications.<InvoicedModel>and()
//            .eq("userId", userId)
//            .eq("invoiceType", invoiceType)
//            .build();
//        return repo.findAll(specification, pageable)
//            .map(mapper::toDto);
//    }

}
