package com.topower.service.invoice;

import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoiceApplyResultDTO;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedTemplateSalVo;
import com.topower.domain.InvoicedRecord;

/**
 * 发票申请
 */
public interface InvoiceApplyStrategy<T> {

    T preApply(InvoicedTemplateSalVo sal, InvoicedRecord record);

    InvoiceApplyResultDTO apply(T req, Long recordId);

    void postApply(InvoiceApplyResultDTO applyResult, InvoicedRecord record, T req);
}
