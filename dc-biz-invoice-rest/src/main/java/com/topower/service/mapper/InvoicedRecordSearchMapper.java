package com.topower.service.mapper;

import com.cdz360.base.utils.DecimalUtils;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoicedRecordDTO;
import com.topower.domain.InvoicedRecord;
import com.topower.domain.InvoicedRecordSearch;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * Mapper for the entity InvoicedRecord and its DTO InvoicedRecordDTO.
 */
@Mapper(imports = DecimalUtils.class, componentModel = "spring", uses = {})
public interface InvoicedRecordSearchMapper extends
    EntityMapper<InvoicedRecordDTO, InvoicedRecordSearch> {

    default InvoicedRecord fromId(Long id) {
        if (id == null) {
            return null;
        }
        InvoicedRecord invoicedRecord = new InvoicedRecord();
        invoicedRecord.setId(id);
        return invoicedRecord;
    }

    @Mapping(target = "invoicingContent", ignore = true)
    @Mapping(source = "user.username", target = "userName")
    @Mapping(source = "user.phone", target = "userPhone")
    @Mapping(target = "invoiceAmount",
        expression = "java( DecimalUtils.divide100(entity.getInvoiceAmount(), 0) )")
    @Override
    InvoicedRecordDTO toDto(InvoicedRecordSearch entity);
}
