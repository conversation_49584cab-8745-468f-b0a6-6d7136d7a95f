package com.topower.service.mapper;

import com.topower.domain.InvoicedConfig;
import com.topower.domain.dto.InvoicedConfigDTO;
import com.topower.domain.enumeration.InvoicedConfigCode;
import org.mapstruct.Mapper;

/**
 * Mapper for the entity InvoicedModel and its DTO InvoicedModelDTO.
 */
@Mapper(componentModel = "spring", uses = {})
public interface InvoicedConfigMapper extends EntityMapper<InvoicedConfigDTO, InvoicedConfig> {


    default InvoicedConfig fromCode(InvoicedConfigCode configCode, Integer val, Long commercialId) {
        if (configCode == null || val == null || commercialId == null) {
            return null;
        }
//        InvoicedConfigKey key = new InvoicedConfigKey();
//        key.setConfigCode(code);
//        key.setCommercialId(commercialId);

        InvoicedConfig invoicedConfig = new InvoicedConfig();
//        invoicedConfig.setIdClass(key);
        invoicedConfig.setConfigCode(configCode.getCode());
        invoicedConfig.setCommercialId(commercialId);
        invoicedConfig.setConfigName(configCode.getDesc());
        invoicedConfig.setValue1(val);
        return invoicedConfig;
    }
}
