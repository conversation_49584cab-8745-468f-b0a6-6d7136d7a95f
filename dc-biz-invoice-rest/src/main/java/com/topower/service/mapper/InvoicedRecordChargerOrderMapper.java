package com.topower.service.mapper;

import com.topower.domain.InvoicedRecordChargerOrder;
import com.topower.domain.dto.InvoicedRecordChargerOrderDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * Mapper for the entity InvoicedRecord and its DTO InvoicedRecordDTO.
 */
@Mapper(componentModel = "spring", uses = {})
public interface InvoicedRecordChargerOrderMapper extends
        EntityMapper<InvoicedRecordChargerOrderDTO, InvoicedRecordChargerOrder> {

    @Mapping(target = "invoiceAmount", ignore = true)
    @Mapping(target = "stationId", ignore = true)
    @Override
    InvoicedRecordChargerOrderDTO toDto(InvoicedRecordChargerOrder entity);
}
