package com.topower.service;

import com.topower.domain.dto.InvoicedLogisticInfoDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;

/**
 * Service Interface for managing InvoicedLogisticInfo.
 */
public interface InvoicedLogisticInfoService {

    /**
     * Save a invoicedLogisticInfo.
     *
     * @param invoicedLogisticInfoDTO the entity to save
     * @return the persisted entity
     */
    InvoicedLogisticInfoDTO save(InvoicedLogisticInfoDTO invoicedLogisticInfoDTO);

    /**
     * Get all the invoicedLogisticInfos.
     *
     * @param pageable the pagination information
     * @return the list of entities
     */
    Page<InvoicedLogisticInfoDTO> findAll(Pageable pageable);


    /**
     * Get the "id" invoicedLogisticInfo.
     *
     * @param id the id of the entity
     * @return the entity
     */
    Optional<InvoicedLogisticInfoDTO> findOne(Long id);

    /**
     * Delete the "id" invoicedLogisticInfo.
     *
     * @param id the id of the entity
     */
    void delete(Long id);
}
