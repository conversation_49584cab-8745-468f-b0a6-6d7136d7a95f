package com.topower.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.topower.domain.dto.InvoicedSiteValidYNDTO;

/**
 * 调用东正接口
 */
public interface DzInvoicedSiteService {

    /**
     * 获取不可开票的站点信息
     * @return 站点信息
     */
    String findNotInvoicedSiteDesc(String dzToken);

//    /**
//     * Get all the invoicedRecords.
//     *
//     * @param siteStatus 场站状态
//     * @param invoicedValid 是否可开票
//     * @param keyword the keyword of search
//     * @param pageable the pagination information
//     * @return the list of entities
//     */
//    DzSiteRes findAll(
//            Integer siteStatus,
//            Boolean invoicedValid,
//            String keyword,
//            Pageable pageable,
//            String dzToken) throws UnsupportedEncodingException;

    BaseResponse saveInvoicedValid(InvoicedSiteValidYNDTO invoicedSiteValidYNDTO);

}
