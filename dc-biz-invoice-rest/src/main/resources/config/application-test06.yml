## ===================================================================
## Spring Boot configuration for the "dev" profile.
##
## This configuration overrides the application.yml file.
##
## More information on profiles: https://www.jhipster.tech/profiles/
## More information on configuration properties: https://www.jhipster.tech/common-application-properties/
## ===================================================================
#
## ===================================================================
## Standard Spring Boot properties.
## Full reference is available at:
## http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
## ===================================================================
#
#logging:
#  level:
#    ROOT: DEBUG
#    io.github.jhipster: DEBUG
#    com.topower: DEBUG
#    org.springframework.boot.autoconfigure: ERROR
#
#feign:
#  hystrix:
#    enabled: true
#
#hystrix:
#  command:
#    default:
#      execution:
#        isolation:
#          thread:
#            timeoutInMilliseconds: 5000
#
#spring:
#  profiles:
#    active: dev
#    include:
#      - swagger
#      # Uncomment to activate TLS for the dev profile
#      #- tls
#  devtools:
#    restart:
#      enabled: true
#    livereload:
#      enabled: false # we use Webpack dev server + BrowserSync for livereload
#  jackson:
#    serialization:
#      indent-output: true
#  datasource:
#    type: com.zaxxer.hikari.HikariDataSource
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    #        url: *******************************************************************************************************************************
#    #        url: **************************************************************************************************************************************************
#    url: *******************************************************************************************************************************
#    username: invoice
#    password: invoice@2019
#    hikari:
#      auto-commit: false
#      data-source-properties:
#        cachePrepStmts: true
#        prepStmtCacheSize: 250
#        prepStmtCacheSqlLimit: 2048
#        useServerPrepStmts: true
#  jpa:
#    database-platform: org.hibernate.dialect.MySQL5InnoDBDialect
#    database: MYSQL
#    show-sql: true
#    properties:
#      hibernate.id.new_generator_mappings: true
#      hibernate.connection.provider_disables_autocommit: true
#      hibernate.cache.use_second_level_cache: true
#      hibernate.cache.use_query_cache: false
#      hibernate.generate_statistics: true
#      hibernate.cache.region.factory_class: com.hazelcast.hibernate.HazelcastCacheRegionFactory
#      hibernate.cache.hazelcast.instance_name: invoiceService
#      hibernate.cache.use_minimal_puts: true
#      hibernate.cache.hazelcast.use_lite_member: true
#      hibernate.jdbc.time_zone: Asia/Shanghai
#  #            hibernate.naming.physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
#  #    liquibase:
#  #        contexts: dev
#  mail:
#    host: localhost
#    port: 25
#    username:
#    password:
#  messages:
#    cache-duration: PT1S # 1 second, see the ISO 8601 standard
#  thymeleaf:
#    cache: false
#  zipkin: # Use the "zipkin" Maven profile to have the Spring Cloud Zipkin dependencies
#    base-url: http://localhost:9411
#    enabled: false
#    locator:
#      discovery:
#        enabled: true
#  data:
#    web:
#      pageable:
#        one-indexed-parameters: true
#
#server:
#  port: 8083
#
## ===================================================================
## JHipster specific properties
##
## Full reference is available at: https://www.jhipster.tech/common-application-properties/
## ===================================================================
#
#jhipster:
#  http:
#    version: V_1_1 # To use HTTP/2 you will need to activate TLS (see application-tls.yml)
#  cache: # Cache configuration
#    hazelcast: # Hazelcast distributed cache
#      time-to-live-seconds: 3600
#      backup-count: 1
#      management-center: # Full reference is available at: http://docs.hazelcast.org/docs/management-center/3.9/manual/html/Deploying_and_Starting.html
#        enabled: false
#        update-interval: 3
#        url: http://localhost:8180/mancenter
#    # CORS is disabled by default on microservices, as you should access them through a gateway.
#    # If you want to enable it, please uncomment the configuration below.
#    # cors:
#    # allowed-origins: "*"
#    # allowed-methods: "*"
#    # allowed-headers: "*"
#    # exposed-headers: "Authorization,Link,X-Total-Count"
#    # allow-credentials: true
#    # max-age: 1800
#  security:
#    authentication:
#      jwt:
#        # This token must be encoded using Base64 (you can type `echo 'secret-key'|base64` on your command line)
#        base64-secret: Nzg0MjczNmExNWRiYTNmZWFhNjZiMjk1YWZjYjJmYzJjYmE4ZmU1NGQ4OWJlM2IyZjc1MjAzMzBkNDNiOGU4YTM5NzgzOTdjNzMwNmJlOGY0OTNkZjMwYmUxNDJlOWFiYjViZjIyY2U5YmJkMWUyOTE4MzgxNzMxMjFjZjUxOWI=
#        # Token is valid 24 hours
#        token-validity-in-seconds: 86400
#        token-validity-in-seconds-for-remember-me: 2592000
#  mail: # specific JHipster mail property, for standard properties see MailProperties
#    from: invoiceService@localhost
#    base-url: http://127.0.0.1:8083
#  metrics: # DropWizard Metrics configuration, used by MetricsConfiguration
#    jmx:
#      enabled: true
#    logs: # Reports Dropwizard metrics in the logs
#      enabled: false
#      report-frequency: 60 # in seconds
#  logging:
#    logstash: # Forward logs to logstash over a socket, used by LoggingConfiguration
#      enabled: false
#      host: localhost
#      port: 5000
#      queue-size: 512
#
## ===================================================================
## Application specific properties
## Add your own application properties here, see the ApplicationProperties class
## to have type-safe configuration, like in the JHipsterProperties above
##
## More documentation is available at:
## https://www.jhipster.tech/common-application-properties/
## ===================================================================
#
## application:
##kd:
##    eBusinessId: 1406061
##    appKey: e5d89fee-6b9a-464d-aa6f-6ff116f5c5a8
##    reqUrl: http://api.kdniao.com/api/Eorderservice
##    printUrl: http://www.kdniao.com/External/PrintOrder.aspx
##    shipperCode: SF
##    monthCode: '0210096367'
#
#invoicegen:
#  url: http://127.0.0.1:8087/api/invoice/gen