
# See https://github.com/Netflix/Hystrix/wiki/Configuration
hystrix:
    command:
        default:
            execution:
                isolation:
                    strategy: SEMAPHORE
# See https://github.com/spring-cloud/spring-cloud-netflix/issues/1330
#                    thread:
#                        timeoutInMilliseconds: 10000
    shareSecurityContext: true

management:
    info:
        git:
            mode: full
    metrics:
        enabled: false # http://micrometer.io/ is disabled by default, as we use http://metrics.dropwizard.io/ instead

spring:
    profiles:
############################## ！！！！！！！！！！！！！！！！！！
#        根据环境修改配置: test01
#        根据环境修改配置: prod
############################## ！！！！！！！！！！！！！！！！！！
        active: test01
    jpa:
        open-in-view: false
        properties:
            hibernate.jdbc.time_zone: UTC
        hibernate:
            ddl-auto: none
            naming:
                physical-strategy: org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy
                implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy

server:
    servlet:
        session:
            cookie:
                http-only: true

# Properties to be exposed on the /info management endpoint
#info:
#    # Comma separated list of profiles that will trigger the ribbon to show
#    display-ribbon-on-profiles: "dev"

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
    async:
        core-pool-size: 2
        max-pool-size: 50
        queue-capacity: 10000

logging:
    file:
        name: target/invoiceService.log

# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

# application:
file:
    root: /file/
