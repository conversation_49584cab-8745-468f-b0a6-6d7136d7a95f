package com.cdz360.biz.ess.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.es.vo.EssAlarmVo;
import com.cdz360.biz.ess.model.param.ListEssAlarmParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;


@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_IOT_MONITOR,
    fallbackFactory = DeviceMonitorFeignHystrix.class)
public interface DeviceMonitorFeignClient {

    // 获取储能设备告警参数
    @PostMapping(value = "/ess/alarm/getEssAlarmRecordList")
    Mono<ListResponse<EssAlarmVo>> getEssAlarmRecordList(
        @RequestBody ListEssAlarmParam param);
}
