package com.cdz360.biz.ess.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ess.model.dto.BaseUserAccount;
import com.cdz360.biz.ess.model.dto.UserRegisterDto;
import com.cdz360.biz.ess.model.param.ModifyAccountParam;
import com.cdz360.biz.model.cus.user.po.UserPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@SuppressWarnings("ALL")
public class EssUserFeignHystrix implements FallbackFactory<EssUserFeignClient> {

    @Override
    public EssUserFeignClient apply(Throwable throwable) {
        log.error("err = {}", throwable.getMessage(), throwable);
        return new EssUserFeignClient() {
            @Override
            public Mono<ObjectResponse<UserPo>> essUserLogin(BaseUserAccount param) {
                log.error("服务[{}]接口熔断 - 户用储能登录, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_USER, param);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<UserPo>> getEssUserAccountDetail(Long uid) {
                log.error("服务[{}]接口熔断 - 获取用户信息, uid = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_USER, uid);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> essUserRegister(UserRegisterDto param) {
                log.error("服务[{}]接口熔断 - 户用储能注册, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_USER, param);
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<ObjectResponse<String>> settingLanguage(Long uid, String lang) {
                log.error("服务[{}]接口熔断 - 用户选择语言, uid = {}, lang = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_USER, uid, lang);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<UserPo>> modifyAccount(ModifyAccountParam param) {
                log.error("服务[{}]接口熔断 - 用户账户信息修改, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_USER, param);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> cusDeregister(long uid, Long opUid) {
                log.error("服务[{}]接口熔断 - 用户账户注销, uid = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_USER, uid);
                return Mono.just(RestUtils.serverBusy());
            }
        };
    }


}