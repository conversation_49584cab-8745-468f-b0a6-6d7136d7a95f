package com.cdz360.biz.ess.aop;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 用于接口防刷
 *
 * <p>单位时间@second允许最大访问次数@max</p>
 */
@Documented
@Inherited
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ApiLimit {

    /**
     * 时间周期(秒)
     */
    int second() default 1;

    /**
     * 时间周期内允许访问最大次数
     */
    int max() default 3;
}
