package com.cdz360.biz.ess.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.param.SamplingParam;
import com.cdz360.base.model.es.dto.InOutTimeRangeDto;
import com.cdz360.base.model.es.dto.UpdateEssCfgDto;
import com.cdz360.base.model.es.type.DevicePowerOpType;
import com.cdz360.base.model.es.vo.EssCfgVo;
import com.cdz360.base.model.es.vo.EssInOutStrategyDto;
import com.cdz360.base.model.es.vo.EssPriceDto;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ess.model.EssDynamicCfgRWParam;
import com.cdz360.biz.ess.model.dto.BindDeviceDto;
import com.cdz360.biz.ess.model.dto.ModifyDeviceBasicDto;
import com.cdz360.biz.ess.model.param.FetchUserEssParam;
import com.cdz360.biz.ess.model.vo.DayUserEssRtDataBi;
import com.cdz360.biz.model.ess.po.EssPo;
import com.cdz360.biz.model.ess.vo.EssAttachCfgVo;
import com.cdz360.biz.model.ess.vo.EssVo;
import com.cdz360.biz.model.ess.vo.UserEssVo;
import com.cdz360.biz.model.upgradepg.param.StartUpgradeTaskParam;
import com.cdz360.biz.model.upgradepg.vo.UpgradeLogVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@SuppressWarnings("ALL")
public class EssFeignHystrix implements FallbackFactory<EssFeignClient> {

    @Override
    public EssFeignClient apply(Throwable throwable) {
        log.error("err = {}", throwable.getMessage(), throwable);
        return new EssFeignClient() {
            @Override
            public Mono<ObjectResponse<Integer>> scanAndBindEss(Long shareUid, Long bindUid) {
                log.error("服务[{}]接口熔断 - 绑定分享设备列表, shareUid = {}, bindUid = {}",
                    DcConstants.KEY_FEIGN_IOT_ESS, shareUid, bindUid);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<UserEssVo>> fetchUserEss(FetchUserEssParam param) {
                log.error("服务[{}]接口熔断 - 获取用户设备列表, param = {}",
                    DcConstants.KEY_FEIGN_IOT_ESS, param);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<EssPo>> userEssBind(BindDeviceDto dto) {
                log.error("服务[{}]接口熔断 - 用户绑定设备, dto = {}",
                    DcConstants.KEY_FEIGN_IOT_ESS, dto);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<EssPo>> userEssDetail(Long uid, String sn, String dno) {
                log.error("服务[{}]接口熔断 - 获取设备信息, uid = {}, sn = {}, dno = {}",
                    DcConstants.KEY_FEIGN_IOT_ESS, uid, sn, dno);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<EssAttachCfgVo>> userEssDetailAttachCfg(
                Long uid, String dno) {
                log.error("服务[{}]接口熔断 - 获取设备信息(附带配置信息), uid = {}, dno = {}",
                    DcConstants.KEY_FEIGN_IOT_ESS, uid, dno);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<EssPo>> userEssDelete(Long uid, String dno) {
                log.error("服务[{}]接口熔断 - 用户删除设备, uid = {}, dno = {}",
                    DcConstants.KEY_FEIGN_IOT_ESS, uid, dno);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<EssPo>> userEssModifyBasicInfo(ModifyDeviceBasicDto dto) {
                log.error("服务[{}]接口熔断 - 用户更新设备信息, {}}",
                    DcConstants.KEY_FEIGN_IOT_ESS, dto);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> deviceFaultClear(String dno) {
                log.error("服务[{}]接口熔断 - 设备告警清除 dno = {}}",
                    DcConstants.KEY_FEIGN_IOT_ESS, dno);
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<ObjectResponse<EssPriceDto>> getEssPriceCfg(String dno) {
                log.error("服务[{}]接口熔断 - 获取储能设备计费模板 dno = {}}",
                    DcConstants.KEY_FEIGN_IOT_ESS, dno);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> configEssPriceCfg(String dno, EssPriceDto strategy) {
                log.error("服务[{}]接口熔断 - 配置储能设备计费模板 dno = {}}",
                    DcConstants.KEY_FEIGN_IOT_ESS, dno);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> deviceRefreshSetting(InOutTimeRangeDto dto) {
                log.error("服务[{}]接口熔断 - 更新设备配置 dto = {}}",
                    DcConstants.KEY_FEIGN_IOT_ESS, dto);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> deviceNotifySetting(InOutTimeRangeDto dto) {
                log.error("服务[{}]接口熔断 - APP设备配置更新通知 dto = {}}",
                    DcConstants.KEY_FEIGN_IOT_ESS, dto);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<String>> userEssReadCfg(String dno,
                EssDynamicCfgRWParam param) {
                log.error("服务[{}]接口熔断 - 户储读取配置信息 dno = {}}",
                    DcConstants.KEY_FEIGN_IOT_ESS, dno);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<String>> userEssRefreshCfg(String dno,
                EssDynamicCfgRWParam param) {
                log.error("服务[{}]接口熔断 - 户储更新配置信息 dno = {}}",
                    DcConstants.KEY_FEIGN_IOT_ESS, dno);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<EssInOutStrategyDto>> getEssChargeStrategy(String dno) {
                log.error("服务[{}]接口熔断 - 获取储能设备充放电策略 dno = {}}",
                    DcConstants.KEY_FEIGN_IOT_ESS, dno);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> configEssChargeStrategy(String dno,
                EssInOutStrategyDto strategy) {
                log.error("服务[{}]接口熔断 - 配置储能设备充放电策略 dno = {}}",
                    DcConstants.KEY_FEIGN_IOT_ESS, dno);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<DayUserEssRtDataBi>> userEssDayOfRangeKwh(
                SamplingParam param) {
                log.error("服务[{}]接口熔断 - 获取户用ESS下指定时间范围储能数据量 param = {}}",
                    DcConstants.KEY_FEIGN_IOT_ESS, param);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<BaseResponse> devicePowerOp(String dno, Boolean on) {
                log.error("服务[{}]接口熔断 - 设备开机/关机 dno = {}, on = {}",
                    DcConstants.KEY_FEIGN_IOT_ESS, dno, on);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<BaseResponse> devicePcsPowerOp(String dno, DevicePowerOpType powerOp) {
                log.error("服务[{}]接口熔断 - PCS设备开机/关机 dno = {}, powerOp = {}",
                    DcConstants.KEY_FEIGN_IOT_ESS, dno, powerOp);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<BaseResponse> deviceUpgradeOp(StartUpgradeTaskParam param) {
                log.error("服务[{}]接口熔断 - 启动升级任务, param = {}",
                    DcConstants.KEY_FEIGN_IOT_ESS, param);
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<ObjectResponse<EssCfgVo>> getEssConfig(String dno) {
                log.error("服务[{}]接口熔断 - 获取设备配置 dno = {}",
                    DcConstants.KEY_FEIGN_IOT_ESS, dno);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<String>> deviceUpdateSetting(String dno,
                UpdateEssCfgDto dto) {
                log.error("服务[{}]接口熔断 - 更新设备配置 dto = {}",
                    DcConstants.KEY_FEIGN_IOT_ESS, dto);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<UpgradeLogVo>> getDeviceUpgradeInfo(String dno) {
                log.error("服务[{}]接口熔断 - 设备升级信息 dno = {}",
                    DcConstants.KEY_FEIGN_IOT_ESS, dno);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<EssVo>> getEssVo(String dno) {
                log.error("服务[{}]接口熔断 - getEssVo dno = {}",
                    DcConstants.KEY_FEIGN_IOT_ESS, dno);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

        };
    }
}
