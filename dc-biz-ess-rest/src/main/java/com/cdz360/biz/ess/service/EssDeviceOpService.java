package com.cdz360.biz.ess.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.es.dto.PcsControlParamDto;
import com.cdz360.biz.ess.feign.EssFeignClient;
import com.cdz360.biz.ess.model.JobType;
import com.cdz360.biz.ess.utils.FeignResponseAndValidate;
import com.cdz360.biz.ess.utils.RedisMqUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssDeviceOpService {

    @Autowired
    private EssFeignClient essFeignClient;

    @Autowired
    private RedisMqUtils redisMqUtils;

    public Mono<BaseResponse> devicePowerOp(String dno, Boolean on) {
        return essFeignClient.devicePowerOp(dno, on)
            .doOnNext(FeignResponseAndValidate::check)
            .doOnNext(x -> redisMqUtils.subscribe(dno, JobType.OP_POWER));
    }

    public Mono<BaseResponse> devicePcsControl(String dno, PcsControlParamDto pcsCtrl) {
        if (null == pcsCtrl.getPowerOp()) {
            throw new DcArgumentException("请提供PCS控制参数");
        }
        return essFeignClient.devicePcsPowerOp(dno, pcsCtrl.getPowerOp());
    }
}
