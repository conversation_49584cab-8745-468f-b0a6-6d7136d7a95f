package com.cdz360.biz.utils.feign.yw;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.trading.yw.param.CreateYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.CusRecNoticeParam;
import com.cdz360.biz.model.trading.yw.param.ListYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.SolvedYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.TransYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.UpdateYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.UpdateYwOrderStatusParam;
import com.cdz360.biz.model.trading.yw.po.YwOrderPo;
import com.cdz360.biz.model.trading.yw.vo.YwOrderBi;
import com.cdz360.biz.model.trading.yw.vo.YwOrderLogVo;
import com.cdz360.biz.model.trading.yw.vo.YwOrderVo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE,
    fallbackFactory = YwOrderHystrix.class
)
public interface YwOrderClient {

    // 更新运维工单标签
    @PostMapping(value = "/dataCore/ywOrder/updateTag")
    Mono<ObjectResponse<YwOrderVo>> updateYwOrderTag(@RequestBody UpdateYwOrderParam param);

    // 获取运维工单列表
    @PostMapping(value = "/dataCore/ywOrder/findAll")
    Mono<ListResponse<YwOrderVo>> findYwOrder(@RequestBody ListYwOrderParam param);

    // 统计运维人员运维数据
    @PostMapping(value = "/dataCore/ywOrder/ywOrderBi")
    Mono<ListResponse<YwOrderBi>> ywOrderBi(@RequestBody ListYwOrderParam param);

    // 获取运维工单详情
    @GetMapping(value = "/dataCore/ywOrder/getYwOrderDetail")
    Mono<ObjectResponse<YwOrderVo>> getYwOrderDetail(
        @RequestParam(value = "ywOrderNo") String ywOrderNo);

    // 创建运维工单
    @PostMapping(value = "/dataCore/ywOrder/createOrder")
    Mono<ObjectResponse<YwOrderPo>> createYwOrder(@RequestBody CreateYwOrderParam param);

    // 运维工单转派
    @PostMapping(value = "/dataCore/ywOrder/trans")
    Mono<ObjectResponse<YwOrderVo>> transYwOrder(@RequestBody TransYwOrderParam param);

    // 运维工单状态更新
    @PostMapping(value = "/dataCore/ywOrder/updateStatus")
    Mono<BaseResponse> updateOrderStatus(@RequestBody UpdateYwOrderStatusParam param);

    // 运维工单保存操作
    @PostMapping(value = "/dataCore/ywOrder/save")
    Mono<ObjectResponse<YwOrderPo>> saveYwOrder(@RequestBody SolvedYwOrderParam param);

    // 运维工单解决提交操作
    @PostMapping(value = "/dataCore/ywOrder/solved")
    Mono<ObjectResponse<YwOrderPo>> solvedYwOrder(@RequestBody SolvedYwOrderParam param);

    // 获取运维工单转派记录
    @PostMapping(value = "/dataCore/ywOrder/transList")
    Mono<ListResponse<YwOrderLogVo>> ywOrderTransList(@RequestParam("ywOrderNo") String ywOrderNo);

    // 获取场站最近一条运维工单记录
    @GetMapping(value = "/dataCore/ywOrder/getSiteLatestRec")
    Mono<ObjectResponse<YwOrderVo>> getSiteLatestRec(@RequestParam("siteId") String siteId);

    // 推送用户运维工单处理结果前端反馈
    @GetMapping(value = "/dataCore/ywOrder/recNoticeFeedback")
    Mono<BaseResponse> recNoticeFeedback(@RequestBody CusRecNoticeParam param);
}
