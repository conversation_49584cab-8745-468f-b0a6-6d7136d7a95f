package com.cdz360.biz.utils.feign.hlht;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.charge.vo.ChargeOrderVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.biz.auth.corpWx.JsCode2SessionRes;
import com.cdz360.biz.model.gaode.GaoDeSearchParam;
import com.cdz360.biz.model.gaode.PlaceGaoDe;
import com.cdz360.biz.model.gaode.StaticMapGaoDe;
import com.cdz360.biz.model.trading.hlht.dto.CecQueryQeuipBusinessPolicyResult;
import com.cdz360.biz.model.trading.hlht.param.AddAccountParam;
import com.cdz360.biz.model.trading.hlht.param.CheckAccountParam;
import com.cdz360.biz.model.trading.hlht.param.DataSyncSitePrices;
import com.cdz360.biz.model.trading.hlht.param.EditAccountParam;
import com.cdz360.biz.model.trading.hlht.param.HlhtSiteParam;
import com.cdz360.biz.model.trading.hlht.po.PartnerPo;
import com.cdz360.biz.model.trading.hlht.po.PartnerSitePo;
import com.cdz360.biz.model.trading.hlht.vo.ClientOperaterVo;
import com.cdz360.biz.model.trading.hlht.vo.HlhtSiteVo;
import com.cdz360.biz.model.trading.hlht.vo.InvoiceHlhtCallbackVo;
import com.cdz360.biz.model.trading.hlht.vo.OperatorVo;
import com.chargerlinkcar.framework.common.domain.param.CorpWxPushMsgParam;
import com.chargerlinkcar.framework.common.domain.param.CorpWxSendMsgCommonParam;
import com.chargerlinkcar.framework.common.domain.param.CorpWxSendMsgParam;
import com.chargerlinkcar.framework.common.domain.param.CorpWxSendScheduledMsgParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import java.math.BigDecimal;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_OPEN_HLHT,
    fallbackFactory = OpenHlhtFeignHystrix.class
)
public interface OpenHlhtFeignClient {

    // 高德充电站搜索
    @PostMapping("/open/gaoDe/searchChargingStation")
    Mono<ListResponse<PlaceGaoDe>> searchChargingStation(@RequestBody GaoDeSearchParam param);

    // 高德静态地图
    @GetMapping("/open/gaoDe/staticmap")
    Mono<ObjectResponse<StaticMapGaoDe>> gaodeStaticMap(
        @RequestParam("location") String location);

    // 获取企业微信用户敏感信息
    @GetMapping("/open/corpWx/{topCommId}/jsCode2Session")
    Mono<ObjectResponse<JsCode2SessionRes>> jsCode2Session(
        @PathVariable(value = "topCommId") Long topCommId,
        @RequestParam("code") String code);

    /**
     * @param topCommId
     * @param qrCode
     * @return 校验成功返回枪头编号
     */
    @PostMapping(value = "/open/cec/{topCommId}/plugAuth")
    Mono<ObjectResponse<String>> plugAuth(
        @PathVariable(value = "topCommId") Long topCommId,
        @RequestParam(value = "qrCode") String qrCode);


    /**
     * 启动充电
     *
     * @param topCommId
     * @param plugNo
     * @param orderNo
     * @param qrCode
     * @return 启动成功返回互联互通订单号
     */
    @PostMapping("/open/cec/{topCommId}/startCharge")
    Mono<ObjectResponse<ChargeOrderVo>> startCharge(
        @PathVariable(value = "topCommId") Long topCommId,
        @RequestParam(value = "plugNo") String plugNo,
        @RequestParam(value = "orderNo") String orderNo,
        @RequestParam(value = "qrCode") String qrCode,
        @RequestParam(value = "frozenAmount") BigDecimal frozenAmount);


    /**
     * 停止充电
     */
    @PostMapping("/open/cec/{topCommId}/stopCharge")
    Mono<BaseResponse> stopCharge(
        @PathVariable(value = "topCommId") Long topCommId,
        @RequestParam(value = "orderNo") String orderNo);


    /**
     * 查询枪头信息列表
     */
    @PostMapping("/open/cec/{topCommId}/getPlugList")
    Mono<ListResponse<PlugVo>> getPlugList(
        @PathVariable(value = "topCommId") Long topCommId,
        @RequestParam(value = "partnerCode") String partnerCode,
        @RequestParam(value = "siteId") String siteId);

    /**
     * 查询计费业务策略信息
     *
     * @param topCommId
     * @param partnerCode
     * @param connectorId
     * @return
     */
    @PostMapping("/open/cec/{topCommId}/getPriceInfo")
    Mono<ObjectResponse<CecQueryQeuipBusinessPolicyResult>> getPriceInfo(
        @PathVariable(value = "topCommId") Long topCommId,
        @RequestParam(value = "partnerCode") String partnerCode,
        @RequestParam(value = "connectorId") String connectorId);

    /**
     * 互联运营商列表
     *
     * @param code
     * @param name
     * @return
     */
    @GetMapping(value = "/open/partner/equip/getList")
    Mono<ListResponse<OperatorVo>> partnerList(
        @RequestParam(value = "code", required = false) String code,
        @RequestParam(value = "name", required = false) String name,
        @RequestParam(value = "start") Long start,
        @RequestParam(value = "size") Integer size);

    /**
     * 更新插入互联运营商
     *
     * @param vo
     * @return
     */
    @PostMapping(value = "/open/partner/equip/upsertPartner")
    Mono<BaseResponse> upsertPartner(@RequestBody OperatorVo vo);

    /**
     * 删除互联运营商
     *
     * @param pId
     * @return
     */
    @GetMapping(value = "/open/partner/equip/delPartner")
    Mono<BaseResponse> delPartner(@RequestParam(value = "pId") Long pId);

    /**
     * 互联站点列表
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/open/partner/equip/hlhtSiteList")
    Mono<ListResponse<HlhtSiteVo>> hlhtSiteList(@RequestBody HlhtSiteParam param);

    /**
     * 批量插入
     *
     * @param poList
     * @return
     */
    @PostMapping(value = "/open/partner/equip/addPartnerSite")
    Mono<BaseResponse> addPartnerSite(@RequestBody List<PartnerSitePo> poList);

    /**
     * 批量disable
     *
     * @param topCommId
     * @param siteIdList
     * @return
     */
    @PostMapping(value = "/open/partner/equip/disablePartnerSite")
    Mono<BaseResponse> disablePartnerSite(@RequestParam(value = "topCommId") Long topCommId,
        @RequestBody List<String> siteIdList);

    @Operation(summary = "互联运营商列表")
    @GetMapping(value = "/open/partner/client/getList")
    Mono<ListResponse<OperatorVo>> partnerList(@RequestParam(value = "start") Long start,
        @RequestParam(value = "size") Integer size,
        @Parameter(name = "运营商标识, 模糊匹配")
        @RequestParam(value = "code", required = false) String code,
        @Parameter(name = "客户侧运营商名称, 模糊匹配")
        @RequestParam(value = "name", required = false) String name,
        @Parameter(name = "完整结算企业名称, 精确匹配")
        @RequestParam(value = "corpName", required = false) String corpName,
        @Parameter(name = "完整站点名称, 精确匹配")
        @RequestParam(value = "siteName", required = false) String siteName);

    @Operation(summary = "获取详情")
    @GetMapping(value = "/open/partner/client/getDetail")
    Mono<ObjectResponse<ClientOperaterVo>> getDetail(
        @RequestParam(value = "partnerId") Long partnerId);

    @Operation(summary = "修改停充超时配置")
    @GetMapping(value = "/open/partner/client/editBillingBack")
    Mono<BaseResponse> editBillingBack(@RequestParam(value = "partnerId") Long partnerId,
        @RequestParam(value = "billingBack") Boolean billingBack);

    @Operation(summary = "禁用结算账户")
    @GetMapping(value = "/open/partner/client/switchAccountStatus")
    Mono<BaseResponse> switchAccountStatus(@RequestParam(value = "accountId") Long accountId);

    @Operation(summary = "新增结算账户")
    @PostMapping(value = "/open/partner/client/addAccount")
    Mono<BaseResponse> addAccount(@RequestBody AddAccountParam param);

    @Operation(summary = "是否变更了可用场站 true(变更了) false(未变更)")
    @PostMapping(value = "/open/partner/client/isModifiedSiteList")
    Mono<ObjectResponse<Boolean>> isModifiedSiteList(@RequestBody CheckAccountParam param);

    @Operation(summary = "修改结算账户的可用站点")
    @PostMapping(value = "/open/partner/client/editAccount")
    Mono<BaseResponse> editAccount(@RequestBody EditAccountParam param);

    @Operation(summary = "检查运营商是否重复配置该场站")
    @PostMapping(value = "/open/partner/client/checkSiteforDuplicates")
    Mono<ObjectResponse<PartnerPo>> checkSiteforDuplicates(@RequestBody CheckAccountParam param);

    @GetMapping(value = "/open/partner/client/getByCode")
    Mono<ObjectResponse<PartnerPo>> getByCode(@RequestParam(value = "code") String code);

    @Operation(summary = "同步互联互通场站最新价格策略到孙建飞库")
    @GetMapping(value = "/open/partner/client/syncHlhtStationPrice")
    Mono<BaseResponse> syncHlhtStationPrice(
        @RequestBody List<DataSyncSitePrices> dataSyncSitePricesList);

    @Operation(summary = "互联互通订单开票处理")
    @PostMapping(value = "/open/partner/client/syncHlhtInvoice")
    Mono<BaseResponse> syncHlhtInvoice(@RequestBody InvoiceHlhtCallbackVo invoiceHlhtCallbackVo);

    /**
     * 同步场站信息
     *
     * @param topCommId
     * @param partnerCode
     * @param stationIdList
     * @return
     */
    @PostMapping("/open/cec/{topCommId}/syncSitesInfo")
    Mono<BaseResponse> syncSitesInfo(@PathVariable(value = "topCommId") Long topCommId,
        @RequestParam(value = "partnerCode", required = false) String partnerCode,
        @RequestBody(required = false) List<String> stationIdList);

    @GetMapping("/open/site/getPartnerInfoBySiteId")
    Mono<ListResponse<HlhtSiteVo>> getPartnerInfoBySiteId(
        @RequestParam(value = "siteId") String siteId);

    // 发送企业微信消息
    @PostMapping("/open/corpWx/{topCommId}/pushMsg")
    Mono<ObjectResponse<String>> corpWxPushMsg(
        @PathVariable(value = "topCommId") Long topCommId,
        @RequestBody CorpWxPushMsgParam param);

    // 发送企业微信文本卡片消息
    @PostMapping("/open/corpWx/{topCommId}/sendMsg")
    Mono<ObjectResponse<String>> sendMsg(
        @PathVariable(value = "topCommId") Long topCommId,
        @RequestBody CorpWxSendMsgParam param);

    // 发送企业微信文本卡片通用消息
    @PostMapping("/open/corpWx/{topCommId}/sendMsgCommon")
    Mono<ObjectResponse<String>> sendMsgCommon(
        @PathVariable(value = "topCommId") Long topCommId,
        @RequestBody CorpWxSendMsgCommonParam param);

    // 发送定时企业微信消息
    @PostMapping("/open/corpWx/{topCommId}/sendScheduledMsg")
    Mono<ObjectResponse<String>> sendScheduledMsg(
        @PathVariable(value = "topCommId") Long topCommId,
        @RequestBody CorpWxSendScheduledMsgParam param);
}
