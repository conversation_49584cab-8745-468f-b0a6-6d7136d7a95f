package com.cdz360.biz.utils.feign.order;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.order.param.NotInSettlementOrderParam;
import com.cdz360.biz.model.trading.contract.param.ContractListParam;
import com.cdz360.biz.model.trading.contract.vo.ContractVo;
import com.cdz360.biz.model.trading.coupon.vo.DiscountVo;
import com.cdz360.biz.model.trading.order.param.GetOrderDetailParam;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.cdz360.biz.model.trading.order.vo.PayBillVo;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceRecordDetail;
import com.chargerlinkcar.framework.common.domain.order.ChargerOrderWithBLOBs;
import com.chargerlinkcar.framework.common.domain.vo.ChargerDetailVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderBiVo;
import java.math.BigDecimal;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, fallbackFactory = TdChargeFeignHystrix.class
)
public interface DataCoreOrderFeignClient {

    // 确认是否存在异常订单未加入到指定账单
    @PostMapping(value = "/dataCore/order/notInSettlementOrders")
    Mono<ObjectResponse<Integer>> notInSettlementOrders(
        @RequestBody NotInSettlementOrderParam param);

    /**
     * 获取该商户列表下的客户 订单详情
     *
     * @return
     */
    @RequestMapping(value = "/dataCore/orderData/getOrderDetail", method = RequestMethod.POST)
    Mono<ObjectResponse<ChargerOrderWithBLOBs>> getOrderDetail(
        @RequestBody GetOrderDetailParam param);

    /**
     * 根据订单编号获取订单相关信息
     *
     * @param orderNo
     * @return
     */
    @GetMapping(value = "/dataCore/orderData/samplingInfo")
    Mono<ListResponse<ChargerDetailVo>> getOrderSamplingInfo(
        @RequestParam("orderNo") String orderNo);

    @GetMapping(value = "/dataCore/activity/discountInfo")
    Mono<ObjectResponse<DiscountVo>> discountInfo(@RequestParam("commId") Long commId,
        @RequestParam("amount") BigDecimal amount);

    @PostMapping(value = "/dataCore/invoice/includeChargerOrderBi")
    Mono<ObjectResponse<OrderBiVo>> includeChargerOrderBi(@RequestBody ListChargeOrderParam param);

    @GetMapping(value = "/dataCore/invoice/corpInvoiceRecordDetail")
    Mono<ObjectResponse<CorpInvoiceRecordDetail>> corpInvoiceRecordDetail(
        @RequestParam(value = "applyNo") String applyNo);

    @PostMapping("/dataCore/contract/getContractList")
    Mono<ListResponse<ContractVo>> getContractList(@RequestBody ContractListParam param);

    @PostMapping("/dataCore/site/getSiteByBillNoList")
    Mono<ListResponse<String>> getSiteByBillNoList(@RequestBody List<String> billNoList);

//    @PostMapping("/dataCore/paybill/orderList")
//    Mono<ListResponse<PayBillVo>> payBillList(@RequestBody PayBillParam payBillParam);
}
