package com.cdz360.biz.utils.feign.site;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.cus.settlement.vo.SettlementVo;
import com.cdz360.biz.model.iot.param.SiteBiParam;
import com.cdz360.biz.model.trading.bi.dto.SiteAccountProfitDto;
import com.cdz360.biz.model.trading.bi.dto.SiteProfitDto;
import com.cdz360.biz.model.trading.bi.param.AccountSiteIncomeParam;
import com.cdz360.biz.model.trading.site.param.ListBiSiteOrderParam;
import com.cdz360.biz.model.trading.site.param.SiteIncomeExpenseParam;
import com.cdz360.biz.model.trading.site.po.BiSiteGcDailyPo;
import com.cdz360.biz.model.trading.site.vo.BiSiteOrderVo;
import com.cdz360.biz.model.trading.site.vo.SiteIncomeExpenseVo;
import com.cdz360.biz.model.trading.site.vo.SiteOrderAccountBi;
import com.cdz360.biz.model.trading.site.vo.SiteOrderHlhtAccountBi;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDataBiVo;
import java.time.LocalDate;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_BI,
    fallbackFactory = BiSiteOrderFeignHystrix.class
)
public interface BiSiteOrderFeignClient {

    // 通过指定时间计算最近六个月的收支汇总信息(用于OA电费支付历史台账)
    @GetMapping("/bi/site/sixMonthIncomeExpense")
    Mono<ListResponse<SiteIncomeExpenseVo>> siteSixMonthIncomeExpense(
        @RequestParam("siteId") String siteId,
        @RequestParam("month") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate month);

    // 获取场站收支按月汇总信息(用于OA电费支付历史台账)
    @PostMapping("/bi/site/incomeExpenseSumByMonth")
    Mono<ListResponse<SiteIncomeExpenseVo>> siteIncomeExpenseSumByMonth(
        @RequestBody SiteIncomeExpenseParam param);

    // 场站消费统计
    @PostMapping(value = "/bi/site/biSiteOrderList")
    Mono<ListResponse<BiSiteOrderVo>> findBiSiteOrderList(
        @RequestBody ListBiSiteOrderParam param);

    // 客群趋势数据获取
    @PostMapping(value = "/bi/site/biSiteOrderAccount")
    Mono<ObjectResponse<SiteOrderAccountBi>> getBiSiteOrderAccount(@RequestBody SiteBiParam param);

    // 互联企客趋势数据获取
    @PostMapping(value = "/bi/site/biSiteOrderHlhtAccount")
    Mono<ObjectResponse<SiteOrderHlhtAccountBi>> getBiSiteOrderHlhtAccount(
        @RequestBody SiteBiParam param);

    //    @Operation(summary = "按月统计场站收入")
    @PostMapping("/bi/site/profit/accountSiteIncome")
    Mono<ObjectResponse<SiteProfitDto>> accountSiteIncome(
        @RequestBody AccountSiteIncomeParam param);

    // 通过后付费账单统计场站月度收入
    @PostMapping("/bi/site/profit/accountSitePostPayCorpIncome")
    Mono<ListResponse<SiteAccountProfitDto>> accountSitePostPayCorpIncome(
        @RequestBody SettlementVo sett);

    //    @Operation(summary = "计算(国充)场站每日运营收入数据")
    @PostMapping("/bi/site/profit/accountSiteDailyIncome")
    Mono<ObjectResponse<BiSiteGcDailyPo>> accountSiteDailyIncome(
        @RequestParam(value = "siteId") String siteId,
        @RequestParam(value = "date", required = false) String date);


    //    @Operation(summary = "查询订单信息")
    @PostMapping("/bi/order/getChargerOrderDetailData")
    Mono<ObjectResponse<ChargerOrderDataBiVo>> getChargerOrderDetailData(
        @RequestBody ChargerOrderParam chargerOrderParam);
}
