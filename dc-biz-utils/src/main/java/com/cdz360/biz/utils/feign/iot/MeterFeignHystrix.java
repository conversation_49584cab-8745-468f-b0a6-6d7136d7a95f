package com.cdz360.biz.utils.feign.iot;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.iot.param.MeterRecordBiParam;
import com.cdz360.biz.model.trading.meter.param.MeterListParam;
import com.cdz360.biz.model.trading.meter.po.BiMeterPo;
import com.cdz360.biz.model.trading.meter.po.DeviceMeterPo;
import com.cdz360.biz.model.trading.meter.vo.MeterEvseVo;
import com.cdz360.biz.model.trading.meter.vo.MeterVo;
import com.cdz360.biz.model.trading.meter.vo.SiteMeterVo;
import com.cdz360.biz.model.trading.site.po.BiSiteMeterSummaryDto;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @since 9/24/2020 3:53 PM
 * <AUTHOR>
 */
@Slf4j
@Component
public class MeterFeignHystrix implements FallbackFactory<MeterFeignClient> {

    @Override
    public MeterFeignClient create(Throwable cause) {
        return new MeterFeignClient() {
            @Override
            public ListResponse<MeterEvseVo> getMeterList(MeterListParam param) {
                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_METER);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse createMeter(MeterEvseVo param) {
                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_METER);
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse updateMeter(MeterEvseVo param) {
                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_METER);
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse deleteMeter(Long id) {
                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_METER);
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<Integer> refreshMeterStatus(Integer ttl) {
                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_METER);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<Boolean> getCommoditySync() {
                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_METER);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<BiMeterPo> getPrevDayReading(Date date) {
                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_METER);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<DeviceMeterPo> getEvseMeterList(Long meterId) {
                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_METER);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<MeterVo> findMeterList(MeterListParam param) {
                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_METER);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<BiSiteMeterSummaryDto> getBiSiteMeterRecord(
                MeterRecordBiParam param) {
                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_METER);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SiteMeterVo> getSiteMeterList(MeterListParam meterListParam) {
                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_METER);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<String> getSiteIdWithMeterList(List<String> siteIdList) {
                log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_METER);
                return RestUtils.serverBusy4ListResponse();
            }
        };

    }
}