package com.cdz360.biz.utils.feign.auth;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.cus.message.po.MessagePo;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.sys.param.GetYwUserParam;
import com.cdz360.biz.model.sys.vo.SubscribeOrderVo;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.domain.CommercialManage;
import java.util.Optional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_AUTH, fallbackFactory = AuthCommFeignHystrix.class
)
public interface AuthCommFeignClient {
    /**
     * 获取商户信息
     *
     * @param commId
     * @return
     */
    @GetMapping("/api/comm/getCommercial")
    Mono<ObjectResponse<Commercial>> getCommercial(@RequestParam(value = "commId") Long commId);

    @GetMapping("/api/commercialManage/getCommercialManage")
    Mono<ObjectResponse<CommercialManage>> getCommercialManage(@RequestParam(value = "topCommId") Long topCommId);

    // 获取运维人员信息
    @PostMapping(value = "/api/sys/user/getYwUser")
    Mono<ObjectResponse<Optional<SysUserVo>>> getYwUser(@RequestBody GetYwUserParam param);

    // 添加站内信
    @PostMapping("/api/msg/addMessage")
    Mono<ObjectResponse<Integer>> addMessage(
        @RequestParam(value = "token", required = false) String token,
        @RequestBody MessagePo message);

    @GetMapping("/api/sub/getOrderById")
    Mono<ObjectResponse<SubscribeOrderVo>> getOrderById(@RequestParam(value = "payNo") String payNo);

    // 两个用于的团队标签是否一致
    @GetMapping("/data/users/sameTeamCatalog")
    Mono<ObjectResponse<Boolean>> sameTeamCatalog(
        @RequestParam("uid1") Long uid1,
        @RequestParam("uid2") Long uid2);
}
