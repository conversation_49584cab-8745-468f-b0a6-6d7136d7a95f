package com.cdz360.biz.utils.feign.iot;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.iot.param.ListCtrlParam;
import com.cdz360.biz.model.trading.iot.po.SrsPo;
import com.cdz360.biz.model.trading.iot.vo.DeviceInfoVo;
import com.cdz360.biz.model.trading.iot.vo.RedisSrsRtData;
import java.util.List;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class SrsFeignHystrix implements FallbackFactory<SrsFeignClient> {

    @Override
    public SrsFeignClient apply(Throwable throwable) {
        log.debug("err = {}", throwable.getMessage(), throwable);

        return new SrsFeignClient() {


            @Override
            public Mono<ListResponse<SrsPo>> findSrsList(ListCtrlParam param) {
                log.error("【服务熔断】 辐射仪列表. Service = {}, api = findSrsList. param = {}",
                    DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, param);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<DeviceInfoVo>> findSrsVoList(List<String> siteIdList) {
                log.error(
                    "【服务熔断】 获取场站控制器及挂载辐射仪个数. Service = {}, api = findSrsVoList. siteIdList.size = {}",
                    DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, siteIdList.size());
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<RedisSrsRtData>> srsInfoInTime(String dno) {
                log.error("【服务熔断】 辐射仪实时数据. Service = {}, api = gtiInfoInTime. dno = {}",
                    DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, dno);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

        };
    }

    @Override
    public <V> Function<V, SrsFeignClient> compose(
        Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_DEVICE_MGM);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(Function<? super SrsFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_DEVICE_MGM);
        return null;
    }
}
