package com.cdz360.biz.utils.feign.user;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceInfoVo;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import org.springframework.web.bind.annotation.*;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_USER,
        fallbackFactory = UserCorpFeignHystrix.class)
public interface UserCorpFeignClient {


    /**
     * 根据phone查询集团用户
     *
     * @param rBlocUser(phone和blocUserId必传,userId选传)
     * @return
     */
    @PostMapping("/api/rblocUser/selectRBlocUserByPhone")
    Mono<ObjectResponse<RBlocUser>> selectRBlocUserByPhone(@RequestBody RBlocUser rBlocUser);


//    @RequestMapping(value = "/api/blocWallet/frozenBlocAmount", method = RequestMethod.POST)
//    ObjectResponse<Integer> frozenBlocAmount(@RequestParam(value = "orderNo") String orderNo,
//                                             @RequestParam(value = "id") Long id,
//                                             @RequestParam(value = "frozenAmount") BigDecimal frozenAmount,
//                                             @RequestParam(value = "sourceId") Integer sourceId,
//                                             @RequestParam(value = "realTimeFlag") boolean realTimeFlag);

    @RequestMapping(value = "/api/blocWallet/frozenBlocAmount", method = RequestMethod.POST)
    Mono<ObjectResponse<BigDecimal>> frozenBlocAmount(@RequestParam(value = "orderNo") String orderNo,
                                                      @RequestParam(value = "id") Long id,
                                                      @RequestParam(value = "frozenAmount") BigDecimal frozenAmount,
                                                      @RequestParam(value = "sourceId") Integer sourceId,
                                                      @RequestParam(value = "realTimeFlag") boolean realTimeFlag);


    @PostMapping(value = "/api/rblocUser/moveCorp")
    Mono<ObjectResponse<Integer>> moveCorp(@RequestParam("corpId") Long corpId, @RequestParam("commId") Long commId);

    @PostMapping(value = "/api/user/setLatestOrder")
    Mono<ObjectResponse<Boolean>> setLatestOrder(@RequestParam("userId") Long userId,
                                                 @RequestParam("orderNo") String orderNo);

    @GetMapping(value = "/api/invoice/getCorpInvoiceInfo")
    Mono<ObjectResponse<CorpInvoiceInfoVo>> getCorpInvoiceInfo(@RequestParam(value = "corpId") Long corpId);

}
