package com.cdz360.biz.utils.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "dc.hlht")
public class HlhtCfg {

    private List<String> operatorCodeList;

    private List<ThirdQrCode> thirdQrCodeList;

    private List<QrCodeOperator> qrCodeOperatorList;

    @Data
    public static class ThirdQrCode {
        private String thirdQrCodeRegex;
        private String thirdQrCodeOperatorId;
    }

    @Data
    public static class QrCodeOperator {
        private String qrCodePrefix;
        private String operatorId;
    }
}
