package com.cdz360.biz.utils.feign.bill;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.model.trading.bill.dto.ZftThirdOrderDto;
import com.cdz360.biz.model.trading.bill.param.ListZftDailyBillParam;
import com.cdz360.biz.model.trading.bill.param.ListZftThirdOrderParam;
import com.cdz360.biz.model.trading.bill.vo.TradeOrderBi;
import com.cdz360.biz.model.trading.bill.vo.ZftDailyBillVo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE,
        fallbackFactory = DailyBillDataCoreFeignHystrix.class
)
public interface DailyBillDataCoreFeignClient {

    /**
     * 获取支付平台账单记录
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/dataCore/findAllZftDailyBill")
    Mono<ListResponse<ZftDailyBillVo>> findAllZftDailyBill(
            @RequestBody ListZftDailyBillParam param);

    /**
     * 获取支付平台账单订单列表
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/dataCore/findAllZftThirdOrder")
    Mono<ListResponse<ZftThirdOrderDto>> findAllZftThirdOrder(
            @RequestBody ListZftThirdOrderParam param);

    /**
     * 企业直付交易记录统计
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/dataCore/tradeOrderBi")
    Mono<ListResponse<TradeOrderBi>> tradeOrderBi(
            @RequestBody ListZftThirdOrderParam param);

    /**
     * 定时下载账单
     *
     * @return
     */
    @GetMapping(value = "/api/dataCore/downloadZftDailyBill")
    Mono<BaseResponse> downloadZftDailyBill();

    /**
     * 对账重试
     *
     * @param topCommId
     * @param dailyBillId
     * @return
     */
    @GetMapping(value = "/api/dataCore/retryCheckBill")
    Mono<BaseResponse> retryCheckBill(@RequestParam(value = "topCommId") Long topCommId,
                                      @RequestParam(value = "dailyBillId") Long dailyBillId);
}
