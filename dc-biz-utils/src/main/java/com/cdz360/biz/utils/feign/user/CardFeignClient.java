package com.cdz360.biz.utils.feign.user;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.chargerlinkcar.framework.common.domain.vo.AccountInfoVo;
import com.chargerlinkcar.framework.common.domain.vo.Card;
import org.springframework.web.bind.annotation.*;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

import java.util.List;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_USER,
        fallbackFactory = CardFeignHystrix.class
)
public interface CardFeignClient {

    @RequestMapping(value = "/api/card/queryCardNoListByBlocUserId")
    Mono<ListResponse<String>> queryCardNoListByBlocUserId(@RequestParam(value = "blocUserId") Long blocUserId);

    @RequestMapping(value = "/api/card/selectBlocUserNameByCardNos", method = RequestMethod.POST)
    Mono<ListResponse<Card>> selectBlocUserNameByCardNos(@RequestBody List<String> cardNos);


    /**
     * 根据卡号获取卡信息
     *
     * @return
     */
    @RequestMapping(value = "/api/card/getCardByCardNo", method = RequestMethod.POST)
    Mono<ObjectResponse<Card>> getCardByCardNo(@RequestParam(value = "cardNo") String cardNo,
                                               @RequestParam(value = "topCommId") Long topCommId);


    /**
     * 无固定条件
     *
     * @param cardNo
     * @return
     */
    @PostMapping("/api/card/queryCardByCardNo")
    Mono<ObjectResponse<Card>> queryCardByCardNo(@RequestParam(value = "cardNo") String cardNo);

    /**
     * 根据卡号获取卡账户信息
     *
     * @param cardNo
     * @return
     */
    @GetMapping("/api/card/getAccountByCardNo")
    Mono<ObjectResponse<AccountInfoVo>> getAccountByCardNo(@RequestParam(value = "cardNo") String cardNo,
                                                           @RequestParam(value = "topCommId") Long topCommId);

    /**
     * 根据逻辑卡号获取物流卡号
     *
     * @return
     */
    @RequestMapping(value = "/api/card/getCardChipNoByCardNo", method = RequestMethod.POST)
    Mono<ObjectResponse<String>> getCardChipNoByCardNo(@RequestParam(value = "cardNo") String cardNo);


}
