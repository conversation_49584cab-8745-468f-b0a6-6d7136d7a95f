package com.cdz360.biz.utils.feign.user;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.biz.ds.cus.ro.balance.param.BalanceApplicationCheckParam;
import com.cdz360.biz.ess.model.param.EnableAccountParam;
import com.cdz360.biz.model.commercialUser.param.UserRegisterParam;
import com.cdz360.biz.model.common.param.BaseListParam;
import com.cdz360.biz.model.cus.auth.dto.CusAuthReqEx;
import com.cdz360.biz.model.cus.auth.dto.CusAuthRes;
import com.cdz360.biz.model.cus.balance.po.BalanceApplicationPo;
import com.cdz360.biz.model.cus.balance.vo.BalanceApplicationVo;
import com.cdz360.biz.model.cus.basic.vo.UserVo;
import com.cdz360.biz.model.cus.card.param.CardAmountSyncParam;
import com.cdz360.biz.model.cus.card.vo.CardAmountSyncVo;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.discount.dto.SelectDiscount;
import com.cdz360.biz.model.cus.discount.dto.SiteDiscount;
import com.cdz360.biz.model.cus.discount.param.ChangeCorpCommIdRmParam;
import com.cdz360.biz.model.cus.discount.po.DiscountPricePrimaryKey;
import com.cdz360.biz.model.cus.discount.po.DiscountStrategyPo;
import com.cdz360.biz.model.cus.discount.vo.ChangeCorpCommIdDiscountList;
import com.cdz360.biz.model.cus.discount.vo.DiscountStrategyVo;
import com.cdz360.biz.model.cus.post.param.CommentParam;
import com.cdz360.biz.model.cus.post.param.CusPostChangeStatusParam;
import com.cdz360.biz.model.cus.post.param.CusPostChangeTagParam;
import com.cdz360.biz.model.cus.post.param.CusPostReplyParam;
import com.cdz360.biz.model.cus.post.param.ListCusPostParam;
import com.cdz360.biz.model.cus.post.vo.CusPostVo;
import com.cdz360.biz.model.cus.score.dto.ScoreSettingDiscountDto;
import com.cdz360.biz.model.cus.score.dto.ScoreSettingDto;
import com.cdz360.biz.model.cus.score.param.ScoreUpdateParam;
import com.cdz360.biz.model.cus.score.po.ScoreSettingPo;
import com.cdz360.biz.model.cus.settlement.param.ListSettlementParam;
import com.cdz360.biz.model.cus.settlement.vo.SettlementVo;
import com.cdz360.biz.model.cus.site.param.ListSiteBlacklistParam;
import com.cdz360.biz.model.cus.site.param.SiteBlacklistEnableParam;
import com.cdz360.biz.model.cus.site.param.SiteWhitelistParam;
import com.cdz360.biz.model.cus.site.po.SiteWhitelistPo;
import com.cdz360.biz.model.cus.site.vo.SiteBlacklistVo;
import com.cdz360.biz.model.cus.site.vo.SiteWhitelistVo;
import com.cdz360.biz.model.cus.soc.po.UserSocTimePo;
import com.cdz360.biz.model.cus.user.dto.CusBiDto;
import com.cdz360.biz.model.cus.user.dto.CusSampleDto;
import com.cdz360.biz.model.cus.user.dto.UserAndBalanceAndTokenVo;
import com.cdz360.biz.model.cus.user.param.AddUserParam;
import com.cdz360.biz.model.cus.user.param.EssRegisterUserParam;
import com.cdz360.biz.model.cus.user.param.ListCustomerParam;
import com.cdz360.biz.model.cus.user.param.UserConditionVo;
import com.cdz360.biz.model.cus.user.po.UserOpenidPo;
import com.cdz360.biz.model.cus.user.po.UserPo;
import com.cdz360.biz.model.cus.vin.param.VINCarNoParam;
import com.cdz360.biz.model.cus.vin.param.VinSearchParam;
import com.cdz360.biz.model.cus.vo.SiteCommentTotal;
import com.cdz360.biz.model.merchant.param.ListCommercialParam;
import com.cdz360.biz.model.trading.cus.param.CusPayBillListParam;
import com.cdz360.biz.model.trading.cus.param.CusRefundAllParam;
import com.cdz360.biz.model.trading.cus.param.CusRefundOrderListParam;
import com.cdz360.biz.model.trading.cus.vo.CusPayBillVo;
import com.cdz360.biz.model.trading.cus.vo.CusRefundOrderVo;
import com.cdz360.biz.model.trading.order.po.ChargerOrderPo;
import com.cdz360.biz.model.trading.order.po.PayBillPo;
import com.chargerlinkcar.framework.common.constant.AccountType;
import com.chargerlinkcar.framework.common.constant.TransTypeEnum;
import com.chargerlinkcar.framework.common.domain.BlocUserDto;
import com.chargerlinkcar.framework.common.domain.MsgTemplatePO;
import com.chargerlinkcar.framework.common.domain.OrderFeeRenewalResponse;
import com.chargerlinkcar.framework.common.domain.OrderOvertimeParkingBi;
import com.chargerlinkcar.framework.common.domain.PointLog;
import com.chargerlinkcar.framework.common.domain.param.AuthMediaParam;
import com.chargerlinkcar.framework.common.domain.param.MerchantBalanceParam;
import com.chargerlinkcar.framework.common.domain.vo.AuthMediaResult;
import com.chargerlinkcar.framework.common.domain.vo.BalanceRollbackParam;
import com.chargerlinkcar.framework.common.domain.vo.CheckPhoneBindVo;
import com.chargerlinkcar.framework.common.domain.vo.CommCusRef;
import com.chargerlinkcar.framework.common.domain.vo.OrderBiVo;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUserVo;
import com.chargerlinkcar.framework.common.domain.vo.UserPropVO;
import com.chargerlinkcar.framework.common.domain.vo.VinDto;
import io.swagger.v3.oas.annotations.Operation;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_USER,
    fallbackFactory = UserFeignHystrix.class
)
public interface UserFeignClient {


    /**
     * 用户通过手机(或验证码)登录或者注册
     */
    @RequestMapping(value = "/api/user/loginOrRegister", method = RequestMethod.POST)
    Mono<ObjectResponse<UserAndBalanceAndTokenVo>> loginOrRegister(
        @RequestBody UserConditionVo param);

    /**
     * 根据ID 查询用户级别信息
     *
     * @param userId
     * @return
     */
    @RequestMapping(value = "/api/user/queryByUidAndCommId", method = RequestMethod.GET)
    Mono<ObjectResponse<UserVo>> queryUserByUidAndCommId(
        @RequestParam(value = "userId") Long userId,
        @RequestParam(value = "commId", required = false) Long commId);

    /**
     * 根据用户id获得用户所属商户
     *
     * @param userId
     * @return
     */
    @RequestMapping(value = "/api/user/findInfoByUid", method = RequestMethod.POST)
    Mono<ObjectResponse<UserVo>> findInfoByUid(@RequestParam(value = "userId") Long userId,
        @RequestParam(value = "topCommId", required = false) Long topCommId,
        @RequestParam(value = "commIdChain", required = false) String commIdChain);


    @PostMapping(value = "/api/user/loginByWxUnionId")
    Mono<ObjectResponse<UserAndBalanceAndTokenVo>> loginByWxUnionId(
        @RequestBody UserConditionVo param);


    @PostMapping(value = "/api/user/loginByAlipayUserId")
    Mono<ObjectResponse<UserAndBalanceAndTokenVo>> loginByAlipayUserId(
        @RequestBody UserConditionVo param);

    /**
     * 查询用户第三方关联帐号信息
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/user/getUserOpenid")
    Mono<ObjectResponse<UserOpenidPo>> getUserOpenid(@RequestBody UserOpenidPo param);


    @PostMapping(value = "/api/user/getUserOpenidByUid")
    Mono<ObjectResponse<UserOpenidPo>> getUserOpenidByUid(@RequestBody UserOpenidPo param);

    /**
     * 修改用户第三方关联帐号信息
     */
    @PostMapping(value = "/api/user/addOrUpdateUserOpenid")
    Mono<BaseResponse> addOrUpdateUserOpenid(@RequestBody UserOpenidPo param);

    @Operation(summary = "查询支付宝的即插即充服务状态接口,将已开通的车辆信息同步到数据库")
    @PostMapping(value = "/api/user/syncAlipayVin")
    Mono<BaseResponse> syncAlipayVin(@RequestParam(value = "topCommId") Long topCommId,
        @RequestParam(value = "appId") String appId,
        @RequestParam(value = "openid") String openid,
        @RequestParam(value = "uid") Long uid);

    /**
     * 集团客户id查询集团客户详情
     *
     * @param userId
     * @return
     */
    @RequestMapping(value = "/api/rblocUser/findRBlocUserByUserId", method = RequestMethod.GET)
    Mono<ListResponse<RBlocUserVo>> findRBlocUserByUserId(
        @RequestParam(value = "userId") Long userId,
        @RequestParam(value = "commIdChain", required = false) String commIdChain);

    @RequestMapping(value = "/api/rblocUser/findRBlocUserVoById", method = RequestMethod.GET)
    Mono<ObjectResponse<RBlocUserVo>> findRBlocUserVoById(
        @RequestParam(value = "rBlocUserId") Long rBlocUserId);

    @RequestMapping(value = "/api/balance/payChargeOrder", method = RequestMethod.POST)
    Mono<BaseResponse> payChargeOrder(@RequestBody ChargerOrderPo chargerOrder);

    @RequestMapping(value = "/api/rblocUser/selectRBlocUserIds", method = RequestMethod.POST)
    Mono<ListResponse<RBlocUser>> selectRBlocUserIds(@RequestBody List<Long> ids);

    @GetMapping("/api/rblocUser/findRBlocUserById")
    Mono<ObjectResponse<RBlocUser>> findRBlocUserById(
        @RequestParam(value = "rBlocUserId") Long rBlocUserId);

    @PostMapping("/api/rblocUser/insertRBlocUser")
    Mono<BaseResponse> insertRBlocUser(@RequestBody RBlocUser rBlocUser);

    @GetMapping("/api/corp/getCorp")
    Mono<ObjectResponse<CorpPo>> getCorp(@RequestParam(value = "corpId") Long corpId);


    @RequestMapping(value = "/api/vin/selectCarNoByVins", method = RequestMethod.POST)
    Mono<ObjectResponse> selectCarNoByVins(@RequestBody VINCarNoParam param);


    @GetMapping(value = "/api/msg/template/getMsgTemplate")
    Mono<ObjectResponse<MsgTemplatePO>> getMsgTemplate(@RequestParam("key") String key,
        @RequestParam("topCommId") Long topCommId);

    /**
     * 确认授信账户是否时候结算方式
     *
     * @param rBlocUserId
     * @return
     */
    @GetMapping(value = "/api/corp/checkCorpSettlementByRBlocUserId")
    Mono<ObjectResponse<Boolean>> checkCorpSettlementByRBlocUserId(
        @RequestParam(value = "rBlocUserId") Long rBlocUserId);


    @RequestMapping(value = "/api/cus/auth", method = RequestMethod.POST)
    Mono<ObjectResponse<CusAuthRes>> auth(
        @RequestParam(value = "passcode", required = false) String passcode,
        @RequestBody CusAuthReqEx cusAuthReq);

    /**
     * 查询商户用户关联表
     *
     * @param userPhone   用户手机号
     * @param commId      商户id
     * @param enable      状态（1启用，0禁用）
     * @param commIdChain 当前商户及子商户id列表
     * @return
     */
    @RequestMapping(value = "/api/user/queryCommCusRefs", method = RequestMethod.GET)
    Mono<ListResponse<CommCusRef>> queryCommCusRefs(@RequestParam("_index") Integer _index,
        @RequestParam("_size") Integer _size,
        @RequestParam(value = "userPhone") String userPhone,
        @RequestParam(value = "enable") Boolean enable,
        @RequestParam(value = "commId") Long commId,
        @RequestParam(value = "userId") Long userId,
        @RequestParam(value = "cusName") String cusName,
        @RequestParam(value = "commIdChain") String commIdChain);


    @PostMapping(value = "/api/blocWallet/appendFrozenBlocAmount")
    Mono<ObjectResponse<BigDecimal>> appendFrozenBlocAmount(
        @RequestParam("orderNo") String orderNo,
        @RequestParam("rBlocUserId") Long rBlocUserId,
        @RequestParam("freeze") BigDecimal freeze,
        @RequestParam("sourceId") Integer sourceId);


    /**
     * 根据用户ids获得用户所属商户
     *
     * @param userIds
     * @return
     */
    @RequestMapping(value = "/api/user/findInfoByUids", method = RequestMethod.POST)
    Mono<ObjectResponse<Map<Long, UserVo>>> findInfoByUids(@RequestBody List<Long> userIds);


    @RequestMapping(value = "/api/rblocUser/queryIdsByBlocUserName", method = RequestMethod.GET)
    Mono<ListResponse<Long>> queryIdsByBlocUserName(
        @RequestParam(value = "blocUserName") String blocUserName,
        @RequestParam(value = "isSureBlocUserName") Boolean isSureBlocUserName);


    /**
     * 根据账户类型以及账户ID鉴权获取可用余额和冻结金额
     *
     * @return UserBalanceVo
     */
    @RequestMapping(value = "/api/user/authByBalanceIdAndPayType", method = RequestMethod.POST)
    Mono<ObjectResponse<AuthMediaResult>> authByBalanceIdAndPayType(
        @RequestBody AuthMediaParam param);


    @PostMapping(value = "/api/user/getCusSampleList")
    Mono<ListResponse<CusSampleDto>> getCusSampleList(@RequestBody ListCustomerParam param);


    /**
     * 企业平台数据清洗 旧数据(在线卡，VIN码，授信客户)转移 自动创建一级组织 企业管理平台第一版（2020-02）上线时
     *
     * @return
     */
    @GetMapping(value = "/api/card/user/corpDataCleaning")
    Mono<BaseResponse> corpDataCleaning();


    @RequestMapping(value = "/api/balance/rollbackBalanceFrozenMoney", method = RequestMethod.POST)
    Mono<BaseResponse> rollbackBalanceFrozenMoney(@RequestBody BalanceRollbackParam param);

    @GetMapping(value = "/api/merchantBalance/findById")
    Mono<ObjectResponse<CommCusRef>> merFindById(@RequestParam("id") Long id);

    @RequestMapping(value = "/api/merchantBalance/rollbackMerchantFrozenMoney", method = RequestMethod.POST)
    Mono<BaseResponse> rollbackMerchantFrozenMoney(@RequestBody BalanceRollbackParam param);

    @RequestMapping(value = "/api/blocWallet/rollbackBlocFrozenMoney", method = RequestMethod.POST)
    Mono<BaseResponse> rollbackBlocFrozenMoney(@RequestBody BalanceRollbackParam param);


    @RequestMapping(value = "/api/blocWallet/frozenRenewalBlocAmount", method = RequestMethod.POST)
    Mono<ObjectResponse<OrderFeeRenewalResponse>> frozenRenewalBlocAmount(
        @RequestParam(value = "settlementType", required = false) SettlementType settlementType,
        @RequestParam(value = "orderNo") String orderNo,
        @RequestParam(value = "rBlocUserId", required = false) Long rBlocUserId,
        @RequestParam(value = "sourceId", required = false) Integer sourceId,
        @RequestParam(value = "realTimeFlag") boolean realTimeFlag,
        @RequestParam(value = "frozenAmount", required = false) BigDecimal frozenAmount);


    @RequestMapping(value = "/api/blocWallet/unFrozenBlocAmount", method = RequestMethod.POST)
    Mono<BaseResponse> unFrozenBlocAmount(
        @RequestParam(value = "accountType") AccountType accountType,
        // @RequestParam(value = "unFrozenType") UnFrozenType unFrozenType,
        @RequestParam(value = "accountId") long accountId,
        @RequestParam(value = "orderPrice") BigDecimal orderPrice,
        @RequestParam(value = "orderFrozenAmount") BigDecimal orderFrozenAmount,
        @RequestParam(value = "orderNo") String orderNo);

    @RequestMapping(value = "/api/merchantBalance/initMerchantBalance", method = RequestMethod.POST)
    Mono<BaseResponse> initMerchantBalance(@RequestBody MerchantBalanceParam param);

    @RequestMapping(value = "/api/merchantBalance/disableOrEnable", method = RequestMethod.POST)
    Mono<BaseResponse> disableOrEnableMerchantBalance(@RequestParam(value = "userId") long userId,
        @RequestParam(value = "topCommId") long topCommId,
        @RequestParam(value = "subCommId") long subCommId,
        @RequestParam(value = "status") int status);

    @RequestMapping(value = "/api/merchantBalance/updateMerchantName", method = RequestMethod.POST)
    Mono<BaseResponse> updateMerchantName(@RequestParam(value = "userId") long userId,
        @RequestParam(value = "topCommId") long topCommId,
        @RequestParam(value = "userName") String userName,
        @RequestParam(value = "commId") Long commId);

    @RequestMapping(value = "/api/balance/updateBalance", method = RequestMethod.POST)
    Mono<BaseResponse> updateBalance(@RequestParam(value = "userId") long userId,
        @RequestParam(value = "commId") long commId,
        @RequestParam(value = "amount") BigDecimal amount,
        @RequestParam(value = "transType") TransTypeEnum transType,
        @RequestParam(value = "reason", required = false) String reason,
        @RequestParam(value = "remark", required = false) String remark);

    @RequestMapping(value = "/api/balance/updateBalanceV2", method = RequestMethod.POST)
    Mono<ObjectResponse<Integer>> updateBalanceV2(@RequestBody PayBillPo po);

    @RequestMapping(value = "/api/merchantBalance/updateMerchantBalance", method = RequestMethod.POST)
    Mono<BaseResponse> updateMerchantBalance(@RequestParam(value = "userId") long userId,
        @RequestParam(value = "topCommId") long topCommId,
        @RequestParam(value = "subCommId") long subCommId,
        @RequestParam(value = "amount") BigDecimal amount,
        @RequestParam(value = "transType") TransTypeEnum transType,
        @RequestParam(value = "reason", required = false) String reason,
        @RequestParam(value = "remark", required = false) String remark);

    /**
     * 查询商户用户关联表
     *
     * @param userPhone  用户手机号
     * @param commId     商户id
     * @param enable     状态（1启用，0禁用）
     * @param commIdList 当前商户及子商户id列表
     * @return
     */
    @RequestMapping(value = "/api/user/queryCommCusRefs", method = RequestMethod.GET)
    Mono<ListResponse<CommCusRef>> queryCommCusRefs(@RequestParam("_index") Integer _index,
        @RequestParam("_size") Integer _size, @RequestParam(value = "userPhone") String userPhone,
        @RequestParam(value = "enable") Integer enable,
        @RequestParam(value = "commId") Long commId,
        @RequestParam(value = "userId") Long userId,
        @RequestParam(value = "commIdList") List<Long> commIdList);

    @RequestMapping(value = "/api/user/addUser", method = RequestMethod.POST)
    Mono<ObjectResponse<Long>> addUser(@RequestBody AddUserParam param);

    /**
     * 根据手机号和商户id查询客户信息
     *
     * @param phone
     * @param commId
     * @return
     */
    @RequestMapping(value = "/api/user/findByPhone", method = RequestMethod.POST)
    Mono<ObjectResponse<UserPropVO>> findByPhone(@RequestParam(value = "phone") String phone,
        @RequestParam(value = "commId") Long commId);

    /**
     * 查询商户用户关联表
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/user/queryCommCusRefsNoPage")
    Mono<ListResponse<CommCusRef>> queryCommCusRefsNoPage(@RequestBody ListCommercialParam param);

    /**
     * 根据条件查询
     *
     * @param rBlocUser
     * @return
     */
    @PostMapping("/api/rblocUser/findByCondition")
    Mono<ListResponse<RBlocUser>> findByCondition(@RequestBody RBlocUser rBlocUser);

    /**
     * 根据授信客户ID查询企业客户信息
     *
     * @param rBlocUserId
     * @return
     */
    @GetMapping("/api/blocUser/getByRBlocUserId")
    Mono<ObjectResponse<BlocUserDto>> getByRBlocUserId(
        @RequestParam("rBlocUserId") Long rBlocUserId);

    @GetMapping(value = "/api/merchantBalance/findByCommIdAndPhone")
    Mono<ObjectResponse<CommCusRef>> findByCommIdAndPhone(@RequestParam("commId") Long commId,
        @RequestParam("phone") String phone);

    @PostMapping(value = "/api/merchantBalance/makeSureCommCusRefEnable")
    Mono<BaseResponse> makeSureCommCusRefEnable(@RequestParam(value = "userId") long userId,
        @RequestParam(value = "subCommId") long subCommId);

    @PostMapping(value = "/api/balance/syncAllOldAccountsToCusBalance")
    Mono<BaseResponse> syncAllOldAccountsToCusBalance();

    @PostMapping("/api/user/checkPhoneWxBindAndUserStatus")
    Mono<ObjectResponse<CheckPhoneBindVo>> checkPhoneWxBindAndUserStatus(
        @RequestParam(value = "phone") String phone,
        @RequestParam(value = "commId") Long commId,
        @RequestParam(value = "nationalCode") String nationalCode,
        @RequestParam(value = "openId") String openId);

    @RequestMapping(value = "/api/merchantBalance/checkCommCusRefDisabled", method = RequestMethod.GET)
    Mono<ObjectResponse<Boolean>> checkCommCusRefDisabled(
        @RequestParam(value = "userId") Long userId,
        @RequestParam(value = "balanceId") Long balanceId);


    /**
     * 获取用户可用的充值记录列表
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/wallet/getPayBillList")
    Mono<ListResponse<CusPayBillVo>> getCusPayBillList(@RequestBody CusPayBillListParam param);

    /**
     * 用户一键退款
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/wallet/refundAll")
    Mono<BaseResponse> refundAll(@RequestBody CusRefundAllParam param);

    /**
     * 获取用户退款记录
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/wallet/getRefundOrderRecord")
    Mono<ListResponse<CusRefundOrderVo>> getRefundOrderRecord(CusRefundOrderListParam param);

    @PostMapping(value = "/api/siteBlacklist/find")
    Mono<ListResponse<SiteBlacklistVo>> findSiteBlacklist(
        @RequestBody ListSiteBlacklistParam param);

    @PostMapping(value = "/api/siteBlacklist/enable")
    Mono<BaseResponse> enable(@RequestBody SiteBlacklistEnableParam param);

    /**
     * 统计最近7天注册用户和总用户数
     *
     * @param topCommId
     * @param commIdChain
     * @return
     */
    @PostMapping(value = "/api/cus/bi/getCusBi")
    Mono<ObjectResponse<CusBiDto>> getCusBi(
        @RequestParam(value = "topCommId", required = false) Long topCommId,
        @RequestParam(value = "commIdChain", required = false) String commIdChain);


    @RequestMapping(value = "/api/vin/select", method = RequestMethod.POST)
    Mono<ListResponse<VinDto>> select(@RequestBody VinSearchParam vinSearchParam);

    /**
     * 用户超停处理
     *
     * @param bi
     */
    @PostMapping(value = "/api/siteBlacklist/appendAndEnable")
    Mono<ObjectResponse<Boolean>> appendAndEnable(@RequestBody OrderOvertimeParkingBi bi);

    /**
     * 用于获取发送APP推送消息的deviceToken
     *
     * @param appType
     * @param cusId
     * @return
     */
    @GetMapping(value = "/api/user/app/getAppDeviceToken")
    Mono<ObjectResponse<String>> getAppDeviceToken(@RequestParam("appType") Integer appType,
        @RequestParam("cusId") Long cusId);

    /**
     * 查看账单
     *
     * @param billNo
     * @return
     */
    @GetMapping(value = "/api/corp/getSettlementByBillNo")
    Mono<ObjectResponse<SettlementVo>> getSettlementByBillNo(
        @RequestParam(value = "billNo") String billNo);

    /**
     * 用户订单欠费状态
     *
     * @param cusId
     * @param debt
     * @return
     */
    @PostMapping(value = "/api/user/orderDebt")
    Mono<BaseResponse> cusOrderDebt(
        @RequestParam(value = "cusId") Long cusId,
        @RequestParam(value = "debt") boolean debt);

    @Operation(summary = "生成下一个互联运营商虚拟手机号")
    @GetMapping(value = "/api/user/getNextPartnerUserPhone")
    Mono<ObjectResponse<String>> getNextPartnerUserPhone();


    /**
     * 获取用户在某个场站的优惠配置
     *
     * @param primaryKey
     * @return
     */
    @PostMapping(value = "/api/discount/get")
    Mono<ObjectResponse<DiscountStrategyPo>> getDiscountByPrimaryKey(
        @RequestBody DiscountPricePrimaryKey primaryKey);

    /**
     * 获取用户在某个场站的优惠配置
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/api/discount/getById")
    Mono<ObjectResponse<DiscountStrategyVo>> getDiscountStrategyById(
        @RequestParam(value = "id") Long id);

    /**
     * 获取用户场站协议价配置信息
     *
     * @param selectDiscount
     * @return
     */
    @PostMapping(value = "/api/discount/findSiteDiscount")
    Mono<ListResponse<SiteDiscount>> findSiteDiscount(@RequestBody SelectDiscount selectDiscount);

    /**
     * 获取协议价 保留/删除列表
     *
     * @param corpId
     * @param commId
     * @return
     */
    @GetMapping(value = "/api/discount/changeCorpCommId")
    Mono<ObjectResponse<ChangeCorpCommIdDiscountList>> changeCorpCommIdDiscountList(
        @RequestParam(value = "corpId") Long corpId,
        @RequestParam(value = "commId") Long commId);

    /**
     * 移除协议价
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/discount/changeCorpCommIdRm")
    Mono<ObjectResponse<Boolean>> changeCorpCommIdRm(@RequestBody ChangeCorpCommIdRmParam param);

    /**
     * 充电订单评论
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/post/comment")
    Mono<BaseResponse> cusPostComment(@RequestBody CommentParam param);

    /**
     * 通过充电订单号获取用户评论
     *
     * @param orderNo 充电订单号
     * @return
     */
    @GetMapping(value = "/api/post/getByOrderNo")
    Mono<ObjectResponse<CusPostVo>> getCusPostByOrderNo(
        @RequestParam("orderNo") String orderNo);

    /**
     * 获取用户评论
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/api/post/get")
    Mono<ObjectResponse<CusPostVo>> getCusPost(
        @RequestParam("id") Long id);

    /**
     * 获取场站评论综合信息
     *
     * @param siteId
     * @param open
     * @return
     */
    @GetMapping(value = "/api/post/siteCommentTotal")
    Mono<ObjectResponse<SiteCommentTotal>> siteCommentTotal(
        @RequestParam("siteId") String siteId,
        @RequestParam(value = "open", required = false) Boolean open);


    /**
     * 查询场站评级信息(列表)
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/post/selectSiteCommentTotal")
    Mono<ListResponse<SiteCommentTotal>> selectSiteCommentTotal(@RequestBody BaseListParam param);

    /**
     * 用户评论列表
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/post/list")
    Mono<ListResponse<CusPostVo>> cusPostList(@RequestBody ListCusPostParam param);

    /**
     * 切换用户评论公开状态
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/post/changeOpen")
    Mono<BaseResponse> cusPostChangeOpen(@RequestBody CusPostChangeStatusParam param);

    /**
     * 用户评论标签调整
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/post/changeTag")
    Mono<BaseResponse> cusPostChangeTag(@RequestBody CusPostChangeTagParam param);

    /**
     * 用户评论回复
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/post/reply")
    Mono<BaseResponse> cusPostReply(@RequestBody CusPostReplyParam param);

    /**
     * 获取场站白名单
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/siteWhitelist/querySiteWhitelist")
    Mono<ListResponse<SiteWhitelistVo>> querySiteWhitelist(@RequestBody SiteWhitelistPo param);

    /**
     * 新增场站白名单
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/siteWhitelist/add")
    Mono<ObjectResponse<Boolean>> addWhitelist(@RequestBody SiteWhitelistParam param);

    /**
     * 删除场站白名单
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/siteWhitelist/remove")
    Mono<ObjectResponse<Integer>> removeWhitelist(List<SiteWhitelistParam> param);

    // 手机号是否在场站白名单中
    @GetMapping(value = "/api/siteWhitelist/phoneInWhitelist")
    Mono<ObjectResponse<Boolean>> phoneInWhitelist(
        @RequestParam("phone") String phone,
        @RequestParam("siteId") String siteId);

    // 用户或手机号是否在场站黑名单中
    @GetMapping(value = "/api/siteBlacklist/phoneOrUidInBlacklist")
    Mono<ObjectResponse<Boolean>> phoneOrUidInBlacklist(
        @RequestParam(value = "uid", required = false) Long uid,
        @RequestParam(value = "phone", required = false) String phone,
        @RequestParam("siteId") String siteId);

    // 企业是否在场站白名单中
    @GetMapping(value = "/api/siteWhitelist/corpInWhitelist")
    Mono<ObjectResponse<Boolean>> corpInWhitelist(
        @RequestParam("corpId") Long corpId,
        @RequestParam("siteId") String siteId);

    // 企业是否在场站黑名单中
    @GetMapping(value = "/api/siteBlacklist/corpInBlacklist")
    Mono<ObjectResponse<Boolean>> corpInBlacklist(
        @RequestParam("corpId") Long corpId,
        @RequestParam("siteId") String siteId);

    // 获取用户充电限制时段
    @GetMapping(value = "/soc/site/socLimitTimeList")
    Mono<ListResponse<UserSocTimePo>> socLimitTimeList(
        @RequestParam(value = "payAccountId") Long payAccountId,
        @RequestParam(value = "vin", required = false) String vin);

    // 同步卡片金额
    @PostMapping(value = "/api/card/syncCardAmount")
    Mono<ObjectResponse<Boolean>> syncAmount(@RequestParam(value = "cardNo") String cardNo,
        @RequestParam(value = "amount") BigDecimal amount,
        @RequestParam(value = "result") Integer result,
        @RequestParam(value = "topCommId") Long topCommId,
        @RequestParam(value = "userId") Long userId);

    // 获取同步卡片金额记录
    @PostMapping(value = "/api/card/getSyncCardAmount")
    Mono<ListResponse<CardAmountSyncVo>> getSyncCardAmount(CardAmountSyncParam param);

    // 获取卡片金额账户总余额
    @PostMapping(value = "/api/card/getCardAmount")
    Mono<ObjectResponse<BigDecimal>> getCardAmount(@RequestParam(value = "cardNo") String cardNo,
        @RequestParam(value = "topCommId") Long topCommId,
        @RequestParam(value = "cardMaker") Boolean cardMaker);

    @RequestMapping(value = "/api/blocWallet/selectBlocPointLogsByOrderNo", method = RequestMethod.GET)
    Mono<ListResponse<PointLog>> selectBlocPointLogsByOrderNo(
        @RequestParam(value = "orderNo") String orderNo);

    // 获取商户会员折扣
    @GetMapping(value = "/api/commScore/getDiscount")
    Mono<ObjectResponse<Optional<BigDecimal>>> getDiscount(
        @RequestParam(value = "commId") Long commId,
        @RequestParam(value = "cusId") Long cusId);

    // 增加商户会员积点并记录日志
    @GetMapping(value = "/api/commScore/increaseScoreAndLog")
    Mono<BaseResponse> increaseScoreAndLog(@RequestParam("commId") Long commId,
        @RequestParam("cusId") Long cusId,
        @RequestParam("addScore") BigDecimal addScore);

    @PostMapping(value = "/balanceApplication/check")
    Mono<ObjectResponse<Boolean>> checkBalanceApplication(
        @RequestBody BalanceApplicationCheckParam param);

    // 获取充值申请记录信息
    @GetMapping(value = "/balanceApplication/getById")
    Mono<ObjectResponse<BalanceApplicationVo>> getBalanceApplyById(@RequestParam("id") Long id);

    // 充值申请OA流程执行结果通知
    @PostMapping(value = "/balanceApplication/oaNotify")
    Mono<ObjectResponse<Boolean>> oaNotifyBalanceApplication(
        @RequestBody BalanceApplicationCheckParam param);

    // 更新充值申请记录的流程实例ID
    @GetMapping("/balanceApplication/updateProcessInstanceId")
    Mono<BaseResponse> updateApplyOaProcessInstanceId(
        @RequestParam(value = "id") Long id,
        @RequestParam(value = "procInstId") String procInstId);

    @PostMapping(value = "/api/corp/settlementBiForCorp")
    Mono<ObjectResponse<OrderBiVo>> settlementBiForCorp(@RequestBody ListSettlementParam param);

    @PostMapping("/balanceApplication/updateByProInsId")
    Mono<ObjectResponse<BalanceApplicationVo>> updateByProInsId(
        @RequestBody BalanceApplicationPo params);

    @GetMapping("/balanceApplication/deleteBalanceById")
    Mono<BaseResponse> deleteBalanceById(@RequestParam(value = "id") Long id);

    @PostMapping(value = "/api/corp/findSettlementList")
    Mono<ListResponse<SettlementVo>> findSettlementList(@RequestBody ListSettlementParam param);

    @GetMapping(value = "/api/accountScore/getScoreSettingFavored")
    Mono<ObjectResponse<ScoreSettingDto>> getScoreSettingFavored(
        @RequestParam("userId") Long userId,
        @RequestParam("gids") List<String> gids);

    @PostMapping(value = "/api/accountScore/getScoreSettingStrategy")
    Mono<ObjectResponse<ScoreSettingDiscountDto>> getScoreSettingStrategy(
        @RequestParam("userId") Long userId,
        @RequestParam("gids") List<String> gids,
        @RequestParam(value = "id", required = false) Long id);

    @PostMapping(value = "/api/accountScore/updateScore")
    Mono<BaseResponse> updateScore(@RequestBody ScoreUpdateParam param);

    // 充值申请(旧数据数据结构调整)
    @GetMapping(value = "/balanceApplication/fixImages")
    Mono<BaseResponse> applicationFixImages();

    // 判断账号状态
    @GetMapping(value = "/api/user/checkAccountStatus")
    Mono<ObjectResponse<Boolean>> checkAccountStatus(
        @RequestParam(value = "accountType") PayAccountType accountType,
        @RequestParam(value = "commId") Long commId,
        @RequestParam(value = "userId") Long userId);

    @GetMapping(value = "/api/accountScore/getScoreInfo")
    Mono<ObjectResponse<ScoreSettingPo>> getScoreInfo(
        @RequestParam("scoreSettingId") Long scoreSettingId);

    @PostMapping(value = "/ess/user/enable")
    Mono<ObjectResponse<UserPo>> enableAccount(@RequestBody EnableAccountParam param);

    @PostMapping(value = "/ess/user/fetch")
    Mono<ListResponse<UserPo>> fetchEssUser(@RequestBody EssRegisterUserParam param);

    // 获取ess用户信息
    @GetMapping(value = "/ess/user/accountInfo")
    Mono<ObjectResponse<UserPo>> getEssUserAccountDetail(@RequestParam("uid") Long uid);

    @PostMapping(value = "/api/CommercialUser/registerByEmail")
     Mono<ObjectResponse<UserAndBalanceAndTokenVo>> registerByEmail(
        @RequestBody UserRegisterParam param);

    @PostMapping(value = "/api/CommercialUser/loginByEmail")
     Mono<ObjectResponse<UserAndBalanceAndTokenVo>> loginByEmail(
        @RequestBody UserRegisterParam param);

    @PostMapping(value = "/api/CommercialUser/modifyByEmail")
     Mono<ObjectResponse<UserAndBalanceAndTokenVo>> modifyByEmail(
        @RequestBody UserRegisterParam param);
}
