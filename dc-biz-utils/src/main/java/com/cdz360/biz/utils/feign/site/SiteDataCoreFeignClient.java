package com.cdz360.biz.utils.feign.site;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.discount.param.DiscountServiceParam;
import com.cdz360.biz.model.discount.vo.SiteDiscountVo;
import com.cdz360.biz.model.site.dto.OaSiteMonthDataDto;
import com.cdz360.biz.model.site.dto.UpdateSiteInvoicedValidDto;
import com.cdz360.biz.model.site.param.ListSiteBaseParam;
import com.cdz360.biz.model.site.type.SiteStatus;
import com.cdz360.biz.model.tj.kc.param.ListSiteWithinTjAreaParam;
import com.cdz360.biz.model.tj.kc.vo.SiteWithinTjVo;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import com.cdz360.biz.model.trading.site.vo.SiteWeatherVo;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, fallbackFactory = SiteDataCoreFeignHystrix.class)
public interface SiteDataCoreFeignClient {

    /**
     * 获取场站ID列表， gids 或 commIdChain 必须传一个
     */
    @PostMapping("/dataCore/site/getSiteIds")
    Mono<ListResponse<String>> getSiteIds(@RequestBody ListSiteBaseParam paramIn);

    @PostMapping("/dataCore/site/getSiteById")
    Mono<ObjectResponse<SitePo>> getSiteById(@RequestParam("siteId") String siteId);

    // 获取场站信息列表
    @PostMapping("/dataCore/site/getSiteVoList")
    Mono<ListResponse<SiteVo>> getSiteVoList(@RequestBody ListSiteParam param);

    /**
     * <p> 通过MQ消息将场站信息同步给所有微服务, 用于定时任务触发</p>
     * <p> 默认仅同步‘上线’的场站</p>
     */
    @PostMapping(value = "/dataCore/site/syncSitesInfo")
    Mono<ListResponse<String>> syncSitesInfo(
        @RequestParam(value = "siteIdList", required = false) List<String> siteIdList,
        @RequestParam(value = "statusList", required = false) List<SiteStatus> statusList);

    // 获取场站天气
    @GetMapping("/dataCore/siteWeather/findBySiteId")
    Mono<ObjectResponse<SiteWeatherVo>> findBySiteId(@RequestParam(value = "siteId") String siteId);

    // 获取协议价服务费
    @PostMapping(value = "/dataCore/site/discountServiceFeeByCode")
    Mono<ObjectResponse<SiteDiscountVo>> discountServiceFeeByCode(
        @RequestBody DiscountServiceParam param);

    // 场站电费支付数据记录
    @PostMapping(value = "/dataCore/site/oaMonthData")
    Mono<BaseResponse> oaSiteDailyData(@RequestBody OaSiteMonthDataDto dto);

    // 场站电费支付数据记录
    @PostMapping(value = "/dataCore/site/invoice/updateInvoicedValid")
    Mono<BaseResponse> updateSiteInvoicedValid(@RequestBody UpdateSiteInvoicedValidDto dto);

    // 通知场站更新mobileTempSalId
    @PostMapping(value = "/dataCore/site/invoice/notifyMobileTempSal")
    Mono<BaseResponse> notifyMobileTempSal(
        @RequestParam(name = "oldTempSalId") Long oldTempSalId,
        @RequestParam(name = "newTempSalId") Long newTempSalId);

    // 获取投建区域内的场站列表
    @PostMapping(value = "/dataCore/site/tj/rel/findSiteWithinTjArea")
    Mono<ListResponse<SiteWithinTjVo>> findSiteWithinTjArea(
        @RequestBody ListSiteWithinTjAreaParam param);


}
