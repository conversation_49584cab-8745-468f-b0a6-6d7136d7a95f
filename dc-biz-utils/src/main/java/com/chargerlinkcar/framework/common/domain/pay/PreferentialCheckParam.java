package com.chargerlinkcar.framework.common.domain.pay;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PreferentialCheckParam {

    @Schema(name = "优惠券编号(农行优惠查询返回的优惠券编号)")
    @JsonInclude(Include.NON_EMPTY)
    private String couponId;

    @Schema(description = "消费总金额")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal totalAmount;

    @Schema(description = "实付金额")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal payAmount;

    @Schema(description = "优惠金额")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal preferentialAmount;

    @Schema(description = "支付单号(商E付订单号)")
    private String serialNo;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
