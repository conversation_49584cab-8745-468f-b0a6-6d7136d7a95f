package com.chargerlinkcar.framework.common.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.discount.param.DiscountServiceParam;
import com.cdz360.biz.model.discount.vo.SiteDiscountVo;
import com.cdz360.biz.model.merchant.dto.CommercialSiteDto;
import com.cdz360.biz.model.merchant.dto.SiteTinyDto;
import com.cdz360.biz.model.merchant.param.CommcialSiteParam;
import com.cdz360.biz.model.site.dto.SiteGeoDto;
import com.cdz360.biz.model.site.param.AddSiteParam;
import com.cdz360.biz.model.site.param.ListSiteBaseParam;
import com.cdz360.biz.model.site.param.UpdateSiteParam;
import com.cdz360.biz.model.site.po.PriceItemPo;
import com.cdz360.biz.model.site.type.SiteStatus;
import com.cdz360.biz.model.trading.hlht.dto.CecQueryQeuipBusinessPolicyResult;
import com.cdz360.biz.model.trading.order.po.CardPo;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.site.po.PriceTemplatePo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.site.vo.SiteChargePriceVo;
import com.cdz360.biz.model.trading.site.vo.SiteNoVo;
import com.cdz360.biz.model.trading.site.vo.SiteQrCodeVo;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import com.cdz360.data.sync.model.Site;
import com.chargerlinkcar.framework.common.domain.PlugStatusCountDto;
import com.chargerlinkcar.framework.common.domain.SiteDetailInfoVo;
import com.chargerlinkcar.framework.common.domain.SitePersonaliseDTO;
import com.chargerlinkcar.framework.common.domain.request.SiteAroundRequest;
import com.chargerlinkcar.framework.common.domain.request.SiteGeoListRequest;
import com.chargerlinkcar.framework.common.domain.vo.SiteAroundVo;
import com.chargerlinkcar.framework.common.domain.vo.SiteDebitAccountVo;
import com.chargerlinkcar.framework.common.domain.vo.SiteInMongoVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

@Slf4j
@Component
public class SiteDataCoreFeignClientHystrix implements FallbackFactory<SiteDataCoreFeignClient> {

    @Override
    public SiteDataCoreFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE,
            throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE,
            throwable.getStackTrace());

        return new SiteDataCoreFeignClient() {

            @Override
            public ObjectResponse<SitePo> addSite(AddSiteParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<SitePo> updateSiteInfo(@RequestBody UpdateSiteParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<SitePo> updateSiteStatus(String siteId, SiteStatus status) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<SiteVo> getSiteVoList(ListSiteParam param) {
                return RestUtils.serverBusy4ListResponse();
            }


            @Override
            public BaseResponse updateSiteInvoicedValid(List<String> list, Integer invoicedValid) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<SitePo> getSiteById(String siteId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse setDefaultPriceScheme(Long priceSchemeId, String siteId) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<PriceTemplatePo> getSitePriceScheme(String siteId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<CardPo> getCardAmountBySiteId(ListSiteBaseParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse updateSiteDebitAccent(SiteDebitAccountVo request) {
                return null;
            }

            @Override
            public BaseResponse setOnlineDate(String siteId, String date) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<SiteTinyDto> getSiteTinyList(ListSiteParam param) {
                return RestUtils.serverBusy4ListResponse();
            }


            @Override
            public ListResponse<Site> getSiteList(Long commId,
                String cityCode,
                List<String> siteIdList,
                long start,
                int size) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<SiteDetailInfoVo> getSiteDetail(SiteGeoListRequest param) {
                return RestUtils.serverBusy4ObjectResponse();

            }


            @Override
            public ObjectResponse<SiteNoVo> getSiteInfoBySiteNo(Long topCommId, String siteNo) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<SiteNoVo> getSiteNoById(String siteId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<String> getExistingOperateCorpCodes() {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<SiteInMongoVo> getSiteFromMongo(String siteId, Long userId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<SiteInMongoVo> getSiteListFromMongo(ListSiteParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SiteAroundVo> getSiteAroundInfo(SiteAroundRequest param) {
                return RestUtils.serverBusy4ListResponse();
            }


            @Override
            public BaseResponse syncSite2Mongo() {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse syncSitePrice2SiteTemplate() {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse recordEvsePlugInfo(String siteId, String unBindEvseNo) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<PlugStatusCountDto> getChargerStatusStatisticsBySiteId(
                String siteId) {
                return RestUtils.serverBusy4ObjectResponse();
            }


            @Override
            public ListResponse<SiteGeoDto> getSiteGeoList(ListSiteParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<CommercialSiteDto> getCommercialSiteList(CommcialSiteParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<String> getInvoiceDescList(String commIdchain, String desc) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SiteDiscountVo> discountServiceFee(DiscountServiceParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<SiteQrCodeVo> getSiteQrCodeVo(String siteId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<SiteChargePriceVo> getSitePriceList(String siteId) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<PriceItemPo> getPriceSchemaItem(List<Long> priceIdList) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<String> getSiteListByGids(ListSiteParam params) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse syncHlhtTemplate(CecQueryQeuipBusinessPolicyResult cecQueryQeuipBusinessPolicyResult) {
                return RestUtils.serverBusy();
            }
            @Override
            public ObjectResponse<SitePersonaliseDTO> getPersonalise(String siteId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

        };
    }
}