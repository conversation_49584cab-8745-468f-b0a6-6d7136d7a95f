package com.chargerlinkcar.framework.common.domain.vo;

import com.cdz360.biz.model.cus.corp.type.LimitCycle;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @since 2019/12/13 16:20
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CorpCreditAccountVo {
    @Schema(description = "主键")
    private Long id;
    @Schema(description = "手机号")
    private String phone;
    @Schema(description = "用户姓名")
    private String userName;
    @Schema(description = "所属组织名称")
    private String corpOrgName;
    @Schema(description = "所属组织id")
    private Long corpOrgId;
    @Schema(description = "限额 元")
    private BigDecimal limitMoney;

    @Schema(description = "限额周期")
    private LimitCycle limitCycle;

    @Schema(description = "已使用（额度）, 单位‘元’", example = "123.45")
    private BigDecimal usedAmount;

    @Schema(description = "冻结金额, 单位‘元’", example = "123.45")
    private BigDecimal frozenAmount;

    @Schema(description = "可用额度, 单位‘元’", example = "123.45")
    private BigDecimal availableAmount;

    @Schema(description = "停用/启用")
    private Boolean status;

    @Schema(description = "欠费状态: 未欠费(false), 欠费(true)")
    private Boolean userDebt;

    @Schema(description = "待支付订单数目")
    private Integer unPayOrderCount;

    @Schema(description = "历史订单金额")
    private BigDecimal paidFee;

    /**
     * CREATE, MODIFY, DISABLE
     */
    @Schema(description = "操作类型 CREATE, MODIFY, DISABLE")
    private String type;

}
