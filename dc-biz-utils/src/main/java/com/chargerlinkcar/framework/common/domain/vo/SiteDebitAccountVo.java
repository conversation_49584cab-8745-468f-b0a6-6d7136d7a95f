package com.chargerlinkcar.framework.common.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 关于场站批量开启充电账户
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SiteDebitAccountVo extends SiteDebitAccountBase {
//    @Schema(description = "场站ID",required = true)
//    private String siteId;
//
//    @Schema(description = "顶级商户ID", hidden = true)
//    private Long topCommId;
//
//    @Schema(description = "是否开启后台启动充电",required = true)
//    private Boolean startCharingEnable;
//
//    @Schema(description = "指定结算账户方式, 1启动时指定；2场站统一指定",required = false)
//    private Integer settlementMethod;
//
//    @Schema(description = "结算账户类型, 1个人客户, 2企业客户, 3商户会员",required = false)
//    private SiteChargePayType payType;
//
//    @Schema(description = "所属商户(指定企业客户，商户会员时传入)",required = false)
//    private Long commId;
//
//    @Schema(description = "企业授信客户ID(指定企业客户时传入)",required = false)
//    private Long corpUserId;
//
//    @Schema(description = "客户手机号(指定个人客户，商户会员时传入)",required = false)
//    private String phone;

    /**
     * 所属商户下拉-模糊查询
     */
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String commercialName;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private Long siteCommId;
//    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
//    private List<Long> subCommIdList;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String commIdChain;





    @Schema(description = "页面展示-个人客户名称")
    private String userName;

//    @Schema(description = "页面展示-企业客户ID")
//    private Long blocUserId;

    @Schema(description = "页面展示-企业客户名称")
    private String blocUserName;

    @Schema(description = "页面展示-企业授信客户名称")
    private String corpUserName;

    @Schema(description = "页面展示-所属商户名称(账号类型为企业or商户时显示)")
    private String commName;

    @Schema(description = "页面展示-商户会员名称")
    private String commUserName;


}
