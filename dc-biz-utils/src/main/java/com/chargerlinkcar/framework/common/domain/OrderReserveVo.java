package com.chargerlinkcar.framework.common.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "云端充电请求队列下发情况")
public class OrderReserveVo {
    @Schema(description = "未知")
    private Integer unknown;
    @Schema(description = "待下发数")
    private Integer issueNum;
    @Schema(description = "下发成功数")
    private Integer issueSuccessNum;
    @Schema(description = "下发失败数")
    private Integer issueFailNum;
}
