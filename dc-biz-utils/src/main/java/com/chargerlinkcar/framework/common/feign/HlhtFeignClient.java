package com.chargerlinkcar.framework.common.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.chargerlinkcar.framework.common.domain.TransNotification;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * <AUTHOR>
 *  互联互通feign调用
 * @since 2019-05-01 14:24
 */
@Deprecated(since = "20230201")
@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_HLHT)
@Component
public interface HlhtFeignClient {

    @PostMapping("/fundsTrans/send")
    BaseResponse send(@RequestHeader("Authorization") String Authorization,
        @RequestBody TransNotification transNotification);
}
