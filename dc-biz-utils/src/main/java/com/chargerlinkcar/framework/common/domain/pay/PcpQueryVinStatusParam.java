package com.chargerlinkcar.framework.common.domain.pay;

import com.cdz360.base.utils.JsonUtils;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 查询即插即充状态
 */
@Data
@Accessors(chain = true)
public class PcpQueryVinStatusParam {

    private String openid;

    private Long uid;

    private Long topCommId;

    private String appId;

    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}
