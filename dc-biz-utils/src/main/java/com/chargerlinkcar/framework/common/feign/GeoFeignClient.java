package com.chargerlinkcar.framework.common.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.model.geo.po.CityPo;
import com.cdz360.biz.model.geo.po.ProvincePo;
import com.cdz360.biz.model.geo.vo.ProvinceTreeVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 *  行政/地理数据
 * @since 2019/10/31 15:25
 * <AUTHOR>
 */
@FeignClient(DcConstants.KEY_FEIGN_DC_BIZ_TRADING)
public interface GeoFeignClient {


    @RequestMapping(value = "/api/geo/listProvince", method = RequestMethod.GET)
    ListResponse<ProvincePo> listProvince();
    /**
     * 获取城市列表信息
     * @return
     */
    @RequestMapping(value = "/api/geo/listCity", method = RequestMethod.GET)
    ListResponse<CityPo> listCity();

    /**
     * 获取省/市/区的树形结构数据
     *
     * @return 省份列表(含子市, 区数据)
     */
    @GetMapping("/api/geo/getGeoTree")
    ListResponse<ProvinceTreeVo> getGeoTree();
}
