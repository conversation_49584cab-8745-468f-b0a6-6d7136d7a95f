package com.chargerlinkcar.framework.common.domain.param;


import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.biz.model.finance.type.FlowInAccountType;
import com.cdz360.biz.model.finance.type.TaxType;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.cdz360.biz.model.trading.deposit.vo.DepositSourceType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @since 2019/11/4 10:41
 * <AUTHOR>
 */
@Deprecated
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "充值信息查询")
public class PayBillParam extends SearchParam {

    private static final long serialVersionUID = 1L;

    /**
     * 商户及子商户列表
     */
    @Schema(description = "商户及子商户列表")
    private List<Long> commIdList;

    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private long userId;

    /**
     * 充值来源
     */
    @Schema(description = "充值来源")
    private List<DepositSourceType> sourceTypeList;

    /**
     * 充值类型
     */
    @Schema(description = "充值类型")
    private DepositFlowType flowType;

    /**
     * 充值账户类型. 个人账户/商户会员
     */
    @Schema(description = "充值账户类型. 个人账户/商户会员")
    private PayAccountType accountType;

    /**
     * 账户编号. 个人账户为集团商户编号; 商户会员为商户编号
     */
    @Schema(description = "账户编号. 个人账户为集团商户编号; 商户会员为商户编号")
    private Long accountCode;

    /**
     * 支付方式
     */
    @Schema(description = "支付方式")
    private List<PayChannel> payChannelList;

    /**
     * 到账账户
     */
    @Schema(description = "到账账户")
    private FlowInAccountType flowInAccountType;

    /**
     * 发票类型(税种)
     */
    @Schema(description = "发票类型(税种)")
    private List<TaxType> taxTypeList;

    /**
     * 订单开始时间
     */
    @Schema(description = "订单开始时间")
    private String startTime;

    /**
     * 订单结束时间
     */
    @Schema(description = "订单结束时间")
    private String endTime;

    /**
     * 订单id
     */
    @Schema
    private String orderId;

}
