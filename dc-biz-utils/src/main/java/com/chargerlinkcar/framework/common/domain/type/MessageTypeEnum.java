package com.chargerlinkcar.framework.common.domain.type;

public enum MessageTypeEnum {
    SMS("SMS", "短信"),
    APP("APP", "App"),
    WECHAT("WECHAT", "微信"),
    DINGTALK("DING<PERSON>LK", "钉钉"),
    ALI("ALI", "阿里"),
    EMAIL("EMA<PERSON>", "邮件");

    private String code;
    private String message;

    MessageTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }
}