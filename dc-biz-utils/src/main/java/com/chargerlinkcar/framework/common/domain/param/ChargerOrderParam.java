package com.chargerlinkcar.framework.common.domain.param;


import com.cdz360.base.model.base.type.ChargeOrderStatus;
import com.cdz360.base.model.charge.type.HlhtType;
import com.cdz360.base.model.charge.type.OrderAbnormalReason;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.order.type.OrderPayType;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.bill.type.DailyBillCheckResult;
import com.cdz360.biz.model.trading.order.type.BiDependOnType;
import com.chargerlinkcar.framework.common.domain.Dict;
import com.chargerlinkcar.framework.common.domain.PayAccount;
import com.chargerlinkcar.framework.common.domain.type.ProcessType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 *  订单列表查询传参
 * @since 2016年9月7日 下午2:09:57
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "订单查询")
public class ChargerOrderParam extends SearchParam {

    private static final long serialVersionUID = 1L;
    /**
     * 公有云订单ID
     */
    @Schema(description = "公有云订单ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    protected Long orderId;
    /**
     * 公有云订单编号
     */
    @Schema(description = "公有云订单编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected String orderNo;
    /**
     * 公有云订单编号
     */
    @Schema(description = "公有云订单编号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    protected List<String> orderNos;

    @Schema(description = "操作人集团商户ID", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long topCommId;

    @Schema(description = "场站对应商户的ID链", example = "34474,12478")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteCommIdChain;

    /**
     * 站点ID
     */
    @Schema(description = "站点ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected String stationId;

    /**
     * 站点ID数组
     */
    @Schema(description = "站点ID数组")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    protected List<String> stationIds;

    /**
     * 只用于订单汇总列表-商户关联场站抽屉控件查询,其他页面请使用stationIds
     */
    @Schema(description = "只用于订单汇总列表-商户关联场站抽屉控件查询,其他页面请使用stationIds")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    protected List<String> commStationIds;

    /**
     * 导出字段参数
     */
    @Schema(description = "导出字段参数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    protected List<String> filter;

    /**
     * 站点名称
     */
    @Schema(description = "站点名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected String stationName;

    /**
     * 站点编号
     */
    @Schema(description = "站点编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected String siteNo;

    /**
     * 页面充电桩ID(充电强connectId)
     */
    @Schema(description = "页面充电桩ID(充电强connectId)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    protected Long evseId;

    /**
     * 第三方的订单号：app，互联互通
     */
    @Schema(description = "第三方的订单号：app，互联互通")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected String openOrderId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", example = "123")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    protected Long customerId;

    /**
     * 充电卡号/身份唯一识别号
     */
    @Schema(description = "充电卡号/身份唯一识别号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected String cardNo;

    /**
     * 订单来源类型,0:微信渠道;1:APP在线发起充电;2:APP蓝牙发起充电:3:刷卡充电:4:桩上报离线数据
     */
    @Schema(description = "订单来源类型(废弃)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    protected Integer channelId;

    /**
     * 订单金额
     */
    @Schema(description = "订单金额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    protected Long orderPrice;

    /**
     * 订单电量
     */
    @Schema(description = "订单电量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    protected Long orderElectricity;

    /**
     * 状态,-500：硬件故障；-40：上报超时；-35：断连超时；-30关电超时；-29：结束充电失败；-20：链接超时；-10：打开电闸超时；-7：开启充电失败，PIN可能错误；-5:系统繁忙；0：订单未激活；100：电闸打开；200：充电中；250：关电闸，充电停止；300：充电完成；800：订单结束；1000：用户已支付；2000：钱已到账
     */
    @Schema(description = "订单状态(废弃)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    protected Integer status;

    /**
     * 订单状态
     */
    @Schema(description = "订单状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    protected ChargeOrderStatus orderStatus;

    /**
     * 星标标志，默认0，0：非星标；1：星标；
     */
//    @Schema(description = "星标标志，默认0，0：非星标；1：星标；")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    protected Integer starFlag;

    /**
     * 计费模板ID
     */
    @Schema(description = "计费模板ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    protected Long priceSchemeId;

    /**
     * 充电车位ID
     */
//    @Schema(description = "充电车位ID")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    protected Long carportId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected String remark;

    /**
     * 订单创建时间
     */
    @Schema(description = "订单创建时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    protected Date createTime;

    /**
     * 查询开始时间
     */
    @Schema(description = "查询开始时间")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected String beginTime;

    /**
     * 查询结束时间
     */
    @Schema(description = "查询结束时间")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected String endTime;

    /**
     * 充电接口ID
     */
    @Schema(description = "充电接口ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected String connectId;
    /**
     * 显示字段 内容：出厂编号+evseId+connectorId （枪头id）
     */
    @Schema(description = "显示字段 内容：出厂编号+evseId+connectorId （枪头id）")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String showId;

    /**
     * 枪头编号
     */
    @Schema(description = "枪头编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String bcCode;

    /**
     * 枪头编号
     */
    @Schema(description = "枪头编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String connectorId;

    @Schema(description = "枪头编号:12位桩编号+2位枪编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String plugNo;

    /**
     * 商户ID
     */
//    @Schema(description = "商户ID")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Long commercialId;

    /**
     * 目前已知用于普通客户-订单信息-所属商户 2020-04-30
     */
    @Schema(description = "所属商户名称")
    private String commName;

    /**
     * app商户ID
     */
    @Schema(description = "app商户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long appCommId;

    /**
     * 设备商户id
     */
    @Schema(description = "设备商户id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long deviceCommId;
    /**
     * 用户手机号
     */
    @Schema(description = "用户手机号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String mobilePhone;

    @Schema(description = "邮箱")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String email;
    /**
     * 用户名称
     */
    @Schema(description = "用户名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String customerName;
    @Schema(description = "--")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Integer> statusList;

    @Schema(description = "结算账户")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Long> payAccountTypeList;

    @Schema(description = "支付渠道")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Integer> payChannelList;

    @Schema(description = "订单状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<ChargeOrderStatus> orderStatusList;

    /**
     * 支付方式
     **/
    @Schema(description = "支付方式")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer payModes;
    /**
     * 支付状态
     **/
    @Schema(description = "支付状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer payStatus;

//    /**
//     * 客户订单金额，向客户提供商(客户)收取的钱
//     */
//    @Schema(description = "客户订单金额，向客户提供商(客户)收取的钱")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    protected Long customerPrice;

    /**
     * 设备运营商ID
     */
    @Schema(description = "设备运营商ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    protected Long deviceCommercialId;

    /**
     * 客户运营商名称
     */
    @Schema(description = "客户运营商名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String customerCommercialName;
    /**
     * 客户运营商ID
     */
//    @Schema(description = "客户运营商ID")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Long customerCommercialId;

    /**
     * 车队名称
     */
    @Schema(description = "车队名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String carDepart;

    /**
     * 车辆Vin码
     */
    @Schema(description = "车辆Vin码")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String vin;

    @Schema(description = "物理卡号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cardChipNo;
    /**
     * 桩号
     */
    @Schema(description = "桩号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String boxOutFactoryCode;

    /**
     * 发票号码
     */
    @Schema(description = "发票号码")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String invoiceNumber;

    /**
     * 桩名称
     */
    @Schema(description = "桩名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String evseName;

    /**
     * 桩名称精确查找
     */
    @Schema(description = "桩名称精确查找")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String evseNameEqual;

    /**
     * 枪名称精确查找
     */
    @Schema(description = "枪名称精确查找")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String plugNameEqual;

//    @Schema(description = "--")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Integer hlht;

    @Schema(description = "0,非互联互通订单; 1,正向互联互通; 2,反向互联互通")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<HlhtType> hlhtTypeList;

    @Schema(description = "二维码编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String qrCode;

    public String getCustomerName() {
//        if (Base64Util.isBase64(customerName)) {
//            return Base64Util.getFromBase64(customerName);
//        } else {
        return customerName;
//        }
    }

    /**
     * 商户列表
     */
    @Schema(description = "商户列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Deprecated
    private List<Long> commIdList;

    /**
     * 订单来源类型列表,0:微信渠道;1:APP在线发起充电;2:APP蓝牙发起充电:3:刷卡充电:4:桩上报离线数据
     */
    @Schema(description = "订单来源类型列表,0:微信渠道;1:APP在线发起充电;2:APP蓝牙发起充电:3:刷卡充电:4:桩上报离线数据")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Integer> channelIdList;

    /**
     * 订单启动方式
     */
    @Schema(description = "订单启动方式")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Integer> orderTypeList;
    /**
     * 按照订单结束时间排序：1:asc 2:desc
     */
    @Schema(description = "按照订单结束时间排序：1:asc 2:desc")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer orderByChargeEndTime;
    /**
     * 结束原因
     */
    @Schema(description = "结束原因")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String stopReason;

//    @Schema(description = "--")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Long merchantId;

    @Schema(description = "企业授信ID列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Long> rBlocUserIds;

    @Schema(description = "下载文件类型")
    //下载文件类型
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String type;

    @Schema(description = "--")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String subDir;
    //
//    private String subFileName;
    @Schema(description = "excel位置")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ExcelPosition excelPosition;

    @Schema(description = "sheet名称", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String sheetName;

    @Schema(description = "订单来源")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Dict> orderSourceLst;

    @Schema(description = "支付方式")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Dict> payModesLst;

    @Schema(description = "订单状态: 字典")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Dict> orderStatusLst;

    @Schema(description = "订单方式: 字典")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Dict> payTypeDictList;

    /**
     * 1个人账户 2集团授权账户
     */
    @Schema(description = "1个人账户 2集团授权账户")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer defaultPayType;

    /**
     * 订单异常原因
     */
    @Schema(description = "订单异常原因")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private OrderAbnormalReason abnormal;

    @Schema(description = "订单异常原因列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<OrderAbnormalReason> abnormalList;

    @Schema(description = "是否包含异常订单 true 表示包含; false 表示不包含")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean includedAbnormal;

    /**
     * 小程序查询时是否排除orderStatus为Cancel的订单
     */
    @Schema(description = "小程序查询时是否排除orderStatus为Cancel的订单")
    private boolean excludeCancelOrder;

    /**
     * 账户标识
     */
    @Schema(description = "账户标识")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long payAccountId;

    @Schema(description = "订单超过设定时间未更新 分钟 预设30分钟;异常订单查询")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long maxIdleTime;//订单超过设定时间未更新 分钟 预设30分钟;异常订单查询

    @Schema(description = "“充电中”状态超过设定时间未改变 分钟 预设24小时;异常订单查询")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long maxCharingTime;//“充电中”状态超过设定时间未改变 分钟 预设24小时;异常订单查询

    @Schema(description = "超过设定时间“订单启动”状态未改变 分钟 预设5分钟;异常订单查询")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long maxStartingTime;//超过设定时间“订单启动”状态未改变 分钟 预设5分钟;异常订单查询

    @Schema(description = "查询开始时间(充电订单创建时间)")
    @JsonInclude(Include.NON_NULL)
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
//    @JsonSerialize(using = LocalDateTimeSerializer.class)
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private LocalDateTime fromCreateDate;

    /**
     * 卡名称
     */
    @Schema(description = "卡名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cardName;
    /**
     * 车牌号
     */
    @Schema(description = "车牌号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String carNo;
    /**
     * 车辆自编号
     */
    @Schema(description = "车辆自编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String carNum;

    /**
     * 线路
     */
    @Schema(description = "线路")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String lineNum;

    /**
     * 所属集团客户
     */
    @Schema(description = "所属集团客户")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String blocUserName;

    /**
     * 商户列表
     */
    @Schema(description = "商户列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Long> deviceCommIdList;

    /**
     * 桩编号
     */
    @Schema(description = "桩编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String evseNo;

    /**
     * 查询创建时间-from
     */
    @Schema(description = "查询创建时间-from")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected String createTimeFrom;

    /**
     * 查询创建时间-to
     */
    @Schema(description = "查询创建时间-to")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected String createTimeTo;

    /**
     * 查询支付时间-from
     */
    @Schema(description = "查询支付时间-from")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected String payTimeFrom;

    /**
     * 查询支付时间-to
     */
    @Schema(description = "查询支付时间-to")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected String payTimeTo;

    /**
     * 查询充电开始时间-from
     */
    @Schema(description = "查询充电开始时间-from")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected String chargeStartTimeFrom;

    /**
     * 查询充电开始时间-to
     */
    @Schema(description = "查询充电开始时间-to")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected String chargeStartTimeTo;

    /**
     * 查询充电结束时间-from
     */
    @Schema(description = "查询充电结束时间-from")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected String chargeEndTimeFrom;

    /**
     * 查询充电结束时间-to
     */
    @Schema(description = "查询充电结束时间-to")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected String chargeEndTimeTo;

    /**
     * 查询上传时间-from
     */
    @Schema(description = "查询上传时间-from")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected LocalDateTime stopTimeStart;

    /**
     * 查询上传时间-from
     */
    @Schema(description = "查询上传时间-from")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected String stopTimeFrom;

    /**
     * 查询上传时间-to
     */
    @Schema(description = "查询上传时间-to")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected String stopTimeTo;

    @Schema(description = "订单处理类型:0正常处理、1异常手工未处理、2异常手工处理", example = "1")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<ProcessType> processTypes;

    @Schema(description = "结算类型: UNKNOWN(0)-未知,BALANCE(1)-账户余额扣减(先付费),GUARANTEE(2)-担保消费结算,"
        +
        "POSTPAID(3)-预消费结算(后付费),PARTNER(4)-外部平台结算", format = "java.lang.Integer")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private SettlementType settlementType;

    @Schema(description = "结算类型: UNKNOWN(0)-未知,BALANCE(1)-账户余额扣减(先付费),GUARANTEE(2)-担保消费结算,"
        +
        "POSTPAID(3)-预消费结算(后付费),PARTNER(4)-外部平台结算", format = "java.lang.Integer")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Long> settlementTypeList;

    @Schema(description = "扣款账户 多选 账户类型-账户标识")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<PayAccount> payAccountList;

    @Schema(description = "支付方式 多选 账户类型或账户类型-orderType")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<OrderPayType> payTypeList;

    /**
     * 集团组织id
     */
    @Schema(description = "集团组织id列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Long> corpOrgIds;

    @Schema(description = "企业id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long corpId;

    /**
     * 集团客户Id
     */
    @Schema(description = "集团客户Id", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long blocUserId;

    @Schema(description = "逻辑卡号集合")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> cardNoList;

    @Schema(description = "物理卡号集合")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> cardChipNoList;

    @Schema(description = "VIN码集合")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> vinList;


    @Schema(description = "最后更新时间开始")
    @JsonInclude(JsonInclude.Include.NON_NULL)
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
//    @JsonSerialize(using = LocalDateTimeSerializer.class)
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private LocalDateTime updateTimeStart;

    @Schema(description = "最后更新时间结束")
    @JsonInclude(JsonInclude.Include.NON_NULL)
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
//    @JsonSerialize(using = LocalDateTimeSerializer.class)
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private LocalDateTime updateTimeEnd;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long start;

    @Schema(description = "充电订单关联账单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String billNo;

    @Schema(description = "归属平台. 0,未知; 21,充电管理平台WEB; 22,运营支撑平台WEB; 23,企业客户平台WEB")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer platform;

    @Schema(description = "是否包含互联互通场站 null or true 表示包含; false 表示不包含")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean includedHlhtSite;

    @Schema(description = "是否0电量订单 true:0电量;false:不是0电量；null:都包含")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean isZeroOrder;

    @Schema(description = "互联互通渠道")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String operatorName;

    @Schema(description = "对账结果")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private DailyBillCheckResult checkResult;

    @Schema(description = "订单号是否精确查找")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean isSureOrderNo;

    @Schema(description = "互联互通订单号是否精确查找")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean isSureOpenOrderId;

    @Schema(description = "手机号是否精确查找")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean isSureMobilePhone;

    @Schema(description = "邮箱是否精确查找")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean isSureEmail;

    @Schema(description = "企业客户是否精确查找")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean isSureBlocUserName;

    @Schema(description = "电桩编号是否精确查找")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean isSureEvseNo;

    @Schema(description = "枪头编号是否精确查找")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean isSurePlugNo;

    @Schema(description = "卡号是否精确查找")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean isSureCardChipNo;

    @Schema(description = "VIN是否精确查找")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean isSureVin;

    @Schema(description = "车牌号是否精确查找")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean isSureCarNo;

    @Schema(description = "税票号是否精确查找")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean isSureInvoiceNumber;

    @Schema(description = "关联账单号是否精确查找")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean isSureBillNo;

    @Schema(description = "场站ID列表 仅用户后台相关接口")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> gids;

    @Schema(description = "是否每日汇总")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean isDailySummary;

    @Schema(description = "每日汇总时间的维度")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BiDependOnType biDependOnType;
    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

    @Schema(description = "最小关注的订单电量，大于这个值的订单才会被统计")
    private Integer minElectricity;

    @Schema(description = "最大关注的订单电量，小于这个值的订单才会被统计")
    private Integer maxElectricity;
}
