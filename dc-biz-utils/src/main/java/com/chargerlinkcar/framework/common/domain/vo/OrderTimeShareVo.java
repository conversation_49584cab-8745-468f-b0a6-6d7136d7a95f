package com.chargerlinkcar.framework.common.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class OrderTimeShareVo {

    @Schema(description = "1：尖峰 2：高峰 3：平峰 4：低谷")
    private Integer tag;

    @Schema(description = "电费")
    private BigDecimal elecFee = BigDecimal.ZERO;

    @Schema(description = "服务费")
    private BigDecimal servOriginFee = BigDecimal.ZERO;

    @Schema(description = "总金额")
    private BigDecimal totalFee = BigDecimal.ZERO;
}