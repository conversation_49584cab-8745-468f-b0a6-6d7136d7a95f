package com.chargerlinkcar.framework.common.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;

/**
 * <AUTHOR>
 *
 * @since 2019/8/20
 **/
public class JacksonStringDeserialize extends JsonDeserializer<Object> {
    public JacksonStringDeserialize() {
    }

    public Object deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        return p.getText();
    }
}
