package com.chargerlinkcar.framework.common.utils;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.util.Base64;
import java.util.regex.Pattern;

public class Base64Util {
    private static Logger logger = LoggerFactory.getLogger(Base64Util.class);

    private final static String ENCODE = "utf-8";



    /**
     * 加密
     * @param str
     * @return
     */
    public static String getBase64(String str) {
        byte[] b = null;
        String s = null;
        try {
            b = str.getBytes(ENCODE);
        } catch (UnsupportedEncodingException e) {
            logger.error(e.getMessage(),e);
        }
        if (b != null) {
            //s = new BASE64Encoder().encode(b);
            s = Base64.getEncoder().encodeToString(b);
        }
        return s;
    }



    /**
     * 解密
     * @param s
     * @return
     */
    public static String getFromBase64(String s) {
        byte[] b = null;
        String result = null;
        if (s != null) {
            //BASE64Decoder decoder = new BASE64Decoder();
            try {
                //b = decoder.decodeBuffer(s);
                b = Base64.getDecoder().decode(s);
                result = new String(b, ENCODE);
            } catch (Exception e) {
                logger.error(e.getMessage(),e);
            }
        }
        return result;
    }


    /**
     * 判断字符串是否base64编码
     *
     * @param str
     * @return
     */
    public static boolean isBase64(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        String base64Pattern = "^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{4}|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)$";
        return Pattern.matches(base64Pattern, str);
    }


}
