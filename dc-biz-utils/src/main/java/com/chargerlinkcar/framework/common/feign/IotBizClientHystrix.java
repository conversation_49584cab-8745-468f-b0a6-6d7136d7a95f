package com.chargerlinkcar.framework.common.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.iot.param.*;
import com.cdz360.biz.model.iot.vo.EvseModelVo;
import com.cdz360.biz.model.iot.vo.OfflineEvseVo;
import com.chargerlinkcar.framework.common.domain.request.ListPlugRequest;
import com.chargerlinkcar.framework.common.domain.request.OrderLimitSocRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class IotBizClientHystrix implements FallbackFactory<IotBizClient> {
    @Override
    public IotBizClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_IOT_WORKER, throwable.getMessage(), throwable);
        //log.error("【服务熔断】。Service = {},St", DcConstants.KEY_FEIGN_IOT_WORKER, throwable);

        return new IotBizClient() {
//            @Override
//            public BaseResponse createOrder(DcStartChargeRequest req) {
//                return RestUtils.serverBusy();
//            }

//            @Override
//            public BaseResponse stopOrder(String evseNo, int plugIdx, String orderNo) {
//                return RestUtils.serverBusy();
//            }

            @Override
            public BaseResponse addEvse(AddEvseParam param) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse editEvse(EditEvseParam param) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse bindEvse2Site(BindEvseParam param) {
                return RestUtils.serverBusy();
            }


            @Override
            public BaseResponse unbindEvse2Site(UnbindEvseParam param) {
                return RestUtils.serverBusy();
            }


            @Override
            public BaseResponse updateEvseInfo(ModifyEvseInfoParam param) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse updateEvseInfoList(List<ModifyEvseInfoParam> param) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse batchImport(List<EvseModelVo> param) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse rebootEvse(String evseNo) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse batchUpdateEvseInfo(ModifyEvseInfoParam param) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse batchAddOfflineEvse(List<OfflineEvseVo> param) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse updateOfflineEvse(ModifyEvseInfoParam param) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse removeOfflineEvse(List<OfflineEvseParam> param) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse updatePlugInfo(String evseNo, Integer plugIdx, String plugName) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse collectEvseStatus() {
                return RestUtils.serverBusy();
            }


            @Override
            public BaseResponse healthCheck(Long sleep) {
                return RestUtils.serverBusy();
            }


            @Override
            public BaseResponse modifyEvseCfgV2(ModifyEvseCfgParam param) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse modifyEvsePrice(ModifyEvseCfgParam param) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse reboot(String ctrlNum) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse siteCtrlSend(String ctrlNum) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<PlugVo> getPlugList(ListPlugRequest listPlugRequest) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<String> changeLimitSoc(OrderLimitSocRequest req) {
                return RestUtils.serverBusy4ObjectResponse();
            }
        };
    }
}
