package com.chargerlinkcar.framework.common.domain.order;

import com.cdz360.biz.model.annotations.ExcelField;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "企业开票充电订单导出信息")
@Data
@Accessors(chain = true)
public class CorpInvoiceOrderExportVo implements Serializable {

    @ExcelField(title = "订单号", sort = 1)
    @Schema(description = "充电订单号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String orderNo;

    /**
     * 状态,-500：硬件故障；-40：上报超时；-35：断连超时；-30关电超时；-29：结束充电失败；-20：链接超时；-10：打开电闸超时；
     * -7：开启充电失败，PIN可能错误；-5:系统繁忙；0：订单未激活；100：电闸打开；200：充电中；250：关电闸，充电停止；
     * 300：充电完成；800：订单结束；1000：用户已支付；2000：钱已到账
     */
    @ExcelField(title = "订单状态", sort = 4)
    @Schema(description = "订单状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String status;

    @ExcelField(title = "手机号", sort = 6)
    @Schema(description = "手机号码")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String mobilePhone;

    @ExcelField(title = "站点名称", sort = 10)
    @Schema(description = "站点名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String stationName;

    @ExcelField(title = "开票金额(元)", sort = 12)
    @Schema(description = "可开票金额: 单位，元")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal invoiceAmount;

    @ExcelField(title = "订单总额(元)", sort = 16)
    @Schema(description = "订单金额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal orderPrice;

    @ExcelField(title = "实际金额(元)", sort = 19)
    @Schema(description = "本金(实际收入): 单位,元")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal principalAmount;

    @ExcelField(title = "赠送金额(元)", sort = 22)
    @Schema(description = "赠送金: 单位,元")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal freeGoldAmount;

//    @Schema(description = "已开票金额: 单位，元")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private BigDecimal invoicedAmount;

//    @Schema(description = "开票状态", example = "NO")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private TaxStatus taxStatus;

    @ExcelField(title = "创建时间", sort = 24, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "订单创建时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date createTime;

    @ExcelField(title = "结算时间", sort = 26, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "订单支付时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date payTime;

    @ExcelField(title = "充电时间", sort = 28)
    @Schema(description = "充电时间 充电开始时间 - 充电结束时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String chargeTime;

    @ExcelField(title = "上传时间", sort = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "订单上传时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date stopTime;
}
