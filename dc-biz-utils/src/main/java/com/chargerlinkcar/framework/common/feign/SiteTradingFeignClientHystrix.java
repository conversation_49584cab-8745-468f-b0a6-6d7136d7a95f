package com.chargerlinkcar.framework.common.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.chargerlinkcar.framework.common.domain.vo.SiteInMongo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SiteTradingFeignClientHystrix implements FallbackFactory<SiteTradingFeignClient> {
    @Override
    public SiteTradingFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_TRADING, throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_TRADING, throwable.getStackTrace());

        return new SiteTradingFeignClient() {


            @Override
            public ObjectResponse<SitePo> getSiteById(String siteId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<SiteInMongo> getSiteFromMongo(String siteId) {
                return RestUtils.serverBusy4ObjectResponse();
            }
        };
    }
}