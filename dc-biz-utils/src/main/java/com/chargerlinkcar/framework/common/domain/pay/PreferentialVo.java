package com.chargerlinkcar.framework.common.domain.pay;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "支付渠道优惠信息")
@Data
@Accessors(chain = true)
public class PreferentialVo {

    @Schema(name = "优惠金额")
    @JsonInclude(Include.NON_EMPTY)
    private BigDecimal preferentialAmount;

    @Schema(name = "总金额")
    @JsonInclude(Include.NON_EMPTY)
    private BigDecimal totalAmount;

    @Schema(name = "实付金额")
    @JsonInclude(Include.NON_EMPTY)
    private BigDecimal payAmount;

    @Schema(name = "优惠券编号")
    @JsonInclude(Include.NON_EMPTY)
    private String couponId;

}
