package com.chargerlinkcar.framework.common.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @since 2/25/2020 6:28 PM
 * <AUTHOR>
 */
@Data
public class AddUserRequest {
    private String username;
    private String mobile;

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private Long commercialId;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String nationalCode;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String password;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String email;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String avatar;
}