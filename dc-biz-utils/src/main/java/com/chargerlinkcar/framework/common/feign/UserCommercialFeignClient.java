package com.chargerlinkcar.framework.common.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.chargerlinkcar.core.domain.Commercial;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_USER, fallbackFactory = UserCommercialFeignClientHystrix.class)
public interface UserCommercialFeignClient {


    /**
     * 商户id查询指定商户
     *
     * @param commId
     * @return
     */
    @RequestMapping(value = "/api/commercial/getCommercialByCommId", method = RequestMethod.GET)
    ObjectResponse<Commercial> getCommercialByCommId(@RequestParam("commId") Long commId);

//    @GetMapping("/api/commercial/getCommercialManageByCommId")
//    ObjectResponse<CommercialManage> getCommercialManageByCommId(@RequestParam("commId") Long commId);

    /**
     * 根据token获取商户
     *
     * @param token
     * @return 商户
     */
    @RequestMapping(value = "/api/commercial/getCommercialByToken", method = RequestMethod.GET)
    ObjectResponse<Commercial> getCommercialByToken(@RequestParam("token") String token);

    /**
     * 根据商户id集合获取商户集合
     *
     * @param token
     * @return 商户
     */
    @RequestMapping(value = "/api/commercial/getCommercials", method = RequestMethod.POST)
    ListResponse<Commercial> getCommercials(@RequestParam("token") String token, @RequestParam("commIdChain") String commIdChain);



}
