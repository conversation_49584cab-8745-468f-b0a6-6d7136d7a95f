package com.chargerlinkcar.framework.common.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

/**
 * 按照场站汇总订单
 */
@Data
@Schema(description = "按照场站汇总订单")
public class ChargerOrderSite implements Serializable {

    /**
     * 场站ID
     */
    @Schema(description = "场站ID")
    private String siteId;

    /**
     * 场站名称
     */
    @Schema(description = "场站名称")
    private String siteName;

    /**
     * 场站性质
     */
    @Schema(description = "场站性质")
    private Long gcType;

    /**
     * 订单总数
     */
    @Schema(description = "订单总数")
    private Long amount;

}
