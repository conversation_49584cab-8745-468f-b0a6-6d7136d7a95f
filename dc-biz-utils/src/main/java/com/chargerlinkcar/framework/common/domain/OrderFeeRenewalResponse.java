package com.chargerlinkcar.framework.common.domain;

import com.cdz360.base.model.base.vo.BaseObject;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 充电中金额请求响应
 *
 * <AUTHOR>
 * @since Create on 2019/04/19
 * 参考iot-server\iot-model\src\main\java\com\cdz360\iot\model\evse\OrderFeeRefreshResponseV2.java
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class OrderFeeRenewalResponse extends BaseObject {

//    @Schema(description = "账户总余额, 单位'元'")
//    private BigDecimal totalAmount;
//
//    @Schema(description = "增量余额, 单位'元'")
//    private BigDecimal frozenAmount;

    @Schema(description = "账户总余额, 单位'元'")
    private BigDecimal totalAmount;

    @Schema(description = "增加的冻结金额, 单位'元'")
    private BigDecimal frozenAmount;

    @Schema(description = "增加后订单冻结金额, 单位'元'")
    private BigDecimal afterOrderFrozenAmount;

    @Schema(description = "电量抵扣时必传, 电量账户总余额, 单位'kwh'")
    private BigDecimal totalPower;

    @Schema(description = "电量抵扣时必传, 增加的冻结电量, 单位'kwh'")
    private BigDecimal frozenPower;

}
