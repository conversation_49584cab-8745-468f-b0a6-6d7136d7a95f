package com.chargerlinkcar.framework.common.domain.param;

import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.chargerlinkcar.framework.common.domain.StopMode;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 云端下发充电请求
 *
 * <AUTHOR>
 * @since 2019/04/29
 */
@Data
public class DcStartChargeRequest implements Serializable {

    private static final long serialVersionUID = 5692508517508316728L;

    private String orderNo;

    private String evseId;
    private String evseNo;
    private String plugNo;
//    private int plugId;

    private BigDecimal balance;

    private OrderStartType startType;

    private String accountNo;//账号, 此处为卡号 (逻辑卡号) 或 17位 VIN 码

    private BigDecimal amount;
    private BigDecimal totalAmount;
    private BigDecimal frozenAmount;
    private StopMode stopMode;

    // 扣款账户类型: 个人账户, 集团账户 Constant.PAY_PERSON_TYPE, Constant.PAY_BLOC_TYPE
    private Integer defaultPayType;

    //扣款账户类型
    private PayAccountType accountType;

    // 限制的最大SOC
    @JsonProperty("soc")
    private Integer limitSoc;

    // 差异化价格信息
    private ChargePriceVo price;
}
