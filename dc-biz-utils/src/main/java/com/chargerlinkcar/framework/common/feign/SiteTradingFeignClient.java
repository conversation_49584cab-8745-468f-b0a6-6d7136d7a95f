package com.chargerlinkcar.framework.common.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.common.constant.DcBizConstants;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.chargerlinkcar.framework.common.domain.vo.SiteInMongo;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 仅充电相关的场站相关数据接口
 * *** 非充电相关的场站数据访问要用 SiteDataCoreFeignClient ***
 */
@Component
@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_TRADING,
        fallbackFactory = SiteTradingFeignClientHystrix.class)
public interface SiteTradingFeignClient {



    /**
     * 根据站点ID获取站点简单信息
     *
     * @return
     */
    @PostMapping("/api/td/site/getSiteById")
    ObjectResponse<SitePo> getSiteById(@RequestParam("siteId") String siteId);


    @Cacheable(cacheNames = DcBizConstants.CACHE_NAME_MONGO_SITE)
    @PostMapping("/api/td/site/getSiteFromMongo")
    ObjectResponse<SiteInMongo> getSiteFromMongo(@RequestParam(value = "siteId") String siteId);

}
