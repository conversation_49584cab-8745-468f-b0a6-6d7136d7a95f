package com.chargerlinkcar.framework.common.domain.invoice.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.utils.JsonUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "查询开票主体列表参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListInvoicedTempSalParam extends BaseListParam {

    @Schema(description = "顶级商户ID")
    private Long topCommId;

    @Schema(description = "商户Id链", required = true)
    private String commIdChain;

    @Schema(description = "主体名称 模糊查询")
    private String saleName;

    @Schema(description = "纳税识别号 模糊查询")
    private String saleTin;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
