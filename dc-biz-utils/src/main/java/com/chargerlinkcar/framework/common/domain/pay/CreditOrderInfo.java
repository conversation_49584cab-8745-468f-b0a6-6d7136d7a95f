package com.chargerlinkcar.framework.common.domain.pay;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 信用充 - 查询支付订单结果
 */
@Data
public class CreditOrderInfo {

    @Schema(title = "第三方支付单号", description = "如微信/支付宝支付单号")
    private String partnerTradeNo;

    //    当前单据状态
    //    CREATED: 服务订单已创建
    //    DOING: 服务订单进行中
    //    DONE: 服务订单已完成
    //    REVOKED: 商户取消服务订单
    //    EXPIRED: 服务订单已失效，"CREATED"状态超过1小时未变动，则订单失效
    @Schema(description = "服务订单状态(微信反馈状态值)")
    private String orderState;

    // 是否已支付
    private Boolean paid;

    // 总金额
    private BigDecimal totalFee;

    @Schema(description = "用户是否已经确认")
    public Boolean getUserConfirm() {
        return "DOING".equals(orderState);
    }
}
