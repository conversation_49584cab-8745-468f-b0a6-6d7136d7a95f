package com.chargerlinkcar.framework.common.domain.pay;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 信用充 - 取消订单参数
 */
@Data
@Accessors(chain = true)
public class CreditCancelOrderParam {


    @Schema(title = "我方支付单号")
    private String tradeNo;


    @Schema(title = "芝麻信用订单号")
    private String creditOrderNo;

    @JsonProperty("reason")
    @Schema(title = "微信支付服务订单号")
    private String reason;

}
