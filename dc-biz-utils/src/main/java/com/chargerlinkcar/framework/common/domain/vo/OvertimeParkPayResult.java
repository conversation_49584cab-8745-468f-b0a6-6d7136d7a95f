package com.chargerlinkcar.framework.common.domain.vo;

import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.vo.BaseObject;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @since 2/21/2022 1:24 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "占位费支付返回")
public class OvertimeParkPayResult extends BaseObject {
    @Schema(description = "账户类型", example = "PERSONAL", required = true)
    private PayAccountType accountType;

    @Schema(description = "账户ID", required = true)
    private long accountId;

    @Schema(description = "商户ID(商户会员充值时必传)")
    private Long commId;

    @Schema(description = "充值金额, 单位'元', 2位小数", example = "123.45", required = true)
    private BigDecimal amount;
}