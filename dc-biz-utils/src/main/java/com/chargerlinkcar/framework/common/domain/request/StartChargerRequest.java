package com.chargerlinkcar.framework.common.domain.request;

import com.cdz360.base.model.charge.type.OrderStartType;
import com.chargerlinkcar.framework.common.domain.vo.SiteDebitAccountVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class StartChargerRequest extends SiteDebitAccountVo {

    @Schema(description = "要开启的枪头编号")
    private String plugNo;
    @Schema(description = "要开启的枪头编号列表")
    private List<String> plugNoList;

    @Schema(description = "通过car-gateway获取/通过业务逻辑赋值，无需前端传入", hidden = true, required = false)
    private Long sysUserId;
    @Schema(description = "通过业务逻辑赋值，无需前端传入", hidden = true, required = false)
    private OrderStartType startType;

    @Schema(description = "通过业务逻辑赋值，无需前端传入", hidden = true, required = false)
    private Long scheduleJobAccountId;
    @Schema(description = "通过业务逻辑赋值，无需前端传入", hidden = true, required = false)
    private Integer scheduleJobAccountType;

    @Schema(description = "停充SOC 批量开启SOC限制")
    @JsonInclude(Include.NON_NULL)
    private Integer stopSoc;
}
