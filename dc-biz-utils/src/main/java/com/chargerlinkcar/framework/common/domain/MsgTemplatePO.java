package com.chargerlinkcar.framework.common.domain;

import com.chargerlinkcar.framework.common.constant.CommercialMsgTemplate;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @since 2019/11/15 14:56
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MsgTemplatePO {
  private Long id;
  private Long topCommId;
  private CommercialMsgTemplate key;
  private String value;
  private String note;
  private Boolean enable;
  private Date createTime;
  private Date updateTime;
}
