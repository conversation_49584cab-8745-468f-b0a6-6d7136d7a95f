package com.chargerlinkcar.framework.common.domain.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 *  值班表信息
 * @since 2019-04-02 11:40
 */
@Data
public class SiteDutyInfo implements Serializable{

    /**
     * 值班表Id
     */
    private String dutyId;

    /**
     * 站点Id
     */
    private String siteId;

    /**
     * 周几（0是周日,1-6 为周一到周六）
     */
    private String week;

    /**
     * 0白班，1夜班
     */
    private String day;

    /**
     * 值班人姓名
     */
    private String name;

    /**
     * 值班人电话
     */
    private String tel;

    /**
     * 有效标志
     */
    private String yxBz;

    /**
     * 值班开始时间（HH：mm）
     */
    private String startTime;

    /**
     * 值班结束时间（HH：mm）
     */
    private String endTime;
}
