package com.chargerlinkcar.framework.common.service;

import com.cdz360.base.utils.NumberUtils;
import com.cdz360.biz.model.finance.type.BizType;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.data.sync.model.Site;
import com.cdz360.data.sync.service.DcEventPublisher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *  推送MQ消息
 * @since 2019/11/1
 * <AUTHOR>
 */
@Service
public class DcEventPublisherService {

    @Autowired(required = false)
    private DcEventPublisher dcEventPublisher;


    public void publishSiteInfo(SitePo site) {

        if (NumberUtils.equals(site.getBizType(), BizType.HLHT.getCode())) {
            return; //互联场站无需同步
        }
        Site siteEvent = new Site();
        siteEvent.setSiteId(site.getId())
            .setName(site.getSiteName())
            .setTopCommId(site.getTopCommId())
            .setCommId(site.getOperateId())
            .setStatus(site.getStatus())
            .setProvince(site.getProvince() == null ? null : site.getProvince().toString())
            .setCity(site.getCity() == null ? null : site.getCity().toString())
            .setArea(site.getArea() == null ? null : site.getArea().toString())
            .setAddress(site.getAddress())
            .setLon(site.getLongitude())
            .setLat(site.getLatitude())
            .setTimeZone(site.getTimeZone())
            .setPriceCode(site.getTemplateId())
            .setSiteType(site.getType())
            .setBizType(site.getBizType())
            .setCommPhone(site.getPhone())
            .setPhone(site.getContactsPhone())
            .setSiteNo(site.getSiteNo());
        this.dcEventPublisher.publishSiteInfo(siteEvent);
    }
}