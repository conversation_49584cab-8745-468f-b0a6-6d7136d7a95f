package com.chargerlinkcar.framework.common.domain;

import com.cdz360.base.model.base.type.UserType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

/**
 * @since 2019/12/2 10:05
 * <AUTHOR>
 */
@Data
public class OperatorInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "操作人类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private UserType opUserType;  // UserType

    @Schema(description = "操作人ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long opUid;

    @Schema(description = "操作人姓名")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String opName;

}
