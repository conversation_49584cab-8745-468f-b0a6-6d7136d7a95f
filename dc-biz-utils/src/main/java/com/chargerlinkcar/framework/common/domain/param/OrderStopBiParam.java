package com.chargerlinkcar.framework.common.domain.param;

import com.chargerlinkcar.framework.common.domain.request.BaseRequest;
import com.chargerlinkcar.framework.common.domain.type.GroupType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 异常订单汇总
 * <AUTHOR>
 */
@Data
public class OrderStopBiParam  {

    /**
     * 停充原因
     */
    private List<Long> stopCodeList;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;
    /**
     * 站点ID
     */
    private List<String> siteIdList;
    /**
     * 设备型号
     */
    private List<String> modelNameList;
    /**
     * 软件版本
     */
    private List<String> firmwareList;
    /**
     * 用户链
     */
    private String idChain;
    /**
     * 排序
     */
    private String order;
    /**
     * 分组
     */
    private GroupType groupBy;
    /**
     * 分页开始
     */
    private Long start;
    /**
     * 每页显示条数
     */
    private Long size;

}
