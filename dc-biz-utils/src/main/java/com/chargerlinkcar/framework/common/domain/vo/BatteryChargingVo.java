package com.chargerlinkcar.framework.common.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@Schema(description = "近30次电池充电/用电数据")
public class BatteryChargingVo {

    @Schema(description = "充电时间")
    private Date chargingTime;

    @Schema(description = "充电订单")
    private String orderNo;

    @Schema(description = "充电前soc")
    private Integer startSoc;

    @Schema(description = "充电soc(充电后SOC- 充电前SOC)")
    private Integer soc;

    @Schema(description = "单soc充电量(订单充电电量 ÷ 充电SOC)")
    private BigDecimal socCharge;

    @Schema(description = "最高电池单体温度", example = "11")
    private BigDecimal maxTemp;

    @Schema(description = "最低电池单体温度", example = "11")
    private BigDecimal minTemp;

    @Schema(description = "单体最高电压, 单位'伏'")
    private BigDecimal maxVoltage;

    @Schema(description = "单体最低电压, 单位'伏'")
    private BigDecimal minVoltage;

}
