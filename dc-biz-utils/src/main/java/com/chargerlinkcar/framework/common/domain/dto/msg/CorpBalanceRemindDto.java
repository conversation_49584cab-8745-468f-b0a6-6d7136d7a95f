package com.chargerlinkcar.framework.common.domain.dto.msg;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "企客余额推送数据")
@Data
@Accessors(chain = true)
public class CorpBalanceRemindDto {

    @Schema(description = "企业名称", required = true)
    @JsonInclude(Include.NON_NULL)
    private String corpName;

    @Schema(description = "企业余额", required = true)
    @JsonInclude(Include.NON_NULL)
    private BigDecimal remainder;
}
