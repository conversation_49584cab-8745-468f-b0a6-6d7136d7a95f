package com.chargerlinkcar.framework.common.domain;

import com.cdz360.biz.model.common.param.BaseListParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2019/11/12 18:55
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ListPointRecLogParam extends BaseListParam {

    @Schema(description = "用户ID")
    private String uid;

    @Schema(description = "用户二级ID")
    private String subUid;

    @Schema(description = "用户记录ID")
    private Long pid;

    @Schema(description = "变更流水号")
    private List<Long> seqNumList;

    @Schema(description = "积分记录ID")
    private List<Long> recIdList;

    @Schema(description = "变更的订单号")
    private List<String> orderNoList;

    @Schema(description = "标签")
    private String tag;

}
