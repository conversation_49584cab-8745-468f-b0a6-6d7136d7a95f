package com.chargerlinkcar.framework.common.domain;

import com.cdz360.base.model.base.vo.BaseObject;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
/**
 *  参考\iot-server\iot-model\src\main\java\com\cdz360\iot\model\evse\OrderDetailV2.java
 * @since 11/12/2019 2:06 PM
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderDetailV2 extends BaseObject {
    @Schema(description = "分段计费开始时间, unix时间戳", example = "1557111811")
    private Long startTime;
    @Schema(description = "分段计费结束时间, unix时间戳", example = "1557111811")
    private Long stopTime;
    @Schema(description = "价格分段编号")
    private Integer priceCode;
    @Schema(description = "当前累计电量, 单位'kwh'", example = "34567")
    private BigDecimal kwh;
    @Schema(description = "当前累计电费金额, 单位'元'")
    private BigDecimal elecFee;
    @Schema(description = "电费单价, 单位'元'")
    private BigDecimal electricUnit;
    @Schema(description = "当前累计服务费金额, 单位'元'")
    private BigDecimal servFee;
    @Schema(description = "服务费单价, 单位'元'")
    private BigDecimal serviceUnit;
}
