package com.chargerlinkcar.framework.common.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @since 2019/7/16 18:53
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
@ToString(callSuper = true)
@Accessors(chain = true)
@Schema(description = "订单统计数据（包含尖峰平谷）")
public class ChargerOrderDetailVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "订单总数")
    private OrderPowerVo orderNum;

    @Schema(description = "订单总金额: 单位，元")
    private OrderPowerVo orderPriceAmount;

    @Schema(description = "订单总电量: 单位，kwh")
    private OrderPowerVo electricityAmount;

    @Schema(description = "订单总电费: 单位，元")
    private OrderPowerVo elecPriceAmount;

    @Schema(description = "订单总服务费: 单位，元")
    private OrderPowerVo servicePriceAmount;

    /**
     * 判断是否有充电统计记录
     * @return
     */
    public boolean isEmpty() {
        return electricityAmount == null ||
                electricityAmount.getTotal() == null ||
                electricityAmount.getTotal().replaceAll("(\\.|0|,)", "").length() == 0;
    }
}
