package com.chargerlinkcar.framework.common.domain.invoice;


import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.invoice.vo.InvoiceApplyVo;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoicedRecordDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "开票记录")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class InvoicedRecordVo extends InvoicedRecordDTO implements Serializable {

    @Deprecated
    @Schema(description = "订单号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String orderId;

    @Deprecated(since = "20201230")
    @Schema(description = "电子票PDF")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String pdfUrl;

    @Deprecated(since = "20201230")
    @Schema(description = "电子票JPG")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String jpgUrl;

    @Schema(description = "电子票PDF")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> pdfUrlList;

    @Schema(description = "电子票JPG")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> jpgUrlList;

    @Schema(description = "申请开票拆分记录信息列表")
    @JsonInclude(Include.NON_NULL)
    private List<InvoiceApplyVo> applyVos;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
