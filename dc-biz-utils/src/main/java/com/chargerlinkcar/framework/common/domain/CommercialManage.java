package com.chargerlinkcar.framework.common.domain;

import com.cdz360.biz.model.sys.constant.SmsOperatorType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

@Data
public class CommercialManage implements Serializable {
    /**
     * 
     */
    private Long id;

    /**
     * 
     */
    private Long comId;

    /**
     * 短信验证码模板编号
     */
    private String smsModelVerifyCodeNo;

    /**
     * 
     */
    private String smsModelNationNo;

    /**
     * 
     */
    private String smsModelAppNo;

    /**
     * 发送定时充电结果短信
     */
    private String smsModelTimerChargeNo;
    /**
     * 短信推送个人账户充值模板
     */
    private String smsModelPersonCharge;
    /**
     * 短信推送商户会员充值模板
     */
    private String smsModelMerchantCharge;
    /**
     * 短信推送授信账户充值模板
     */
    private String smsModelCreditCharge;

    private SmsOperatorType smsOperatorType;

    /**
     * 短信apikey(云片平台apikey或动动客平台UID)
     */
    private String smsApiKey;

    /**
     * 短信平台用户密码(运营商为云片时不填，动动客时必填)
     */
    private String smsApiPwd;

    /**
     * 微信消息通知apikey
     */
    private String wxMsgApiKey;

    /**
     * 
     */
    private String wxMsgTemplateRecharge;

    /**
     * 
     */
    private String wxMsgTemplateRefund;

    /**
     * 
     */
    private String wxMsgTemplateChargingEnd;

    /**
     * 异常订单推送模板
     */
    private String wxMsgTemplateChargingEndAbnormal;

    /**
     * 
     */
    private String wxMsgTemplateChargingStartAfter;

    /**
     * 
     */
    private String wxMsgTemplateChargingStartPre;

    /**
     * 支付apikey
     */
    private String payApiKey;

    /**
     * 微信appId
     */
    private String wxAppid;

    /**
     * 微信appSecret
     */
    private String wxAppSecret;

    /**
     * 微信wxGrantType
     */
    private String wxGrantType;


    /**
     * 微信公众号appId
     */
    private String wechatAppid;

    /**
     * 微信公众号AppSecret
     */
    private String wechatAppSecret;

    @Schema(description = "支付宝小程序appid")
    private String alipayAppletAppId;

    @Schema(description = "支付宝公钥")
    private String alipayPubKey;

    @Schema(description = "支付宝私钥")
    private String alipayPrvKey;

    // 支付宝验签升级: 证书相关参数
    @Schema(description = "支付宝小程序应用公钥证书路径")
    private String alipayAppletCertPath;

    @Schema(description = "支付宝公钥证书文件路径")
    private String alipayPublicCertPath;

    @Schema(description = "支付宝CA根证书文件路径")
    private String alipayRootCertPath;
    // 支付宝验签升级: 证书相关参数

    /**
     * 友盟ios appId
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String uPushIosAppId;


    /**
     * 友盟ios appSecret
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String uPushIosAppSecret;

    /**
     * 友盟android appId
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String uPushAndroidAppId;


    /**
     * 友盟android appSecret
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String uPushAndroidAppSecret;

    /**
     * t_commercial_manage
     */
    private static final long serialVersionUID = 1L;

}