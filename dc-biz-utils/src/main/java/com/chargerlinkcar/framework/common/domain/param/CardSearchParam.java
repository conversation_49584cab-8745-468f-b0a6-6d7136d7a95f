package com.chargerlinkcar.framework.common.domain.param;

import java.util.Locale;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @since 2020/1/2 17:24
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CardSearchParam {
    private String cardStatus;
    private String beginTime;
    private String endTime;
    private String keyword;
    private Long topCommId;
    private Long commId;
    private String commIdChain;
    private String corpName;
    private Integer isPackage;
    //    private String stationName;
    private String siteName;
    private String carNo;
    private Long leaderCommId;// 用于转换成idChain
    private List<String> cardNoList;
    private Long cardType;
    private Integer deposit;
    /**
     * 区域语言信息
     */
    private Locale locale;
}
