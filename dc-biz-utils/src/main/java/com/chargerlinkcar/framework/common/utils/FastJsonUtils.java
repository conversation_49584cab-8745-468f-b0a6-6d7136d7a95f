package com.chargerlinkcar.framework.common.utils;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.serializer.JSONLibDataFormatSerializer;
//import com.alibaba.fastjson.serializer.SerializeConfig;
//
///**
// * <AUTHOR> FastJson工具类
// * @since 2018/11/22 15:15
// */
//public class FastJsonUtils {
//
//
//    private static SerializeConfig CONFIG = null;
//
//    static {
//        CONFIG = new SerializeConfig();
//        // 使用和json-lib兼容的日期输出格式
//        CONFIG.put(java.util.Date.class, new JSONLibDataFormatSerializer());
//        // 使用和json-lib兼容的日期输出格式
//        CONFIG.put(java.sql.Date.class, new JSONLibDataFormatSerializer());
//    }
//
//
//    /**
//     * Object 转换成字符串
//     *
//     * @param object
//     * @return
//     */
//    public static String toJSONString(Object object) {
//        return JsonUtils.toJsonString(object, CONFIG);
//    }
//
//
//}
