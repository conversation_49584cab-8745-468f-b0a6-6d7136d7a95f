package com.chargerlinkcar.framework.common.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "站点周边信息")
public class SiteAroundVo {

    /**
     * ID编号
     */
    private String id;
    /**
     * 标题
     */
    private String title;
    /**
     * 地址
     */
    private String address;
    /**
     * 手机号
     */
    private String tel;

    /**
     * 距离
     */
    private double _distance;

    /**
     * 经纬度
     */
    private Location location;

}
