package com.chargerlinkcar.framework.common.utils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *         类型适配转换
 */
//@Slf4j
//public class TypeConversion {
//
//    public static String orderSource(String source) {
//
//        try {
//            if (source != null && !"".equals(source)) {
//                return orderSourceEnum.valueOf(source).getNewSorce() + "";
//            }
//        } catch (Exception e) {
//            log.error("转换失败", e);
//            return null;
//        }
//        return null;
//    }
//
//    public enum orderSourceEnum {
//        card(11, "card", "11_1"),
//        wechatPub(12, "wechatPub", "12_1"),
//        wechat(12, "wechat", "12_2"),
//        alipay_mini(13, "alipay_mini", "13_1"),
//        alipay(13, "alipay",  "13_2"),
//        android(14, "android", "14_1"),
//        ios(14, "ios", "14_2"),
//        wechatPub_bluetooth(15,"wechatPub_bluetooth","15_1"),
//        wechat_bluetooth(15,"wechat_bluetooth","15_2"),
//        alipay_mini_bluetooth(15,"alipay_mini_bluetooth","15_3"),
//        alipay_bluetooth(15,"alipay_bluetooth","15_4"),
//        android_bluetooth(15,"android_bluetooth","15_5"),
//        ios_bluetooth(15,"ios_bluetooth","15_6"),
//        /** 订单来源增加投币 tianms 2018.04.09 */
//        wechatPub_coins(16,"wechatPub_coins","16_1"),
//        wechat_coins(16,"wechat_coins","16_2"),
//        alipay_mini_coins(16,"alipay_mini_coins","16_3"),
//        alipay_coins(16,"alipay_coins","16_4"),
//        android_coins(16,"android_coins","16_5"),
//        ios_coins(16,"ios_coins","16_6"),
//        H5(17,"H5","17_1")
//        ,SCOP(20,"SCOP","20_1")
//        ;
//        private int newSorce;
//        private String context;
//        private String sourceType;
//
//        orderSourceEnum() {
//
//        }
//
//        private orderSourceEnum(int newSorce, String context, String sourceType) {
//            this.newSorce = newSorce;
//            this.sourceType = sourceType;
//            this.context = context;
//        }
//
//        /**
//         * @return the newSorce
//         */
//        public int getNewSorce() {
//            return newSorce;
//        }
//
//        /**
//         * @param newSorce the newSorce to set
//         */
//        public void setNewSorce(int newSorce) {
//            this.newSorce = newSorce;
//        }
//
//        /**
//         * @return the context
//         */
//        public String getContext() {
//            return context;
//        }
//
//        /**
//         * @param context the context to set
//         */
//        public void setContext(String context) {
//            this.context = context;
//        }
//
//        public String getSourceType() {
//            return sourceType;
//        }
//
//        public void setSourceType(String sourceType) {
//            this.sourceType = sourceType;
//        }
//
//
//    }
//
//}
