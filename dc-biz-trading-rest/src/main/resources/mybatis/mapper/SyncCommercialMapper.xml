<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.trading.repository.SyncCommercialMapper">

    <!--`status` 数据库中默认值 1-->
    <update id="syncCommercial" parameterType="com.cdz360.data.sync.model.DzCommercial">
        insert into t_r_commercial(id, pid, comm_level, merchants, comm_type,
        topCommId, comm_name, short_name, phone, idChain
        <if test="status != null">
            , `status`
        </if>)
        values( #{id}, #{pid}, #{commLevel}, #{merchants}, #{commType},
        #{topCommId}, #{commName}, #{shortName}, #{phone}, #{idChain}
        <if test="status != null">
            , #{status}
        </if>)
        ON DUPLICATE KEY UPDATE `pid` = #{pid},
        comm_level = #{commLevel},
        merchants = #{merchants},
        comm_type = #{commType},
        topCommId = #{topCommId},
        comm_name = #{commName},
        `short_name` = #{shortName},
        `status` = #{status},
        phone = #{phone},
        idChain = #{idChain}
    </update>


</mapper>