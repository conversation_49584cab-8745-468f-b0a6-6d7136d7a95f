<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.trading.repository.DcInvoiceOrderMapper">


    <!-- 发票订单列表 -->
    <select id="queryChargerOrders" parameterType="java.util.Map"
            resultType="com.chargerlinkcar.framework.common.domain.invoice.DcInvoiceOrderVo">
        SELECT
        t.order_no AS orderId,
        t.order_no AS orderNo,
        t.device_commercial_id AS commercialId,
        t.station_id AS stationId,
		t.customer_id AS customerId,
		u.id AS userId,
		u.username,
		u.phone userPhone,
		t.topCommId,
		t.commercial_name AS commercialName,
		pay.orderFee AS orderPrice,
		pay.orderFee AS actualPrice,
		t.status,
		t.order_status AS orderStatus,
		t.mobile_phone AS mobilePhone,
		t.station_name AS stationName,
		t.customer_name AS customerName,
		t.create_time as orderTime,
		t.stop_time AS stopTime,
		t.charge_Start_Time AS chargeStartTime,
		t.charge_end_time AS chargeEndTime,
		t.start_electricity AS startElectricity,
		t.end_electricity AS endElectricity,
		t.order_electricity AS orderElectricity,
		t.duration AS chargingTimes,
		t.invoiced_id AS invoicedId,
		pay.invoiceAmount as invoiceAmount,
		pay.servCostFee as servActualFee,
		pay.elecCostFee as elecActualFee,
        topo.parkingFee as parkActualFee,
			  topo.cancelStatus as parkCancelStatus
		FROM
		t_charger_order t
		left join t_charger_order_pay pay on pay.orderNo = t.order_no
        LEFT JOIN d_card_manager.t_user u ON t.customer_id = u.id
        left join t_overtime_parkfee_order topo on topo.orderNo = t.order_no
		where 1=1
		<if test="commonIdList!=null and commonIdList.size()>0">
			and t.device_commercial_id in
			<foreach collection="commonIdList" index="index" item="item" open="("
			         separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="status !=null and status != ''">
			and t.status=#{status}
		</if>
        <if test="orderStatus !=null and orderStatus != ''">
            and t.order_status=#{orderStatus}
        </if>
		<if test="customerId != null and customerId !='' ">
			and u.id =#{customerId} and u.id is NOT NULL
		</if>
		<if test="invoicedId != null">
			and t.invoiced_id =#{invoicedId}
		</if>
		<if test="username!=null and username!=''">
			and u.username like
			CONCAT('%',#{username},'%')
		</if>
		<if test="createTime!=null">
			<![CDATA[and t.create_time>=#{createTime}]]>
		</if>
		<if test="endTime!=null">
			<![CDATA[and t.create_time<=#{endTime}]]>
		</if>
		<if test="invoiceAmount != null">
			and pay.invoiceAmount != #{invoiceAmount}
		</if>
		ORDER BY t.create_time DESC
	</select>

	<!-- 设置订单开票ID -->
	<update id="fillInvoicedId" parameterType="java.util.Map">

		UPDATE t_charger_order
		SET invoiced_id = #{invoicedId}
		WHERE 1=1
		<if test="orderNoList != null and orderNoList.size() >0">
			and order_no IN
			<foreach collection="orderNoList" index="index" item="item" open="("
					 separator="," close=")">
				#{item}
			</foreach>
		</if>
		and invoiced_id = '0'
	</update>
	
	<!-- 清空订单开票ID -->
	<update id="emptyInvoicedIds">
		UPDATE t_charger_order
		SET invoiced_id ='0'
		WHERE invoiced_id IN
		<foreach collection="invoicedIdList" index="index" item="item" open="("
				 separator="," close=")">
			#{item}
		</foreach>
	</update>
	
	
	<!-- 根据用户ID查询订单信息 -->
	<select id="queryUserOrdersByUserId" parameterType="java.util.Map"
	        resultType="com.chargerlinkcar.framework.common.domain.invoice.DcInvoiceOrderVo">
		SELECT
		IF((select max(id) from t_order_overtime_park_division pd where pd.orderNo = t.order_no and pd.enable = true)> 0, true, false) as hasOvertimeDivision,
		t.order_no AS orderId,
		t.order_no AS orderNo,
		t.device_commercial_id AS commercialId,
		t.station_id AS stationId,
		t.customer_id AS customerId,
		u.id AS userId,
		u.username,
		t.topCommId,
		t.commercial_name AS commercialName,
		pay.orderFee AS orderPrice,
		pay.orderFee AS actualPrice,
		t.status,
		t.order_status AS orderStatus,
		t.mobile_phone AS mobilePhone,
		t.station_name AS stationName,
		t.customer_name AS customerName,
		t.create_time as orderTime,
		t.stop_time AS stopTime,
		t.charge_Start_Time AS chargeStartTime,
		t.charge_end_time AS chargeEndTime,
		t.start_electricity AS startElectricity,
		t.end_electricity AS endElectricity,
		t.order_electricity AS orderElectricity,
		t.duration AS chargingTimes,
		t.invoiced_id AS invoicedId,
		pay.invoiceAmount as invoiceAmount,
		pay.servCostFee as servActualFee,
		pay.elecCostFee as elecActualFee,
		tp.parkingFee,
	   	tp.parkingPrice,
	   	tp.calFromTime,
	   	tp.calToTime,
		tp.status as parkStatus,
		tp.cancelStatus as parkCancelStatus
		FROM
		t_charger_order t
		left join t_charger_order_pay pay on pay.orderNo = t.order_no
        LEFT JOIN d_card_manager.t_user u ON t.customer_id = u.id
		LEFT JOIN t_site site ON t.station_id = site.id
		LEFT JOIN t_overtime_parkFee_order tp ON tp.orderNo = t.order_no
		where 1=1
		<if test="siteValid !=null">
			and site.invoiced_valid!=#{siteValid}
		</if>
		<if test="status !=null">
			and t.status=#{status}
		</if>
        <if test="orderStatus !=null and orderStatus != ''">
            and t.order_status=#{orderStatus}
        </if>
		<if test="customerId != null and customerId !='' ">
			and u.id =#{customerId} and u.id is NOT NULL
		</if>
        <if test="keywords != null ">
            and t.invoiced_id!=#{keywords}
        </if>
		<if test="keyword != null ">
			and t.invoiced_id = #{keyword}
        </if>
		<if test="isHalfYear != null and isHalfYear">
			and t.create_time>=DATE_FORMAT(DATE_SUB(CURDATE(),  INTERVAL 183 DAY),'%Y-%m-%d %H:%i:%s')
		</if>
<!--        <if test="invoiceAmount != null">-->
<!--            and t.invoiceAmount != #{invoiceAmount}-->
<!--        </if>-->
		<!-- 存在可开票金额 -->
		and pay.invoiceAmount > 0
		<!-- 过滤异常订单 -->
<!--		<![CDATA[ AND (t.abnormal is null)]]>-->
		<!-- 用户端不显示授信账户充电订单 -->
		AND t.defaultPayType != 2
		ORDER BY t.create_time DESC
	</select>

	<select id="queryOrdersByOrderId" parameterType="java.util.Map"
			resultType="com.chargerlinkcar.framework.common.domain.invoice.DcInvoiceOrderVo">
		SELECT
		*
		From
		t_charger_order t
		left join t_charger_order_pay pay on pay.orderNo = t.order_no
		where 1=1
		<if test="orderNoList != null and orderNoList.size() >0">
			and t.order_no IN
			<foreach collection="orderNoList" index="index" item="item" open="("
					 separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="status !=null">
			and t.status=#{status}
		</if>
        <if test="orderStatus !=null">
            and t.order_status=#{orderStatus}
        </if>
		<if test="keyword != null ">
			and pay.invoicedId = #{keyword}
		</if>
		<if test="keywords != null ">
			and pay.invoicedId!=#{keywords}
		</if>
<!--		<if test="invoiceAmount != null">-->
<!--			and t.invoiceAmount != #{invoiceAmount}-->
<!--		</if>-->
		<!-- 存在可开票金额 -->
		<if test="invoicePlatformSource == 0 ">
			and pay.invoiceAmount > 0
		</if>

	</select>
</mapper>