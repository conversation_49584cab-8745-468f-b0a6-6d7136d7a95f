package com.cdz360.biz.trading.rest;


import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.cus.wallet.param.CreateDepositOrderParam;
import com.cdz360.biz.model.trading.order.po.PayBillPo;
import com.cdz360.biz.model.trading.order.type.OldPayType;
import com.cdz360.biz.trading.domain.PointDeposit;
import com.cdz360.biz.trading.service.BalanceServiceImpl;
import com.cdz360.biz.trading.service.ChargerOrderService;
import com.cdz360.biz.trading.service.PostDepositService;
import com.chargerlinkcar.framework.common.domain.pay.PaySign;
import com.chargerlinkcar.framework.common.domain.vo.CommercialCheckVo;
import com.chargerlinkcar.framework.common.domain.vo.NotifyPay;
import com.chargerlinkcar.framework.common.domain.vo.PrepayMinVo;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import java.math.BigDecimal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户余额相关
 *
 * <AUTHOR>
 * @since 2018-09-27 9:50
 **/
@Slf4j
@RestController
@RequestMapping("/api/balance")
public class BalanceRest extends BaseController {

    @Autowired
    private BalanceServiceImpl balanceService;


    @Autowired
    private ChargerOrderService chargerOrderService;

    @Autowired
    private PostDepositService postDepositService;


    @Operation(summary = "创建充值订单")
    @PostMapping(value = "/createDepositOrder")
    public ObjectResponse<PaySign> createDepositOrder(ServerHttpRequest request,
        @RequestBody CreateDepositOrderParam param) {

        log.info("创建充值订单。{}, param = {}", LoggerHelper2.formatEnterLog(request),
            JsonUtils.toJsonString(param));

        long money = 0L;
        if (!PayAccountType.OTHER.equals(param.getAccountType())) {
            money = param.getAmount().multiply(BigDecimal.valueOf(100)).longValue();
        }

        PointDeposit pointDeposit = new PointDeposit();
        pointDeposit.setTopCommId(param.getTopCommId());
        pointDeposit.setCommId(param.getCommId());
        pointDeposit.setUid(param.getCusId());
        pointDeposit.setAmount(money);
        pointDeposit.setClientIp(param.getClientIp());
        pointDeposit.setRemark(param.getRemark());
        pointDeposit.setPayChannel(param.getPayChannel());
        pointDeposit.setOpenId(param.getOpenid());
        pointDeposit.setSourceId(param.getAppClientType().getCode());
        pointDeposit.setFormId(param.getFormId());
        pointDeposit.setPayAccountType(param.getAccountType());
        pointDeposit.setMerchants(null);
        pointDeposit.setDigiccyAccount(param.getDigiccyAccount());
        pointDeposit.setPayNo(param.getPayNo());
        if (com.cdz360.base.utils.StringUtils.isNotBlank(param.getChargeOrderNo())) {
            pointDeposit.setChargeOrderNo(param.getChargeOrderNo());
            pointDeposit.setPayType(OldPayType.ORDER_DEBT);
        } else {
            pointDeposit.setChargeOrderNo(null);
            pointDeposit.setPayType(OldPayType.BALANCE_RECHARGE);
        }

        if (com.cdz360.base.utils.StringUtils.isNotBlank(param.getOvertimeParkOrderNo())) {
            pointDeposit.setOvertimeParkOrderNo(param.getOvertimeParkOrderNo());
            pointDeposit.setPayType(OldPayType.OVERTIME_PARK_DEBT);
        }

        ObjectResponse<PaySign> resultEntity = balanceService.startDeposit(pointDeposit,
            param.getAppClientType());

        // 数币反馈支付结果
        if (PayChannel.DIGICCY_ACCOUNT.equals(param.getPayChannel())) {
            balanceService.digiccyPayNotify(pointDeposit, resultEntity.getData());
        }

        return resultEntity;
    }

    /**
     * 余额充值/即充即退/微信分确认 回调（支付宝和微信支付）
     *
     * @param notifyPay 回调数据
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/notifyDeposit")
    public BaseResponse notifyDeposit(@RequestBody NotifyPay notifyPay) {
        log.info("支付回调。支付回调请求参数：{}", notifyPay);

        String result_code = notifyPay.getResult_code();
        String notify_type = notifyPay.getNotify_type();
        if (!org.apache.commons.lang3.StringUtils.equals("pay", notify_type)) {
            log.error("回调类型错误,notifyPay:{}", JsonUtils.toJsonString(notifyPay));
            throw new DcServiceException("回调类型错误");
        }

        BaseResponse resultEntity = balanceService.notifyDeposit(notifyPay);
        //this.send(notifyPay);
        return resultEntity;

    }


    @PostMapping("/fixFrozenAbnormal")
    public BaseResponse fixFrozenAbnormal(@RequestBody NotifyPay notifyPay) {
        log.info("即充即退账户冻结失败导致订单异常处理: notifyPay = {}", notifyPay);
        postDepositService.fixFrozenAbnormal(notifyPay);
        return RestUtils.success();
    }

    /**
     * 及充即退 退款回调（支付宝和微信支付）--------- 其实这个回调仅微信会调用，并没有支付宝的。 朱子成 2019-10-28
     *
     * @param notifyPay 回调数据
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/notifyRefund")
    public BaseResponse notifyRefund(@RequestBody NotifyPay notifyPay) {
        log.info("退款回调(处理成功回调): notifyPay = {};", JsonUtils.toJsonString(notifyPay));
//        // 退款相关操作已在调用接口同步处理，此处回调可以忽略
//        return RestUtils.success();

        String result_code = notifyPay.getResult_code();
        String notify_type = notifyPay.getNotify_type();

        if (com.cdz360.base.utils.StringUtils.equals("refund", notify_type) &&
            com.cdz360.base.utils.StringUtils.equals("SUCCESS", result_code)) {
            balanceService.refundApplySuccess(notifyPay);
        }

        return RestUtils.success();
    }


    /**
     * 用户充值时，根据商户号查询商户信息
     *
     * @return
     */
    @Operation(summary = "用户充值时，根据商户号查询商户信息")
    @GetMapping("/getCommInfoAndCheck")
    public ObjectResponse<CommercialCheckVo> getCommInfoAndCheck(
        @Parameter(name = "商户号") @RequestParam(value = "merchants", required = false) String merchants,
        @Parameter(name = "集团商户id") @RequestParam(value = "appCommId", required = false) long appCommId) {

        if (StringUtils.isEmpty(merchants)) {
            throw new DcServiceException("参数错误");
        }

        if (appCommId <= 0) {
            throw new DcServiceException("参数错误");
        }
        ObjectResponse<CommercialCheckVo> res = balanceService.getCommInfoAndCheck(merchants,
            appCommId);
        return res;
    }

    @Operation(summary = "获取即充即退的最小充值金额")
    @GetMapping("/getMinPrepayAmount")
    public ObjectResponse<PrepayMinVo> getMinPrepayAmount() {
        return chargerOrderService.getMinPrepayAmount();
    }

    @Operation(summary = "充值后逻辑处理")
    @PostMapping(value = "/postDeposit")
    public BaseResponse postDeposit(@RequestBody PayBillPo po) {
        postDepositService.otherDeposit(po);
        return RestUtils.success();
    }
}
