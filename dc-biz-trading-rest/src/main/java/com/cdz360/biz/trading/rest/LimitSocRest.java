package com.cdz360.biz.trading.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.trading.service.LimitSocService;
import com.chargerlinkcar.framework.common.domain.request.ExpectLimitSocReq;
import com.chargerlinkcar.framework.common.domain.request.LimitSocFeedbackResult;
import com.chargerlinkcar.framework.common.rest.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * LimitSocRest
 *
 * @since 6/1/2021 2:13 PM
 * <AUTHOR>
 */
@Slf4j
@RestController
public class LimitSocRest extends BaseController {

    @Autowired
    private LimitSocService limitSocService;

    @PostMapping(value = "/soc/limitSoc/changeExpectLimitSoc")
    public BaseResponse changeExpectLimitSoc(@RequestBody ExpectLimitSocReq req) {
        log.info("修改期望限制的soc值: {}", JsonUtils.toJsonString(req));
        return limitSocService.changeExpectLimitSoc(req);
    }

    @PostMapping(value = "/soc/limitSoc/feedback")
    public BaseResponse feedback(@RequestBody LimitSocFeedbackResult req) {
        log.info("修改期望限制的soc值: {}", JsonUtils.toJsonString(req));
        return limitSocService.feedback(req);
    }
}