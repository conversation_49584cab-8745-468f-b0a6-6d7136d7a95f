package com.cdz360.biz.trading.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.ChargeOrderStatus;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.model.order.param.UpdateInvoiceIdParam;
import com.cdz360.biz.trading.service.DcInvoiceOrderServiceImpl;
import com.chargerlinkcar.framework.common.domain.invoice.DcInvoiceOrderVo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 发票订单相关
 *
 * <AUTHOR>
 * @since Created on 17:59 2019/3/7.
 */
@Slf4j
@RestController
@RequestMapping("/api/invoiced")
public class DcInvoiceOrderRest {


    @Autowired
    private DcInvoiceOrderServiceImpl dcInvoiceService;


    /**
     * 查询订单列表
     *
     * @param token
     * @param username
     * @param status
     * @param customerId
     * @param invoicedId
     * @param index
     * @param size
     * @return
     */
    @PostMapping(value = "/chargerOrders")
    public ListResponse<DcInvoiceOrderVo> queryChargerOrders(
            ServerHttpRequest request,
            @RequestHeader("token") String token,
            @RequestParam(value = "username", required = false) String username,
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "orderStatus", required = false) ChargeOrderStatus orderStatus,
            @RequestParam(value = "customerId", required = false) Long customerId,
            @RequestParam(value = "invoicedId", required = false) Long invoicedId,
            @RequestParam(value = "index", required = true) Integer index,
            @RequestParam(value = "size", required = true) Integer size,
            @RequestParam(value = "createTime", required = false) String createTime,
            @RequestParam(value = "endTime", required = false) String endTime) {
        log.info(LoggerHelper2.formatEnterLog(request));

        Map<String, Object> map = new HashMap<>();
        //用户名
        map.put("username", username);
        //状态
        map.put("status", status);
        // 订单状态
        map.put("orderStatus", orderStatus);
        //用户ID
        map.put("customerId", customerId);
        //发票ID
        map.put("invoicedId", invoicedId);
        //当前页
        map.put("index", index);
        //每页显示条数
        map.put("size", size);
        //时间
        map.put("createTime", createTime);
        map.put("endTime", endTime);
        return dcInvoiceService.queryChargerOrders(token, map);
    }

    /**
     * 设置订单的开票申请ID
     *
     * @param orderNos
     * @param invoicedId
     * @return
     */
    @PostMapping(value = "/chargerOrders/fillInvoicedId")
    public ObjectResponse<String> fillInvoicedId(@RequestParam(value = "orderNos") String orderNos,
                                                 @RequestParam(value = "invoicedId") Long invoicedId) {

        if (null == invoicedId) {
            throw new DcArgumentException("发票记录Id无效");
        }

        if (StringUtils.isBlank(orderNos)) {
            throw new DcArgumentException("桩编号列表不能为空");
        }

        UpdateInvoiceIdParam param = new UpdateInvoiceIdParam();
        param.setInvoiceId(invoicedId)
                .setOrderNoList(List.of(orderNos.split(",")));
        return dcInvoiceService.fillInvoicedId(param);
    }

    /**
     * 清空订单的开票申请ID
     *
     * @param invoicedIds
     * @return
     */
    @PostMapping(value = "/chargerOrders/emptyInvoicedIds")
    public ObjectResponse<String> emptyInvoicedIds(@RequestParam(value = "invoicedIds") String invoicedIds) {
//        Map<String, Object> map = new HashMap<>();
//        map.put("invoicedIds", invoicedIds);
        return dcInvoiceService.emptyInvoicedIds(invoicedIds);
    }


    /**
     * 根据用户ID查询用户订单
     *
     * @param userId
     * @param index
     * @param size
     * @return
     */
    @Operation(summary = "根据用户ID查询用户订单列表(可开票)")
    @PostMapping("/queryUserOrdersByUserId")
    public ListResponse<DcInvoiceOrderVo> queryUserOrdersByUserId(
            @RequestParam(value = "userId") String userId,
            @RequestParam(value = "index") Integer index,
            @RequestParam(value = "size") Integer size,
            @RequestParam(value = "keywords", required = false) Long keywords,
            @RequestParam(value = "isHalfYear", required = false) Boolean isHalfYear) {
        Map<String, Object> map = new HashMap<>();
        //用户ID
        map.put("customerId", userId);
        //当前页
        map.put("index", index);
        //每页显示条数
        map.put("size", size);
        map.put("keywords", keywords);
        //是否只显示近半年可开票
        map.put("isHalfYear",isHalfYear);
        return dcInvoiceService.queryUserOrdersByUserId(map);
    }

    /**
     * 查询id查询已开票订单
     *
     * @param orderNoList
     * @return
     */
    @PostMapping(value = "/queryChargerOrdersByOrderNoList", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<DcInvoiceOrderVo> queryChargerOrdersByIds(
            @RequestBody List<String> orderNoList, @RequestParam(value = "invoicePlatformSource") Integer invoicePlatformSource) {
        Map<String, Object> map = new HashMap<>();
        //订单id
        map.put("orderNoList", orderNoList);
        map.put("invoicePlatformSource", invoicePlatformSource);
        return dcInvoiceService.queryChargerOrdersByIds(map);
    }
}
