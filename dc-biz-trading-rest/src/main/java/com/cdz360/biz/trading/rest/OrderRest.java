package com.cdz360.biz.trading.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.bi.vo.OrderBi;
import com.cdz360.base.model.charge.type.OrderAbnormalReason;
import com.cdz360.base.model.charge.vo.ChargeOrderVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderCarRoDs;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.order.param.PayOrderParam;
import com.cdz360.biz.model.trading.order.param.VinOrderCountParam;
import com.cdz360.biz.model.trading.order.po.ChargerOrderPayPo;
import com.cdz360.biz.model.trading.order.po.ChargerOrderPo;
import com.cdz360.biz.model.trading.order.po.ChargerOrderTimeDivision;
import com.cdz360.biz.trading.biz.OrderPaymentBizService;
import com.cdz360.biz.trading.domain.VinOrderCount;
import com.cdz360.biz.trading.domain.vo.ChargerOrderFinishVo;
import com.cdz360.biz.trading.service.ChargerOrderQueryService;
import com.cdz360.biz.trading.service.ChargerOrderService;
import com.cdz360.biz.trading.service.ManageChargerOrderServiceImpl;
import com.cdz360.biz.trading.service.OrderMongoDataServiceImpl;
import com.cdz360.biz.trading.service.OverTimeParkingService;
import com.cdz360.biz.trading.service.OvertimeParkOrderService;
import com.cdz360.biz.trading.service.PushServiceImpl;
import com.cdz360.biz.trading.utils.RedisUtil;
import com.chargerlinkcar.core.common.service.EventPublisherService;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.NoCardPayAccountInfo;
import com.chargerlinkcar.framework.common.domain.param.CardsParam;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import com.chargerlinkcar.framework.common.domain.param.MonitorChargerOrderParam;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrder;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDataVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDetailVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import com.chargerlinkcar.framework.common.domain.vo.CreateChargeOrderParam;
import com.chargerlinkcar.framework.common.domain.vo.UpdateOrderVo;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.AssertUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import com.github.pagehelper.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR> 订单相关
 * @since 2018/11/23 11:08
 */
@Slf4j
@RestController
public class OrderRest extends BaseController {

    @Autowired
    private ChargerOrderCarRoDs chargerOrderCarRoDs;
    @Autowired
    private ChargerOrderService chargerOrderService;
    @Autowired
    private ChargerOrderQueryService chargerOrderQueryService;

    @Autowired
    private OvertimeParkOrderService overtimeParkOrderService;

    @Autowired
    private ManageChargerOrderServiceImpl manageChargerOrderService;


    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private EventPublisherService eventPublisherService;

    @Autowired
    private OrderPaymentBizService orderPaymentBizService;
    @Autowired
    private OrderMongoDataServiceImpl orderMongoDataService;
    @Autowired
    private PushServiceImpl wxPushService;

    @Autowired
    private OverTimeParkingService overTimeParkingService;


    /**
     * 根据条件获取最近一年内的订单总数(订单创建时间)
     *
     * @param param
     * @return
     */
    @PostMapping("/api/order/selectByConditionAndOneYear")
    public ListResponse<ChargerOrderVo> selectByConditionAndOneYear(@RequestBody CardsParam param) {
        return chargerOrderQueryService.selectByConditionAndOneYear(param);
    }


    /**
     * 检查是否能充电
     *
     * @param param
     * @return
     */
    @PostMapping("/api/order/checkAbility")
    public Mono<BaseResponse> checkAbility(@RequestBody CreateChargeOrderParam param) {
        log.info("检查是否能充电: {}", JsonUtils.toJsonString(param));
        return this.chargerOrderService.checkAbility(param);
    }


    /**
     * 根据订单号订单详情
     *
     * @param orderNo
     * @return
     */
    @PostMapping(value = "/api/order/selectByOrderNo")
    public ObjectResponse<ChargerOrder> selectByOrderNo(@RequestParam("orderNo") String orderNo) {
        log.info("获取订单详情orderNo：{}", orderNo);
        if (StringUtils.isBlank(orderNo)) {
            throw new DcArgumentException("获取订单信息失败，orderNo为空");
        }
        try {
            ChargerOrder chargerOrder = chargerOrderService.getChargeOrder(orderNo, false);
            log.info("获取的订单详情：{}", chargerOrder);
            return new ObjectResponse<>(chargerOrder);
        } catch (Exception e) {
            throw new DcServiceException(e.getMessage());
        }

    }


    /**
     * 判断集团授信账户是否有正在进行中的订单
     *
     * @param rblocUserIds
     * @return
     */
    @RequestMapping("/api/order/queryChargingFlagByRblocUserIds")
    public ObjectResponse<Boolean> queryChargingFlagByRblocUserIds(
        @RequestParam("rblocUserIds") List<Long> rblocUserIds) {
        if (CollectionUtils.isEmpty(rblocUserIds)) {
            throw new DcArgumentException("集团授信账户不能为空!");
        }

        try {
            boolean flag = chargerOrderService.queryChargingFlagByRblocUserIds(rblocUserIds);
            return new ObjectResponse<>(flag);
        } catch (Exception e) {
            throw new DcServiceException(e.getMessage());
        }
    }

    @RequestMapping("/api/order/queryChargingOrderByCorpId")
    public ObjectResponse<Boolean> queryChargingOrderByCorpId(@RequestParam("corpId") Long corpId) {
        if (corpId == null) {
            throw new DcArgumentException("集团商户ID不能为空!");
        }

        try {
            boolean flag = chargerOrderService.queryChargingOrderByCorpId(corpId);
            return new ObjectResponse<>(flag);
        } catch (Exception e) {
            throw new DcServiceException(e.getMessage());
        }
    }

    /**
     * 订单列表 分页page： current 页码  size 每页显示数量
     *
     * @return
     */
    @PostMapping("/api/order/queryChargeOrderList")
    public ListResponse<ChargerOrderVo> queryChargeOrderList(
        @RequestBody ChargerOrderParam chargerOrderParam) {
        return biQueryChargeOrderList(chargerOrderParam);
    }

    /**
     * 获取充电订单接口
     *
     * @param request
     * @param param
     * @return
     */
    @PostMapping("/api/order/listChargeOrder")
    public ListResponse<ChargerOrderVo> listChargeOrder(
        ServerHttpRequest request,
        @RequestBody ListChargeOrderParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        return chargerOrderQueryService.listChargeOrder(param);
    }

    /**
     * 订单列表 分页page： current 页码  size 每页显示数量 用于bi统计图的订单查询
     *
     * @return
     */
    @PostMapping("/api/order/biQueryChargeOrderList")
    public ListResponse<ChargerOrderVo> biQueryChargeOrderList(
        @RequestBody ChargerOrderParam chargerOrderParam) {
        log.debug("param = {}", chargerOrderParam);
//        Page<ChargerOrderVo> page = new Page<>(chargerOrderParam.getCurrent(), chargerOrderParam.getSize(),
//                chargerOrderParam.getTotal());
        return manageChargerOrderService.queryChargeOrderList(chargerOrderParam, null);
    }

    /**
     * 订单列表 分页page： current 页码  size 每页显示数量
     *
     * @return
     */
    @PostMapping("/api/order/queryOrderListOfCard")
    public ListResponse<ChargerOrderVo> queryOrderListOfCard(
        @RequestParam("_index") Integer index, @RequestParam("_size") Integer size,
        @RequestParam("userId") Long userId,
        @RequestParam("cardNo") String cardNo, @RequestParam("commId") Long commId) {
        log.info(">> 获取充电卡的使用记录[订单]: commId={}, index={}, size={}, cardNo={}",
            commId, index, size, cardNo);

        if (StringUtils.isEmpty(cardNo)) {
            log.info("<< 充电卡的逻辑卡号不能为空");
            throw new DcArgumentException("请传入正确的充电卡逻辑卡号");
        }

        if (null == commId) {
            log.info("<< 需要提供商户Id");
            throw new DcArgumentException("请提供商户Id");
        }

        Page<ChargerOrderVo> page = new Page<>(index, size);
        ListResponse<ChargerOrderVo> result = manageChargerOrderService.queryOrderListOfCard(
            commId, userId, cardNo, page);
        log.info("<< 查询返回");
        return result;
    }

    /**
     * 判断卡片是否存在进行中的订单
     *
     * @return
     */
    @PostMapping("/api/order/checkOrderOfCard")
    public ObjectResponse<Boolean> checkOrderOfCard(
        @RequestParam("cardChipNo") String cardChipNo,
        @RequestParam("commIdChain") String commIdChain) {
        log.info(">> 判断卡片是否存在进行中的订单: commIdChain={}, cardChipNo={}",
            commIdChain, cardChipNo);

        if (StringUtils.isEmpty(cardChipNo)) {
            log.info("<< 充电卡的物理卡号不能为空");
            throw new DcArgumentException("请传入正确的充电卡物理卡号");
        }

        Boolean result = manageChargerOrderService.checkOrderOfCard(commIdChain, cardChipNo);
        log.info("<< 查询结果: result={}", result);
        return new ObjectResponse<>(result);
    }

    /**
     * 筛选出有未结算订单的卡(返回逻辑卡号)
     *
     * @return cardNo 逻辑卡号
     */
    @PostMapping("/api/order/getNoSettlementCard")
    public ListResponse<String> getNoSettlementCard(@RequestBody ChargerOrderParam param) {
        log.info(">> 筛选出有未结算订单的卡: cardChipNoList={}", JsonUtils.toJsonString(param));

        AssertUtil.isTrue(param != null && (CollectionUtils.isNotEmpty(param.getCardChipNoList())
                || CollectionUtils.isNotEmpty(param.getCardNoList())),
            "物理卡号列表or逻辑卡号列表不能为空");

        List<String> noSettlementCardList = manageChargerOrderService.getNoSettlementCard(param);
        log.info("<< 查询结果: result={}", JsonUtils.toJsonString(noSettlementCardList));
        return new ListResponse<String>(noSettlementCardList);
    }

    /**
     * 订单列表 分页page： current 页码  size 每页显示数量
     *
     * @return
     */
    @PostMapping("/api/order/queryOrderListOfVin")
    public ListResponse<ChargerOrderVo> queryOrderListOfVin(
        @RequestParam("_index") Integer index,
        @RequestParam("_size") Integer size,
        @RequestParam("userId") Long userId,
        @RequestParam("vin") String vin,
        @RequestParam("commId") Long commId) {
        log.info(">> 获取VIN码的使用记录[订单]: index={}, size={}, userId={}, vin={}, commId={}",
            index, size, userId, vin, commId);

        if (null == userId) {
            log.info("<< 用户Id不能为空");
            throw new DcArgumentException("请输入正确的用户Id");
        }

        if (index <= 0) {
            log.info("<< 分页索引必须从1开始");
            throw new DcArgumentException("分页索引必须从1开始");
        }

        if (null == commId) {
            log.info("<< 需要提供商户Id");
            throw new DcArgumentException("请提供商户Id");
        }

        if (StringUtils.isEmpty(vin)) {
            log.info("<<");
            throw new DcArgumentException("请传入正确的VIN码");
        }

        Page<ChargerOrderVo> page = new Page<>(index, size);
        ListResponse<ChargerOrderVo> result = manageChargerOrderService.queryOrderListOfVin(
            commId, userId, vin, page);
        log.info("<<");
        return result;
    }

    /**
     * 判断VIN是否存在进行中的订单
     *
     * @return
     */
    @PostMapping("/api/order/checkOrderOfVin")
    public ObjectResponse<Boolean> checkOrderOfVin(@RequestParam("userId") Long userId,
        @RequestParam("vin") String vin,
        @RequestParam("commId") Long commId) {
        log.info(">> 判断VIN是否存在进行中的订单: commId={}, userId={}, vin={}",
            commId, userId, vin);

        if (StringUtils.isEmpty(vin)) {
            log.info("<< VIN码不能为空");
            throw new DcArgumentException("请传入正确的VIN码");
        }

        if (null == userId) {
            log.info("<< 用户Id不能为空");
            throw new DcArgumentException("请输入正确的用户Id");
        }

        if (null == commId) {
            log.info("<< 需要提供商户Id");
            throw new DcArgumentException("请提供商户Id");
        }

        Boolean result = manageChargerOrderService.checkOrderOfVin(commId, userId, vin);
        log.info("<< 查询结果: result={}", result);
        return new ObjectResponse<>(result);
    }

    /**
     * 筛选出有未结算订单的VIN
     *
     * @return VIN码
     */
    @PostMapping("/api/order/getNoSettlementVin")
    public ListResponse<String> getNoSettlementVin(@RequestBody ChargerOrderParam param) {
        log.info(">> 筛选出有未结算订单的VIN: cardChipNoList={}", JsonUtils.toJsonString(param));

        AssertUtil.notNull(param.getTopCommId(), "topCommId 不能为空");
        AssertUtil.isTrue(param != null && CollectionUtils.isNotEmpty(param.getVinList()),
            "VIN列表不能为空");

        List<String> noSettlementVinList = manageChargerOrderService.getNoSettlementVin(param);
        log.info("<< 查询结果: result={}", JsonUtils.toJsonString(noSettlementVinList));
        return new ListResponse<String>(noSettlementVinList);
    }

    /**
     * 授信账户删除 或 从周期限额切换到无限制(包括相反) 检查是否有未结算的订单
     *
     * @return VIN码
     */
    @GetMapping("/api/order/checkNoSettlementCreditAccount")
    public BaseResponse checkNoSettlementCreditAccount(
        @RequestParam(value = "rBlocUserId") Long rBlocUserId) {
        log.info(
            ">> 授信账户删除 或 从周期限额切换到无限制(包括相反) 检查是否有未结算的订单: rBlocUserId={}",
            rBlocUserId);
        AssertUtil.isTrue(rBlocUserId != null && rBlocUserId > 0, "授信账户ID不能为空");
        manageChargerOrderService.getNoSettlementCreditAccount(rBlocUserId);
        return RestUtils.success();
    }


    /**
     * 根据条件查询订单统计数据
     *
     * @param chargerOrderParam
     * @return
     */
    @RequestMapping("/api/order/getChargerOrderData")
    public ObjectResponse<ChargerOrderDataVo> getChargerOrderData(
        @RequestBody ChargerOrderParam chargerOrderParam) {
        //调用服务查询
        ObjectResponse<ChargerOrderDataVo> res = manageChargerOrderService.getChargerOrderData(
            chargerOrderParam);
        return res;
    }

    /**
     * excel汇总数据导出使用
     *
     * @param chargerOrderParam
     * @return
     */

    @RequestMapping("/api/order/exportChargerOrderData")
    public ListResponse<ChargerOrderDataVo> exportChargerOrderData(
        @RequestBody ChargerOrderParam chargerOrderParam) {
        //调用服务查询
        ListResponse<ChargerOrderDataVo> res = manageChargerOrderService.exportChargerOrderData(
            chargerOrderParam);
        return res;
    }

    /**
     * 根据条件查出订单统计数据（包含尖峰平谷）
     */
    @RequestMapping("/api/order/getChargerOrderDetail")
    public ObjectResponse<ChargerOrderDetailVo> getChargerOrderDetail(
        @RequestBody ChargerOrderParam chargerOrderParam) {
        //调用服务查询
        log.debug("param = {}", chargerOrderParam);
        ObjectResponse<ChargerOrderDetailVo> res = manageChargerOrderService.getChargerOrderDetail(
            chargerOrderParam);
        log.debug("res = {}", res);
        return res;
    }

    /**
     * 分页查询枪头订单列表
     *
     * @param commIdList        当前商户及子商户列表
     * @param boxOutFactoryCode 桩号
     * @param beginTime
     * @param endTime
     * @param status
     * @param channelId
     * @return
     */
    @GetMapping("/api/order/selectChargerOrderListByConnectId")
    public ListResponse<ChargerOrder> selectChargerOrderListByConnectId(ServerHttpRequest request,
        ServerWebExchange exh,
        @RequestParam(value = "commIdList", required = false) List<Long> commIdList,
        String boxOutFactoryCode, String connectorId, String beginTime,
        String endTime, String status, Integer channelId) {

//        if (CollectionUtils.isEmpty(commIdList)) {
//            throw new DcArgumentException("参数错误");
//        }

        OldPageParam page = getPage2(request, exh, false);

        return manageChargerOrderService.selectChargerOrderListByConnectId(commIdList, page,
            boxOutFactoryCode, connectorId, beginTime,
            endTime, status, channelId);
    }


    /**
     * 批量查入分时订单
     *
     * @param chargerOrderTimeDivisionList
     */
    @RequestMapping(value = "/api/order/insertChargerOrderTimeDivisionList")
    public BaseResponse insertChargerOrderTimeDivisionList(
        @RequestParam("chargerOrderTimeDivisionList") String chargerOrderTimeDivisionList) {
        log.info("开始插入分时订单==》{}", chargerOrderTimeDivisionList);
        List<ChargerOrderTimeDivision> chargerOrderTimeDivisions = JsonUtils.toJsonList(
            chargerOrderTimeDivisionList);
        chargerOrderService.insertChargerOrderTimeDivisionList(chargerOrderTimeDivisions);
        return BaseResponse.success();
    }

    /**
     * 查询分时订单列表
     *
     * @param orderNo
     */
    @RequestMapping(value = "/api/order/queryOrderTimeDivisionList")
    public ListResponse<ChargerOrderTimeDivision> queryOrderTimeDivisionList(
        @RequestParam("orderNo") String orderNo) {
        List<ChargerOrderTimeDivision> list = chargerOrderService.queryOrderTimeDivisionList(
            orderNo);
        ListResponse<ChargerOrderTimeDivision> res = new ListResponse<>(list, (long) list.size());
        return res;
    }

    /**
     * 根据openOrderId获取订单信息,互联互通调用
     *
     * @param openOrderId
     * @return
     */
    @RequestMapping(value = "/api/order/selectByOpenOrderId")
    public ObjectResponse<ChargerOrder> selectByOpenOrderId(
        @RequestParam("openOrderId") String openOrderId) {
        if (StringUtils.isBlank(openOrderId)) {
            throw new DcArgumentException("获取订单信息失败，openOrderId为空");
        }
        try {
            ChargerOrder chargerOrder = chargerOrderService.selectByOpenOrderId(openOrderId);
            return new ObjectResponse<>(chargerOrder);
        } catch (Exception e) {
            throw new DcServiceException(e.getMessage());
        }
    }


    /**
     * 根据vin列表查询对应订单数 VIN忽略大小写
     */
    @PostMapping(value = "/api/order/queryOrderCountByVins")
    public ListResponse<VinOrderCount> queryOrderCountByVins(
        @RequestBody VinOrderCountParam param, ServerHttpRequest request) {
        log.info(
            LoggerHelper2.formatEnterLog(request, false) + "; param = " + JsonUtils.toJsonString(
                param));
        return new ListResponse<>(chargerOrderService.queryOrderCountByVins(param));
    }


    /**
     * 同步分时订单数据到统计（异常处理）
     *
     * @return
     */
    @RequestMapping("/api/order/synchroOrderTimeDivisionData")
    public ObjectResponse synchroOrderTimeDivisionData() {
        try {
            log.info("【同步分时订单数据到统计】param===>null");
            ObjectResponse resultEntity = chargerOrderService.synchroOrderTimeDivisionData();
            return resultEntity;
        } catch (Exception e) {
            log.error("【同步分时订单数据到统计】异常：{};e:{}", e.getMessage(), e);
            throw new DcServiceException("系统繁忙");
        }
    }

    /**
     * 手动修改异常订单
     *
     * @param orderVo
     * @return
     */
    @PostMapping("/api/order/updateOrder")
    public ObjectResponse<ChargerOrder> updateOrder(ServerHttpRequest request,
        @RequestBody UpdateOrderVo orderVo) {
        log.info(" 手动修改订单: orderVo={}", JsonUtils.toJsonString(orderVo));

        String orderNo = orderVo.getOrderNo();
        if (StringUtils.isBlank(orderNo)) {
            log.info("<< 订单号为空，终止处理.");
            throw new DcArgumentException("订单号不能为空");
        }

        // 判断当前用户是否具有停止订单权限 -- 暂时不考虑
        try {
            ChargerOrderPo oldOrder = chargerOrderService.updateOrder(orderVo, true);
            chargerOrderService.abnormalOrderPay(oldOrder, orderVo);
        } catch (DcException ex) { // 保留已知处理的异常
            log.info("处理逻辑异常: {}", ex.getMessage(), ex);
            throw ex;
        } catch (Exception e) {
            log.error("error_message：", e);
            throw new DcServiceException("修改出现异常");
        }
        ObjectResponse result = new ObjectResponse<ChargerOrder>(
            chargerOrderService.getChargeOrder(orderNo, false));
        log.info("<< 手动修改订单结果: result={}", result);
        return result;
    }

    /**
     * 异常订单处理
     *
     * @param orderNo
     * @return
     */
    @RequestMapping(value = "/api/order/handleErrorOrder")
    public Mono<ObjectResponse<String>> handleErrorOrder(ServerHttpRequest request,
        @RequestParam String orderNo,
        @RequestParam(required = false) OrderAbnormalReason abnormalReason) {
        log.info(
            LoggerHelper2.formatEnterLog(request)
                + " 处理异常订单开始. orderNo = {} abnormalReason = {}",
            orderNo, abnormalReason);
        if (StringUtils.isBlank(orderNo)) {
            throw new DcServiceException("订单不能为空，请检查");
        }
        return Mono.just(RestUtils.buildObjectResponse(orderNo))
            .doOnNext(o -> chargerOrderService.handleErrorOrder(orderNo, abnormalReason));
//        return Mono.just();
    }


    /**
     * 获取已完成的订单详情
     *
     * @param orderNo
     * @return
     */
    @ResponseBody
    @GetMapping("/api/order/getFinishedOrderDetail")
    public ObjectResponse<ChargerOrderFinishVo> getFinishedOrderDetail(
        @RequestParam(value = "orderNo", required = false) String orderNo) {

        if (ObjectUtils.isEmpty(orderNo)) {
            throw new DcArgumentException("参数错误");
        }

        try {
            return chargerOrderService.queryFinishedOrderDetail(orderNo);
        } catch (Exception e) {
            log.error("获取订单详情失败:" + e.getMessage(), e);
            //return new CommonRpcResponse<>(null, ResultConstant.RES_FAIL_CODE, "获取订单详情失败");
            throw new DcServiceException("获取订单详情失败");
        }

    }

    /**
     * 超时订单自动结算 （用户未进行支付确认的订单（状态=800）超过 30 天，自动以订单产生的实际费用结算。）
     */
    @PostMapping(value = "/api/order/autoPayOrderByBalance")
    @Operation(summary = "超时订单自动结算")
    public BaseResponse autoPayOrderByBalance(
        @Parameter(name = "订单超时时间（小时）") @RequestParam(value = "deadlineTime", required = false) Integer deadlineTime,
        @Parameter(name = "开始日期") @RequestParam(value = "startDays", required = false) Integer startDays,
        @Parameter(name = "上传开始日期") @RequestParam(value = "stopFromDays", required = false) Integer stopFromDays,
        @Parameter(name = "客户UID") @RequestParam(value = "cusId", required = false) Long cusId,
        @Parameter(name = "场站ID") @RequestParam(value = "siteId", required = false) String siteId,
        @Parameter(name = "是否包含异常订单") @RequestParam(value = "includeAbnormal", required = false) Boolean includeAbnormal)
        throws Exception {
        log.info(
            "超时订单自动结算. deadlineTime = {}, startDays = {}, cusId = {}, siteId = {}, includeAbnormal = {}",
            deadlineTime, startDays, cusId, siteId, includeAbnormal);
        this.chargerOrderService.autoPayOrderByBalance(
            deadlineTime, startDays, stopFromDays, cusId, siteId, includeAbnormal);
        return BaseResponse.success();
    }

    @PostMapping(value = "/api/order/orderRedisToMysqlJob")
    public BaseResponse orderRedisToMysqlJob() {
        chargerOrderService.orderRedisToMysqlJob();
        return BaseResponse.success();
    }

    @PostMapping(value = "/api/order/finishZeroFeeErrorOrder")
    public BaseResponse finishZeroFeeErrorOrder() {
        chargerOrderService.finishZeroFeeErrorOrder();
        return BaseResponse.success();
    }

    /**
     * 异常订单处理 若订单超过设定时间未更新或“充电中”状态超过设定时间未改变，则订单标记为异常
     *
     * @return
     */
    @GetMapping("/api/order/abnormalOrderHandle")
    public BaseResponse abnormalOrderHandle() {
        chargerOrderService.abnormalOrderHandle();
        return new BaseResponse();
    }

    /**
     * 异常订单处理 超过设定时间，“订单启动” 状态未改变,则订单标记为异常（即订单未激活）
     *
     * @return
     */
    @GetMapping("/api/order/abnormalOrderStartingHandle")
    public BaseResponse abnormalOrderStartingHandle() {
        chargerOrderService.abnormalOrderStartingHandle();
        return new BaseResponse();
    }


    /**
     * 将超过48小时还未支付的信用充订单标记为异常
     */
    @GetMapping("/api/order/abnormalCreditOrderHandle")
    public BaseResponse abnormalCreditOrderHandle() {
        chargerOrderService.abnormalCreditOrderHandle();
        return new BaseResponse();
    }


    /**
     * 支付充电订单
     *
     * @param param
     * @return
     */
    @Operation(summary = "支付充电订单")
    @PostMapping(value = "/api/order/payChargeOrder")
    public BaseResponse payChargeOrder(@RequestBody PayOrderParam param) {
        orderPaymentBizService.payChargeOrder(param);
        return BaseResponse.success();
    }

    /**
     * 支付占位订单
     *
     * @param param
     * @return
     */
    @Operation(summary = "支付占位订单")
    @PostMapping(value = "/api/order/payOvertimeParkOrder")
    public BaseResponse payOvertimeParkOrder(@RequestBody PayOrderParam param) {
        orderPaymentBizService.payOvertimeParkOrder(param);
        return BaseResponse.success();
    }

    /**
     * @deprecated 已废弃
     */
    @GetMapping("/api/order/getChargeOrder")
    @Deprecated
    public ObjectResponse<ChargeOrderVo> getChargeOrder(
        @RequestParam(value = "orderNo") String orderNo) throws ParseException {
        log.error("deprecated!!! orderNo = {}", orderNo);
        return chargerOrderQueryService.getChargeOrder(orderNo);
    }

    /**
     * 查询订单列表通用接口(可拓展)
     *
     * @param param
     * @return
     */
    @PostMapping("/api/order/getChargeOrderListUnify")
    public ListResponse<ChargeOrderVo> getChargeOrderListUnify(
        @RequestBody ListChargeOrderParam param) throws ParseException {
        return chargerOrderQueryService.getChargeOrderListUnify(param);
    }


    @Operation(summary = "用户获取订单数统计（小程序）")
    @GetMapping("/api/order/getOrderBi")
    public ObjectResponse<OrderBi> getOrderListCount(
        @Parameter(name = "所属商户id") @RequestParam(value = "topCommId", required = false) Long topCommId,
        @Parameter(name = "用户id") @RequestParam(value = "userId", required = false) Long userId) {
        log.debug("查询订单数量（小程序），topCommId：{},userId：{},", topCommId, userId);
        if (topCommId == null || topCommId == 0) {
            throw new DcArgumentException("商户id参数错误");
        }
        if (userId == null || userId == 0) {
            throw new DcArgumentException("用户id参数错误");
        }

        ObjectResponse<OrderBi> res = chargerOrderQueryService.getOrderListCount(topCommId, userId);
        FeignResponseValidate.check(res);
        log.info("查询订单数量（小程序）res：{}", JsonUtils.toJsonString(res.getData()));
        return res;
    }

    @Operation(summary = "获取用户未处理的异常订单（小程序）")
    @GetMapping("/api/order/getUserAbnormalOrder")
    public ListResponse<ChargerOrder> getUserAbnormalOrder(
        @Parameter(name = "所属商户id") @RequestParam(value = "commId", required = false) Long commId,
        @Parameter(name = "用户id") @RequestParam(value = "userId", required = false) Long userId) {
        log.debug("获取用户未处理的异常订单（小程序），commId：{},userId：{},", commId, userId);
        if (commId == null || commId == 0) {
            throw new DcArgumentException("商户id参数错误");
        }
        if (userId == null || userId == 0) {
            throw new DcArgumentException("用户id参数错误");
        }

        ListResponse<ChargerOrder> res = chargerOrderQueryService.getUserAbnormalOrder(userId,
            commId);
        FeignResponseValidate.check(res);
        log.debug("获取用户未处理的异常订单（小程序）res：{}",
            JsonUtils.toJsonString(res.getData().size()));
        return res;
    }

    /**
     * 导出excel用
     *
     * @param chargerOrderParam
     * @return
     */
    @Operation(summary = "导出excel用")
    @PostMapping("/api/order/exportChargeOrderListV2")
    public ListResponse<ChargerOrderVo> exportChargeOrderListV2(
        @RequestBody ChargerOrderParam chargerOrderParam) {
        return manageChargerOrderService.exportChargeOrderListV2(chargerOrderParam);
    }


    @Operation(summary = "用户是否存在异常充电订单: true -- 存在；false -- 不存在（仅支持个人账户查询）")
    @GetMapping("/api/order/userHasAbnormalOrder")
    public ObjectResponse<Boolean> userHasAbnormalOrder(
        @Parameter(name = "顶级商户ID", required = true) @RequestParam(value = "topCommId") Long topCommId,
        @Parameter(name = "用户ID", required = true) @RequestParam(value = "uid") Long uid,
        @Parameter(name = "商户ID", required = false) @RequestParam(value = "commId", required = false) Long commId,
        ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request));
        Boolean result = chargerOrderService.userHasAbnormalOrder(topCommId, uid, commId);
        log.info("result = {}", result);
        return RestUtils.buildObjectResponse(result);
    }

    @Operation(summary = "订单超停标记")
    @GetMapping(value = "/api/order/overtimeParking")
    public BaseResponse overtimeParking(
        ServerHttpRequest request,
        @Parameter(name = "充电订单no", required = true)
        @RequestParam(value = "orderNo") String orderNo) {
        log.info(LoggerHelper2.formatEnterLog(request));
//        chargerOrderService.overtimeParking(orderNo);
        overTimeParkingService.overtimeParking(orderNo);
        return RestUtils.success();
    }

//    @Operation(summary = "订单超停标记")
//    @GetMapping(value = "/api/order/overtimeParkingTest")
//    public BaseResponse test(
//        ServerHttpRequest request,
//        @Parameter(name = "充电订单no", required = true)
//        @RequestParam(value = "orderNo") String orderNo,
//        @Parameter(name = "场站id", required = true)
//        @RequestParam(value = "siteId") String siteId,
//        @RequestParam("fromDate") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date fromDate,
//        @RequestParam("toDate") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date toDate) {
//        log.info(LoggerHelper2.formatEnterLog(request));
//        OvertimeParkFeeOrderPo po = new OvertimeParkFeeOrderPo();
//        po.setCalFromTime(fromDate)
//            .setOrderNo(orderNo)
//            .setCalToTime(toDate);
//        chargerOrderService.createOverTimeParkingOrder(po, siteId);
//        return RestUtils.success();
//    }

    /**
     * 根据场站id结合场站配置的无卡充电订单结算账号，尝试结算该场站下所有未支付订单
     *
     * @param siteId
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/api/order/payNoCardOrderBySiteId")
    @Operation(summary = "尝试结算该场站下所有未支付订单")
    public BaseResponse payNoCardOrderBySiteId(@Parameter(name = "订单超时时间（小时）")
    @RequestParam(value = "siteId") String siteId,
        @Parameter(name = "支付账户信息")
        @RequestBody NoCardPayAccountInfo accountInfo) throws Exception {
        this.chargerOrderService.payNoCardOrderBySiteId(siteId, accountInfo);
        return BaseResponse.success();
    }


    @Operation(summary = "根据站点ID获取一条非完成订单记录")
    @GetMapping("/api/order/getUnfinishOrderBysiteId")
    public ListResponse<ChargerOrder> getUnfinishOrderBysiteId(
        @Parameter(name = "所属商户id") @RequestParam(value = "siteId", required = false) String siteId
    ) {
        log.debug("根据站点ID获取一条非完成订单记录，siteId：{},", siteId);
        if (siteId == null) {
            throw new DcArgumentException("站点ID不能为空");
        }

        return chargerOrderQueryService.getUnfinishOrderBysiteId(siteId);

    }

    @Operation(summary = "根据企业ID获取未完成订单记录")
    @GetMapping("/api/order/getUnfinishOrderByCorpId")
    public ListResponse<ChargerOrder> getUnfinishOrderBysiteId(
        @Parameter(name = "企业id") @RequestParam(value = "corpId") Long corpId) {
        log.debug("根据企业ID获取未完成订单记录，corpId：{},", corpId);
        IotAssert.isNotNull(corpId, "企业id不能为空");

        return chargerOrderQueryService.getUnfinishOrderByCorpId(corpId);

    }

    @Operation(summary = "cec订单对账结果确认")
    @GetMapping("/api/order/cecOrderConfirm")
    public BaseResponse cecOrderConfirm(@RequestParam(value = "openOrderId") String openOrderId,
        @RequestParam(value = "checkingResult") Integer checkingResult) {
        log.info("cec订单对账结果确认 openOrderId: {}, checkingResult: {}", openOrderId,
            checkingResult);
        return chargerOrderService.cecOrderConfirm(openOrderId, checkingResult);
    }

    @Operation(summary = "即充即退订单刷新支付中状态")
    @GetMapping("/api/order/overtimeParking/prepayFlush")
    public BaseResponse prepayFlush(
        @RequestParam(value = "bufSecond", required = false, defaultValue = "60")
        Integer bufSecond) {
        log.info("即充即退订单刷新支付中状态, 延迟: {}秒", bufSecond);
        overtimeParkOrderService.prepayFlush(bufSecond);
        return BaseResponse.success();
    }

    @Operation(summary = "监管订单入库")
    @PostMapping("/api/order/monitorOrderUpdateOrSave")
    public BaseResponse monitorOrderUpdateOrSave(@RequestBody MonitorChargerOrderParam param) {
        ChargerOrderPo chargerOrderPo = param.getChargerOrderPo();
        ChargerOrderPayPo chargerOrderPayPo = param.getChargerOrderPayPo();
        List<ChargerOrderTimeDivision> chargerOrderTimeDivisionList = param.getChargerOrderTimeDivisionList();
        log.info("监管订单入库 param: {} ", JsonUtils.toJsonString(param));
        return chargerOrderService.monitorOrderUpdateOrSave(chargerOrderPo, chargerOrderPayPo,
            chargerOrderTimeDivisionList);
    }

    /**
     * 小程序支付确认页面-根据订单查询付款金额
     *
     * @param userId
     * @param orderNo
     * @param scoreSettingId
     * @param couponId
     * @return
     */
    @GetMapping(value = "/api/charging/getPayAmount")
    public ObjectResponse<BigDecimal> getPayAmount(
        @RequestParam(value = "userId") Long userId,
        @RequestParam(value = "orderNo") String orderNo,
        @RequestParam(value = "scoreSettingId", required = false) Long scoreSettingId,
        @RequestParam(value = "couponId", required = false) Long couponId) {
        log.debug(
            "根据订单查询付款金额: userId = {}, orderNo = {}, scoreSettingId = {}, couponId = {}",
            userId, orderNo,
            scoreSettingId, couponId);
        return chargerOrderService.getPayAmount(userId, orderNo, scoreSettingId, couponId);

    }
}
