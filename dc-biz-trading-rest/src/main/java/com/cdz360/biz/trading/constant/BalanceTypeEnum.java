package com.cdz360.biz.trading.constant;
/**
 * 收支类型枚举
 * <AUTHOR>
 *
 */
public enum BalanceTypeEnum {
    ACTUALLY_RECEIVE(1,"实收"),
    ACTUALLY_PAID(2,"实付"),
    PROXY_RECEIVE(3,"代收"),
    PROXY_PAID(4,"代付");

    private Integer code;
    private String value;

    private BalanceTypeEnum(Integer code,String value){
        this.code = code;
        this.value = value;
    }

    public static String getValue(Integer code){
        for (BalanceTypeEnum e : BalanceTypeEnum.values()) {
            if( e.getCode().equals(code) ){
                return e.value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    //TODO 根据业务类型判断收支类型拓展业务还需更改

    /**
     *
     * @param billType
     * @return
     */
    public static Integer getBanlanceTypeBy(Integer billType) {
        if (billType==null) {
            return null;
        }else if (billType.equals(BillTypeEnum.RECHARGE.getCode()) || billType.equals(BillTypeEnum.REFUND_TO_BALANCE.getCode()) || billType.equals(BillTypeEnum.DEPOSIT_PAY.getCode())
                || billType.equals(BillTypeEnum.RECHARGE_TO_BYDCARD.getCode())) {
            return BalanceTypeEnum.PROXY_RECEIVE.getCode();
        }else if (billType.equals(BillTypeEnum.BALANCE_DEDUCTION.getCode()) || billType.equals(BillTypeEnum.WITHDRAWALS.getCode()) || billType.equals(BillTypeEnum.DEPOSIT_WITHDRAWALS.getCode())) {
            return BalanceTypeEnum.PROXY_PAID.getCode();
        }else if(billType.equals(BillTypeEnum.ORDER_ENTER.getCode())){
            return BalanceTypeEnum.ACTUALLY_RECEIVE.getCode();
        }else if (billType.equals(BillTypeEnum.REFUND.getCode()) || billType.equals(BillTypeEnum.RECHARGE_TO_BYDCARDCONSUM.getCode())) {
            return BalanceTypeEnum.ACTUALLY_PAID.getCode();
        }else{
            return null;
        }

    }

}