package com.cdz360.biz.trading.config;

import java.math.BigDecimal;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * orderAbnormalConditionConfig
 *  异常
 * @since 2019/8/6
 * <AUTHOR>
 */
@Data
@Component
public class OrderAbnormalConditionConfig {
    //    @Value("${order.abnormal.heartbeatTimeout:120000}")
//    private long heartbeatTimeout; //心跳超过设定时间未上报 毫秒，预设2分钟; //桩枪状态上报时实现(时间暂不能确定为2分钟)
    @Value("${order.abnormal.maxIdleTime:30}")
    private long maxIdleTime; //订单超过设定时间未更新,需大于redis中订单落库的定时时间(目前是10分钟) 分钟 预设30分钟;
    @Value("${order.abnormal.maxKwh:500}")
    private long maxKwh; //订单电量超过设定值 KWH 预设500KWH;
    @Value("${order.abnormal.maxPrice:500}")
    private BigDecimal maxPrice; //订单金额超过设定值 元 预设2000元;
    @Value("${order.abnormal.maxDuration:172800}")
    private int maxDuration; //订单时长超过设定值 秒 预设48小时;
    @Value("${order.abnormal.maxCharingTime:1440}")
    private long maxCharingTime; //“充电中”状态超过设定时间未改变 分钟 预设1440分钟(24小时);
    @Value("${order.abnormal.maxStartingTime:5}")
    private long maxStartingTime; //超过设定时间“订单启动”状态未改变 分钟 预设5分钟;
}