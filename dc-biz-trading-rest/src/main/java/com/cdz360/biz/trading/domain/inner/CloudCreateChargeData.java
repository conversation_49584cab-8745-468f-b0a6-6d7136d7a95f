package com.cdz360.biz.trading.domain.inner;

import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.biz.model.trading.order.vo.ChargerOrderData;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.domain.vo.AuthMediaResult;
import com.chargerlinkcar.framework.common.domain.vo.CreateChargeOrderParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CloudCreateChargeData extends ChargerOrderData {
    CreateChargeOrderParam param;
    PlugVo plug;
    com.cdz360.biz.model.cus.basic.vo.UserVo user;
    SitePo site;
    Commercial siteComm;
    AuthMediaResult authResult;
    String gaodeOpenid;
}
