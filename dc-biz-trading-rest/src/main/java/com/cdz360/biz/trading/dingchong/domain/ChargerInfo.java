package com.cdz360.biz.trading.dingchong.domain;
//
//import lombok.Data;
//
//import java.io.Serializable;
//
///**
// * <AUTHOR>
// * 
// * @since 2019/5/30
// **/
//@Data
//public class ChargerInfo implements Serializable {
//    /**
//     * bcCode :
//     * bcId : 100033
//     * businessId : 33421
//     * chargerName : A枪
//     * commercialName : 中创高科
//     * connectorId : 1
//     * current : 0
//     * currentType : 1
//     * deviceId : 203639556306
//     * deviceType : 0
//     * electric : 0
//     * lastHeartbeatTime : 1559181116727
//     * qrCode :
//     * remark :
//     * serialNumber :
//     * siteId : 20190425458189635487161604
//     * siteName : 南笙哥私人场站
//     * status : 12
//     * templateName :
//     * voltage : 0
//     */
//
//    /**
//     * 主键ID
//     */
//    private Long bcId;
//
//    /**
//     * 枪完整编号 如：010+***********+01
//     */
//    private String fullPlugNo;
//    /**
//     * 枪头名称
//     */
//    private String chargerName;
//    /**
//     * 设备ID
//     */
//    private String deviceId;
//    /**
//     * 设备序列号
//     */
//    private String serialNumber;
//    /**
//     * 设备类型
//     */
//    private Integer deviceType;
//    /**
//     * 充电接口序号
//     */
//    private Integer connectorId;
//    /**
//     * 代理商ID
//     */
//    private String businessId;
//    /**
//     * 代理商名称/运营商名称
//     */
//    private String commercialName;
//    /**
//     * 站点ID
//     */
//    private String siteId;
//    /**
//     * 站点名称
//     */
//    private String siteName;
//    /**
//     * 交直流类型**0-交流 1-直流**
//     */
//    private Integer currentType;
//    /**
//     * 二维码
//     */
//    private String qrCode;
//    /**
//     * 备注
//     */
//    private String remark;
//    /**
//     * 充电接口状态{@linkcom.chargerlink.car.domain.constant.ConnectorStatusEnum}
//     */
//    private Integer status;
//    /**
//     * 最后心跳时间**毫秒时间戳**
//     */
//    private Long lastHeartbeatTime;
//    /**
//     * 计费模板名称
//     */
//    private String templateName;
//    /**
//     * 电流
//     */
//    @Deprecated
//    private Integer current = 0;
//    /**
//     * 电压
//     */
//    @Deprecated
//    private Integer voltage = 0;
//    /**
//     * 电量
//     */
//    @Deprecated
//    private long electric = 0;
//
//    /**
//     * 桩名
//     */
//    private String evseName;
//
//
//}
