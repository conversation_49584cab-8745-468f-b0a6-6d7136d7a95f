package com.cdz360.biz.trading.constant;

public interface PayConstants {

    /**
     * 支付状态
     * 
    */
    int PAY_STATUS_PENDING = 1;//待支付
    int PAY_STATUS_PAID = 2;//已支付
    int PAY_STATUS_FAILED = 3;//支付失败



    /**
     * 支付类型：4,保证金提现
     */
    int REFUND_BY_BOND = 4;//保证金提现

    /**
     * 支付状态：0未支付或未退款
     */
    int PAY_UNPAID  = 0;
    /**
     * 支付状态1已支付或已退款
     */
    int PAY_PAID  = 1;
    /**
     * 支付状态2支付失败或退款失败
     */
    int PAY_FAIL  = 2;
    /**
     * 支付状态3发起支付或退款成功
     */
    int PAY_OPEN_SUCCESS  = 3;
    /**
     * 支付状态4发起支付失败
     */
    int PAY_OPEN_FAIL  = 4;
    /**
     * 支付状态5超时
     */
    int PAY_OPEN_TIMEOUT  = 5;

    int OVERTIME_PARK_ORDER_STATUS_0 = 0;// 占位费未支付
    int OVERTIME_PARK_ORDER_STATUS_100 = 100;// 占位费已支付
    int OVERTIME_PARK_ORDER_STATUS_200 = 200;// 占位费不需支付
    int OVERTIME_PARK_ORDER_STATUS_201 = 201;// 占位费拉黑不需支付
    int OVERTIME_PARK_ORDER_STATUS_202 = 202;// 占位费取消不需支付
    int OVERTIME_PARK_ORDER_STATUS_300 = 300;// 占位费不需记录
    int OVERTIME_PARK_ORDER_STATUS_400 = 400;// 占位费支付中
    int OVERTIME_PARK_ORDER_STATUS_500 = 500;// 占位费后付费

}
