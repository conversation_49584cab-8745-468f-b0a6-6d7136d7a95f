package com.cdz360.biz.trading.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 卡实体
 *
 * <AUTHOR>
 */
@Data
public class CardVo implements Serializable{

    private static final long serialVersionUID = -7368573993693423126L;

    /**
     * 卡Id
     */
    private Long cardId;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 卡类型
     */
    private Integer type;

    /**
     * 卡状态
     */
    private Integer status;

    /**
     * 卡激活时间
     */
    private Date cardActivationDate;
}
