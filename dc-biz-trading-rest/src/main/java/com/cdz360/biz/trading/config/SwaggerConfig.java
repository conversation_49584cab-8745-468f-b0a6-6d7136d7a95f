package com.cdz360.biz.trading.config;

import org.springframework.context.annotation.Configuration;

@Configuration
public class SwaggerConfig {
//
//    final String desc = "交易相关，订单，流水，支付等接口";
//    final ApiInfo apiInfo = new ApiInfo("chargerlink-car-trading", desc,
//            "0.0.1", "", null,
//            "", "", Collections.emptyList());
//
//    @Bean
//    public Docket petApi() {
//        return new Docket(DocumentationType.SWAGGER_2).select()
//                // 仅显示 com.chargerlinkcar.core.rest 目录下的接口
//                .apis(RequestHandlerSelectors.basePackage("com.chargerlinkcar.core"))
//                .build().apiInfo(apiInfo);
//    }


}
