package com.cdz360.biz.trading.dingchong.request;

import com.cdz360.base.model.base.vo.BaseObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 枪头详情参数
 *
 * <AUTHOR>
 * @since 2019/7/8 9:39
 */
@Data
@Schema(description = "枪头详情参数")
@EqualsAndHashCode(callSuper = true)
public class PlugDetailRequest extends BaseObject {

    // 桩编号
    @NotNull
    @Schema(description = "桩编号")
    private String evseId;

    // 枪编号
    @NotNull
    @Schema(description = "枪编号")
    private Integer plugId;

    // 开始时间
    @NotNull
    @Schema(description = "开始时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @Schema(description = "场站id")
    private String siteId;

    // 结束时间
    @NotNull
    @Schema(description = "结束时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stopTime;

    @Schema(description = "页码索引")
    private int page = 0;

    @Schema(description = "每一页大小")
    private int size = 1;
}
