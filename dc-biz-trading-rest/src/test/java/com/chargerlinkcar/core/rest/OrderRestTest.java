package com.chargerlinkcar.core.rest;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.biz.trading.rest.OrderRest;
import com.cdz360.biz.trading.service.ChargerOrderService;
import com.chargerlinkcar.core.BaseMockTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.http.MediaType;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@Slf4j
@WebAppConfiguration
class OrderRestTest extends BaseMockTest {

    @Mock
    private ChargerOrderService chargerOrderService;

    @Test
    void overtimeParking() throws Exception {
        String url = "/api/order/overtimeParking";

        String orderNo = "243013416962";
        MockHttpServletRequestBuilder rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("orderNo", orderNo);

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(OrderRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("overtimeParking")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andDo(print())
                .andReturn();
    }
}