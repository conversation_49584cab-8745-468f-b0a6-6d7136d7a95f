package com.cdz360.biz.bi.service.download;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.biz.bi.BaseMockTest;
import com.cdz360.biz.bi.service.ExcelFileService;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
class DownloadFileProxyTest extends BaseMockTest {

    @Autowired
    private ExcelFileService excelFileService;

    @Test
    void generate() throws IOException {
        String reqParam = "{\"size\": 10, \"carNo\": \"00598\", \"total\": true, \"filter\": [\"orderNo\", \"createTime\", \"payAccountName\", \"orderStatus\", \"orderType\", \"orderPrice\", \"orderElectricity\", \"boxCode\", \"source\", \"cardName\", \"customerName\", \"mobilePhone\"], \"subDir\": \"c302bb91cba4f8398e24218c9975610a\", \"current\": 1, \"orderNo\": \"************\", \"platform\": 21, \"topCommId\": -1, \"createTimeTo\": \"*************\", \"commStationIds\": [\"3160044475212629556\", \"3913277009499505662\", \"*******************\", \"3403180229055990650\", \"1519343806319568001\", \"3684837561742001572\", \"3678244550335226836\", \"7658366861274406829\", \"8058439282852612493\", \"*******************\", \"8840881617541663003\", \"2011023874750909028\", \"7710846335296641879\", \"7294148391574027695\"], \"createTimeFrom\": \"*************\", \"orderSourceLst\": [{\"id\": 185, \"size\": 0, \"type\": \"orderSource\", \"index\": 0, \"label\": \"其他\", \"value\": \"0\", \"description\": \"订单来源\"}, {\"id\": 186, \"size\": 0, \"type\": \"orderSource\", \"index\": 0, \"label\": \"设备触发\", \"value\": \"1\", \"description\": \"订单来源\"}, {\"id\": 187, \"size\": 0, \"type\": \"orderSource\", \"index\": 0, \"label\": \"用户应用\", \"value\": \"2\", \"description\": \"订单来源\"}, {\"id\": 188, \"size\": 0, \"type\": \"orderSource\", \"index\": 0, \"label\": \"互联互通\", \"value\": \"3\", \"description\": \"订单来源\"}, {\"id\": 189, \"size\": 0, \"type\": \"orderSource\", \"index\": 0, \"label\": \"管理后台\", \"value\": \"4\", \"description\": \"订单来源\"}], \"orderStatusLst\": [{\"id\": 42, \"size\": 0, \"type\": \"orderStatus\", \"index\": 0, \"label\": \"订单未激活\", \"value\": \"0\", \"description\": \"订单状态\"}, {\"id\": 43, \"size\": 0, \"type\": \"orderStatus\", \"index\": 0, \"label\": \"充电中\", \"value\": \"200\", \"description\": \"订单状态\"}, {\"id\": 44, \"size\": 0, \"type\": \"orderStatus\", \"index\": 0, \"label\": \"待支付\", \"value\": \"800\", \"description\": \"订单状态\"}, {\"id\": 45, \"size\": 0, \"type\": \"orderStatus\", \"index\": 0, \"label\": \"已结算\", \"value\": \"2000\", \"description\": \"订单状态\"}, {\"id\": 46, \"size\": 0, \"type\": \"orderStatus\", \"index\": 0, \"label\": \"离线中\", \"value\": \"-500\", \"description\": \"订单状态\"}], \"deviceCommIdList\": [34650, 35172, 35176, 35179, 35180, 35181, 35204, 35205, 35206, 35209, 35210, 35212, 34648, 34649, 34734, 34740, 34751], \"excludeCancelOrder\": false}";
        String filePosition = "{\"subDir\": \"download_job/20220606\", \"subFileName\": \"9df3aba3b8684aff9b8a1aede440010b\"}";
        ChargerOrderParam orderParam = JsonUtils.fromJson(reqParam, ChargerOrderParam.class);
        orderParam.setExcelPosition(JsonUtils.fromJson(filePosition, ExcelPosition.class));
        String taskId = orderParam.getExcelPosition().getSubFileName();
        if (NumberUtils.equals(23, orderParam.getPlatform())) { //企业账户导出
            orderParam.setSheetName("订单汇总");
            excelFileService.writeTempExcelByChargeOrderForCompany(taskId, orderParam);
        } else if (NumberUtils.equals(21, orderParam.getPlatform())) { //管理平台导出
            orderParam.setSheetName("订单明细");
            excelFileService.writeTempExcelByChargeOrderList(taskId, orderParam);
        } else {
            log.warn("不支持的请求 platform: {}", orderParam.getPlatform());
        }
    }

}