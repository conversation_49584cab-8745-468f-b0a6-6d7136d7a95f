package com.cdz360.biz.bi.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "流程PDF导出表单字段数据")
@Data
@Accessors(chain = true)
public class OaFieldItem {

    @Schema(description = "索引(可用来排序)")
    private Integer index;

    @Schema(description = "字段ID")
    private String id;

    @Schema(description = "字段类型(可自定义)")
    private String type;

    @Schema(description = "字段LABEL值")
    private String label;

    @Schema(description = "字段具体值")
    private Object value;
}
