package com.cdz360.biz.bi.utils;

import freemarker.core.Environment;
import freemarker.core.InvalidFormatParametersException;
import freemarker.core.TemplateDateFormat;
import freemarker.core.TemplateDateFormatFactory;
import freemarker.core.TemplateFormatUtil;
import freemarker.core.UnformattableValueException;
import freemarker.core.UnparsableValueException;
import freemarker.template.TemplateDateModel;
import freemarker.template.TemplateModelException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

/**
 * 时间格式: yyyy年MM月
 */
public class EnnMonthTemplateDateFormatFactory extends TemplateDateFormatFactory {

    public static final EnnMonthTemplateDateFormatFactory INSTANCE
        = new EnnMonthTemplateDateFormatFactory();

    private EnnMonthTemplateDateFormatFactory() {
        // Defined to decrease visibility
    }

    @Override
    public TemplateDateFormat get(String params, int dateType,
        Locale locale, TimeZone timeZone, boolean zonelessInput,
        Environment env)
        throws InvalidFormatParametersException {
        TemplateFormatUtil.checkHasNoParameters(params);
        return EnnMonthTemplateDateFormat.INSTANCE;
    }

    private static class EnnMonthTemplateDateFormat extends TemplateDateFormat {

        private static final EnnMonthTemplateDateFormat INSTANCE
            = new EnnMonthTemplateDateFormat();

        private final DateFormat javaDateFormat;

        private EnnMonthTemplateDateFormat() {
            javaDateFormat = new SimpleDateFormat("yyyy年MM月");
        }

        @Override
        public String formatToPlainText(TemplateDateModel dateModel)
            throws UnformattableValueException, TemplateModelException {
            return javaDateFormat.format(TemplateFormatUtil.getNonNullDate(dateModel));
        }

        @Override
        public boolean isLocaleBound() {
            return false;
        }

        @Override
        public boolean isTimeZoneBound() {
            return false;
        }

        @Override
        public Date parse(String s, int dateType) throws UnparsableValueException {
            try {
                return javaDateFormat.parse(s);
            } catch (ParseException e) {
                throw new UnparsableValueException(e.getMessage(), e);
            }
        }

        @Override
        public String getDescription() {
            return "时间格式: yyyy年MM月";
        }

    }
}
