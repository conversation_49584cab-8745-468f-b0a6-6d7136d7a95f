package com.cdz360.biz.bi.domain.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * PointLogExportVo
 *
 * @since 12/30/2019 4:57 PM
 * <AUTHOR>
 */
@Data
public class PointLogExportVo implements Serializable {

    @ExcelField(title = "订单号", sort = 1)
    private String orderNo;
    @ExcelField(title = "操作", sort = 2)
    private String operator;
    @ExcelField(title = "操作类型", sort = 3)
    private String operatorType;
    @ExcelField(title = "创建时间", sort = 4)
    private String createTime;
    @ExcelField(title = "交易总金额(元)", sort = 5)
    private BigDecimal amount;
    @ExcelField(title = "实际金额(元)", sort = 6)
    private BigDecimal realAmount;
    @ExcelField(title = "赠送金额(元)", sort = 7)
    private BigDecimal freeAmount;
    @ExcelField(title = "交易后余额(元)", sort = 15)
    private BigDecimal point;
    @ExcelField(title = "操作来源", sort = 20)
    private String sourceType;
    @ExcelField(title = "支付渠道", sort = 24)
    private String payType;

}