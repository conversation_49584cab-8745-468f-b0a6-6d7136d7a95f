package com.cdz360.biz.bi.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.bi.config.ExportFileConfig;
import com.cdz360.biz.bi.domain.vo.BalanceApplicationExportVo;
import com.cdz360.biz.bi.feign.AuthCenterFeignClient;
import com.cdz360.biz.ds.cus.ro.balance.param.BalanceApplicationCheckParam;
import com.cdz360.biz.ds.cus.ro.balance.param.BalanceApplicationParam;
import com.cdz360.biz.model.cus.balance.po.BalanceApplicationCheckPo;
import com.cdz360.biz.model.cus.balance.po.BalanceApplicationPo;
import com.cdz360.biz.model.cus.balance.type.BalanceCheckType;
import com.cdz360.biz.model.cus.balance.type.DepositFlowType;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.chargerlinkcar.framework.common.feign.UserFeignClient;
import com.chargerlinkcar.framework.common.utils.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * BalanceApplicationBiService
 *
 * @since 7/6/2021 11:15 AM
 * <AUTHOR>
 */
@Slf4j
@Service
public class BalanceApplicationBiService {

    @Autowired
    private ExportFileConfig exportFileConfig;

    // 默认启动金额
//    @Value("${excel.dir:/tmp/excel}")
//    private String TEMP_DIR;

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    private static final String PART_FILE_NAME = "part";

    private static final SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public void exportExcel(ExcelPosition position, BalanceApplicationParam balanceApplicationParam)
        throws IOException {
        log.info("充值申请导出: pos={}, param={}", position, balanceApplicationParam);

//        try {
        ExcelUtil.builder(exportFileConfig.getExcelDir(), position, "金额变更申请列表")
            .addHeader(BalanceApplicationExportVo.class)
            .loopAppendData((start, size) -> {
                balanceApplicationParam
                    .setIndex((long) ((start - 1) * size))
                    .setSize(size);

                ListResponse<BalanceApplicationPo> res = userFeignClient.searchBalanceApplication(
                    balanceApplicationParam);
                return new ArrayList<>(res.getData());
            }, list -> new ArrayList<>(list.stream()
                .map(i -> (BalanceApplicationPo) i)
                .map(this::map2ExportVo)
                .collect(Collectors.toList())))
            .write2File();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
    }

    private List<BalanceApplicationExportVo> map2ExportVo(List<BalanceApplicationPo> voList) {
        return voList.stream().map(this::map2ExportVo).collect(Collectors.toList());
    }

    private BalanceApplicationExportVo map2ExportVo(BalanceApplicationPo balanceApplicationPo) {
        BalanceApplicationExportVo ret = new BalanceApplicationExportVo();

        if (balanceApplicationPo.getAmount() == null) {
            balanceApplicationPo.setAmount(BigDecimal.ZERO);
        }
        if (balanceApplicationPo.getFreeAmount() == null) {
            balanceApplicationPo.setFreeAmount(BigDecimal.ZERO);
        }
        BeanUtils.copyProperties(balanceApplicationPo, ret);

        BigDecimal sum = ret.getAmount().add(ret.getFreeAmount());
        if (DepositFlowType.IN_FLOW.equals(ret.getFlowType())) {
            ret.setSumAmount("+" + sum.toString());
        } else {
            ret.setSumAmount("-" + sum.toString());
        }

//        if(balanceApplicationPo.getTaxStatus() != null) {
//            ret.setTaxStatus(balanceApplicationPo.getTaxStatus().getDesc());
//        }

//        if(balanceApplicationPo.getTaxType() != null) {
//            ret.setTaxType(balanceApplicationPo.getTaxType().getDesc());
//        }

//        if(balanceApplicationPo.getFlowInAccountType() != null) {
//            ret.setFlowInAccountType(balanceApplicationPo.getFlowInAccountType().getDesc());
//        }

//        if(balanceApplicationPo.getFlowType() != null) {
//            ret.setFlowType(balanceApplicationPo.getFlowType().getDesc());
//        }

        BalanceApplicationCheckParam params = new BalanceApplicationCheckParam();
        params.setApplicationId(balanceApplicationPo.getId());
        ListResponse<BalanceApplicationCheckPo> checkList = userFeignClient.getCheckList(params);
        // 审核记录列表
        List<BalanceApplicationCheckPo> data;
        if (checkList == null || CollectionUtils.isEmpty(checkList.getData())) {
            data = List.of();
        } else {
            data = checkList.getData();
        }

        List<Long> ids = data.stream()
            .map(BalanceApplicationCheckPo::getOperatorId)
            .collect(Collectors.toList());
        ids.add(balanceApplicationPo.getApplierId());

        // 查询用户列表
        List<Long> idsParam = ids.stream()
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(idsParam)) {

            ListResponse<SysUserVo> sysUserVoListResponse = authCenterFeignClient.querySysUserByIds(
                idsParam);
            if (sysUserVoListResponse != null && CollectionUtils.isNotEmpty(
                sysUserVoListResponse.getData())) {
                Map<Long, SysUserVo> userMap = sysUserVoListResponse.getData()
                    .stream()
                    .collect(Collectors.toMap(SysUserVo::getId, o -> o));

                // 填入申请者
                if (userMap.get(balanceApplicationPo.getApplierId()) != null) {
                    ret.setApplierName(userMap.get(balanceApplicationPo.getApplierId()).getName());
                }

                // 填入各阶段审批人
                data.forEach(e -> {
                    if (BalanceCheckType.CHECK.equals(e.getType())) {
                        ret.setCheckResult(e.getResult());
                        ret.setCheckRemark(e.getRemark());
                        if (userMap.get(e.getOperatorId()) != null) {
                            ret.setCheckUserName(userMap.get(e.getOperatorId()).getName());
                        }
                        ret.setCheckDate(format.format(e.getCreateTime()));
                    } else if (BalanceCheckType.RECHECK.equals(e.getType())) {
                        ret.setRecheckResult(e.getResult());
                        ret.setRecheckRemark(e.getRemark());
                        if (userMap.get(e.getOperatorId()) != null) {
                            ret.setRecheckUserName(userMap.get(e.getOperatorId()).getName());
                        }
                        ret.setRecheckDate(format.format(e.getCreateTime()));
                    } else if (BalanceCheckType.REVIEW.equals(e.getType())) {
                        ret.setReviewResult(e.getResult());
                        ret.setReviewRemark(e.getRemark());
                        if (userMap.get(e.getOperatorId()) != null) {
                            ret.setReviewUserName(userMap.get(e.getOperatorId()).getName());
                        }
                        ret.setReviewDate(format.format(e.getCreateTime()));
                    }
                });
            }

        }

        return ret;
    }

}