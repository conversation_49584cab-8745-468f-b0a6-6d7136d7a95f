package com.cdz360.biz.bi.domain.vo;

import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.biz.model.annotations.ExcelField;
import com.cdz360.biz.model.cus.balance.type.BalanceApplicationStatusType;
import com.cdz360.biz.model.cus.balance.type.BalanceCheckResultType;
import com.cdz360.biz.model.cus.balance.type.DepositFlowType;
import com.cdz360.biz.model.finance.type.FlowInAccountType;
import com.cdz360.biz.model.finance.type.TaxStatus;
import com.cdz360.biz.model.finance.type.TaxType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode
@ToString
@Schema(description = "充电管理平台客户中充值管理导出使用")
public class BalanceApplicationExportVo implements Serializable {

    @ExcelField(title = "申请单号", sort = 1)
    private Long id;

    @ExcelField(title = "状态", sort = 1, convert = "com.cdz360.biz.model.cus.balance.type.BalanceApplicationStatusType")
    private BalanceApplicationStatusType status;

    @ExcelField(title = "申请人", sort = 1)
    private String applierName;

    @ExcelField(title = "申请时间", sort = 1, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ExcelField(title = "申请类型", sort = 1, convert = "com.cdz360.biz.model.cus.balance.type.DepositFlowType")
    private DepositFlowType flowType;

    @ExcelField(title = "账户手机号", sort = 1)
    private String phone;

    @ExcelField(title = "账户类型", sort = 1, convert = "com.cdz360.base.model.base.type.PayAccountType")
    private PayAccountType accountType;

    @ExcelField(title = "账户名称", sort = 1)
    private String username;


    @ExcelField(title = "变更总金额(元)", sort = 1)
    private String sumAmount;

    @ExcelField(title = "实际金额(元)", sort = 1)
    private BigDecimal amount;

    @ExcelField(title = "赠送金额(元)", sort = 1)
    private BigDecimal freeAmount;

    //    @ExcelField(title = "申请人", sort = 1)
//    private Long commId;
    @ExcelField(title = "开票状态", sort = 1, convert = "com.cdz360.biz.model.finance.type.TaxStatus")
    private TaxStatus taxStatus;

    @ExcelField(title = "开票种类", sort = 1, convert = "com.cdz360.biz.model.finance.type.TaxType")
    private TaxType taxType;

    @ExcelField(title = "发票号码", sort = 1)
    private String taxNo;

    @ExcelField(title = "备注", sort = 1)
    private String remark;

    @ExcelField(title = "支付方式", sort = 1, convert = "com.cdz360.base.model.base.type.PayChannel")
    private PayChannel payChannel;

    @ExcelField(title = "支付账户", sort = 1)
    private String outAccountName;

    @ExcelField(title = "支付银行名称", sort = 1)
    private String outBankName;

    @ExcelField(title = "支付银行账号", sort = 1)
    private String outAccountNo;

    @ExcelField(title = "收款账户类型", sort = 1, convert = "com.cdz360.biz.model.finance.type.FlowInAccountType")
    private FlowInAccountType flowInAccountType;

    @ExcelField(title = "收款账户", sort = 1)
    private String inAccountName;

    @ExcelField(title = "收款账户银行名称", sort = 1)
    private String inBankName;

    @ExcelField(title = "收款账户银行账号", sort = 1)
    private String inAccountNo;

//    @ExcelField(title = "申请人", sort = 1)
//    private Long userId;

//    @ExcelField(title = "申请人", sort = 1)
//    private Long accountCode;

    @ExcelField(title = "初审结果", sort = 1, convert = "com.cdz360.biz.model.cus.balance.type.BalanceCheckResultType")
    private BalanceCheckResultType checkResult;

    @ExcelField(title = "初审备注", sort = 1)
    private String checkRemark;

    @ExcelField(title = "初审人", sort = 1)
    private String checkUserName;

    @ExcelField(title = "初审时间", sort = 1, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String checkDate;

    @ExcelField(title = "复审结果", sort = 1, convert = "com.cdz360.biz.model.cus.balance.type.BalanceCheckResultType")
    private BalanceCheckResultType recheckResult;

    @ExcelField(title = "复审备注", sort = 1)
    private String recheckRemark;

    @ExcelField(title = "复审人", sort = 1)
    private String recheckUserName;

    @ExcelField(title = "复审时间", sort = 1, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String recheckDate;

    @ExcelField(title = "复核结果", sort = 1, convert = "com.cdz360.biz.model.cus.balance.type.BalanceCheckResultType")
    private BalanceCheckResultType reviewResult;

    @ExcelField(title = "复核备注", sort = 1)
    private String reviewRemark;

    @ExcelField(title = "复核人", sort = 1)
    private String reviewUserName;

    @ExcelField(title = "复核时间", sort = 1, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String reviewDate;

//    @ExcelField(title = "申请人", sort = 1)
//    private Long applierId;
//    @ExcelField(title = "申请人", sort = 1)
//    private String attachment;
//    @ExcelField(title = "申请人", sort = 1)
//    private Date updateTime;

}