package com.cdz360.biz.bi.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.chargerlinkcar.framework.common.domain.invoice.InvoicedRecordVo;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceInfoParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.ListInvoicedRecordParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceInfoVo;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedModelVo;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedTemplateSalDetailVo;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedTemplateSalVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class InvoiceFeignClientHystrixFactory implements FallbackFactory<InvoiceFeignClient> {
    @Override
    public InvoiceFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_INVOICE, throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_INVOICE, throwable.getStackTrace());
        return new InvoiceFeignClient() {
            @Override
            public ObjectResponse<InvoicedTemplateSalVo> getInvoicedTempSal(Long commId, String saleTin) {
                log.error("获取开票主体: {}, commId = {}, saleTin = {}",
                        DcConstants.KEY_FEIGN_DC_BIZ_INVOICE, commId, saleTin);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<CorpInvoiceInfoVo> getCorpInvoiceDetail(CorpInvoiceInfoParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }



            @Override
            public ListResponse<InvoicedTemplateSalDetailVo> getInvoicedTemplateSalDetailsByInvoiceType(
                    String invoiceType, Long commercialId, int page, int size) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ListResponse();
            }



            @Override
            public BaseResponse signForbiddenExportToInvoice(
                    List<String> orderIds, String token) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<InvoicedRecordVo> getAllInvoicedRecords(ListInvoicedRecordParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<InvoicedRecordVo> getAllInvoicedRecordsByUserId(Long userId, int page, int size) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<InvoicedRecordVo> getAllInvoicedRecordsByUserIdAndStatus(Long userId, String invoicedStatus, int page, int size) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<InvoicedModelVo> getDefaultInvoicedModel(Long userId) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }


        };
    }
}
