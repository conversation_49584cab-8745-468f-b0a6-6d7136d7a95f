package com.cdz360.biz.bi.domain.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * SettlementOrderExcelVo
 * 
 * @since 7/3/2020 5:00 PM
 * <AUTHOR>
 */
@Data
public class SettlementOrderExcelVo implements Serializable {

    private static final long serialVersionUID = -8573784445175081013L;

    @ExcelField(title = "订单号", sort = 1)
    private String orderNo;

    //    @ExcelField(title = "订单状态", sort = 2)
    private String orderStatus;// refill

    @ExcelField(title = "手机号", sort = 3)
    private String mobilePhone;

    @ExcelField(title = "启动方式", sort = 4)
    private String orderType;// refill

    @ExcelField(title = "站点名称", sort = 5)
    private String stationName;

    @ExcelField(title = "尖时电量", sort = 6, digits = 4)
    private BigDecimal elecTag1;

    @ExcelField(title = "峰时电量", sort = 7, digits = 4)
    private BigDecimal elecTag2;

    @ExcelField(title = "平时电量", sort = 8, digits = 4)
    private BigDecimal elecTag3;

    @ExcelField(title = "谷时电量", sort = 9, digits = 4)
    private BigDecimal elecTag4;

    @ExcelField(title = "总电量", sort = 10, digits = 4)
    private BigDecimal orderElectricity;

    @ExcelField(title = "原价电费", sort = 11, digits = 2)
    private BigDecimal elecPrice;

    @ExcelField(title = "原价服务费", sort = 12)
    private BigDecimal servicePrice;

    @ExcelField(title = "原价总额", sort = 13)
    private BigDecimal orderPrice;

    @ExcelField(title = "创建时间", sort = 14, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ExcelField(title = "充电时间", sort = 15)
    private String chargeTime;// refill

    @ExcelField(title = "上传时间", sort = 16, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date stopTime;

}