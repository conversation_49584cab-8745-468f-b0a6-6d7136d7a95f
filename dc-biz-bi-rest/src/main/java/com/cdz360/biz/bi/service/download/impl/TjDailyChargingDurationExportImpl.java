package com.cdz360.biz.bi.service.download.impl;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.biz.bi.service.SimService;
import com.cdz360.biz.bi.service.TjDailyChargingDurationService;
import com.cdz360.biz.bi.service.download.IFileExport;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.sim.param.ListSimParam;
import com.cdz360.biz.model.tj.kc.param.ListTjDailyChargingDurationParam;
import jakarta.annotation.PostConstruct;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TjDailyChargingDurationExportImpl extends AbstractFileExport<ListTjDailyChargingDurationParam, ExcelPosition>
    implements IFileExport<ListTjDailyChargingDurationParam, ExcelPosition> {

    @Autowired
    private TjDailyChargingDurationService tjDailyChargingDurationService;

    @PostConstruct
    public void init() {
        downloadFileProxy.addProxy(DownloadFunctionType.TJ_DAILY_CHARGING_DURATION, this);
    }

    @Override
    public Class<ListTjDailyChargingDurationParam> paramClazz() {
        return ListTjDailyChargingDurationParam.class;
    }

    @Override
    public void genFile(String context, String pos) throws IOException {
        this.tjDailyChargingDurationService.exportTjDailyChargingDurationListExcel(this.convertPos(pos), this.convert(context));
    }
}
