package com.cdz360.biz.bi.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.biz.bi.service.ExcelExportService;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.site.param.ListOvertimeParkFeeOrderParam;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "超停收费充电订单")
@Slf4j
@RestController
public class OvertimeParkFeeOrderRest {

    @Autowired
    private ExcelExportService excelExportService;

    @Operation(summary = "超停收费订单导出")
    @PostMapping(value = "/bi/overtimeParkFeeOrder/exportOrderBi")
    public ObjectResponse<ExcelPosition> exportOvertimeParkFeeOrder(
        @RequestBody ListOvertimeParkFeeOrderParam param) {
        throw new DcServiceException("接口已废弃，请联系开发");

//        log.debug("超停收费订单导出: {}", JsonUtils.toJsonString(param));
//        ExcelPosition position = new ExcelPosition();
//        position.setSubFileName(UUIDUtils.getUuid32());
//        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
//
//        param.setExcelPosition(position);
//        excelExportService.exportOvertimeParkFeeOrder(param);
//        log.debug("excel position : {}", JsonUtils.toJsonString(position));
//        return RestUtils.buildObjectResponse(position);
    }
}
