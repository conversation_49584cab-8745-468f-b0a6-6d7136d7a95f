package com.cdz360.biz.bi.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.bi.service.YwOrderService;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.iot.param.PvProfitTrendParam;
import com.cdz360.biz.model.trading.site.param.RecordParam;
import com.cdz360.biz.model.trading.yw.param.ListYwOrderParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.text.SimpleDateFormat;

@Tag(name = "运维工单相关接口", description = "运维工单相关接口")
@Slf4j
@RestController
public class YwOrderRest {

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMdd");

    @Autowired
    private YwOrderService ywOrderService;

    @Operation(summary = "运维工单列表导出到EXCEL")
    @PostMapping(value = "/bi/excel/exportYwOrderExcel")
    public Mono<ObjectResponse<ExcelPosition>> exportYwOrderExcel(
        @RequestBody ListYwOrderParam param) {
        log.debug("运维工单列表导出到EXCEL: {}", JsonUtils.toJsonString(param));
        throw new DcServiceException("接口已废弃，请联系开发");
//        return Mono.just(new ExcelPosition()
//                .setSubFileName(UUIDUtils.getUuid32())
//                .setSubDir(DATE_FORMAT.format(new Date())))
//                .doOnNext(p -> ywOrderService.exportYwOrderExcel(p, param))
//                .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "巡检工单列表导出到EXCEL")
    @PostMapping(value = "/bi/excel/exportInspectionRecExcel")
    public Mono<ObjectResponse<ExcelPosition>> exportInspectionRecExcel(
        @RequestBody RecordParam param) {
        log.debug("巡检工单列表导出到EXCEL: {}", JsonUtils.toJsonString(param));
        throw new DcServiceException("接口已废弃，请联系开发");
//        return Mono.just(new ExcelPosition()
//                .setSubFileName(UUIDUtils.getUuid32())
//                .setSubDir(DATE_FORMAT.format(new Date())))
//                .doOnNext(p -> ywOrderService.exportInspectionRecExcel(p, param))
//                .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "电量收益趋势到excel")
    @PostMapping(value = "/bi/excel/exportPowerProfitTrendExcel")
    public Mono<ObjectResponse<ExcelPosition>> exportPowerProfitTrendExcel(
        @RequestBody PvProfitTrendParam param) {
        log.debug("电量收益趋势到EXCEL: {}", JsonUtils.toJsonString(param));
        throw new DcServiceException("接口已废弃，请联系开发");
//        return Mono.just(new ExcelPosition()
//                .setSubFileName(UUIDUtils.getUuid32())
//                .setSubDir(DATE_FORMAT.format(new Date())))
//                .doOnNext(p -> ywOrderService.exportPowerProfitTrendExcel(p, param))
//                .map(RestUtils::buildObjectResponse);
    }
}
