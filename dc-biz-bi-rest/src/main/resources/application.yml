
server:
  port: 8288
spring:
  application:
    name: dc-biz-bi-dev
  config:
    import: "configserver:http://oam-test.iot.renwochong.com"
  profiles:
    active: common,rabbitmq,jdbc-bi,redis,zipkin,oss
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    config:
      label: test01

logging:
  level:
    com.cdz360.biz: 'DEBUG'
    com.cdz360.charger: 'DEBUG'
    org.springframework: 'INFO'
    org.springframework.cloud: 'INFO'
    org.springframework.cloud.config: 'INFO'
    org.springframework.cloud.netflix: 'DEBUG'
    org.mybatis: 'DEBUG'
    feign: 'DEBUG'

  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} - %logger{36}.%M\\(%line\\) - %msg%n"

eureka:
  instance:
    hostname: localhost
  client:
    registerWithEureka: false
    fetchRegistry: true
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: http://aaa:<EMAIL>:7001/eureka/

feign:
  hystrix:
    enabled: true

hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 5000
          strategy: SEMAPHORE

##超时时间10000毫秒 = 10秒

ribbon:
  ReadTimeout: 60000
  ConnectTimeout: 60000


springdoc:
  packagesToScan: com.cdz360.biz.bi.rest
  swagger-ui:
    path: /swagger-ui.html



