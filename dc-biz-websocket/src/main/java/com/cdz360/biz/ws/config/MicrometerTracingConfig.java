package com.cdz360.biz.ws.config;

import feign.Capability;
import feign.micrometer.MicrometerCapability;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 日志调用链（traceId）相关配置
 */
@Slf4j
@Configuration
public class MicrometerTracingConfig {


    /**
     * feign接口调用时传递traceId
     */
    @Bean
    public Capability capability(final MeterRegistry registry) {
        return new MicrometerCapability(registry);
    }
}
